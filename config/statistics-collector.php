<?php

use <PERSON><PERSON>ps\StatisticsCollector\Watchers;

return [
    /*
    |--------------------------------------------------------------------------
    | Connection
    |--------------------------------------------------------------------------
    |
    | The connection string provided in the database.php config file. This
    | will be used to collect statistics.
    |
    */
    'connection' =>  'mongodb',

    // If you have an api for a project, enter the id of your application, otherwise enter null.
    'application_id' => 'c0391a56-cdf6-5301-a0a1-c0f645f61eb6',

    // If you have an api for several projects.
    // key => id of application
    // key being the prefix you have defined for your route names
    // Otherwise leave empty
    'applications' => [],

    /*
    |--------------------------------------------------------------------------
    | Allowed / Ignored Paths & Commands
    |--------------------------------------------------------------------------
    |
    | The following array lists the URI paths that will
    | not be watched by the statistic collector.
    |
    */
    'only_paths' => [
        'api/*'
    ],

    'ignore_devices' => [
        'robot',
    ],

    'ignore_paths' => [
        'nova-api*',
        'nova',
    ],

    'watchers' => [
        Watchers\RequestWatcher::class => [
            'enabled' => env('DAILYAPPS_STATISTICS_REQUEST_WATCHER', true),
            'size_limit' => env('DAILYAPPS_STATISTICS_RESPONSE_SIZE_LIMIT', 64),
            'ignore_http_methods' => ['OPTIONS'],
            'ignore_status_codes' => [],
        ],
    ],
];
