## URLs
- **RD** : [http://rd-conges.xefi-apps.fr](https://rd-conges.xefi-apps.fr)
- **RC** : [http://rc-conges.xefi-apps.fr](https://rc-conges.xefi-apps.fr)
- **RH** : [http://rh-conges.xefi-apps.fr](https://rh-conges.xefi-apps.fr)
- **PROD** : [http://conges.xefi.fr](https://conges.xefi.fr)

# Wiki des applications utilisées par l'API Congés

[📚 Required](../../wikis/Required) [📊 ELK logs](https://elastic.lebureauvirtuel.fr/goto/9f213e39a13a9ac5a193b0f6115f2032)

# Lumen PHP Framework

[![Build Status](https://travis-ci.org/laravel/lumen-framework.svg)](https://travis-ci.org/laravel/lumen-framework)
[![Total Downloads](https://poser.pugx.org/laravel/lumen-framework/d/total.svg)](https://packagist.org/packages/laravel/lumen-framework)
[![Latest Stable Version](https://poser.pugx.org/laravel/lumen-framework/v/stable.svg)](https://packagist.org/packages/laravel/lumen-framework)
[![License](https://poser.pugx.org/laravel/lumen-framework/license.svg)](https://packagist.org/packages/laravel/lumen-framework)

Laravel Lumen is a stunningly fast PHP micro-framework for building web applications with expressive, elegant syntax. We believe development must be an enjoyable, creative experience to be truly fulfilling. Lumen attempts to take the pain out of development by easing common tasks used in the majority of web projects, such as routing, database abstraction, queueing, and caching.

## Official Documentation

Documentation for the framework can be found on the [Lumen website](https://lumen.laravel.com/docs).

## Contributing

Thank you for considering contributing to Lumen! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Security Vulnerabilities

If you discover a security vulnerability within Lumen, please send an e-mail to Taylor <NAME_EMAIL>. All security vulnerabilities will be promptly addressed.

## License

The Lumen framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
