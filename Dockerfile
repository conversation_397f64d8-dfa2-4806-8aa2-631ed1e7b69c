FROM registry.xefi-apps.fr/xefi/xefiapp-generic:php8.3 as build

LABEL maintainer="Alexandre DIDIER <<EMAIL>>"

ARG TOKEN

WORKDIR /var/www/

COPY . /var/www/

## Mongo
RUN apt-get update && apt-get install -y libcurl4-openssl-dev \
    pkg-config \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

RUN yes '' | pecl install mongodb

RUN docker-php-ext-enable mongodb


RUN composer config gitlab-token.gitlab.xefi.fr $TOKEN \
    && composer install --no-ansi --no-dev --no-interaction --no-plugins --no-progress --no-scripts --no-suggest --optimize-autoloader \
    && chown -R www-data:www-data .

FROM registry.xefi-apps.fr/xefi/xefiapp-generic:php8.3

LABEL maintainer="Alexandre DIDIER <<EMAIL>>"

WORKDIR /var/www/

## Mongo
RUN apt-get update && apt-get install -y libcurl4-openssl-dev \
    pkg-config \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

RUN yes '' | pecl install mongodb

RUN docker-php-ext-enable mongodb

RUN apt-get update && apt-get install -y supervisor

COPY .supervisord/supervisord.conf /etc/supervisor/supervisord.conf

COPY --from=build /var/www/ /var/www/

RUN chmod +x ./startup.sh

EXPOSE 80

CMD ["./startup.sh"]
