k8s_image: ENC[AES256_GCM,data:Bt9e2IizqMi7wHVSLw3YCO8nsknmh9/fdew/5d24SuuTI3tEHoSjPA==,iv:azSKi7Olu+8Txe+o8+HZz1AitR9nbQgB1LIq3qcgAjU=,tag:auDbZypVQXKrdMnRfzBpJA==,type:str]
k8s_name: ENC[AES256_GCM,data:zLrIkKX0tqQE2xVU3GhAe9ms,iv:1Lx0g3pBJ1b+0r+ne3041hysEtIe8I1av7GOFh9CRHI=,tag:BQr1vSybuvUy6//PQ2lFuw==,type:str]
image_version: ""
replicaCount: ENC[AES256_GCM,data:+w==,iv:gXxV6v4GBnyDnGFQxqNjZV2I6FOLASw+LhUe1l0qB7Q=,tag:ipQEZ7GlwOLbySDc0zZPnw==,type:int]
web_domain: ENC[AES256_GCM,data:cgjQAxcmmLtCMiGU,iv:GWCmb/Gr5Xckh9En1E9VhscvhkL594zZkIsXceWVznw=,tag:MvemcmBIUGUtVItE8niXOQ==,type:str]
sops:
    kms: []
    gcp_kms: []
    azure_kv: []
    hc_vault:
        - vault_address: http://sv-vault-dev.ns-vault-dev.svc.cluster.local:8200
          engine_path: transit
          key_name: rd-conges-api-test
          created_at: "2025-05-19T07:57:46Z"
          enc: vault:v1:FLvIcACxWTQnFZ7N810DSqVWakaD7123lao8mK1Um2VoCOgbkS4LRKEGl3Mi6MmDBu0+7UOpLZ/ocJK3
    age: []
    lastmodified: "2025-05-19T07:57:46Z"
    mac: ENC[AES256_GCM,data:5xXum9T1MwxB5x3yzCN6wXuyJ9EBMLDB8YggpdJ97RgoIrPajwkRO+I2frw75I2iut/BIa2nHYmES/JwnQFEDlFxQgy2Eqv2gzNxHQVoqr+I3qgOMMdNq7v9TU38beafm48LzhaWH3VyUdHxnCQf5ga0X2cbnoh0aAp/VgOrZes=,iv:uVJa46PQYiojvMREOBCS26meLe8HGr9tiS2KNn7qzOk=,tag:Wc2iNEXgkDTQy/Gi+UPaFw==,type:str]
    pgp: []
    unencrypted_suffix: _unencrypted
    version: 3.9.4