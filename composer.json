{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.3.0", "ext-fileinfo": "*", "ext-intl": "*", "ext-json": "*", "dailyapps/mailing": "dev-develop", "dailyapps/statistics-collector": "^1.1", "fakerphp/faker": "^1.9.1", "fisharebest/ext-calendar": "^2.5", "guzzlehttp/guzzle": "^7.0", "iteks/laravel-json": "^1.2", "laravel-notification-channels/fcm": "^4.3", "laravel/framework": "^11.0", "laravel/sail": "^1.16", "laravel/sanctum": "^4.0", "laravel/telescope": "^5.0", "laravel/tinker": "^2.5", "league/flysystem-aws-s3-v3": "^3.0", "maatwebsite/excel": "^3.1", "mongodb/laravel-mongodb": "^5.4", "nunomaduro/collision": "^8.1", "predis/predis": "^2.0", "sentry/sentry-laravel": "^4.4", "spatie/icalendar-generator": "^2.5", "spatie/laravel-ignition": "^2.0", "spatie/laravel-medialibrary": "^11.11", "staudenmeir/belongs-to-through": "^2.5", "tailflow/laravel-orion": "^2.17"}, "require-dev": {"mockery/mockery": "^1.3.1", "phpunit/phpunit": "^10.0"}, "repositories": [{"type": "composer", "url": "https://nova.laravel.com"}, {"type": "vcs", "url": "https://gitlab.xefi.fr/xefi/xefiapps/dailyapps/api/dailyapps-mailing.git"}, {"type": "vcs", "url": "https://gitlab.xefi.fr/xefi/xefiapps/dailyapps/api/statistics-collector.git"}], "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "DailyApps\\Mailing\\app\\": "vendor/dailyapps/mailing/src/", "Dailyapps\\StatisticsCollector\\": "vendor/dailyapps/statistics-collector/src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "php artisan vendor:publish --tag=public --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "secure-http": false, "gitlab-domains": ["gitlab.xefi.fr"], "allow-plugins": {"php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}