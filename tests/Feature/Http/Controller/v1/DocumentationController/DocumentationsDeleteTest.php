<?php

namespace Tests\Feature\Http\Controller\v1\DocumentationController;

use App\Models\Api\Documentation;
use App\Models\Api\Site;
use App\Models\Api\User;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class DocumentationsDeleteTest extends TestCase {

//    public function testDeleteDocumentation(): void{
//        Storage::shouldReceive("disk->delete")->andReturn(true);
//        $user = User::whereIn("profile_id", $this->getProfilsAdmin())->first();
//        $JWT = $this->getDummyJWT($user->id);
//        $doc = Site::find($user->site_id)->documentations()->first();
//
//        // Request
//        $response = $this->delete("/api/v1/documentation/". $doc->id, [], ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(200);
//        $response->seeJsonEquals([
//            "message" => "SuccessDeleteFile",
//            "data" => []
//        ]);
//    }
//
//    public function testDeleteDocumentationDontExist(): void{
//        Storage::shouldReceive("disk->delete")->andReturn(true);
//        $user = User::whereIn("profile_id", $this->getProfilsAdmin())->first();
//        $JWT = $this->getDummyJWT($user->id);
//        $doc = Documentation::orderBy('id','desc')->first()->id+1;
//
//        // Request
//        $response = $this->delete("/api/v1/documentation/". $doc, [], ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(404);
//        $response->seeJsonEquals([
//            "message" => "ErrorDeleteFile",
//            "details" => "DocDontExistForClient"
//        ]);
//    }
//
//    public function testDeleteDocumentationDontBelongToUser(): void{
//        Storage::shouldReceive("disk->delete")->andReturn(true);
//        $user = User::whereIn("profile_id", $this->getProfilsAdmin())->first();
//        $JWT = $this->getDummyJWT($user->id);
//        $clientId = $user->site()->first()->client_id;
//        $doc = Documentation::whereDoesntHave('sites', function($q) use($clientId){
//            $q->where('client_id', '=', $clientId);
//        })->first();
//
//        // Request
//        $response = $this->delete("/api/v1/documentation/". $doc->id, [], ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(404);
//        $response->seeJsonEquals([
//            "message" => "ErrorDeleteFile",
//            "details" => "DocDontExistForClient"
//        ]);
//    }

}
