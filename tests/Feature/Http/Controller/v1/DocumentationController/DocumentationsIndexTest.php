<?php

namespace Tests\Feature\Http\Controller\v1\DocumentationController;

use App\Models\Api\User;
use Tests\TestCase;

class DocumentationsIndexTest extends TestCase {


//	public function testIndexDocumentation(): void{
//	    $user = User::first();
//
//        $JWT = $this->getDummyJWT($user->id);
//
//	    // Request
//		$response = $this->get("/api/v1/documentation", ["Authorization" => $JWT]);
//
//		// Assertions
//		$response->assertResponseStatus(200);
//		$response->seeJsonStructure([
//			"message",
//			"data" => [
//				"*" => [
//					"id",
//					"name",
//					"real_name",
//					"path",
//					"deleted_at",
//					"created_at",
//					"updated_at",
//                    "pivot" => [
//                        "site_id",
//                        "documentation_id"
//                    ]
//				]
//			]
//		]);
//		$response->seeJson([
//		    "site_id" => $user->site_id
//        ]);
//	}
}
