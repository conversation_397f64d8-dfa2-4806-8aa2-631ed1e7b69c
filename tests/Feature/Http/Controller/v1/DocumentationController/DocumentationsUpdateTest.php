<?php

namespace Tests\Feature\Http\Controller\v1\DocumentationController;

use App\Models\Api\Documentation;
use App\Models\Api\Site;
use App\Models\Api\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class DocumentationsUpdateTest extends TestCase {

//    public function testUpdateDocumentation(): void{
//        Storage::shouldReceive("disk->delete")->andReturn(true);
//        Storage::shouldReceive("disk->put")->andReturn(true);
//
//        // get an admin
//        $user = User::whereIn("profile_id", $this->getProfilsAdmin())->first();
//        $JWT = $this->getDummyJWT($user->id);
//        // Get Doc
//        $doc = Site::find($user->site_id)->documentations()->first();
//
//        // Request
//        $this->call("PUT", "/api/v1/documentation/". $doc->id, [], [],
//            [ "documentation" => UploadedFile::fake()->image("image.png") ], $this->transformHeadersToServerVars(['Authorization' => $JWT]));
//
//        // Assertions
//        $this->assertResponseStatus(200);
//        $this->seeJson([
//            "message" => "SuccessUpdateDoc",
//            "id" => $doc->id,
//            "name" => Documentation::find($doc->id)->name,
//            "real_name" => "image.png",
//            "path" => Documentation::find($doc->id)->path
//        ]);
//    }
//
//    public function testUpdateDocumentationNoFile(): void{
//
//        // get an admin
//        $user = User::whereIn("profile_id", $this->getProfilsAdmin())->first();
//        $JWT = $this->getDummyJWT($user->id);
//        // Get Doc
//        $doc = Site::find($user->site_id)->documentations()->first();
//
//        // Request
//        $this->call("PUT", "/api/v1/documentation/". $doc->id, [], [],
//            [], $this->transformHeadersToServerVars(['Authorization' => $JWT]));
//
//        // Assertions
//        $this->assertResponseStatus(500);
//        $this->seeJson([
//            "message" => "ErrorLoadFile",
//            "details" => "ErrorLoadFile",
//        ]);
//    }
//
//    public function testUpdateDocumentationNotExist(): void{
//
//        // get an admin
//        $user = User::whereIn("profile_id", $this->getProfilsAdmin())->first();
//        $JWT = $this->getDummyJWT($user->id);
//        // Get Doc that not exist
//        $clientId = $user->site()->first()->client_id;
//        $doc = Documentation::whereDoesntHave('sites', function($q) use($clientId){
//            $q->where('client_id', '=', $clientId);
//        })->first();
//
//        // Request
//        $this->call("PUT", "/api/v1/documentation/". $doc->id, [], [],
//            [ "documentation" => UploadedFile::fake()->image("image.png") ], $this->transformHeadersToServerVars(['Authorization' => $JWT]));
//
//        // Assertions
//        $this->assertResponseStatus(404);
//        $this->seeJson([
//            "message" => "ErrorLoadFile",
//            "details" => "DocDontExistForClient",
//        ]);
//    }
}
