<?php

namespace Tests\Feature\Http\Controller\v1\DocumentationController;

use App\Models\Api\Site;
use App\Models\Api\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class DocumentationsStoreTest extends TestCase {
//    public function testStoreCollectiveAgreementShouldReturnOK() {
//        Storage::shouldReceive("disk->put")->andReturn(true);
//        $user = User::whereIn('profile_id', $this->getProfilsAdmin())->first();
//        $JWT = $this->getDummyJWT($user->id);
//        $this->call("POST", '/api/v1/documentation', [ "data" => "{\"ids\":[".$user->site_id."]}" ], [],
//            [ "documentation" => UploadedFile::fake()->image("image.png") ], $this->transformHeadersToServerVars(['Authorization' => $JWT]));
//
//        $this->assertResponseStatus(200);
//        $this->seeJsonEquals([
//            "message" => "SuccessStoreDoc",
//            "data" => []
//        ]);
//    }
//
//    public function testStoreCollectiveAgreementIfNoData() {
//        $user = User::whereIn('profile_id', $this->getProfilsAdmin())->first();
//        $JWT = $this->getDummyJWT($user->id);
//        $this->call("POST", '/api/v1/documentation', [], [],
//            [ "documentation" => UploadedFile::fake()->image("image.png") ], $this->transformHeadersToServerVars(['Authorization' => $JWT]));
//
//        $this->assertResponseStatus(500);
//        $this->seeJsonEquals([
//            "details" => "ErrorIdArrayDontExist",
//            "message" => "ErrorLoadFile"
//        ]);
//    }
//
//    public function testStoreCollectiveAgreementIfNoFile() {
//        $user = User::whereIn('profile_id', $this->getProfilsAdmin())->first();
//        $JWT = $this->getDummyJWT($user->id);
//        $this->call("POST", '/api/v1/documentation', [ "data" => "{\"ids\":[".$user->site_id."]}" ], [], [], $this->transformHeadersToServerVars(['Authorization' => $JWT]));
//
//        $this->assertResponseStatus(500);
//        $this->seeJsonEquals([
//            "details" => "ErrorLoadFile",
//            "message" => "ErrorLoadFile"
//        ]);
//    }
//
//    public function testStoreCollectiveAgreementIfSiteNotBelongUser() {
//        $user = User::whereIn('profile_id', $this->getProfilsAdmin())->first();
//        $JWT = $this->getDummyJWT($user->id);
//
//        // Get Site that not belong to User
//        $site = Site::where('client_id', '!=', $user->site()->first()->client_id)->first();
//        $this->call("POST", '/api/v1/documentation', [ "data" => "{\"ids\":[" . $site->id. "]}" ], [], [ "documentation" => UploadedFile::fake()->image("image.png") ], $this->transformHeadersToServerVars(['Authorization' => $JWT]));
//
//        $this->assertResponseStatus(404);
//        $this->seeJsonEquals([
//            "details" => "SiteDontExistForClient",
//            "message" => "ErrorLoadFile"
//        ]);
//    }
}
