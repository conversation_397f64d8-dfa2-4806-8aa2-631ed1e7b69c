<?php

namespace Tests\Feature\Http\Controller\v1\DocumentationController;

use App\Models\Api\Documentation;
use App\Models\Api\Site;
use App\Models\Api\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class DocumentationsShowTest extends TestCase {

//    public function testShowDocumentation(): void
//    {
//        Storage::shouldReceive("disk->exists")->andReturn(true);
//        $fileName = "test.jpg";
//        Storage::shouldReceive("disk->download")->andReturn($mockData = UploadedFile::fake()->image($fileName));
//
//        $user = User::first();
//        $JWT = $this->getDummyJWT($user->id);
//        $doc = Site::find($user->site_id)->documentations()->first();
//        $arrayExt  =explode(".",$doc->name);
//        $docExtension = end($arrayExt);
//
//        // Request
//        $response = $this->get("/api/v1/documentation/" . $doc->id, ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(200);
//    }
//
//    public function testShowDocumentationNotExist(): void
//    {
//        Storage::shouldReceive("disk->get")->andReturn($mockData = "test.jpg");
//
//        $user = User::first();
//        $JWT = $this->getDummyJWT($user->id);
//        $clientId = $user->site()->first()->client_id;
//        $doc = Documentation::whereDoesntHave('sites', function($q) use($clientId){
//            $q->where('client_id', '=', $clientId);
//        })->first();
//
//        // Request
//        $response = $this->get("/api/v1/documentation/" . $doc->id, ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(404);
//        $response->seeJsonContains([
//            "message" => "ErrorGetDoc",
//            "details" => "DocDontExistForSite"
//        ]);
//    }
}
