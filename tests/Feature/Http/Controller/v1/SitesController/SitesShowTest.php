<?php

namespace Tests\Feature\Http\Controller\v1\SitesController;

use App\Models\Api\Client;
use App\Models\Api\Site;
use Tests\TestCase;

class SitesShowTest extends TestCase {

	/**
	 * Teste si on récupère la bonne structure de donnée
	 * @return void
	 */
//	public function testShouldReturnOneSite(): void {
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT();
//		$response = $this->get("/api/v1/sites/".Site::first()->id, ["Authorization" => $JWT]);
//
//		$response->assertResponseStatus(200);
//		$response->seeJsonStructure([
//			"message",
//			"data" => [
//				"id",
//				"client_id",
//				"site_database_id",
//				"name",
//				"deleted_at",
//				"created_at",
//				"updated_at"
//			]
//		]);
//	}
//
//	/**
//	 * Teste si on récupère la bonne structure de donnée
//	 * @return void
//	 */
//	public function testShouldReturnOneSiteWithSiteDatabase(): void {
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT();
//		$response = $this->get("/api/v1/sites/".Site::first()->id."?withSiteDatabase=1", ["Authorization" => $JWT]);
//
//		$response->assertResponseStatus(200);
//		$response->seeJsonStructure([
//			"message",
//			"data" => [
//				"id",
//				"client_id",
//				"site_database_id",
//				"name",
//				"deleted_at",
//				"created_at",
//				"updated_at",
//				"site_database" => [
//					"id",
//					"client_id",
//					"host",
//					"username",
//					"password",
//					"database",
//					"type",
//					"is_locked",
//					"deleted_at",
//					"created_at",
//					"updated_at"
//				]
//			]
//		]);
//	}
//
//    /**
//     * Teste si on récupère la bonne structure de donnée
//     * @return void
//     */
//    public function testShouldReturnOneSiteWithDocumentations(): void {
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT();
//        $response = $this->get("/api/v1/sites/".Site::has('documentations')->first()->id."?withDocumentations=1", ["Authorization" => $JWT]);
//
//        $response->assertResponseStatus(200);
//        $response->seeJsonStructure([
//            "message",
//            "data" => [
//                "id",
//                "client_id",
//                "site_database_id",
//                "name",
//                "deleted_at",
//                "created_at",
//                "updated_at",
//                "documentations" => [
//                    "*" => [
//                        "id",
//                        "name",
//                        "real_name",
//                        "path",
//                        "deleted_at",
//                        "created_at",
//                        "updated_at"
//                    ]
//                ]
//            ]
//        ]);
//    }
//
//	/**
//	 * Test l'id du site n'existe pas
//	 * @return void
//	 */
//	public function testShowIfIdNotExist(): void {
//		$id = (Site::orderBy('id','desc')->first()->id)+1;
//		$response = $this->get("/api/v1/sites/" . $id);
//
//		$response->assertResponseStatus(500);
//		$response->seeJson([
//			"message" => "ErrorGetSite"
//		]);
//	}
//
//    /**
//     * Test l'id du site n'existe pas
//     * @return void
//     */
//    public function testShowIfIdNotExistForClient(): void {
//        $id = Site::where('client_id', '!=', Client::first()->id)->first()->id;
//        $response = $this->get("/api/v1/sites/" . $id);
//
//        $response->assertResponseStatus(500);
//        $response->seeJson([
//            "message" => "ErrorDontOwnSite"
//        ]);
//    }
//
//	/**
//	 * Test l'id du site n'est pas un nombre
//	 * @return void
//	 */
//	public function testShowIfIdNotANumber(): void {
//		$id = "azeezaeza";
//		$response = $this->get("/api/v1/sites/" . $id);
//
//		$response->assertResponseStatus(500);
//	}
//
//	/**
//	 * Test l'id du site est < 0
//	 * @return void
//	 */
//	public function testShowIfIdLessThan0(): void {
//		$id = -582;
//		$response = $this->get("/api/v1/sites/" . $id);
//
//		$response->assertResponseStatus(400);
//	}
}
