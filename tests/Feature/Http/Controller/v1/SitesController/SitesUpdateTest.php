<?php

namespace Tests\Feature\Http\Controller\v1\SitesController;

use App\Models\Api\Client;
use App\Models\Api\Site;
use Tests\TestCase;

class SitesUpdateTest extends TestCase {
	/**
	 * Teste si l'élément a été modifié
	 * @return void
	 */
//	public function testUpdateItemUpdated(): void{
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT();
//		$response = $this->put("/api/v1/sites/". Site::first()->id, [
//			"site_database" => [
//				"host" => "**************/sage",
//				"username" => "sage123",
//				"password" => "sage123",
//				"database" => "sage123",
//				"type" => "SAGE"
//			]
//		], ["Authorization" => $JWT]);
//
//		$response->assertResponseStatus(200);
//		$this->assertEquals($response->response['data']['site_database']['id'], Site::first()->site_database_id);
//	}
//
//	/**
//	 * Teste si l'élément n'existe pas
//	 * @return void
//	 */
//	public function testUpdateIfItemNotExist(): void{
//		$id = (Site::orderBy('id', 'desc')->first()->id)+1;
//	    $response = $this->put("/api/v1/sites/". $id, [
//			"site_database" => [
//				"host" => "**************/sage",
//				"username" => "sage123",
//				"password" => "sage123",
//				"database" => "sage123",
//				"type" => "SAGE"
//			]
//		]);
//
//		$response->assertResponseStatus(500);
//		$response->seeJson([
//		    "message" => "ErrorUpdateSite",
//            "details" => "ErrorGetSiteDontOwn ".$id
//        ]);
//	}
//
//	/**
//	 * Teste si l'élément n'existe pas pour le client
//	 * @return void
//	 */
//	public function testUpdateIfItemNotExistForClient(): void{
//		$id = Site::where('client_id', '!=', Client::first()->id)->first()->id;
//		$response = $this->put("/api/v1/sites/" . $id, [
//			"site_database" => [
//				"host" => "**************/sage",
//				"username" => "sage123",
//				"password" => "sage123",
//				"database" => "sage123",
//				"type" => "SAGE"
//			]
//		]);
//
//		$response->assertResponseStatus(500);
//		$response->seeJson([
//			"message" => "ErrorDontOwnSite"
//		]);
//	}
}
