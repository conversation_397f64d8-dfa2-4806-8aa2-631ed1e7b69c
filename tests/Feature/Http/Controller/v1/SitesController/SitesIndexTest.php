<?php

namespace Tests\Feature\Http\Controller\v1\SitesController;

use Tests\TestCase;

class SitesIndexTest extends TestCase {

	/**
	 * Teste si on récupère la bonne structure de donnée
	 * @return void
	 */
//	public function testShouldReturnAllSites(): void{
//		$response = $this->get("/api/v1/sites");
//
//		$response->assertResponseStatus(200);
//		$response->seeJsonStructure([
//			"message",
//			"data" => [
//				"*" => [
//					"id",
//					"client_id",
//					"site_database_id",
//					"name",
//					"collective_agreement_name",
//					"collective_agreement_path",
//					"deleted_at",
//					"created_at",
//					"updated_at"
//				]
//			],
//			"current_page",
//			"next_page_url",
//			"last_page",
//			"total"
//		]);
//	}
//
//	/**
//	 * Teste si on récupère la bonne structure de donnée with site databases
//	 * @return void
//	 */
//	public function testShouldReturnAllSitesWithSiteDatabase(): void{
//		$response = $this->get("/api/v1/sites?withSiteDatabase=1");
//
//		$response->assertResponseStatus(200);
//		$response->seeJsonStructure([
//			"message",
//			"data" => [
//				"*" => [
//					"id",
//					"client_id",
//					"site_database_id",
//					"name",
//					"collective_agreement_name",
//					"collective_agreement_path",
//					"deleted_at",
//					"created_at",
//					"updated_at",
//					"site_database" => [
//						"id",
//						"client_id",
//						"host",
//						"username",
//						"password",
//						"database",
//						"type",
//						"is_locked",
//						"deleted_at",
//						"created_at",
//						"updated_at"
//					]
//				]
//			],
//			"current_page",
//			"next_page_url",
//			"last_page",
//			"total"
//		]);
//	}
//
//    /**
//     * Teste si on récupère la bonne structure de donnée with site databases
//     * @return void
//     */
//    public function testShouldReturnAllSitesWithDocumentations(): void{
//        $response = $this->get("/api/v1/sites?withDocumentations=1");
//
//        $response->assertResponseStatus(200);
//        $response->seeJsonStructure([
//            "message",
//            "data" => [
//                "*" => [
//                    "id",
//                    "client_id",
//                    "site_database_id",
//                    "name",
//                    "collective_agreement_name",
//                    "collective_agreement_path",
//                    "deleted_at",
//                    "created_at",
//                    "updated_at",
//                    "documentations" => [
//                        "*" => [
//                            "id",
//                            "site_id",
//                            "name",
//                            "path",
//                            "deleted_at",
//                            "created_at",
//                            "updated_at"
//                        ]
//                    ]
//                ]
//            ],
//            "current_page",
//            "next_page_url",
//            "last_page",
//            "total"
//        ]);
//    }
}
