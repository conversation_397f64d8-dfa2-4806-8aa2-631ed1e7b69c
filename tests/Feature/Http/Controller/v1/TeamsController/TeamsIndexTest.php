<?php

namespace Tests\Feature\Http\Controller\v1\TeamsController;

use App\Models\Api\User;
use Tests\TestCase;

//class TeamsIndexTest extends TestCase {
//    //Change JWT if expired (best solution we found)
//    private string $JWT;
//
//	/**
//	 * Teste si on récupère la bonne structure de donnée
//	 * @return void
//	 */
//	public function testShouldReturnAllTeams(): void {
//        //Create team for user
//        $users  = User::has('manager', 1)->get();
//        foreach($users as $user) {
//            $user->manager()->detach($user->manager()->first());
//            $user->manager()->attach(User::first(), ['level' => 1]);
//        }
//        //Obtain JWT of user
//        $this->JWT = $this->getDummyJWT(User::first()->id);
//
//        //HTTP Request
//        $response = $this->get("/api/v1/teams", [
//            "Authorization" => $this->JWT
//        ]);
//        $response->assertResponseStatus(200);
//		$response->seeJsonStructure([
//			"message",
//            "data" => [
//                "*" => [
//                    "user_id",
//                    "manager_id",
//                    "level",
//                    "created_at",
//                    "updated_at",
//                    "user" => [
//                        "id",
//                        "profile_id",
//                        "site_id",
//                        "uuid"
//                    ],
//                    "user_leave_counts" => [
//                        "*" => [
//                            "id",
//                            "user_id",
//                            "leave_type_id",
//                            "acquired",
//                            "taken",
//                            "balance"
//                        ]
//                    ]
//                ]
//            ]
//		]);
//	}
//
//    /**
//     * Teste si on récupère la bonne structure de donnée
//     * @return void
//     */
//    public function testShouldReturnAllTeamsWithSites(): void {
//        //Create team for user
//        $users  = User::has('manager', 1)->get();
//        foreach($users as $user) {
//            $user->manager()->detach($user->manager()->first());
//            $user->manager()->attach(User::first(), ['level' => 1]);
//        }
//        //Obtain JWT of user
//        $this->JWT = $this->getDummyJWT(User::first()->id);
//
//        //HTTP Request
//        $response = $this->get("/api/v1/teams?withSites=1", [
//            "Authorization" => $this->JWT
//        ]);
//        $response->assertResponseStatus(200);
//        $response->seeJsonStructure([
//            "message",
//            "data" => [
//                "*" => [
//                    "user_id",
//                    "manager_id",
//                    "level",
//                    "created_at",
//                    "updated_at",
//                    "user" => [
//                        "id",
//                        "profile_id",
//                        "site_id",
//                        "uuid",
//                        "site" => [
//                            "id",
//                            "client_id",
//                            "site_database_id",
//                            "name"
//                        ]
//                    ],
//                    "user_leave_counts" => [
//                        "*" => [
//                            "id",
//                            "user_id",
//                            "leave_type_id",
//                            "acquired",
//                            "taken",
//                            "balance"
//                        ]
//                    ]
//                ]
//            ]
//        ]);
//    }
//}
