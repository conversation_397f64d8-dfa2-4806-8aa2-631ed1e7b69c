<?php

namespace Tests\Feature\Http\Controller\v1\ExportHistoriesController;

use App\Models\Api\Profile;
use App\Models\Api\User;
use Tests\TestCase;

class IndexExportHistoriesTest extends TestCase {
    /**
     * Teste si les éléments ont été modifié
     * @return void
     */
//    public function testIndexExportHistories(): void{
//        // Get Current User
//        $adminProfile = Profile::whereIn('label', ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->get();
//        $currentUser = User::whereIn('profile_id',$adminProfile->pluck('id')->toArray())->has('export_histories')->first();
//        $currentUserId = $currentUser->id;
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($currentUserId);
//
//        $this->get("/api/v1/export-histories", ["Authorization" => $JWT]);
//
//        $this->assertResponseStatus(200);
//        $this->seeJsonStructure([
//            "message",
//            "data" => [
//                "*" => [
//                    "id",
//                    "user_id",
//                    "file_name",
//                    "extension",
//                    "type",
//                    "user" => [
//                        "id",
//                        "profile_id",
//                        "site_id",
//                        "uuid",
//                        "crm_uuid",
//                        "client_uuid",
//                        "firstname",
//                        "lastname",
//                        "email",
//                        "fcm_token",
//                        "picture_path",
//                        "license_path",
//                        "matricule",
//                        "enter_date"
//                    ],
//                ]
//            ]
//        ]);
//        $this->dontSeeJson([
//            "data" => []
//        ]);
//    }
}
