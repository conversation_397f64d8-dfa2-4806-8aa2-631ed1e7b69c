<?php

namespace Tests\Feature\Http\Controller\v1\ExportHistoriesController;

use App\Models\Api\ExportHistory;
use App\Models\Api\Profile;
use App\Models\Api\User;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class DownloadExportTest extends TestCase {

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     */
//    public function testExportUserClassic(): void
//    {
//        // Suppress  output to console
//        $this->setOutputCallback(function() {});
//
//        // Get Current User
//        $adminProfile = Profile::whereIn('label', ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->get();
//        $currentUser = User::whereIn('profile_id',$adminProfile->pluck('id')->toArray())->first();
//        $currentUserId = $currentUser->id;
//        $clientId = $currentUser->site->client_id;
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($currentUserId);
//
//        // 1st Request to export a file
//        $this->post('/api/v1/export-leave-count', [], ['Authorization' => $JWT]);
//
//        // Get ExportHistories to download
//        $exportHistory = ExportHistory::whereHas('user', function ($query) use ($clientId){
//            $query->join("sites","users.site_id","=","sites.id")->where('client_id', '=', $clientId);
//        })->orderBy('id', 'desc')->first();
//
//        // 2nd Request to obtain
//        $file = $this->get('/api/v1/export-histories/' . $exportHistory->id, ['Authorization' => $JWT]);
//
//        // Assertion
//        $this->assertResponseStatus(200);
//        $this->assertTrue(!empty($file));
//
//        // Delete test export file
//        $path = 'exports/' . $clientId . '/' . $exportHistory->file_name;
//        Storage::disk('minio')->delete($path);
//        // Delete line from ExportHistories
//        $exportHistory->delete();
//    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     */
//    public function testExportUserFromAnotherClient(): void
//    {
//        // Suppress  output to console
//        $this->setOutputCallback(function() {});
//
//        // Get Current User
//        $adminProfile = Profile::whereIn('label', ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->get();
//        $currentUser = User::whereIn('profile_id',$adminProfile->pluck('id')->toArray())->first();
//        $clientId = $currentUser->site->client_id;
//
//        // Get ExportHistories to download
//        $exportHistory = ExportHistory::whereHas('user', function ($query) use ($clientId){
//            $query->join("sites","users.site_id","=","sites.id")->where('client_id', '=', $clientId);
//        })->orderBy('id', 'desc')->first();
//
//        // get another Current User
//        $secondCurrentUser = User::whereIn('profile_id',$adminProfile->pluck('id')->toArray())
//            ->join("sites","users.site_id","=","sites.id")->where('client_id', '!=', $clientId)->first();
//        $secondCurrentUserId = $secondCurrentUser->id;
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($secondCurrentUserId);
//
//        // Request to obtain
//        $response = $this->get('/api/v1/export-histories/' . $exportHistory->id, ['Authorization' => $JWT]);
//
//        // Assertion
//        $this->assertResponseStatus(404);
//        $response->seeJsonEquals([
//            "details" => "Fichier introuvable.",
//            "message" => "ErrorGetExportHistory",
//        ]);
//
//        // Delete test export file
//        $path = 'exports/' . $clientId . '/' . $exportHistory->file_name;
//        Storage::disk('minio')->delete($path);
//        // Delete line from ExportHistories
//        $exportHistory->delete();
//    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     */
//    public function testNotExportUserFileNotExist(): void
//    {
//
//        // Get Current User
//        $adminProfile = Profile::whereIn('label', ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->get();
//        $currentUser = User::whereIn('profile_id',$adminProfile->pluck('id')->toArray())->first();
//        $currentUserId = $currentUser->id;
//        $clientId = $currentUser->site->client_id;
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($currentUserId);
//
//        // Get ExportHistories to download
//        $exportHistory = ExportHistory::whereHas('user', function ($query) use ($clientId){
//            $query->join("sites","users.site_id","=","sites.id")->where('client_id', '=', $clientId);
//        })->orderBy('id', 'desc')->first();
//        // Change file_name
//        $exportHistory->file_name = "abc";
//        $exportHistory->save();
//
//        // Request to obtain
//        $response = $this->get('/api/v1/export-histories/' . $exportHistory->id, ['Authorization' => $JWT]);
//
//        // Assertion
//        $this->assertResponseStatus(404);
//        $response->seeJsonEquals([
//            "details" => "Fichier introuvable.",
//            "message" => "ErrorGetExportHistory",
//        ]);
//    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     */
//    public function testNotExportHistoryNotExist(): void
//    {
//        // Get Current User
//        $adminProfile = Profile::whereIn('label', ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->get();
//        $currentUser = User::whereIn('profile_id',$adminProfile->pluck('id')->toArray())->first();
//        $currentUserId = $currentUser->id;
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($currentUserId);
//
//        // Get ExportHistories to download
//        $exportHistoryId = ExportHistory::orderBy('id', 'desc')->first()->id + 1;
//
//        // Request to obtain
//        $response = $this->get('/api/v1/export-histories/' . $exportHistoryId, ['Authorization' => $JWT]);
//
//        // Assertion
//        $this->assertResponseStatus(404);
//        $response->seeJsonEquals([
//            "details" => "ExportNotFound",
//            "message" => "ErrorGetExportHistory",
//        ]);
//    }
}
