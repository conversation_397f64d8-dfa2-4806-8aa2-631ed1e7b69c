<?php

namespace Tests\Feature\Http\Controller\v1\UserLeaveCountsController;

use App\Models\Api\Profile;
use App\Models\Api\User;
use App\Models\Api\UserLeaveCount;
use Tests\TestCase;

class UserLeaveCountsUpdateTest extends TestCase {
//    private function createModels($userEqualClient = "="){
//        // Get Models
//        $userCurrent = User::where("profile_id", "=", Profile::where("label", "=", "ADMINISTRATEUR")->first()->id)->first();
//        $user = User::whereHas('site', function($query) use($userEqualClient,$userCurrent){
//            $query->where("client_id", $userEqualClient, $userCurrent->site->client_id);
//        })->where("id", "!=", $userCurrent->id)->first();
//        $leaveCount = UserLeaveCount::where("user_id","=", $user->id)->first();
//
//        return [$userCurrent, $leaveCount];
//    }
//	/**
//	 * Teste si l'élément a été modifié
//	 * @return void
//	 */
//	public function testUpdateUserLeaveCountItemUpdated(): void{
//        [$userCurrent, $leaveCount] = $this->createModels();
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($userCurrent->id);
//
//        $data = [
//            "acquired" => 1.2,
//            "taken" => 1.1,
//            "balance" => 0.9,
//            "is_last_year" => 1,
//        ];
//
//		$response = $this->put("/api/v1/leave-count/". $leaveCount->id, $data, ["Authorization" => $JWT]);
//
//		$response->assertResponseStatus(200);
//		$response->seeJson($data);
//	}
//
//	/**
//	 * Teste si l'élément n'existe pas
//	 * @return void
//	 */
//	public function testUpdateIfItemNotExist(): void{
//        [$userCurrent, ] = $this->createModels();
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($userCurrent->id);
//
//        $data = [
//            "acquired" => 1.2,
//            "taken" => 1.1,
//            "balance" => 0.9,
//        ];
//
//        $response = $this->put("/api/v1/leave-count/". (UserLeaveCount::orderBy('id', 'desc')->first()->id+1), $data, ["Authorization" => $JWT]);
//
//        $response->assertResponseStatus(404);
//        $response->seeJson([
//            "message" => "ErrorUpdateCompteur",
//            "details" => "UserLeaveCountDoesntExist"
//        ]);
//	}
//
//	/**
//	 * Teste si l'élément n'existe pas pour le client
//	 * @return void
//	 */
//	public function testUpdateIfItemNotExistForClient(): void{
//        [$userCurrent, $leaveCount] = $this->createModels("!=");
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($userCurrent->id);
//
//        $data = [
//            "acquired" => 1.2,
//            "taken" => 1.1,
//            "balance" => 0.9,
//        ];
//
//        $response = $this->put("/api/v1/leave-count/". $leaveCount->id, $data, ["Authorization" => $JWT]);
//
//        $response->assertResponseStatus(403);
//        $response->seeJson([
//            "message" => "ErrorUpdateCompteur",
//            "details" => "UserLeaveCountBelongToAnotherClient"
//        ]);
//	}
//
//    /**
//     * Teste si acquired string
//     * @return void
//     */
//    public function testUpdateAcquiredString(): void{
//        [$userCurrent, $leaveCount] = $this->createModels();
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($userCurrent->id);
//
//        $data = [
//            "acquired" => "abc",
//            "taken" => 1.1,
//            "balance" => 0.9,
//        ];
//
//        $response = $this->put("/api/v1/leave-count/". $leaveCount->id, $data, ["Authorization" => $JWT]);
//
//        $response->assertResponseStatus(422);
//        $response->seeJson([
//            "message" => "ErrorUpdateCompteur",
//            "acquired MustBeNumber"
//        ]);
//    }
//
//    /**
//     * Teste si taken null
//     * @return void
//     */
//    public function testUpdateTakenNull(): void{
//        [$userCurrent, $leaveCount] = $this->createModels();
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($userCurrent->id);
//
//        $data = [
//            "acquired" => 1.2,
//            "taken" => null,
//            "balance" => 0.9,
//        ];
//
//        $response = $this->put("/api/v1/leave-count/". $leaveCount->id, $data, ["Authorization" => $JWT]);
//
//        $response->assertResponseStatus(422);
//        $response->seeJson([
//            "message" => "ErrorUpdateCompteur",
//            "taken MustBeNumber"
//        ]);
//    }
//
//    /**
//     * Teste si is last year not bool
//     * @return void
//     */
//    public function testUpdateIsLastYearNotBool(): void{
//        [$userCurrent, $leaveCount] = $this->createModels();
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($userCurrent->id);
//
//        $data = [
//            "acquired" => 1.2,
//            "taken" => 0,
//            "balance" => 0.9,
//            "is_last_year" => "a",
//        ];
//
//        $response = $this->put("/api/v1/leave-count/". $leaveCount->id, $data, ["Authorization" => $JWT]);
//
//        $response->assertResponseStatus(422);
//    }
//
//    /**
//     * Teste si balance not in data
//     * @return void
//     */
//    public function testUpdateBalanceNotInData(): void{
//        [$userCurrent, $leaveCount] = $this->createModels();
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($userCurrent->id);
//
//        $data = [
//            "acquired" => 1.2,
//            "taken" => 1.1,
//        ];
//
//        $response = $this->put("/api/v1/leave-count/". $leaveCount->id, $data, ["Authorization" => $JWT]);
//
//        $response->assertResponseStatus(200);
//        $response->seeJson($data);
//    }
}
