<?php

namespace Tests\Feature\Http\Controller\v1\UserLeaveCountsController;

use App\Models\Api\User;
use Tests\TestCase;

class GetCountsTest extends TestCase {
    /**
     * Teste si les éléments ont été modifié
     * @return void
     */
//    public function testIndexUserLeaveCount(): void{
//        // Get Current User
//        $currentUser = User::has('user_leave_counts')->first();
//        $currentUserId = $currentUser->id;
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($currentUserId);
//
//        $this->get("/api/v1/leave-count", ["Authorization" => $JWT]);
//
//        $this->assertResponseStatus(200);
//        $this->seeJsonStructure([
//            "message",
//            "data" => [
//                "*" => [
//                    "id",
//                    "user_id",
//                    "leave_type_id",
//                    "acquired",
//                    "taken",
//                    "balance",
//                ]
//            ]
//        ]);
//        $this->dontSee<PERSON>son([
//            "data" => []
//        ]);
//    }
}
