<?php

namespace Tests\Feature\Http\Controller\v1\UserLeaveCountsController;

use App\Models\Api\Manager;
use App\Models\Api\Profile;
use App\Models\Api\User;
use Tests\TestCase;

class GetTeamCountsTest extends TestCase {
    /**
     * Teste si les éléments ont été retourné
     * @return void
     */
//    public function testIndexTeamUserLeaveCount(): void{
//        // Get Current User
//        $currentUser = User::has('manager')->has('user_leave_counts')->first()->manager()->first();
//        $currentUserId = $currentUser->id;
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($currentUserId);
//
//        $this->get("/api/v1/leave-team-count", ["Authorization" => $JWT]);
//
//        $this->assertResponseStatus(200);
//        $this->seeJsonStructure([
//            "message",
//            "data" => [
//                "*" => [
//                    "id",
//                    "profile_id",
//                    "site_id",
//                    "uuid",
//                    "crm_uuid",
//                    "client_uuid",
//                    "firstname",
//                    "lastname",
//                    "email",
//                    "fcm_token",
//                    "picture_path",
//                    "license_path",
//                    "matricule",
//                    "enter_date",
//                    "user_leave_counts" => [
//                        "*" => [
//                            "id",
//                            "user_id",
//                            "leave_type_id",
//                            "acquired",
//                            "taken",
//                            "balance",
//                        ]
//                    ]
//                ]
//            ]
//        ]);
//        // Get Users bellow Current user
//        $teamUsers = User::whereHas('manager', function ($query) use ($currentUserId) {
//            $query->where('managers.manager_id', '=', $currentUserId)
//                ->where('managers.level', '=', 1);
//        })->get();
//        // Verify data is in JSON response
//        foreach ($teamUsers as $teamUser) {
//            $this->seeJson([
//                "id" => $teamUser->id
//            ]);
//        }
//    }
//    /**
//     * Teste si les éléments ont été retourné
//     * @return void
//     */
//    public function testIndexTeamUserLeaveCountManagerOfNoOne(): void
//    {
//        // Get Current User
//        $currentUser = User::whereNotIn('id', Manager::all()->pluck('manager_id')->toArray())->first();
//        $currentUserId = $currentUser->id;
//        // Set Profile of Manager
//        $managerProfile = Profile::where('label', '=', 'STANDARDMANAGER')->first();
//        $currentUser->profile_id = $managerProfile->id;
//        $currentUser->save();
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($currentUserId);
//
//        $this->get("/api/v1/leave-team-count", ["Authorization" => $JWT]);
//
//        $this->assertResponseStatus(200);
//        $this->seeJsonStructure([
//            "message",
//            "data" => []
//        ]);
//        $this->seeJson([
//            "data" => []
//        ]);
//    }
}
