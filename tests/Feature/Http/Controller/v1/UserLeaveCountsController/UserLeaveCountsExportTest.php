<?php

namespace Tests\Feature\Http\Controller\v1\UserLeaveCountsController;

use App\Excel\Exports\UserLeavesCountsExport;
use App\Models\Api\ExportHistory;
use App\Models\Api\LeaveType;
use App\Models\Api\Site;
use App\Models\Api\User;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use Tests\TestCase;


class UserLeaveCountsExportTest extends TestCase {

//    public function testExportUserLeaveCountClassic(): void
//    {
//        Excel::fake();
//        $userId = User::first()->id;
//        $clientId = User::first()->site->client_id;
//        $JWT = $this->getDummyJWT($userId);
//
//        // Request
//        $this->post('/api/v1/export-leave-count', [], ['Authorization' => $JWT]);
//
//        $users = User::whereHas('site', function ($query) use ($clientId){
//            $query->where('client_id', '=', $clientId);
//        })->with("site")->get();
//        $leavesTypes = LeaveType::where("client_id", "=", $clientId)->where("needs_count", "=", true)->get();
//        $filename = ExportHistory::where("user_id", "=", $userId)->get()->last()->file_name;
//
//        // Assertions
//        Excel::assertStored("exports/".$clientId."/".$filename, 'minio', function(UserLeavesCountsExport $export) use ($users, $leavesTypes, $filename) {
//            $array = collect($export->array()[0]);
//            $map = collect($export->map($array));
//
//            // verify array data
//            $this->assertEquals($users, $array["users"]);
//            $this->assertEquals($leavesTypes, $array["leavesTypes"]);
//
//            // verify array keys
//            $this->assertEquals(collect(["users", "leavesTypes"]), $array->keys());
//
//            // verify number lignes
//            $this->assertCount(count($array["users"]), $map);
//
//            // verify exported data
//            foreach ($users as $key => $user) {
//                $row = $map[$key];
//
//                $name = strtoupper($user->lastname ." ".$user->firstname);
//                $site = Site::find($user->site_id)->name;
//                $dateEnter =  Carbon::parse($user->enter_date)->format("d/m/Y");
//
//                $expectData = [$user->matricule, $user->email, $name, $site, $dateEnter];
//                $actualData = [$row[0], $row[1], $row[2], $row[3], $row[4]];
//                $this->assertEquals($expectData, $actualData);
//            }
//
//            return true;
//        });
//    }
//
//    public function testExportUserLeaveCountWithUserIds(): void
//    {
//        Excel::fake();
//        $userId = User::first()->id;
//        $clientId = User::first()->site->client_id;
//        $JWT = $this->getDummyJWT($userId);
//
//        // All users except current
//        $users = User::where("id","!=",$userId)->whereHas('site', function ($query) use ($clientId){
//            $query->where('client_id', '=', $clientId);
//        })->with("site")->get();
//
//        // Request
//        $this->post('/api/v1/export-leave-count', [
//            "user_ids" => $users->pluck("id")->toArray()
//        ], ['Authorization' => $JWT]);
//
//        $leavesTypes = LeaveType::where("client_id", "=", $clientId)->where("needs_count", "=", true)->get();
//        $filename = ExportHistory::where("user_id", "=", $userId)->get()->last()->file_name;
//
//        // Assertions
//        Excel::assertStored("exports/".$clientId."/".$filename, 'minio', function(UserLeavesCountsExport $export) use ($users, $leavesTypes, $filename) {
//            $array = collect($export->array()[0]);
//            $map = collect($export->map($array));
//
//            // verify array data
//            $this->assertEquals($users, $array["users"]);
//            $this->assertEquals($leavesTypes, $array["leavesTypes"]);
//
//            // verify array keys
//            $this->assertEquals(collect(["users", "leavesTypes"]), $array->keys());
//
//            // verify number lignes
//            $this->assertCount(count($array["users"]), $map);
//
//            // verify exported data
//            foreach ($users as $key => $user) {
//                $row = $map[$key];
//
//                $name = strtoupper($user->lastname ." ".$user->firstname);
//                $site = Site::find($user->site_id)->name;
//                $dateEnter =  Carbon::parse($user->enter_date)->format("d/m/Y");
//
//                $expectData = [$user->matricule, $user->email, $name, $site, $dateEnter];
//                $actualData = [$row[0], $row[1], $row[2], $row[3], $row[4]];
//                $this->assertEquals($expectData, $actualData);
//            }
//
//            return true;
//        });
//    }
}
