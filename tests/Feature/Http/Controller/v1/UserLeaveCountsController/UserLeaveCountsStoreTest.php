<?php
//
//namespace Feature\Http\Controller\v1\UserLeaveCountsController;
//
//use App\Models\Api\Client;
//use App\Models\Api\LeaveType;
//use App\Models\Api\Profile;
//use App\Models\Api\Site;
//use App\Models\Api\User;
//use App\Models\Api\UserLeaveCount;
//use Carbon\Carbon;
//use TestCase;
//
//class UserLeaveCountsStoreTest extends TestCase {
//    private function createModels($userEqualClient = "=", $LeaveTypeEqualClient = "=", $n_1 = false, $deleteAlreadyExist = true){
//        // Get Models
//        $userCurrent = User::where("profile_id", "=", Profile::where("label", "=", "ADMINISTRATEUR")->first()->id)->first();
//        $user = User::whereHas('site', function($query) use($userEqualClient,$userCurrent){
//            $query->where("client_id", $userEqualClient, $userCurrent->site->client_id);
//        })->where("id", "!=", $userCurrent->id)->first();
//        $leaveType = LeaveType::where("client_id", $LeaveTypeEqualClient, $userCurrent->site->client_id)->first();
//        // Delete UserLeaveCount if already exist
//        if($n_1){
//            $now = Carbon::now()->subYear()->format('Y-m-d H:i:s');
//        } else {
//            $now = Carbon::now()->format('Y-m-d H:i:s');
//        }
//        $leaveCount = UserLeaveCount::where("user_id","=", $user->id)
//            ->where("leave_type_id", "=", $leaveType->id)
//            ->where("start_date", "<", $now)->where("end_date", ">", $now)->first();
//        if($leaveCount && $deleteAlreadyExist){
//            $leaveCount->forceDelete();
//        }
//        return [$userCurrent, $user, $leaveType];
//    }
//	/**
//	 * Teste si le compteur est enregistré
//     * n-1 : false
//	 * @return void
//	 */
//	public function testStoreItemSaved(): void{
//        [$userCurrent, $user, $leaveType] = $this->createModels();
//        $leaveStartDate = Carbon::parse($user->site->first()->client->first()->leave_start_date);
//
//		$data = [
//            "user_id" => $user->id,
//            "leave_type_id" => $leaveType->id,
//            "acquired" => 1.2,
//            "taken" => 1.1,
//            "balance" => 0.9
//		];
//
//        $JWT = $this->getDummyJWT($userCurrent->id);
//
//		$response = $this->post("/api/v1/leave-count/false", $data, ["Authorization" => $JWT]);
//		unset($data["n-1"]);
//
//		$response->assertResponseStatus(200);
//		$response->seeJson($data);
//		$this->assertEquals($leaveStartDate->format("m-d"),Carbon::parse($response->response['data']['start_date'])->format('m-d'));
//	}
//
//    /**
//     * Teste si le compteur est enregistré
//     * n-1 : true
//     * @return void
//     */
//    public function testStoreItemSavedN_1(): void{
//        [$userCurrent, $user, $leaveType] = $this->createModels("=", "=", true);
//
//        $data = [
//            "user_id" => $user->id,
//            "leave_type_id" => $leaveType->id,
//            "acquired" => 1.2,
//            "taken" => 1.1,
//            "balance" => 0.9
//        ];
//
//        $JWT = $this->getDummyJWT($userCurrent->id);
//
//        $response = $this->post("/api/v1/leave-count/true", $data, ["Authorization" => $JWT]);
//        unset($data["n-1"]);
//
//        $response->assertResponseStatus(200);
//        $response->seeJson($data);
//    }
//
//    /**
//     * Teste si le compteur est enregistré
//     * n-1 is not true or false
//     * @return void
//     */
//    public function testStoreItemSavedN_1NotBool(): void{
//        [$userCurrent, $user, $leaveType] = $this->createModels();
//
//        $data = [
//            "user_id" => $user->id,
//            "leave_type_id" => $leaveType->id,
//            "acquired" => 1.2,
//            "taken" => 1.1,
//            "balance" => 0.9
//        ];
//
//        $JWT = $this->getDummyJWT($userCurrent->id);
//
//        $response = $this->post("/api/v1/leave-count/0", $data, ["Authorization" => $JWT]);
//        unset($data["n-1"]);
//
//        $response->assertResponseStatus(422);
//        $response->seeJson([
//            "message" => "ErrorStoreCompteur",
//            "details" => "BooleanInStringFromInUrl"
//        ]);
//    }
//
//	/**
//	 * Teste si le compteur existe déjà
//	 * @return void
//	 */
//	public function testStoreItemExist(): void{
//        [$userCurrent, $user, $leaveType] = $this->createModels("=", "=", false, false);
//
//        $data = [
//            "user_id" => $user->id,
//            "leave_type_id" => $leaveType->id,
//            "acquired" => 1.2,
//            "taken" => 1.1,
//            "balance" => 0.9
//        ];
//
//        $JWT = $this->getDummyJWT($userCurrent->id);
//
//        $response = $this->post("/api/v1/leave-count/false", $data, ["Authorization" => $JWT]);
//        unset($data["n-1"]);
//
//        $response->assertResponseStatus(400);
//        $response->seeJson([
//            "message" => "ErrorStoreCompteur",
//            "details" => "UserLeaveCountAlreadyExist"
//        ]);
//	}
//
//    /**
//     * Teste si le user n'existe pas
//     * @return void
//     */
//    public function testStoreItemUserNotExist(): void{
//        [$userCurrent, , $leaveType] = $this->createModels();
//        $user = User::orderBy("id", "desc")->first()->id+1;
//
//        $data = [
//            "user_id" => $user,
//            "leave_type_id" => $leaveType->id,
//            "acquired" => 1.2,
//            "taken" => 1.1,
//            "balance" => 0.9
//        ];
//
//        $JWT = $this->getDummyJWT($userCurrent->id);
//
//        $response = $this->post("/api/v1/leave-count/false", $data, ["Authorization" => $JWT]);
//        unset($data["n-1"]);
//
//        $response->assertResponseStatus(404);
//        $response->seeJson([
//            "message" => "ErrorStoreCompteur",
//            "details" => "UserDontExist"
//        ]);
//    }
//
//    /**
//     * Teste si le leaveType n'existe pas
//     * @return void
//     */
//    public function testStoreItemLeaveTypeNotExist(): void{
//        [$userCurrent, $user, $leaveType] = $this->createModels();
//        $leaveType = LeaveType::orderBy("id", "desc")->first()->id+1;
//
//        $data = [
//            "user_id" => $user->id,
//            "leave_type_id" => $leaveType,
//            "acquired" => 1.2,
//            "taken" => 1.1,
//            "balance" => 0.9
//        ];
//
//        $JWT = $this->getDummyJWT($userCurrent->id);
//
//        $response = $this->post("/api/v1/leave-count/false", $data, ["Authorization" => $JWT]);
//        unset($data["n-1"]);
//
//        $response->assertResponseStatus(404);
//        $response->seeJson([
//            "message" => "ErrorStoreCompteur",
//            "details" => "LeaveTypeDoesntExist"
//        ]);
//    }
//
//    /**
//     * Teste si le user n'appartient pas au même client
//     * @return void
//     */
//    public function testStoreItemUserNotSameCLient(): void{
//        [$userCurrent, $user, $leaveType] = $this->createModels("!=");
//
//        $data = [
//            "user_id" => $user->id,
//            "leave_type_id" => $leaveType->id,
//            "acquired" => 1.2,
//            "taken" => 1.1,
//            "balance" => 0.9
//        ];
//
//        $JWT = $this->getDummyJWT($userCurrent->id);
//
//        $response = $this->post("/api/v1/leave-count/false", $data, ["Authorization" => $JWT]);
//        unset($data["n-1"]);
//
//        $response->assertResponseStatus(403);
//        $response->seeJson([
//            "message" => "ErrorStoreCompteur",
//            "details" => "UserBelongToAnotherClient"
//        ]);
//    }
//
//    /**
//     * Teste si le leaveType n'appartient pas au même client
//     * @return void
//     */
//    public function testStoreItemLeaveTypeNotSameCLient(): void{
//        [$userCurrent, $user, $leaveType] = $this->createModels("=", "!=");
//
//        $data = [
//            "user_id" => $user->id,
//            "leave_type_id" => $leaveType->id,
//            "acquired" => 1.2,
//            "taken" => 1.1,
//            "balance" => 0.9
//        ];
//
//        $JWT = $this->getDummyJWT($userCurrent->id);
//
//        $response = $this->post("/api/v1/leave-count/false", $data, ["Authorization" => $JWT]);
//        unset($data["n-1"]);
//
//        $response->assertResponseStatus(403);
//        $response->seeJson([
//            "message" => "ErrorStoreCompteur",
//            "details" => "LeaveTypeBelongToAnotherClient"
//        ]);
//    }
//
//    /**
//     * Teste si acquired string
//     * @return void
//     */
//    public function testStoreItemAcquired(): void{
//        [$userCurrent, $user, $leaveType] = $this->createModels();
//
//        $data = [
//            "user_id" => $user->id,
//            "leave_type_id" => $leaveType->id,
//            "acquired" => "abc",
//            "taken" => 1.1,
//            "balance" => 0.9
//        ];
//
//        $JWT = $this->getDummyJWT($userCurrent->id);
//
//        $response = $this->post("/api/v1/leave-count/false", $data, ["Authorization" => $JWT]);
//        unset($data["n-1"]);
//
//        $response->assertResponseStatus(422);
//        $response->seeJson([
//            "message" => "ErrorStoreCompteur",
//            "acquired MustBeNumber"
//        ]);
//    }
//
//    /**
//     * Teste si taken null
//     * @return void
//     */
//    public function testStoreItemTaken(): void{
//        [$userCurrent, $user, $leaveType] = $this->createModels();
//
//        $data = [
//            "user_id" => $user->id,
//            "leave_type_id" => $leaveType->id,
//            "acquired" => 1.2,
//            "taken" => null,
//            "balance" => 0.9
//        ];
//
//        $JWT = $this->getDummyJWT($userCurrent->id);
//
//        $response = $this->post("/api/v1/leave-count/false", $data, ["Authorization" => $JWT]);
//        unset($data["n-1"]);
//
//        $response->assertResponseStatus(422);
//        $response->seeJson([
//            "message" => "ErrorStoreCompteur",
//            "taken IsRequired"
//        ]);
//    }
//
//    /**
//     * Teste si balance not in data
//     * @return void
//     */
//    public function testStoreItemBalance(): void{
//        [$userCurrent, $user, $leaveType] = $this->createModels();
//
//        $data = [
//            "user_id" => $user->id,
//            "leave_type_id" => $leaveType->id,
//            "acquired" => 1.2,
//            "taken" => 1.1
//        ];
//
//        $JWT = $this->getDummyJWT($userCurrent->id);
//
//        $response = $this->post("/api/v1/leave-count/false", $data, ["Authorization" => $JWT]);
//        unset($data["n-1"]);
//
//        $response->assertResponseStatus(422);
//        $response->seeJson([
//            "message" => "ErrorStoreCompteur",
//            "balance IsRequired"
//        ]);
//    }
//}
