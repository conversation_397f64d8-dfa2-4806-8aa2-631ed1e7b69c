<?php

namespace Tests\Feature\Http\Controller\v1\LeavesController;

use App\Models\Api\Leave;
use App\Models\Api\User;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class LeavesAttachmentTest extends TestCase {

//    public function testAttachmentClassic() {
//
//        // Mock
//        Storage::fake('attachment');
//        Storage::shouldReceive("disk->get")->once()->andReturn("test");
//
//        // Get valid data
//        $user_id = User::first()->id;
//        $JWT = $this->getDummyJWT($user_id);
//        $leave = Leave::where("user_id", "=", $user_id)->first();
//
//        // Request
//        $response = $this->get("/api/v1/leaves/".$leave->id."/attachment", ['Authorization' => $JWT]);
//
//        // Assertions
//        $this->assertResponseStatus(200);
//
//        $this->seeJsonStructure([
//            "message",
//            "data" => [
//                "real_name",
//                "real_path",
//                "file",
//              ]
//        ]);
//
//        $response = $this->response->json();
//        $this->AssertEquals("SuccessGetLeave", $response["message"]);
//        $this->AssertEquals($leave->attachment_name, $response["data"]["real_name"]);
//        $this->AssertEquals($leave->attachment_path, $response["data"]["real_path"]);
//    }
//
//    public function testAttachmentDataFilePng() {
//
//        // Mock
//        Storage::fake('attachment');
//        Storage::shouldReceive("disk->get")->once()->andReturn($mockData = "test");
//
//        // Get valid data
//        $user_id = User::first()->id;
//        $JWT = $this->getDummyJWT($user_id);
//        $leave = Leave::where("user_id", "=", $user_id)->where("attachment_name","like","%.png")->first();
//
//        // Request
//        $this->get("/api/v1/leaves/".$leave->id."/attachment", ['Authorization' => $JWT]);
//
//        // Assertions
//        $actual = $this->response->json()["data"]["contentType"];
//        $expected = "png";
//        $this->AssertEquals($expected, $actual);
//        $actual = $this->response->json()["data"]["file"];
//        $expected = base64_encode($mockData);
//        $this->AssertEquals($expected, $actual);
//    }
//
//    public function testAttachmentDataFilePdf() {
//
//        // Mock
//        Storage::fake('attachment');
//        Storage::shouldReceive("disk->get")->once()->andReturn($mockData = "test");
//
//        // Get valid data
//        $user_id = User::first()->id;
//        $JWT = $this->getDummyJWT($user_id);
//        $leave = Leave::where("user_id", "=", $user_id)->where("attachment_name","like","%.pdf")->first();
//
//        // Request
//        $this->get("/api/v1/leaves/".$leave->id."/attachment", ['Authorization' => $JWT]);
//
//        // Assertions
//        $actual = $this->response->json()["data"]["contentType"];
//        $expected = "pdf";
//        $this->AssertEquals($expected, $actual);
//        $actual = $this->response->json()["data"]["file"];
//        $expected = base64_encode($mockData);
//        $this->AssertEquals($expected, $actual);
//    }
//
//    public function testAttachmentDataFileJpeg() {
//
//        // Mock
//        Storage::fake('attachment');
//        Storage::shouldReceive("disk->get")->once()->andReturn($mockData = "test");
//
//        // Get valid data
//        $user_id = User::first()->id;
//        $JWT = $this->getDummyJWT($user_id);
//        $leave = Leave::where("user_id", "=", $user_id)->where("attachment_name","like","%.jpeg")->first();
//
//        // Request
//        $this->get("/api/v1/leaves/".$leave->id."/attachment", ['Authorization' => $JWT]);
//
//        // Assertions
//        $actual = $this->response->json()["data"]["contentType"];
//        $expected = "jpeg";
//        $this->AssertEquals($expected, $actual);
//        $actual = $this->response->json()["data"]["file"];
//        $expected = base64_encode($mockData);
//        $this->AssertEquals($expected, $actual);
//    }
//
//    public function testAttachmentDataFileJpg() {
//
//        // Mock
//        Storage::fake('attachment');
//        Storage::shouldReceive("disk->get")->once()->andReturn($mockData = "test");
//
//        // Get valid data
//        $user_id = User::first()->id;
//        $JWT = $this->getDummyJWT($user_id);
//        $leave = Leave::where("user_id", "=", $user_id)->where("attachment_name","like","%.jpg")->first();
//
//        // Request
//        $this->get("/api/v1/leaves/".$leave->id."/attachment", ['Authorization' => $JWT]);
//
//        // Assertions
//        $actual = $this->response->json()["data"]["contentType"];
//        $expected = "jpg";
//        $this->AssertEquals($expected, $actual);
//        $actual = $this->response->json()["data"]["file"];
//        $expected = base64_encode($mockData);
//        $this->AssertEquals($expected, $actual);
//    }
//
//    public function testAttachmentInvalideId() {
//
//        // Get valid data
//        $user_id = User::first()->id;
//        $JWT = $this->getDummyJWT($user_id);
//        $id = (Leave::all()->last()->id)+1;
//
//        // Request
//        $this->get("/api/v1/leaves/".$id."/attachment", ['Authorization' => $JWT]);
//
//        // Assertions
//        $this->assertResponseStatus(404);
//        $this->seeJsonEquals([
//            "message" => "ErrorGetAttachment",
//            "details" => "ErrorLeaveDontExist"
//        ]);
//    }
//
//    public function testAttachmentLeavesDoesntBelongToUser() {
//
//        // Get valid data
//        $user_id = User::first()->id;
//        $JWT = $this->getDummyJWT($user_id);
//        $leave = Leave::where("user_id", "!=", $user_id)->first();
//
//        // Request
//        $this->get("/api/v1/leaves/".$leave->id."/attachment", ['Authorization' => $JWT]);
//
//        // Assertions
//        $this->assertResponseStatus(403);
//        $this->seeJsonEquals([
//            "message" => "ErrorGetAttachment",
//            "details" => "ErrorDontOwnLeave ".$leave->id
//        ]);
//    }
}
