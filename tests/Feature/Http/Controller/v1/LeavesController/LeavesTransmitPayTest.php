<?php

namespace Tests\Feature\Http\Controller\v1\LeavesController;

use App\Models\Api\Client;
use App\Models\Api\Day;
use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\Profile;
use App\Models\Api\Site;
use App\Models\Api\Status;
use App\Models\Api\User;
use App\Models\Api\UserLeaveCount;
use App\Http\Middleware\CheckProfile;
use Tests\TestCase;

class LeavesTransmitPayTest extends TestCase
{

    protected function setUp(): void
    {
        parent::setUp();
        $this->withoutMiddleware(CheckProfile::class);
    }

    private function getStatuses(): array
    {
        return [
            'submitted' => Status::firstOrCreate(['tag' => 'SUBMITTED'], ['name' => 'Soumis à validation']),
            'validated' => Status::firstOrCreate(['tag' => 'VALIDATED'], ['name' => 'Validé']),
            'transmitted' => Status::firstOrCreate(['tag' => 'TRANSMITTED'], ['name' => 'Transmis en paie']),
        ];
    }

    private function createProfiles(): array
    {
        return [
            'admin' => Profile::firstOrCreate(['label' => 'ADMINISTRATEUR']),
            'manager' => Profile::firstOrCreate(['label' => 'ADMINISTRATEURMANAGER']),
            'standard' => Profile::firstOrCreate(['label' => 'STANDARD'])
        ];
    }

    private function createClient(string $scheme = 'VERTICAL', int $levels = 1): Client
    {
        $client = Client::factory()->create([
            'validation_scheme' => $scheme,
            'number_managers_can_validate' => $levels,
        ]);
        $this->assertNotNull($client->uuid);

        $day = Day::firstOrCreate(['day_name' => 'MONDAY']);
        $client->days()->syncWithoutDetaching([$day->id]);

        return $client;
    }

    private function createSite(Client $client): Site
    {
        return Site::factory()->create(['client_id' => $client->id]);
    }

    private function createLeaveType(Client $client, bool $needsCount = true): LeaveType
    {
        return LeaveType::factory()->create([
            'client_id' => $client->id,
            'is_active' => true,
            'is_half_day' => true,
            'needs_count' => $needsCount,
            'can_exceed' => false,
        ]);
    }

    private function createUser(array $attributes = []): User
    {
        if (isset($attributes['site_id']) && !isset($attributes['client_uuid'])) {
            $site = Site::find($attributes['site_id']);
            if ($site?->client) {
                $attributes['client_uuid'] = $site->client->uuid;
            }
        } elseif (!isset($attributes['site_id'])) {
            $client = Client::factory()->create();
            $site = Site::factory()->create(['client_id' => $client->id]);
            $attributes['site_id'] = $site->id;
            $attributes['client_uuid'] = $client->uuid;
        }
        $user = User::factory()->state($attributes)->create();
        $this->assertNotNull($user->uuid);
        return $user;
    }

    private function createLeave(User $user, int $statusId, int $leaveTypeId, int $validatorLevel = 1): Leave
    {
        $startDate = now()->addDays(20)->startOfDay()->setHour(9);
        $endDate = now()->addDays(20)->startOfDay()->setHour(18);

        $leave = Leave::factory()->create([
            'user_id' => $user->id,
            'creator_id' => $user->id,
            'status_id' => $statusId,
            'leave_type_id' => $leaveTypeId,
            'duration' => 1,
            'current_validator_level' => $validatorLevel,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'n' => 1,
            'n1' => 0,
        ]);
        return $leave;
    }

    private function createUserLeaveCount(int $userId, int $leaveTypeId): void
    {
        UserLeaveCount::updateOrCreate(
            ['user_id' => $userId, 'leave_type_id' => $leaveTypeId, 'is_last_year' => false],
            ['balance' => 10, 'acquired' => 10, 'taken' => 0]
        );
        UserLeaveCount::updateOrCreate(
            ['user_id' => $userId, 'leave_type_id' => $leaveTypeId, 'is_last_year' => true],
            ['balance' => 5, 'acquired' => 5, 'taken' => 0]
        );
    }

    /**
     * Test that an administrator can transmit a leave that is already validated.
     * The leave status should change to TRANSMITTED and the validator level should increment.
     */
    public function test_admin_can_transmit_validated_leave(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 1);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client, true);

        $adminUser = $this->createUser(['profile_id' => $profiles['admin']->id, 'site_id' => $site->id]);
        $collaborator = $this->createUser(['profile_id' => $profiles['standard']->id, 'site_id' => $site->id]);
        $this->createUserLeaveCount($collaborator->id, $leaveType->id);

        $leave = $this->createLeave($collaborator, $statuses['validated']->id, $leaveType->id, 2);
        $this->assertEquals('VALIDATED', $leave->status->tag);

        $payload = [['action' => 'TRANSMITTED', 'leave_id' => $leave->id]];

        $response = $this
            ->actingAs($adminUser, 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload);

        $response->assertStatus(200);
        $updatedLeave = Leave::find($leave->id);
        $this->assertNotNull($updatedLeave);
        $this->assertEquals('TRANSMITTED', $updatedLeave->status->tag);
        $this->assertEquals(3, $updatedLeave->current_validator_level);
    }

    /**
     * Test that an administrator can transmit a leave that is still submitted (bypassing manager validation).
     * The leave status should change to TRANSMITTED and the validator level should increment.
     */
    public function test_admin_can_transmit_submitted_leave(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 1);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client, true);

        $adminUser = $this->createUser(['profile_id' => $profiles['admin']->id, 'site_id' => $site->id]);
        $collaborator = $this->createUser(['profile_id' => $profiles['standard']->id, 'site_id' => $site->id]);
        $manager = $this->createUser(['profile_id' => $profiles['manager']->id, 'site_id' => $site->id]);
        $collaborator->managers()->attach($manager->id, ['level' => 1]);
        $this->createUserLeaveCount($collaborator->id, $leaveType->id);

        $leave = $this->createLeave($collaborator, $statuses['submitted']->id, $leaveType->id, 1);
        $this->assertEquals('SUBMITTED', $leave->status->tag);

        $payload = [['action' => 'TRANSMITTED', 'leave_id' => $leave->id]];

        $response = $this
            ->actingAs($adminUser, 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload);

        $response->assertStatus(200);
        $updatedLeave = Leave::find($leave->id);
        $this->assertNotNull($updatedLeave);
        $this->assertEquals('TRANSMITTED', $updatedLeave->status->tag);
        $this->assertEquals(2, $updatedLeave->current_validator_level);
    }

    /**
     * Test that a non-administrator user cannot transmit a validated leave.
     * Expects a 500 error as the controller likely throws an exception due to profile check.
     */
    public function test_non_admin_cannot_transmit_leave(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient();
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client);

        $nonAdminUser = $this->createUser(['profile_id' => $profiles['standard']->id, 'site_id' => $site->id]);
        $collaborator = $this->createUser(['profile_id' => $profiles['standard']->id, 'site_id' => $site->id]);
        $this->createUserLeaveCount($collaborator->id, $leaveType->id);
        $leave = $this->createLeave($collaborator, $statuses['validated']->id, $leaveType->id, 2);
        $initialStatusTag = $leave->status->tag;

        $payload = [['action' => 'TRANSMITTED', 'leave_id' => $leave->id]];

        $response = $this
            ->actingAs($nonAdminUser, 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload);

        $response->assertStatus(500);

        $finalLeave = Leave::find($leave->id);
        $this->assertNotNull($finalLeave);
        $this->assertEquals($initialStatusTag, $finalLeave->status->tag);
    }
}
