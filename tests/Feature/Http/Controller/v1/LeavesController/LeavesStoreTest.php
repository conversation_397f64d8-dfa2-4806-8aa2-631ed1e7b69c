<?php

namespace Tests\Feature\Http\Controller\v1\LeavesController;

use App\Models\Api\LeaveType;
use App\Models\Api\Status;
use App\Models\Api\User;
use App\Models\Api\UserLeaveCount;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class LeavesStoreTest extends TestCase {

    public function test_basic_test(): void
    {
        $this->assertTrue(true);
    }
    /**
     * Teste si le type de congés est enregistré
     * @return void
     */
//        public function testStoreItemSaved(): void{
//
//            Storage::shouldReceive("disk->put")->andReturn(true);
//
//            $user = User::has('managers')->first();
//
//            $JWT = $this->getDummyJWT($user->id);
//
//            $data = json_encode([
//                "user_id" => $user->id,
//                "leave_type_id" => LeaveType::where("client_id", "=", $user->site->client->id)->first()->id,
//                "leave_type_sub_family_id" => null,
//                "start_date" => "2020-11-01",
//                "end_date" => "2020-11-02",
//                "comment" => null,
//                "duration" => 1
//            ]);
//
//            $file = UploadedFile::fake()->image('file.png');
//
//            $res = $this->call('POST', '/api/v1/leaves',
//                ["data" =>  $data],
//                array(),
//                ['attachment' => $file],
//                $this->transformHeadersToServerVars(['Authorization' => $JWT])
//            );
//            $jsonResp = json_decode($res->getContent());
//            $submitted = Status::where("tag", "=", "SUBMITTED")->first()->id;
//
//            $this->seeJson([
//                "message" => "SuccessStoreLeave",
//                "status_id" => $submitted
//            ]);
//            $this->seeInDatabase('leaves', ["id" => $jsonResp->data->id,"status_id" => $submitted]);
//            $this->assertResponseOk();
//        }
//
//        /**
//         * Teste si le type de congés est enregistré
//         * Has no manager -> Leave is Validated directly
//         * @return void
//         */
//        public function testStoreItemSaved2(): void{
//
//            Storage::shouldReceive("disk->put")->andReturn(true);
//
//            $user = User::has('managers', '=', 0)->first();
//
//            $JWT = $this->getDummyJWT($user->id);
//
//            $data = json_encode([
//                "user_id" => $user->id,
//                "leave_type_id" => LeaveType::where("client_id", "=", $user->site->client->id)->first()->id,
//                "leave_type_sub_family_id" => null,
//                "start_date" => "2020-11-01",
//                "end_date" => "2020-11-02",
//                "comment" => null,
//                "duration" => 1
//            ]);
//
//            $file = UploadedFile::fake()->image('file.png');
//
//            $res = $this->call('POST', '/api/v1/leaves',
//                ["data" =>  $data],
//                array(),
//                ['attachment' => $file],
//                $this->transformHeadersToServerVars(['Authorization' => $JWT])
//            );
//            $jsonResp = json_decode($res->getContent());
//            $validated = Status::where("tag", "=", "VALIDATED")->first()->id;
//
//            $this->seeJson([
//                "message" => "SuccessStoreLeave",
//                "status_id" => $validated
//            ]);
//            $this->seeJsonStructure([
//                "message",
//                "data" => [
//                    "attachment_name",
//                    "attachment_path",
//                    "comment",
//                    "created_at",
//                    "current_validator_level",
//                    "duration",
//                    "end_date",
//                    "id",
//                    "leave_type_id",
//                    "leave_type_sub_family_id",
//                    "last_updater_id",
//                    "n",
//                    "n1",
//                    "start_date",
//                    "user_id",
//                ],
//            ]);
//            $this->seeInDatabase('leaves', ["id" => $jsonResp->data->id,"status_id" => $validated]);
//            $this->assertResponseOk();
//        }
//
//        /**
//         * Teste si le type de congés est enregistré
//         * Has no manager -> Leave is Validated directly
//         * LeaveType : needs_count = false
//         * @return void
//         */
//        public function testStoreItemSaved2LeaveTypeNeedsCountFalse(): void{
//            Storage::shouldReceive("disk->put")->andReturn(true);
//
//            $user = User::has('managers', '=', 0)->first();
//
//            $JWT = $this->getDummyJWT($user->id);
//
//            $data = json_encode([
//                "user_id" => $user->id,
//                "leave_type_id" => LeaveType::where("client_id", "=", $user->site->client->id)->where('needs_count', '=', false)->first()->id,
//                "leave_type_sub_family_id" => null,
//                "start_date" => "2020-11-01",
//                "end_date" => "2020-11-02",
//                "comment" => null,
//                "duration" => 1
//            ]);
//
//            $file = UploadedFile::fake()->image('file.png');
//
//            $res = $this->call('POST', '/api/v1/leaves',
//                ["data" =>  $data],
//                array(),
//                ['attachment' => $file],
//                $this->transformHeadersToServerVars(['Authorization' => $JWT])
//            );
//            $jsonResp = json_decode($res->getContent());
//            $validated = Status::where("tag", "=", "VALIDATED")->first()->id;
//
//            $this->seeJson([
//                "message" => "SuccessStoreLeave",
//                "status_id" => $validated
//            ]);
//            $this->seeJsonStructure([
//                "message",
//                "data" => [
//                    "attachment_name",
//                    "attachment_path",
//                    "comment",
//                    "created_at",
//                    "current_validator_level",
//                    "duration",
//                    "end_date",
//                    "id",
//                    "leave_type_id",
//                    "leave_type_sub_family_id",
//                    "last_updater_id",
//                    "n",
//                    "n1",
//                    "start_date",
//                    "user_id",
//                ],
//            ]);
//            $this->seeInDatabase('leaves', ["id" => $jsonResp->data->id,"status_id" => $validated]);
//            $this->assertResponseOk();
//        }
//
//        /**
//         * Teste si le type de congés est enregistré
//         * Durée de 1.5
//         * LeaveType is_half_day = true
//         * @return void
//         */
//        public function testStoreItemSavedDuration(): void{
//            Storage::shouldReceive("disk->put")->andReturn(true);
//
//            $user = User::has('managers')->first();
//
//            $leaveType = LeaveType::where("client_id", "=", $user->site->client->id)->first();
//            $leaveType->is_half_day = true;
//            $leaveType->save();
//
//            $JWT = $this->getDummyJWT($user->id);
//
//            $data = json_encode([
//                "user_id" => $user->id,
//                "leave_type_id" => $leaveType->id,
//                "leave_type_sub_family_id" => null,
//                "start_date" => "2020-11-03",
//                "end_date" => "2020-11-04 12:00:00",
//                "comment" => null,
//                "duration" => 1.5
//            ]);
//
//            $file = UploadedFile::fake()->image('file.png');
//
//            $res = $this->call('POST', '/api/v1/leaves',
//                ["data" =>  $data],
//                array(),
//                ['attachment' => $file],
//                $this->transformHeadersToServerVars(['Authorization' => $JWT])
//            );
//            $jsonResp = json_decode($res->getContent());
//            $submitted = Status::where("tag", "=", "SUBMITTED")->first()->id;
//
//            $this->seeJson([
//                "message" => "SuccessStoreLeave",
//                "duration" => 1.5
//            ]);
//            $this->seeInDatabase('leaves', ["id" => $jsonResp->data->id,"status_id" => $submitted]);
//            $this->assertResponseOk();
//        }
//
//        /**
//         * Teste si le type de congés est enregistré
//         * Durée de 1.5
//         * LeaveType is_half_day = false
//         * @return void
//         */
//        public function testStoreItemSavedDurationNotPermitHalfDay(): void{
//            Storage::shouldReceive("disk->put")->andReturn(true);
//
//            $user = User::has('managers')->first();
//
//            $leaveType = LeaveType::where("client_id", "=", $user->site->client->id)->first();
//            $leaveType->is_half_day = false;
//            $leaveType->save();
//
//            $JWT = $this->getDummyJWT($user->id);
//
//            $data = json_encode([
//                "user_id" => $user->id,
//                "leave_type_id" => $leaveType->id,
//                "leave_type_sub_family_id" => null,
//                "start_date" => "2020-11-03",
//                "end_date" => "2020-11-04 12:00:00",
//                "comment" => null,
//                "duration" => 1.5
//            ]);
//
//            $file = UploadedFile::fake()->image('file.png');
//
//            $res = $this->call('POST', '/api/v1/leaves',
//                ["data" =>  $data],
//                array(),
//                ['attachment' => $file],
//                $this->transformHeadersToServerVars(['Authorization' => $JWT])
//            );
//            $jsonResp = json_decode($res->getContent());
//            $submitted = Status::where("tag", "=", "SUBMITTED")->first()->id;
//
//            $this->seeJson([
//                "message" => "ErrorDemandeLeave",
//                "details" => "ErrorLeaveDurationHalfDayNotPermit"
//            ]);
//            $this->assertResponseStatus(400);
//        }
//
//        /**
//         * Teste si le type de congés est enregistré
//         * Durée de 1.75
//         * @return void
//         */
//        public function testStoreItemNotSavedBadDuration(): void{
//            Storage::shouldReceive("disk->put")->andReturn(true);
//            $user = User::has('managers')->first();
//
//            $JWT = $this->getDummyJWT($user->id);
//
//            $data = json_encode([
//                "user_id" => $user->id,
//                "leave_type_id" => LeaveType::where("client_id", "=", $user->site->client->id)->first()->id,
//                "leave_type_sub_family_id" => null,
//                "start_date" => "2020-11-03",
//                "end_date" => "2020-11-04 18:00:00",
//                "comment" => null,
//                "duration" => 1.75
//            ]);
//
//            $file = UploadedFile::fake()->image('file.png');
//
//            $this->call('POST', '/api/v1/leaves',
//                ["data" =>  $data],
//                array(),
//                ['attachment' => $file],
//                $this->transformHeadersToServerVars(['Authorization' => $JWT])
//            );
//
//            $this->seeJson([
//                "message" => "ErrorDemandeLeave",
//                "details" => "ErrorLeaveDuration"
//            ]);
//            $this->assertResponseStatus(400);
//        }
//
//        public function testStoreInvalideLeaveType(): void {
//            $user = User::has('managers')->first();
//
//            $JWT = $this->getDummyJWT($user->id);
//
//            $data = json_encode([
//                "user_id" => $user->id,
//                "leave_type_id" => (LeaveType::all()->last()->id) +1,
//                "leave_type_sub_family_id" => null,
//                "last_updater_id" => null,
//                "current_validator_level" => 1,
//                "start_date" => "2021-01-01",
//                "end_date" => "2021-02-02",
//                "duration" => 1,
//                "attachment_name" => "",
//                "attachment_path" => "",
//                "comment" => null,
//            ]);
//
//            $file = UploadedFile::fake()->image('file.png');
//
//            $this->call('POST', '/api/v1/leaves',
//                ["data" =>  $data],
//                array(),
//                ['attachment' => $file],
//                $this->transformHeadersToServerVars(['Authorization' => $JWT])
//            );
//
//            $this->assertResponseStatus(404);
//            $this->seeJson([
//                "details" => "ErrorLeaveTypeDontExist",
//            ]);
//        }
//
//        public function testStoreInvalideDateFormatStart(): void {
//            $user = User::has('managers')->first();
//
//            $JWT = $this->getDummyJWT($user->id);
//
//            $data = json_encode([
//                "user_id" => $user->id,
//                "leave_type_id" => LeaveType::where("client_id", "=", $user->site->client->id)->first()->id,
//                "leave_type_sub_family_id" => null,
//                "last_updater_id" => null,
//                "current_validator_level" => 1,
//                "start_date" => "01/011/2021",
//                "end_date" => "02/02/2021",
//                "duration" => 1,
//                "attachment_name" => "",
//                "attachment_path" => "",
//                "comment" => null,
//            ]);
//
//            $file = UploadedFile::fake()->image('file.png');
//
//            $this->call('POST', '/api/v1/leaves',
//                ["data" =>  $data],
//                array(),
//                ['attachment' => $file],
//                $this->transformHeadersToServerVars(['Authorization' => $JWT])
//            );
//
//            $this->assertResponseStatus(400);
//            $this->seeJson([
//                "details" => "ErrorStartDateFormat",
//            ]);
//        }
//
//        public function testStoreInvalideDateFormatEnd(): void {
//            $user = User::has('managers')->first();
//
//            $JWT = $this->getDummyJWT($user->id);
//
//            $data = json_encode([
//                "user_id" => $user->id,
//                "leave_type_id" => LeaveType::where("client_id", "=", $user->site->client->id)->first()->id,
//                "leave_type_sub_family_id" => null,
//                "last_updater_id" => null,
//                "current_validator_level" => 1,
//                "start_date" => "2021-01-01",
//                "end_date" => "2020-0200-02",
//                "duration" => 1,
//                "attachment_name" => "",
//                "attachment_path" => "",
//                "comment" => null,
//            ]);
//
//            $file = UploadedFile::fake()->image('file.png');
//
//            $this->call('POST', '/api/v1/leaves',
//                ["data" =>  $data],
//                array(),
//                ['attachment' => $file],
//                $this->transformHeadersToServerVars(['Authorization' => $JWT])
//            );
//
//            $this->assertResponseStatus(400);
//            $this->seeJson([
//                "details" => "ErrorEndDateFormat",
//            ]);
//        }
//
//        public function testStoreInvalideDateStart(): void{
//            $user = User::has('managers')->first();
//
//            $JWT = $this->getDummyJWT($user->id);
//
//            $data = json_encode([
//                "user_id" => $user->id,
//                "leave_type_id" => LeaveType::where("client_id", "=", $user->site->client->id)->first()->id,
//                "leave_type_sub_family_id" => null,
//                "start_date" => "1021-01-01",
//                "end_date" => "2022-02-02",
//                "comment" => null,
//            ]);
//
//            $file = UploadedFile::fake()->image('file.png');
//
//            $this->call('POST', '/api/v1/leaves',
//                ["data" =>  $data],
//                array(),
//                ['attachment' => $file],
//                $this->transformHeadersToServerVars(['Authorization' => $JWT])
//            );
//
//            $this->assertResponseStatus(400);
//            $this->seeJson([
//                "details" => "LeaveAlreadyExisteForDate",
//            ]);
//        }
//
//        public function testStoreInvalideDurationMissing(): void {
//            $user = User::has('managers')->first();
//
//            $JWT = $this->getDummyJWT($user->id);
//
//            $data = json_encode([
//                "user_id" => $user->id,
//                "leave_type_id" => LeaveType::where("client_id", "=", $user->site->client->id)->first()->id,
//                "leave_type_sub_family_id" => null,
//                "last_updater_id" => null,
//                "current_validator_level" => 1,
//                "start_date" => "2020-11-01",
//                "end_date" => "2020-11-02",
//                "attachment_name" => "",
//                "attachment_path" => "",
//                "comment" => null,
//            ]);
//
//            $file = UploadedFile::fake()->image('file.png');
//
//            $this->call('POST', '/api/v1/leaves',
//                ["data" =>  $data],
//                array(),
//                ['attachment' => $file],
//                $this->transformHeadersToServerVars(['Authorization' => $JWT])
//            );
//
//            $this->assertResponseStatus(422);
//            $this->seeJson([
//                "details" => "DurationRequired",
//                "message" => "ErrorDemandeLeave"
//            ]);
//        }
//
//        public function testStoreLeaveCanExceed(): void{
//
//            Storage::shouldReceive("disk->put")->andReturn(true);
//
//            $user = User::has('managers')->first();
//            $JWT = $this->getDummyJWT($user->id);
//
//            $leaveType = LeaveType::where("client_id", "=", $user->site->client->id)
//                ->where("can_exceed", 1)
//                ->first();
//
//            // set number day left
//            UserLeaveCount::where("user_id", "=", $user->id)
//                ->where("leave_type_id", $leaveType->id)
//                ->update(["balance" => 11]);
//
//            $data = json_encode([
//                "user_id" => $user->id,
//                "leave_type_id" => $leaveType->id,
//                "leave_type_sub_family_id" => null,
//                "start_date" => "2020-11-01",
//                "end_date" => "2020-12-05",
//                "comment" => null,
//                "duration" => 32 // > nb days left
//            ]);
//
//            $file = UploadedFile::fake()->image('file.png');
//
//            $res = $this->call('POST', '/api/v1/leaves',
//                ["data" =>  $data],
//                array(),
//                ['attachment' => $file],
//                $this->transformHeadersToServerVars(['Authorization' => $JWT])
//            );
//            $jsonResp = json_decode($res->getContent());
//            $submitted = Status::where("tag", "=", "SUBMITTED")->first()->id;
//
//            $this->seeJson([
//                "message" => "SuccessStoreLeave",
//                "status_id" => $submitted
//            ]);
//            $this->seeInDatabase('leaves', ["id" => $jsonResp->data->id,"status_id" => $submitted]);
//            $this->assertResponseOk();
//        }
//        public function testStoreLeaveCanNotExceed(): void{
//
//            Storage::shouldReceive("disk->put")->andReturn(true);
//
//            $user = User::has('managers')->first();
//            $JWT = $this->getDummyJWT($user->id);
//
//            $leaveType = LeaveType::where("client_id", "=", $user->site->client->id)
//                ->where("can_exceed", 0)
//                ->first();
//
//            // set number day left
//            UserLeaveCount::where("user_id", "=", $user->id)
//                ->where("leave_type_id", $leaveType->id)
//                ->update(["balance" => 11]);
//
//            $data = json_encode([
//                "user_id" => $user->id,
//                "leave_type_id" => $leaveType->id,
//                "leave_type_sub_family_id" => null,
//                "start_date" => "2020-11-01",
//                "end_date" => "2020-12-05",
//                "comment" => null,
//                "duration" => 32 // > nb days left
//            ]);
//
//            $file = UploadedFile::fake()->image('file.png');
//
//            $res = $this->call('POST', '/api/v1/leaves',
//                ["data" =>  $data],
//                array(),
//                ['attachment' => $file],
//                $this->transformHeadersToServerVars(['Authorization' => $JWT])
//            );
//            $jsonResp = json_decode($res->getContent());
//            $submitted = Status::where("tag", "=", "SUBMITTED")->first()->id;
//
//            $this->seeJson([
//                "message" => "NotEnoughLeaveDay",
//            ]);
//        }
}
