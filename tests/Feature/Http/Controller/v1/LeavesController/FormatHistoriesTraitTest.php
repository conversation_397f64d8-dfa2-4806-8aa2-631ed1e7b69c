<?php

namespace Tests\Feature\Http\Controller\v1\LeavesController;

use App\Models\Api\History;
use App\Models\Api\Leave;
use App\Models\Api\Status;
use App\Models\Api\User;
use App\Models\Api\Client;
use App\Models\Api\Site;
use App\Models\Api\LeaveType;
use App\Utils\Traits\FormatHistoriesTrait;
use Tests\TestCase;

class FormatHistoriesTraitTest extends TestCase
{
    use FormatHistoriesTrait;

    private $statuses;
    private $verticalClient;
    private $horizontalClient;
    private $verticalSite;
    private $horizontalSite;
    private $user;
    private $manager;
    private $leaveType;

    /**
     * Initial setup for tests
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->statuses = $this->getStatuses();
        $this->verticalClient = Client::factory()->create(['validation_scheme' => 'VERTICAL']);
        $this->horizontalClient = Client::factory()->create(['validation_scheme' => 'HORIZONTAL']);
        $this->verticalSite = Site::factory()->create(['client_id' => $this->verticalClient->id]);
        $this->horizontalSite = Site::factory()->create(['client_id' => $this->horizontalClient->id]);
        $this->user = User::factory()->create(['site_id' => $this->verticalSite->id]);
        $this->manager = User::factory()->create(['site_id' => $this->verticalSite->id]);
        $this->user->managers()->attach($this->manager->id, ['level' => 1]);
        $this->leaveType = LeaveType::factory()->create(['client_id' => $this->verticalClient->id]);
        LeaveType::factory()->create(['client_id' => $this->horizontalClient->id]);
    }

    /**
     * Create and return statuses for tests
     */
    private function getStatuses()
    {
        return [
            'submitted' => Status::firstOrCreate(['tag' => 'SUBMITTED'], ['name' => 'Soumis à validation']),
            'validated' => Status::firstOrCreate(['tag' => 'VALIDATED'], ['name' => 'Validé']),
            'refused' => Status::firstOrCreate(['tag' => 'REFUSED'], ['name' => 'Refusée']),
            'transmitted' => Status::firstOrCreate(['tag' => 'TRANSMITTED'], ['name' => 'Transmis en paie']),
            'canceled' => Status::firstOrCreate(['tag' => 'CANCELED'], ['name' => 'Annulé']),
            'submitted_to_cancellation' => Status::firstOrCreate(['tag' => 'SUBMITTED_TO_CANCELLATION'], ['name' => 'Soumis à annulation']),
            'imported' => Status::firstOrCreate(['tag' => 'IMPORTED'], ['name' => 'Importé']),
        ];
    }

    /**
     * Create a leave with associated history
     */
    private function createLeaveWithHistory(
        string $statusKey,
        User $user = null,
        int $validatorLevel = null,
        string $validationScheme = 'VERTICAL'
    ): Leave {
        $user = $user ?? $this->user;
        $site = $validationScheme === 'VERTICAL' ? $this->verticalSite : $this->horizontalSite;
        $client = $validationScheme === 'VERTICAL' ? $this->verticalClient : $this->horizontalClient;

        if ($user->site_id !== $site->id) {
            $user = User::factory()->create(['site_id' => $site->id]);
            if ($validationScheme === 'VERTICAL' && $validatorLevel) {
                $manager = User::factory()->create(['site_id' => $site->id]);
                $user->managers()->attach($manager->id, ['level' => $validatorLevel]);
            }
        }

        $leaveType = LeaveType::where('client_id', $client->id)->first();

        $leaveData = [
            'user_id' => $user->id,
            'status_id' => $this->statuses[$statusKey]->id,
            'leave_type_id' => $leaveType->id
        ];

        if ($validatorLevel) {
            $leaveData['current_validator_level'] = $validatorLevel;
        }

        $leave = Leave::factory()->create($leaveData);

        $leave->histories()->create([
            'status_id' => $this->statuses[$statusKey]->id,
            'user_id' => $user->id,
            'reason' => 'Test reason',
        ]);

        return $leave->fresh(['histories', 'status']);
    }

    /**
     * Test formatting histories for a leave with submitted status and vertical validation scheme
     */
    public function test_format_histories_with_submitted_status()
    {
        $leave = $this->createLeaveWithHistory('submitted', $this->user, 1);
        $formattedLeave = $this->formatHistories($leave);

        $this->assertCount(2, $formattedLeave->histories);

        $firstHistory = $formattedLeave->histories->first();
        $this->assertNotNull($firstHistory->status);
        $this->assertEquals('SUBMITTED', $firstHistory->status->tag);
        $this->assertNotNull($firstHistory->user);

        $lastHistoryItem = $formattedLeave->histories->last();
        $this->assertInstanceOf(History::class, $lastHistoryItem);
        $this->assertNotNull($lastHistoryItem->status);
        $this->assertInstanceOf(Status::class, $lastHistoryItem->status);

        $this->assertEquals('WAITING_VALIDATION', $lastHistoryItem->status->tag);
        $this->assertEquals(__('messages.WaitingValidationManager'), $lastHistoryItem->status->name);
        $this->assertEquals('0xFFFFBB00', $lastHistoryItem->status->color);

        $this->assertNotNull($lastHistoryItem->user);
        $this->assertEquals($this->manager->firstname, $lastHistoryItem->user->firstname);
        $this->assertEquals($this->manager->lastname, $lastHistoryItem->user->lastname);
    }

    /**
     * Test formatting histories for a leave with submitted to cancellation status
     */
    public function test_format_histories_with_submitted_to_cancellation()
    {
        $leave = $this->createLeaveWithHistory('submitted_to_cancellation', $this->user, 1);
        $formattedLeave = $this->formatHistories($leave);

        $this->assertCount(2, $formattedLeave->histories);

        $lastHistoryItem = $formattedLeave->histories->last();
        $this->assertInstanceOf(History::class, $lastHistoryItem);
        $this->assertEquals('WAITING_CANCELLATION', $lastHistoryItem->status->tag);
        $this->assertEquals(__('messages.WaitingCancelationManager'), $lastHistoryItem->status->name);
        $this->assertEquals('0xFFFFBB00', $lastHistoryItem->status->color);

        $this->assertNotNull($lastHistoryItem->user);
        $this->assertEquals($this->manager->firstname, $lastHistoryItem->user->firstname);
    }

    /**
     * Test formatting histories for a leave with validated status
     */
    public function test_format_histories_with_validated_status()
    {
        $leave = $this->createLeaveWithHistory('validated');
        $formattedLeave = $this->formatHistories($leave);

        $this->assertCount(1, $formattedLeave->histories);
        $this->assertEquals('VALIDATED', $formattedLeave->histories->first()->status->tag);
    }

    /**
     * Test formatting histories with horizontal validation scheme
     */
    public function test_format_histories_with_horizontal_validation_scheme()
    {
        $horizontalUser = User::factory()->create(['site_id' => $this->horizontalSite->id]);
        $leave = $this->createLeaveWithHistory('submitted', $horizontalUser, null, 'HORIZONTAL');
        $formattedLeave = $this->formatHistories($leave);

        $this->assertCount(2, $formattedLeave->histories);

        $lastHistoryItem = $formattedLeave->histories->last();
        $this->assertEquals('WAITING_VALIDATION', $lastHistoryItem->status->tag);
        $this->assertEquals(__('messages.WaitingValidationManagers'), $lastHistoryItem->status->name);
        $this->assertNull($lastHistoryItem->user);
    }
}
