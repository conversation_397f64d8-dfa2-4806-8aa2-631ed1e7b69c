<?php

namespace Tests\Feature\Http\Controller\v1\LeavesController;

use App\Models\Api\Client;
use App\Models\Api\Day;
use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\Profile;
use App\Models\Api\Site;
use App\Models\Api\Status;
use App\Models\Api\User;
use App\Models\Api\UserLeaveCount;
use App\Http\Middleware\CheckProfile;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class PayrollTransmissionCounterTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();
        $this->withoutMiddleware(CheckProfile::class);
    }

    public function test_counter_recalculation_after_payroll_transmission(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 1);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client, true);

        $adminUser = $this->createUser(['profile_id' => $profiles['admin']->id, 'site_id' => $site->id]);
        $collaborator = $this->createUser(['profile_id' => $profiles['standard']->id, 'site_id' => $site->id]);
        
        $this->createUserLeaveCount($collaborator->id, $leaveType->id, 10, 5);

        Carbon::setTestNow(Carbon::create(2024, 8, 1));
        $leave = $this->createLeave($collaborator, $statuses['validated']->id, $leaveType->id, 2, 3.0, Carbon::create(2024, 4, 15));

        $initialNCount = UserLeaveCount::where('user_id', $collaborator->id)
            ->where('leave_type_id', $leaveType->id)
            ->where('is_last_year', false)
            ->first();

        $initialN1Count = UserLeaveCount::where('user_id', $collaborator->id)
            ->where('leave_type_id', $leaveType->id)
            ->where('is_last_year', true)
            ->first();

        $payload = [['action' => 'TRANSMITTED', 'leave_id' => $leave->id]];

        $response = $this
            ->actingAs($adminUser, 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload);

        $response->assertStatus(200);

        $updatedLeave = Leave::find($leave->id);
        $this->assertEquals('TRANSMITTED', $updatedLeave->status->tag);
        $this->assertEquals(3, $updatedLeave->current_validator_level);

        $finalNCount = $initialNCount->fresh();
        $finalN1Count = $initialN1Count->fresh();

        $this->assertEquals(10, $finalNCount->balance);
        $this->assertEquals(5, $finalN1Count->balance);
    }

    public function test_multiple_leaves_counter_distribution_after_transmission(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 1);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client, true);

        $adminUser = $this->createUser(['profile_id' => $profiles['admin']->id, 'site_id' => $site->id]);
        $collaborator = $this->createUser(['profile_id' => $profiles['standard']->id, 'site_id' => $site->id]);
        
        $this->createUserLeaveCount($collaborator->id, $leaveType->id, 10, 3);

        Carbon::setTestNow(Carbon::create(2024, 8, 1));

        $leave1 = $this->createLeave($collaborator, $statuses['validated']->id, $leaveType->id, 2, 2.0, Carbon::create(2024, 4, 15));
        $leave2 = $this->createLeave($collaborator, $statuses['validated']->id, $leaveType->id, 2, 2.5, Carbon::create(2024, 5, 10));

        $payload = [
            ['action' => 'TRANSMITTED', 'leave_id' => $leave1->id],
            ['action' => 'TRANSMITTED', 'leave_id' => $leave2->id]
        ];

        $response = $this
            ->actingAs($adminUser, 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload);

        $response->assertStatus(200);

        $leave1->refresh();
        $leave2->refresh();

        $this->assertEquals('TRANSMITTED', $leave1->status->tag);
        $this->assertEquals('TRANSMITTED', $leave2->status->tag);

        $this->assertEquals(0, $leave1->n);
        $this->assertEquals(2.0, $leave1->n1);

        $this->assertEquals(1.5, $leave2->n);
        $this->assertEquals(1.0, $leave2->n1);
    }

    public function test_counter_distribution_with_insufficient_balance(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 1);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client, true);

        $adminUser = $this->createUser(['profile_id' => $profiles['admin']->id, 'site_id' => $site->id]);
        $collaborator = $this->createUser(['profile_id' => $profiles['standard']->id, 'site_id' => $site->id]);
        
        $this->createUserLeaveCount($collaborator->id, $leaveType->id, 5, 2);

        Carbon::setTestNow(Carbon::create(2024, 8, 1));
        $leave = $this->createLeave($collaborator, $statuses['validated']->id, $leaveType->id, 2, 6.0, Carbon::create(2024, 4, 15));

        $payload = [['action' => 'TRANSMITTED', 'leave_id' => $leave->id]];

        $response = $this
            ->actingAs($adminUser, 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload);

        $response->assertStatus(200);

        $leave->refresh();
        $this->assertEquals('TRANSMITTED', $leave->status->tag);
        $this->assertEquals(4.0, $leave->n);
        $this->assertEquals(2.0, $leave->n1);
        $this->assertEquals(1, $leave->out_day);
    }

    public function test_counter_distribution_after_june_boundary(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 1);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client, true);

        $adminUser = $this->createUser(['profile_id' => $profiles['admin']->id, 'site_id' => $site->id]);
        $collaborator = $this->createUser(['profile_id' => $profiles['standard']->id, 'site_id' => $site->id]);
        
        $this->createUserLeaveCount($collaborator->id, $leaveType->id, 10, 5);

        Carbon::setTestNow(Carbon::create(2024, 8, 1));
        $leave = $this->createLeave($collaborator, $statuses['validated']->id, $leaveType->id, 2, 3.0, Carbon::create(2024, 7, 15));

        $payload = [['action' => 'TRANSMITTED', 'leave_id' => $leave->id]];

        $response = $this
            ->actingAs($adminUser, 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload);

        $response->assertStatus(200);

        $leave->refresh();
        $this->assertEquals('TRANSMITTED', $leave->status->tag);
        $this->assertEquals(3.0, $leave->n);
        $this->assertEquals(0, $leave->n1);
        $this->assertEquals(0, $leave->out_day);
    }

    public function test_counter_distribution_without_previous_year_counter(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 1);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client, false);

        $adminUser = $this->createUser(['profile_id' => $profiles['admin']->id, 'site_id' => $site->id]);
        $collaborator = $this->createUser(['profile_id' => $profiles['standard']->id, 'site_id' => $site->id]);
        
        UserLeaveCount::updateOrCreate(
            ['user_id' => $collaborator->id, 'leave_type_id' => $leaveType->id, 'is_last_year' => false],
            ['balance' => 10, 'acquired' => 10, 'taken' => 0]
        );

        $leave = $this->createLeave($collaborator, $statuses['validated']->id, $leaveType->id, 2, 4.0, Carbon::create(2024, 4, 15));

        $payload = [['action' => 'TRANSMITTED', 'leave_id' => $leave->id]];

        $response = $this
            ->actingAs($adminUser, 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload);

        $response->assertStatus(200);

        $leave->refresh();
        $this->assertEquals('TRANSMITTED', $leave->status->tag);
        $this->assertEquals(4.0, $leave->n);
        $this->assertEquals(0, $leave->n1);
        $this->assertEquals(0, $leave->out_day);
    }

    private function getStatuses(): array
    {
        return [
            'submitted' => Status::firstOrCreate(['tag' => 'SUBMITTED'], ['name' => 'Soumis à validation']),
            'validated' => Status::firstOrCreate(['tag' => 'VALIDATED'], ['name' => 'Validé']),
            'transmitted' => Status::firstOrCreate(['tag' => 'TRANSMITTED'], ['name' => 'Transmis en paie']),
        ];
    }

    private function createProfiles(): array
    {
        return [
            'admin' => Profile::firstOrCreate(['label' => 'ADMINISTRATEUR']),
            'manager' => Profile::firstOrCreate(['label' => 'ADMINISTRATEURMANAGER']),
            'standard' => Profile::firstOrCreate(['label' => 'STANDARD'])
        ];
    }

    private function createClient(string $scheme = 'VERTICAL', int $levels = 1): Client
    {
        $client = Client::factory()->create([
            'validation_scheme' => $scheme,
            'number_managers_can_validate' => $levels,
        ]);

        $day = Day::firstOrCreate(['day_name' => 'MONDAY']);
        $client->days()->syncWithoutDetaching([$day->id]);

        return $client;
    }

    private function createSite(Client $client): Site
    {
        return Site::factory()->create(['client_id' => $client->id]);
    }

    private function createLeaveType(Client $client, bool $isPay = true): LeaveType
    {
        return LeaveType::factory()->create([
            'client_id' => $client->id,
            'is_active' => true,
            'is_half_day' => true,
            'needs_count' => true,
            'can_exceed' => false,
            'is_pay' => $isPay
        ]);
    }

    private function createUser(array $attributes = []): User
    {
        if (isset($attributes['site_id']) && !isset($attributes['client_uuid'])) {
            $site = Site::find($attributes['site_id']);
            if ($site?->client) {
                $attributes['client_uuid'] = $site->client->uuid;
            }
        }
        return User::factory()->state($attributes)->create();
    }

    private function createLeave(User $user, int $statusId, int $leaveTypeId, int $validatorLevel = 1, float $duration = 1.0, Carbon $startDate = null): Leave
    {
        $startDate = $startDate ?? now()->addDays(20)->startOfDay()->setHour(9);
        $endDate = $startDate->copy()->addDays($duration - 1)->setHour(18);

        return Leave::factory()->create([
            'user_id' => $user->id,
            'creator_id' => $user->id,
            'status_id' => $statusId,
            'leave_type_id' => $leaveTypeId,
            'duration' => $duration,
            'current_validator_level' => $validatorLevel,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'n' => 0,
            'n1' => 0,
        ]);
    }

    private function createUserLeaveCount(int $userId, int $leaveTypeId, float $nBalance = 10, float $n1Balance = 5): void
    {
        UserLeaveCount::updateOrCreate(
            ['user_id' => $userId, 'leave_type_id' => $leaveTypeId, 'is_last_year' => false],
            ['balance' => $nBalance, 'acquired' => $nBalance, 'taken' => 0]
        );
        UserLeaveCount::updateOrCreate(
            ['user_id' => $userId, 'leave_type_id' => $leaveTypeId, 'is_last_year' => true],
            ['balance' => $n1Balance, 'acquired' => $n1Balance, 'taken' => 0]
        );
    }
}
