<?php

namespace Tests\Feature\Http\Controller\v1\LeavesController;

//use App\Excel\Exports\LeavesExport;
use App\Models\Api\ExportHistory;
use App\Models\Api\Leave;
use App\Models\Api\Profile;
use App\Models\Api\Status;
use App\Models\Api\User;
use Carbon\Carbon;
use Tests\TestCase;

//use Maatwebsite\Excel\Facades\Excel;

class LeavesExportTest extends TestCase {

//    public function testExportLeaveClassic(): void
//    {
//        Excel::fake();
//        $adminProfile = Profile::whereIn('label', ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->get();
//        $currentUser = User::whereIn('profile_id',$adminProfile->pluck('id')->toArray())->first();
//        $clientId = $currentUser->site->client_id;
//        $currentUserId = $currentUser->id;
//        $JWT = $this->getDummyJWT($currentUserId);
//
//        // Request
//        $this->post('/api/v1/export-leaves', [], ['Authorization' => $JWT]);
//
//        $statusTransmisttedId = Status::where('tag', '=', 'TRANSMITTED')->first()->id;
//
//        $leaves = Leave::whereHas('user', function ($query) use ($clientId, $statusTransmisttedId){
//            $query->join("sites","users.site_id","=","sites.id")->where('client_id', '=', $clientId);
//        })->where('status_id', '=', $statusTransmisttedId)->with('user')->with('leave_type')->get();
//        $filename = ExportHistory::where("user_id", "=", $currentUserId)->get()->last()->file_name;
//
//        // Assertions
//        Excel::assertStored("exports/".$clientId."/".$filename, 'minio', function(LeavesExport $export) use ($leaves, $filename) {
//            $array = collect($export->array()[0]);
//            $map = collect($export->map($array));
//
//            // verify array data
//            $this->assertEquals($leaves, $array["leaves"]);
//
//            // verify array keys
//            $this->assertEquals(collect(["leaves"]), $array->keys());
//
//            // verify number lignes
//            $this->assertCount(count($array["leaves"]), $map);
//
//            // verify exported data
//            foreach ($leaves as $key => $leave) {
//                $row = $map[$key];
//                $matricule = $leave->user['matricule'];
//                $name = strtoupper($leave->user['lastname'] ." ".$leave->user['firstname']);
//                $leave_code = $leave->leave_type['leave_code'];
//
//                $expectData = [$matricule, $name, $leave_code];
//                $actualData = [$row[0], $row[1], $row[2]];
//                $this->assertEquals($expectData, $actualData);
//            }
//
//            return true;
//        });
//    }
//
//    public function testExportLeaveWithLeaveIdsArray(): void
//    {
//        Excel::fake();
//        $adminProfile = Profile::whereIn('label', ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->get();
//        $currentUser = User::whereIn('profile_id',$adminProfile->pluck('id')->toArray())->first();
//        $clientId = $currentUser->site->client_id;
//        $currentUserId = $currentUser->id;
//        $JWT = $this->getDummyJWT($currentUserId);
//
//        //Get Leaves without user's leaves
//        $leaves = Leave::whereHas('user', function ($query) use ($clientId) {
//            $query->join("sites", "users.site_id", "=", "sites.id")->where('client_id', '=', $clientId);
//        })->where("user_id", "!=", $currentUserId)->with('user')->with('leave_type')->get();
//
//        // Request
//        $this->post( '/api/v1/export-leaves', [
//            "leave_ids" => $leaves->pluck('id')->toArray()
//        ], ['Authorization' => $JWT]);
//
//        $filename = ExportHistory::where("user_id", "=", $currentUserId)->get()->last()->file_name;
//
//        // Assertions
//        Excel::assertStored("exports/".$clientId."/".$filename, 'minio', function(LeavesExport $export) use ($leaves, $filename) {
//            $array = collect($export->array()[0]);
//            $map = collect($export->map($array));
//
//            // verify array data
//            $this->assertEquals($leaves, $array["leaves"]);
//
//            // verify array keys
//            $this->assertEquals(collect(["leaves"]), $array->keys());
//
//            // verify number lignes
//            $this->assertCount(count($array["leaves"]), $map);
//
//            // verify exported data
//            foreach ($leaves as $key => $leave) {
//                $row = $map[$key];
//                $matricule = $leave->user['matricule'];
//                $name = strtoupper($leave->user['lastname'] ." ".$leave->user['firstname']);
//                $leave_code = $leave->leave_type['leave_code'];
//
//                $expectData = [$matricule, $name, $leave_code];
//                $actualData = [$row[0], $row[1], $row[2]];
//                $this->assertEquals($expectData, $actualData);
//            }
//
//            return true;
//        });
//    }
//
//    public function testExportLeaveStartAndEndDate(): void
//    {
//        Excel::fake();
//        $adminProfile = Profile::whereIn('label', ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->get();
//        $currentUser = User::whereIn('profile_id',$adminProfile->pluck('id')->toArray())->first();
//        $clientId = $currentUser->site->client_id;
//        $currentUserId = $currentUser->id;
//        $JWT = $this->getDummyJWT($currentUserId);
//
//        // Set start and end date
//        $statusTransmisttedId = Status::where('tag', '=', 'TRANSMITTED')->first()->id;
//        $randLeave = Leave::whereHas('user', function ($query) use ($clientId, $statusTransmisttedId){
//            $query->join("sites","users.site_id","=","sites.id")->where('client_id', '=', $clientId);
//        })->where('status_id', '=', $statusTransmisttedId)->get()->random();
//        $start_date = Carbon::parse($randLeave->start_date)->format('d/m/Y H:i:s');
//        $end_date = Carbon::parse($randLeave->end_date)->format('d/m/Y H:i:s');
//
//        // Request
//        $this->post('/api/v1/export-leaves?start_date='.$start_date.'&end_date='.$end_date, [], ['Authorization' => $JWT]);
//
//        $leaves = Leave::whereHas('user', function ($query) use ($clientId, $statusTransmisttedId){
//            $query->join("sites","users.site_id","=","sites.id")->where('client_id', '=', $clientId);
//        })->where('status_id', '=', $statusTransmisttedId)
//            ->where('start_date', '>=', Carbon::createFromFormat("d/m/Y H:i:s",$start_date)->format('Y-m-d H:i:s'))
//            ->where('end_date', '<=', Carbon::createFromFormat("d/m/Y H:i:s",$end_date)->format('Y-m-d H:i:s'))
//            ->with('user')->with('leave_type')->get();
//        $filename = ExportHistory::where("user_id", "=", $currentUserId)->get()->last()->file_name;
//
//        // Assertions
//        Excel::assertStored("exports/".$clientId."/".$filename, 'minio', function(LeavesExport $export) use ($leaves, $filename) {
//            $array = collect($export->array()[0]);
//            $map = collect($export->map($array));
//
//            // verify array data
//            $this->assertEquals($leaves, $array["leaves"]);
//
//            // verify array keys
//            $this->assertEquals(collect(["leaves"]), $array->keys());
//
//            // verify number lignes
//            $this->assertCount(count($array["leaves"]), $map);
//
//            // verify exported data
//            foreach ($leaves as $key => $leave) {
//                $row = $map[$key];
//                $matricule = $leave->user['matricule'];
//                $name = strtoupper($leave->user['lastname'] ." ".$leave->user['firstname']);
//                $leave_code = $leave->leave_type['leave_code'];
//
//                $expectData = [$matricule, $name, $leave_code];
//                $actualData = [$row[0], $row[1], $row[2]];
//                $this->assertEquals($expectData, $actualData);
//            }
//
//            return true;
//        });
//    }
//
//    public function testExportBadStartAndEndDateFormat(): void
//    {
//        $adminProfile = Profile::whereIn('label', ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->get();
//        $currentUser = User::whereIn('profile_id',$adminProfile->pluck('id')->toArray())->first();
//        $currentUserId = $currentUser->id;
//        $JWT = $this->getDummyJWT($currentUserId);
//
//        $start_date = "ABC";
//        $end_date = "26/12/2020";
//        // Request
//        $response = $this->post('/api/v1/export-leaves?start_date='.$start_date.'&end_date='.$end_date, [], ['Authorization' => $JWT]);
//        $this->assertResponseStatus(422);
//        $response->seeJsonEquals([
//            "details" => [
//                "start_date" => [
//                    "start date MustRespectFormat d/m/Y H:i:s"
//                ],
//                "end_date" => [
//                    "end date MustRespectFormat d/m/Y H:i:s"
//                ]
//            ],
//            "message" => "ErrorExportLeave",
//        ]);
//    }
}
