<?php

namespace Tests\Feature\Http\Controller\v1\LeavesController;

use App\Models\Api\Client;
use App\Models\Api\History;
use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\Profile;
use App\Models\Api\Site;
use App\Models\Api\Status;
use App\Models\Api\User;
use App\Models\Api\UserLeaveCount;
use App\Http\Middleware\CheckProfile;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class LeavesCancelTest extends TestCase
{

    protected function setUp(): void
    {
        parent::setUp();
        $this->withoutMiddleware(CheckProfile::class);
        Notification::fake();
    }

    private function getStatuses()
    {
        return [
            'submitted' => Status::firstOrCreate(['tag' => 'SUBMITTED'], ['name' => 'Soumis à validation']),
            'validated' => Status::firstOrCreate(['tag' => 'VALIDATED'], ['name' => 'Validé']),
            'refused' => Status::firstOrCreate(['tag' => 'REFUSED'], ['name' => 'Refusée']),
            'transmitted' => Status::firstOrCreate(['tag' => 'TRANSMITTED'], ['name' => 'Transmis en paie']),
            'canceled' => Status::firstOrCreate(['tag' => 'CANCELED'], ['name' => 'Annulé']),
            'submitted_to_cancellation' => Status::firstOrCreate(['tag' => 'SUBMITTED_TO_CANCELLATION'], ['name' => 'Soumis à annulation']),
            'imported' => Status::firstOrCreate(['tag' => 'IMPORTED'], ['name' => 'Importé']),
        ];
    }

    private function createProfiles(): array
    {
        return [
            'admin' => Profile::firstOrCreate(['label' => 'ADMINISTRATEUR']),
            'manager' => Profile::firstOrCreate(['label' => 'ADMINISTRATEURMANAGER']),
            'standard_manager' => Profile::firstOrCreate(['label' => 'STANDARDMANAGER']),
            'standard' => Profile::firstOrCreate(['label' => 'STANDARD'])
        ];
    }

    private function createClient(string $scheme = 'VERTICAL', int $levels = 1): Client
    {
        return Client::factory()->create([
            'validation_scheme' => $scheme,
            'number_managers_can_validate' => $levels,
        ]);
    }

    private function createSite(Client $client): Site
    {
        return Site::factory()->create(['client_id' => $client->id]);
    }

    private function createLeaveType(Client $client, bool $needsCount = true): LeaveType
    {
        return LeaveType::factory()->create([
            'client_id' => $client->id,
            'is_active' => true,
            'is_half_day' => true,
            'needs_count' => $needsCount,
            'can_exceed' => false,
        ]);
    }

    private function createLeave(User $user, int $statusId, int $leaveTypeId, int $validatorLevel = 1): Leave
    {
        $startDate = now()->addDays(20)->startOfDay()->setHour(9);
        $endDate = now()->addDays(20)->startOfDay()->setHour(18);

        $leave = Leave::factory()->create([
            'user_id' => $user->id,
            'creator_id' => $user->id,
            'status_id' => $statusId,
            'leave_type_id' => $leaveTypeId,
            'duration' => 1,
            'current_validator_level' => $validatorLevel,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);

        if ($statusId === Status::firstWhere('tag', 'SUBMITTED')?->id) {
            History::create([
                'leave_id' => $leave->id,
                'user_id' => $user->id,
                'status_id' => $statusId,
                'reason' => 'Soumission initiale',
            ]);
        }
        else if ($statusId === Status::firstWhere('tag', 'VALIDATED')?->id) {
            History::create([
                'leave_id' => $leave->id,
                'user_id' => $user->managers()->first()?->id ?? $user->id,
                'status_id' => $statusId,
                'reason' => 'Validation initiale',
            ]);
        }

        return $leave->refresh();
    }

    private function createUserLeaveCount(int $userId, int $leaveTypeId, float $initialNBalance = 10, float $initialN1Balance = 5): void
    {
        UserLeaveCount::updateOrCreate(
            ['user_id' => $userId, 'leave_type_id' => $leaveTypeId, 'is_last_year' => false],
            ['balance' => $initialNBalance, 'acquired' => $initialNBalance, 'taken' => 0]
        );
        UserLeaveCount::updateOrCreate(
            ['user_id' => $userId, 'leave_type_id' => $leaveTypeId, 'is_last_year' => true],
            ['balance' => $initialN1Balance, 'acquired' => $initialN1Balance, 'taken' => 0]
        );
    }

    /**
     * Test that a collaborator can cancel their own leave when it has the 'SUBMITTED' status.
     */
    public function test_collaborator_can_cancel_own_submitted_leave(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient();
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client, true);

        $collaborator = User::factory()->state([
            'profile_id' => $profiles['standard']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();
        $manager = User::factory()->state([
            'profile_id' => $profiles['standard_manager']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();
        $collaborator->managers()->attach($manager->id, ['level' => 1]);
        $this->createUserLeaveCount($collaborator->id, $leaveType->id, 10, 5);

        $leave = $this->createLeave($collaborator, $statuses['submitted']->id, $leaveType->id, 1);

        $response = $this
            ->actingAs($collaborator, 'jwt')
            ->putJson('/api/v1/leaves-cancel', ['leave_id' => $leave->id]);

        $response->assertStatus(200)
            ->assertJsonFragment(['message' => __('messages.SuccessCancelLeave')]);

        $this->assertDatabaseHas('leaves', [
            'id' => $leave->id,
            'status_id' => $statuses['canceled']->id
        ]);

        $this->assertDatabaseMissing('histories', [
            'leave_id' => $leave->id,
            'user_id' => $collaborator->id,
            'status_id' => $statuses['canceled']->id
        ]);

        $this->assertDatabaseHas('user_leave_counts', [
            'user_id' => $collaborator->id,
            'leave_type_id' => $leaveType->id,
            'is_last_year' => false,
            'balance' => 10
        ]);
        $this->assertDatabaseHas('user_leave_counts', [
            'user_id' => $collaborator->id,
            'leave_type_id' => $leaveType->id,
            'is_last_year' => true,
            'balance' => 5
        ]);
    }

    /**
     * Test that a collaborator requests cancellation for their own leave when it has the 'VALIDATED' status.
     */
    public function test_collaborator_requests_cancellation_for_own_validated_leave(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 1);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client, true);

        $collaborator = User::factory()->state([
            'profile_id' => $profiles['standard']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();
        $manager = User::factory()->state([
            'profile_id' => $profiles['standard_manager']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();
        $collaborator->managers()->attach($manager->id, ['level' => 1]);
        $this->createUserLeaveCount($collaborator->id, $leaveType->id, 10, 5);

        $leave = $this->createLeave($collaborator, $statuses['validated']->id, $leaveType->id, 2);
        UserLeaveCount::where('user_id', $collaborator->id)
            ->where('leave_type_id', $leaveType->id)
            ->where('is_last_year', false)
            ->decrement('balance', $leave->duration);

        $response = $this
            ->actingAs($collaborator, 'jwt')
            ->putJson('/api/v1/leaves-cancel', ['leave_id' => $leave->id]);

        $response->assertStatus(200)
            ->assertJsonFragment(['message' => __('messages.AskForCancelLeave')]);

        $this->assertDatabaseHas('leaves', [
            'id' => $leave->id,
            'status_id' => $statuses['submitted_to_cancellation']->id
        ]);

        $this->assertDatabaseHas('histories', [
            'leave_id' => $leave->id,
            'user_id' => $collaborator->id,
            'status_id' => $statuses['submitted_to_cancellation']->id
        ]);

        $this->assertDatabaseHas('user_leave_counts', [
            'user_id' => $collaborator->id,
            'leave_type_id' => $leaveType->id,
            'is_last_year' => false,
            'balance' => 9
        ]);
    }

    /**
     * Test that a collaborator cannot cancel their own leave when it has the 'TRANSMITTED' status.
     */
    public function test_collaborator_cannot_cancel_own_transmitted_leave(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient();
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client, true);

        $collaborator = User::factory()->state([
            'profile_id' => $profiles['standard']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();
        $this->createUserLeaveCount($collaborator->id, $leaveType->id);
        $leave = $this->createLeave($collaborator, $statuses['transmitted']->id, $leaveType->id, 3);

        $response = $this
            ->actingAs($collaborator, 'jwt')
            ->putJson('/api/v1/leaves-cancel', ['leave_id' => $leave->id]);

        $response->assertStatus(400)
            ->assertJsonFragment(['message' => __('messages.CantCancelLeave')])
            ->assertJsonFragment(['details' => __('messages.CantCancelLeaveTransmitted')]);

        $this->assertDatabaseHas('leaves', [
            'id' => $leave->id,
            'status_id' => $statuses['transmitted']->id
        ]);
    }

    /**
     * Test that a manager can cancel a managed user's leave when it has the 'SUBMITTED' status.
     */
    public function test_manager_can_cancel_managed_user_submitted_leave(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient();
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client, true);

        $collaborator = User::factory()->state([
            'profile_id' => $profiles['standard']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();
        $manager = User::factory()->state([
            'profile_id' => $profiles['standard_manager']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();
        $collaborator->managers()->attach($manager->id, ['level' => 1]);
        $this->createUserLeaveCount($collaborator->id, $leaveType->id, 10, 5);

        $leave = $this->createLeave($collaborator, $statuses['submitted']->id, $leaveType->id, 1);

        $response = $this
            ->actingAs($manager, 'jwt')
            ->putJson('/api/v1/leaves-cancel', ['leave_id' => $leave->id]);

        $response->assertStatus(200)
            ->assertJsonFragment(['message' => __('messages.SuccessTransmittedLeaveCanceled')]);

        $this->assertDatabaseHas('leaves', [
            'id' => $leave->id,
            'status_id' => $statuses['canceled']->id
        ]);

        $this->assertDatabaseHas('histories', [
            'leave_id' => $leave->id,
            'user_id' => $manager->id,
            'status_id' => $statuses['canceled']->id
        ]);

        $this->assertDatabaseHas('histories', [
            'leave_id' => $leave->id,
            'user_id' => $collaborator->id,
            'status_id' => $statuses['submitted']->id
        ]);

        $this->assertDatabaseHas('user_leave_counts', [
            'user_id' => $collaborator->id, 'leave_type_id' => $leaveType->id, 'is_last_year' => false, 'balance' => 10
        ]);
        $this->assertDatabaseHas('user_leave_counts', [
            'user_id' => $collaborator->id, 'leave_type_id' => $leaveType->id, 'is_last_year' => true, 'balance' => 5
        ]);
    }

    /**
     * Test that a manager can cancel a managed user's leave that they previously validated.
     */
    public function test_manager_can_cancel_managed_user_validated_leave(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 1);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client, true);

        $collaborator = User::factory()->state([
            'profile_id' => $profiles['standard']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();
        $manager = User::factory()->state([
            'profile_id' => $profiles['standard_manager']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();
        $collaborator->managers()->attach($manager->id, ['level' => 1]);
        $this->createUserLeaveCount($collaborator->id, $leaveType->id, 10, 5);

        $leave = $this->createLeave($collaborator, $statuses['validated']->id, $leaveType->id, 2);

        UserLeaveCount::where('user_id', $collaborator->id)
            ->where('leave_type_id', $leaveType->id)
            ->where('is_last_year', false)
            ->decrement('balance', $leave->duration);

        $response = $this
            ->actingAs($manager, 'jwt')
            ->putJson('/api/v1/leaves-cancel', ['leave_id' => $leave->id]);


        $response->assertStatus(200)
            ->assertJsonFragment(['message' => __('messages.SuccessTransmittedLeaveCanceled')]);

        $this->assertDatabaseHas('leaves', [
            'id' => $leave->id,
            'status_id' => $statuses['canceled']->id
        ]);

        $this->assertDatabaseHas('histories', [
            'leave_id' => $leave->id,
            'user_id' => $manager->id,
            'status_id' => $statuses['canceled']->id
        ]);

        $this->assertDatabaseHas('histories', [
            'leave_id' => $leave->id,
            'status_id' => $statuses['validated']->id
        ]);

        $this->assertDatabaseHas('user_leave_counts', [
            'user_id' => $collaborator->id,
            'leave_type_id' => $leaveType->id,
            'is_last_year' => false,
            'balance' => 9 // Attendu que le solde reste à 9
        ]);
        $this->assertDatabaseHas('user_leave_counts', [
            'user_id' => $collaborator->id,
            'leave_type_id' => $leaveType->id,
            'is_last_year' => true,
            'balance' => 5
        ]);
    }

    /**
     * Test that an administrator can cancel a leave that has the 'TRANSMITTED' status.
     */
    public function test_admin_can_cancel_transmitted_leave(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient();
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client, true);

        $collaborator = User::factory()->state([
            'profile_id' => $profiles['standard']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();
        $adminUser = User::factory()->state([
            'profile_id' => $profiles['admin']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();

        $this->createUserLeaveCount($collaborator->id, $leaveType->id, 10, 5);

        $leave = $this->createLeave($collaborator, $statuses['transmitted']->id, $leaveType->id, 3);

        UserLeaveCount::where('user_id', $collaborator->id)
            ->where('leave_type_id', $leaveType->id)
            ->where('is_last_year', false)
            ->decrement('balance', $leave->duration);

        History::create([
            'leave_id' => $leave->id,
            'user_id' => $adminUser->id,
            'status_id' => $statuses['transmitted']->id,
            'reason' => 'Transmission simulée',
        ]);
        $leave->refresh();


        $response = $this
            ->actingAs($adminUser, 'jwt')
            ->putJson('/api/v1/leaves-cancel', ['leave_id' => $leave->id]);

        $response->assertStatus(200)
            ->assertJsonFragment(['message' => __('messages.SuccessTransmittedLeaveCanceled')]);

        $this->assertDatabaseHas('leaves', [
            'id' => $leave->id,
            'status_id' => $statuses['canceled']->id
        ]);

        $this->assertDatabaseHas('histories', [
            'leave_id' => $leave->id,
            'user_id' => $adminUser->id,
            'status_id' => $statuses['canceled']->id
        ]);

        $this->assertDatabaseHas('histories', [
            'leave_id' => $leave->id,
            'user_id' => $adminUser->id,
            'status_id' => $statuses['transmitted']->id
        ]);

        $this->assertDatabaseHas('user_leave_counts', [
            'user_id' => $collaborator->id, 'leave_type_id' => $leaveType->id, 'is_last_year' => false, 'balance' => 9 // Attendu que le solde reste à 9
        ]);
        $this->assertDatabaseHas('user_leave_counts', [
            'user_id' => $collaborator->id, 'leave_type_id' => $leaveType->id, 'is_last_year' => true, 'balance' => 5 // N1 non touché
        ]);
    }

    /**
     * Test that a user who is not the owner, manager, or admin cannot cancel a leave.
     */
    public function test_non_owner_non_manager_non_admin_cannot_cancel_leave(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient();
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client);

        $collaborator = User::factory()->state([
            'profile_id' => $profiles['standard']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();
        $otherUser = User::factory()->state([
            'profile_id' => $profiles['standard']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();
        $this->createUserLeaveCount($collaborator->id, $leaveType->id);

        $leave = $this->createLeave($collaborator, $statuses['submitted']->id, $leaveType->id, 1);

        $response = $this
            ->actingAs($otherUser, 'jwt')
            ->putJson('/api/v1/leaves-cancel', ['leave_id' => $leave->id]);

        $response->assertStatus(400)
            ->assertJsonFragment(['message' => __('messages.CantCancelLeave')])
            ->assertJsonFragment(['details' => __('messages.DontOwnLeaveOrManagerUser')]);

        $this->assertDatabaseHas('leaves', [
            'id' => $leave->id,
            'status_id' => $statuses['submitted']->id
        ]);
    }

    /**
     * Test that a collaborator can cancel their own leave in a 2-level management circuit when it has not been accepted by any manager.
     */
    public function test_collaborator_can_cancel_own_leave_in_two_level_circuit_not_yet_accepted(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 2);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client, true);

        $collaborator = User::factory()->state([
            'profile_id' => $profiles['standard']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();

        $manager1 = User::factory()->state([
            'profile_id' => $profiles['standard_manager']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();

        $manager2 = User::factory()->state([
            'profile_id' => $profiles['standard_manager']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();

        $collaborator->managers()->attach($manager1->id, ['level' => 1]);
        $collaborator->managers()->attach($manager2->id, ['level' => 2]);

        $this->createUserLeaveCount($collaborator->id, $leaveType->id, 10, 5);

        $leave = $this->createLeave($collaborator, $statuses['submitted']->id, $leaveType->id, 1);

        $response = $this
            ->actingAs($collaborator, 'jwt')
            ->putJson('/api/v1/leaves-cancel', ['leave_id' => $leave->id]);

        $response->assertStatus(200)
            ->assertJsonFragment(['message' => __('messages.SuccessCancelLeave')]);

        $this->assertDatabaseHas('leaves', [
            'id' => $leave->id,
            'status_id' => $statuses['canceled']->id
        ]);

        $this->assertDatabaseHas('user_leave_counts', [
            'user_id' => $collaborator->id,
            'leave_type_id' => $leaveType->id,
            'is_last_year' => false,
            'balance' => 10
        ]);
    }

    /**
     * Test that a collaborator requests cancellation for their own leave in a 2-level management circuit
     * when it has been accepted by the first level manager but not yet by the second level manager.
     */
    public function test_collaborator_requests_cancellation_for_own_leave_accepted_by_m1_not_m2(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 2);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client, true);

        $collaborator = User::factory()->state([
            'profile_id' => $profiles['standard']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();

        $manager1 = User::factory()->state([
            'profile_id' => $profiles['standard_manager']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();

        $manager2 = User::factory()->state([
            'profile_id' => $profiles['standard_manager']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();

        $collaborator->managers()->attach($manager1->id, ['level' => 1]);
        $collaborator->managers()->attach($manager2->id, ['level' => 2]);

        $this->createUserLeaveCount($collaborator->id, $leaveType->id, 10, 5);

        $leave = $this->createLeave($collaborator, $statuses['submitted']->id, $leaveType->id, 2);

        History::create([
            'leave_id' => $leave->id,
            'user_id' => $manager1->id,
            'status_id' => $statuses['validated']->id,
            'reason' => 'Validation par M1',
        ]);

        // Update leave status to reflect M1 validation but still in submitted status
        $leave->status_id = $statuses['submitted']->id;
        $leave->save();
        $leave->refresh();

        $response = $this
            ->actingAs($collaborator, 'jwt')
            ->putJson('/api/v1/leaves-cancel', ['leave_id' => $leave->id]);

        $response->assertStatus(200);

        $this->assertDatabaseHas('leaves', [
            'id' => $leave->id,
            'status_id' => $statuses['submitted_to_cancellation']->id
        ]);

        $this->assertDatabaseHas('histories', [
            'leave_id' => $leave->id,
            'user_id' => $collaborator->id,
            'status_id' => $statuses['submitted_to_cancellation']->id
        ]);

        $this->assertDatabaseHas('user_leave_counts', [
            'user_id' => $collaborator->id,
            'leave_type_id' => $leaveType->id,
            'is_last_year' => false,
            'balance' => 10
        ]);
    }

    /**
     * Test that a collaborator requests cancellation for their own leave in a 2-level management circuit
     * when it has been accepted by both level managers.
     */
    public function test_collaborator_requests_cancellation_for_own_leave_accepted_by_both_managers(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 2);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client, true);

        $collaborator = User::factory()->state([
            'profile_id' => $profiles['standard']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();

        $manager1 = User::factory()->state([
            'profile_id' => $profiles['standard_manager']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();

        $manager2 = User::factory()->state([
            'profile_id' => $profiles['standard_manager']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();

        $collaborator->managers()->attach($manager1->id, ['level' => 1]);
        $collaborator->managers()->attach($manager2->id, ['level' => 2]);

        $this->createUserLeaveCount($collaborator->id, $leaveType->id, 10, 5);

        $leave = $this->createLeave($collaborator, $statuses['validated']->id, $leaveType->id, 3);

        History::create([
            'leave_id' => $leave->id,
            'user_id' => $manager1->id,
            'status_id' => $statuses['validated']->id,
            'reason' => 'Validation par M1',
        ]);

        // Simulate M2 validation
        History::create([
            'leave_id' => $leave->id,
            'user_id' => $manager2->id,
            'status_id' => $statuses['validated']->id,
            'reason' => 'Validation par M2',
        ]);

        UserLeaveCount::where('user_id', $collaborator->id)
            ->where('leave_type_id', $leaveType->id)
            ->where('is_last_year', false)
            ->decrement('balance', $leave->duration);

        $response = $this
            ->actingAs($collaborator, 'jwt')
            ->putJson('/api/v1/leaves-cancel', ['leave_id' => $leave->id]);

        $response->assertStatus(200)
            ->assertJsonFragment(['message' => __('messages.AskForCancelLeave')]);

        $this->assertDatabaseHas('leaves', [
            'id' => $leave->id,
            'status_id' => $statuses['submitted_to_cancellation']->id
        ]);

        $this->assertDatabaseHas('histories', [
            'leave_id' => $leave->id,
            'user_id' => $collaborator->id,
            'status_id' => $statuses['submitted_to_cancellation']->id
        ]);

        $this->assertDatabaseHas('user_leave_counts', [
            'user_id' => $collaborator->id,
            'leave_type_id' => $leaveType->id,
            'is_last_year' => false,
            'balance' => 9
        ]);
    }

    /**
     * Test that a first level manager can approve cancellation for a leave that was previously validated by both managers.
     */
    public function test_m1_can_approve_cancellation_for_leave_validated_by_both_managers(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 2);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client, true);

        $collaborator = User::factory()->state([
            'profile_id' => $profiles['standard']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();

        $manager1 = User::factory()->state([
            'profile_id' => $profiles['standard_manager']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();

        $manager2 = User::factory()->state([
            'profile_id' => $profiles['standard_manager']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();

        $collaborator->managers()->attach($manager1->id, ['level' => 1]);
        $collaborator->managers()->attach($manager2->id, ['level' => 2]);

        $this->createUserLeaveCount($collaborator->id, $leaveType->id, 10, 5);

        $leave = $this->createLeave($collaborator, $statuses['submitted_to_cancellation']->id, $leaveType->id, 1);

        History::create([
            'leave_id' => $leave->id,
            'user_id' => $manager1->id,
            'status_id' => $statuses['validated']->id,
            'reason' => 'Validation par M1',
        ]);

        History::create([
            'leave_id' => $leave->id,
            'user_id' => $manager2->id,
            'status_id' => $statuses['validated']->id,
            'reason' => 'Validation par M2',
        ]);

        History::create([
            'leave_id' => $leave->id,
            'user_id' => $collaborator->id,
            'status_id' => $statuses['submitted_to_cancellation']->id,
            'reason' => 'Demande d\'annulation',
        ]);

        UserLeaveCount::where('user_id', $collaborator->id)
            ->where('leave_type_id', $leaveType->id)
            ->where('is_last_year', false)
            ->decrement('balance', $leave->duration);

        $payload = [['action' => 'TRANSMITTED', 'leave_id' => $leave->id]];

        $response = $this
            ->actingAs($manager1, 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload);

        $response->assertStatus(200);

        $this->assertDatabaseHas('leaves', [
            'id' => $leave->id,
            'status_id' => $statuses['canceled']->id
        ]);

        $this->assertDatabaseHas('histories', [
            'leave_id' => $leave->id,
            'user_id' => $manager1->id,
            'status_id' => $statuses['canceled']->id
        ]);

        $this->assertDatabaseHas('user_leave_counts', [
            'user_id' => $collaborator->id,
            'leave_type_id' => $leaveType->id,
            'is_last_year' => false,
            'balance' => 9
        ]);
    }

    /**
     * Test that a first level manager can refuse cancellation for a leave that was previously validated by both managers.
     */
    public function test_m1_can_refuse_cancellation_for_leave_validated_by_both_managers(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 2);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client, true);

        $collaborator = User::factory()->state([
            'profile_id' => $profiles['standard']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();

        $manager1 = User::factory()->state([
            'profile_id' => $profiles['standard_manager']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();

        $manager2 = User::factory()->state([
            'profile_id' => $profiles['standard_manager']->id, 'site_id' => $site->id, 'client_uuid' => $site->client->uuid
        ])->create();

        $collaborator->managers()->attach($manager1->id, ['level' => 1]);
        $collaborator->managers()->attach($manager2->id, ['level' => 2]);

        $this->createUserLeaveCount($collaborator->id, $leaveType->id, 10, 5);

        $leave = $this->createLeave($collaborator, $statuses['submitted_to_cancellation']->id, $leaveType->id, 1);

        // Simulate previous validations
        History::create([
            'leave_id' => $leave->id,
            'user_id' => $manager1->id,
            'status_id' => $statuses['validated']->id,
            'reason' => 'Validation par M1',
        ]);

        History::create([
            'leave_id' => $leave->id,
            'user_id' => $manager2->id,
            'status_id' => $statuses['validated']->id,
            'reason' => 'Validation par M2',
        ]);

        History::create([
            'leave_id' => $leave->id,
            'user_id' => $collaborator->id,
            'status_id' => $statuses['submitted_to_cancellation']->id,
            'reason' => 'Demande d\'annulation',
        ]);

        // Decrement balance to simulate leave validation
        UserLeaveCount::where('user_id', $collaborator->id)
            ->where('leave_type_id', $leaveType->id)
            ->where('is_last_year', false)
            ->decrement('balance', $leave->duration);

        $payload = [['action' => 'REFUSED', 'leave_id' => $leave->id]];

        $response = $this
            ->actingAs($manager1, 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload);

        $response->assertStatus(200);

        $this->assertDatabaseHas('leaves', [
            'id' => $leave->id,
            'status_id' => $statuses['submitted']->id
        ]);

        $this->assertDatabaseHas('histories', [
            'leave_id' => $leave->id,
            'user_id' => $manager1->id,
            'status_id' => $statuses['refused']->id
        ]);

        $this->assertDatabaseHas('user_leave_counts', [
            'user_id' => $collaborator->id,
            'leave_type_id' => $leaveType->id,
            'is_last_year' => false,
            'balance' => 9
        ]);
    }
}
