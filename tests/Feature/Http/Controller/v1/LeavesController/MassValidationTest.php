<?php

namespace Tests\Feature\Http\Controller\v1\LeavesController;

use App\Models\Api\Client;
use App\Models\Api\Day;
use App\Models\Api\History;
use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\Profile;
use App\Models\Api\Site;
use App\Models\Api\Status;
use App\Models\Api\User;
use Tests\TestCase;
use Illuminate\Support\Facades\DB;

class MassValidationTest extends TestCase
{

    protected function setUp(): void
    {
        parent::setUp();
        $this->withoutMiddleware();
    }

    private function getStatuses()
    {
        return [
            'submitted' => Status::firstOrCreate(['tag' => 'SUBMITTED'], ['name' => 'Soumis à validation']),
            'validated' => Status::firstOrCreate(['tag' => 'VALIDATED'], ['name' => 'Validé']),
            'refused' => Status::firstOrCreate(['tag' => 'REFUSED'], ['name' => 'Refusée']),
            'transmitted' => Status::firstOrCreate(['tag' => 'TRANSMITTED'], ['name' => 'Transmis en paie']),
            'canceled' => Status::firstOrCreate(['tag' => 'CANCELED'], ['name' => 'Annulé']),
            'submitted_to_cancellation' => Status::firstOrCreate(['tag' => 'SUBMITTED_TO_CANCELLATION'], ['name' => 'Soumis à annulation']),
            'imported' => Status::firstOrCreate(['tag' => 'IMPORTED'], ['name' => 'Importé']),
        ];
    }

    private function createProfiles()
    {
        return [
            'admin' => Profile::firstOrCreate(['label' => 'ADMINISTRATEUR']),
            'manager' => Profile::firstOrCreate(['label' => 'ADMINISTRATEURMANAGER']),
            'standard' => Profile::firstOrCreate(['label' => 'STANDARD'])
        ];
    }

    private function createClient()
    {
        $client = Client::factory()->create([
            'validation_scheme' => 'HORIZONTAL',
            'number_managers_can_validate' => 1,
        ]);

        $day = Day::firstOrCreate(['day_name' => 'MONDAY']);
        if (!$client->days()->where('day_id', $day->id)->exists()) {
            $client->days()->attach($day->id);
        }

        return $client;
    }

    private function createSite($client)
    {
        return Site::factory()->create(['client_id' => $client->id]);
    }

    private function createLeaveType($client)
    {
        return LeaveType::factory()->create([
            'client_id' => $client->id,
            'is_active' => true,
        ]);
    }

    private function createUsers($profiles, $site)
    {
        $admin = User::factory()
            ->state([
                'profile_id' => $profiles['admin']->id,
                'site_id' => $site->id,
                'client_uuid' => $site->client->uuid,
            ])
            ->create();

        $manager = User::factory()
            ->state([
                'profile_id' => $profiles['manager']->id,
                'site_id' => $site->id,
                'client_uuid' => $site->client->uuid,
            ])
            ->create();

        $admin->managers()->attach($manager->id, ['level' => 1]);

        return [
            'admin' => $admin,
            'manager' => $manager
        ];
    }

    private function createLeave($user, $statusId, $leaveTypeId)
    {
        $leave = Leave::factory()->create([
            'user_id' => $user->id,
            'creator_id' => $user->id,
            'status_id' => $statusId,
            'leave_type_id' => $leaveTypeId,
            'duration' => 1,
            'current_validator_level' => 1,
            'start_date' => now()->addDays(10)->startOfDay(),
            'end_date' => now()->addDays(10)->endOfDay(),
        ]);

        if ($statusId === Status::firstWhere('tag', 'SUBMITTED')?->id) {
            History::create([
                'leave_id' => $leave->id,
                'user_id' => $user->id,
                'status_id' => $statusId,
                'reason' => 'Soumission initiale (simulée)',
            ]);
        }

        return $leave->refresh();
    }

    /**
     * Test that a manager cannot mass validate a leave with imported status
     */
    public function test_manager_cannot_mass_validate_imported_leave()
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient();
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client);
        $users = $this->createUsers($profiles, $site);
        $leave = $this->createLeave($users['admin'], $statuses['imported']->id, $leaveType->id);
        $initialHistoryCount = History::where('leave_id', $leave->id)->count();

        $payload = [['action'=>'VALIDATED','leave_id'=>$leave->id,'reason'=>'test']];

        $response = $this
            ->actingAs($users['manager'], 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload);

        $response->assertStatus(200)
            ->assertJsonPath('data.0.message', __('warnings.ErrorLeaveStatusDontAllowModification'));

        $this->assertEquals('IMPORTED', $leave->fresh()->status->tag);

        $this->assertDatabaseCount('histories', $initialHistoryCount + History::where('leave_id', '!=', $leave->id)->count());
        $this->assertDatabaseMissing('histories', [
            'leave_id' => $leave->id,
            'user_id' => $users['manager']->id,
            'status_id' => $statuses['validated']->id,
        ]);
    }

    /**
     * Test that a manager can mass validate a leave with submitted status
     */
    public function test_manager_can_mass_validate_submitted_leave()
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient();
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client);
        $users = $this->createUsers($profiles, $site);
        $leave = $this->createLeave($users['admin'], $statuses['submitted']->id, $leaveType->id);

        $payload = [['action'=>'VALIDATED','leave_id'=>$leave->id]];

        $response = $this
            ->actingAs($users['manager'], 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload);

        $response->assertStatus(200)
            ->assertJsonPath('data.0.status_id', $statuses['validated']->id);

        $this->assertEquals('VALIDATED', $leave->fresh()->status->tag);

        $this->assertDatabaseHas('histories', [
            'leave_id' => $leave->id,
            'user_id' => $users['manager']->id,
            'status_id' => $statuses['validated']->id,
        ]);
    }

    /**
     * Test that an admin can transmit/approve cancellation for a leave with submitted_to_cancellation status
     */
    public function test_admin_can_transmit_submitted_to_cancellation_leave()
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient();
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client);
        $users = $this->createUsers($profiles, $site);

        $collab = User::factory()
            ->state([
                'profile_id' => $profiles['standard']->id,
                'site_id' => $site->id,
                'client_uuid' => $site->client->uuid,
            ])
            ->create();
        $collab->managers()->attach($users['manager']->id, ['level' => 1]);

        $leave = $this->createLeave($collab, $statuses['submitted_to_cancellation']->id, $leaveType->id);

        History::create([
            'leave_id' => $leave->id,
            'user_id' => $collab->id,
            'status_id' => $statuses['submitted_to_cancellation']->id,
            'reason' => 'Demande annulation',
        ]);
        $leave->refresh();


        $payload = [['action'=>'TRANSMITTED','leave_id'=>$leave->id]];

        $response = $this
            ->actingAs($users['admin'], 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload);

        $response->assertStatus(200);

        $this->assertEquals('CANCELED', $leave->fresh()->status->tag);

        $this->assertDatabaseHas('histories', [
            'leave_id' => $leave->id,
            'user_id' => $users['admin']->id,
            'status_id' => $statuses['canceled']->id,
        ]);
    }

    /**
     * Test that a manager can refuse cancellation for a leave with submitted_to_cancellation status
     */
    public function test_manager_can_refuse_submitted_to_cancellation_leave()
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient();
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client);
        $users = $this->createUsers($profiles, $site);
        $leave = $this->createLeave($users['admin'], $statuses['submitted_to_cancellation']->id, $leaveType->id);

        History::create([
            'leave_id' => $leave->id,
            'user_id' => $users['admin']->id,
            'status_id' => $statuses['submitted_to_cancellation']->id,
            'reason' => 'Demande annulation',
        ]);
        $leave->refresh();

        $payload = [['action'=>'REFUSED','leave_id'=>$leave->id]];

        $response = $this
            ->actingAs($users['manager'], 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload);

        $response->assertStatus(200);

        $this->assertEquals('SUBMITTED', $leave->fresh()->status->tag);

        $this->assertDatabaseHas('histories', [
            'leave_id' => $leave->id,
            'user_id' => $users['manager']->id,
            'status_id' => $statuses['refused']->id,
        ]);
    }
}
