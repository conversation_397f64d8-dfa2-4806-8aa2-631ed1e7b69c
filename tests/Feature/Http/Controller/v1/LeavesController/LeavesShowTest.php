<?php

namespace Tests\Feature\Http\Controller\v1\LeavesController;

use App\Models\Api\History;
use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\User;
use Tests\TestCase;

class LeavesShowTest extends TestCase {
	/**
	 * Teste si on récupère la bonne structure de donnée
	 * @return void
	 */
//	public function testShouldReturnOneLeaveType(): void {
//
//        $JWT = $this->getDummyJWT();
//        $id = Leave::where("user_id", "=" , User::first()->id)->first()->id;
//
//		$response = $this->get("/api/v1/leaves/".$id, ['Authorization' => $JWT]);
//
//		$response->assertResponseStatus(200);
//		$response->seeJsonStructure([
//			"message",
//			"data" => [
//                "id",
//                "user_id",
//                "status_id",
//                "leave_type_id",
//                "leave_type_sub_family_id",
//                "last_updater_id",
//                "start_date",
//                "end_date",
//                "duration",
//                "attachment_name",
//                "attachment_path",
//                "comment",
//                "comment",
//			]
//		]);
//	}
//
//    public function testShowLeaveWithHistoriesStatusTransmitted(): void {
//
//        $statusId = $this->getStatusId("TRANSMITTED");
//
//        $JWT = $this->getDummyJWT();
//
//        $leave = Leave::create([
//            "user_id" => User::first()->id,
//            "status_id" => $statusId,
//            "leave_type_id" => LeaveType::first()->id,
//            "start_date" => "2021-01-01 00:00:00",
//            "end_date" =>  "2021-01-02 00:00:00",
//            "attachment_name" => "text",
//            "attachment_path" => "text"
//        ]);
//
//        History::create([
//            "leave_id" => $leave->id,
//            "status_id" => $statusId,
//            "user_id" => User::first()->id,
//            "reason" => "reason",
//        ]);
//
//        $response = $this->get("/api/v1/leaves/".$leave->id."?withHistories=true", ['Authorization' => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(200);
//        $response->seeJsonStructure([
//            "message",
//            "data" => [
//                "id",
//                "user_id",
//                "status_id",
//                "leave_type_id",
//                "leave_type_sub_family_id",
//                "last_updater_id",
//                "start_date",
//                "end_date",
//                "duration",
//                "attachment_name",
//                "attachment_path",
//                "comment",
//                "comment",
//                "histories" => [
//                    "*" => [
//                        "reason",
//                        "created_at",
//                        "status" => [
//                            "tag",
//                            "name",
//                            "color",
//                            "class",
//                            "created_at",
//                        ],
//                        "user" => []
//                    ]
//                ],
//            ]
//        ]);
//
//        $lastStatusName = collect($response->response["data"]["histories"])->last()["status"]["name"];
//        $this->assertEquals("Transmis en paie", $lastStatusName);
//    }
//
//    public function testShowLeaveWithHistoriesStatusRefused(): void {
//
//        $statusId = $this->getStatusId("REFUSED");
//
//        $JWT = $this->getDummyJWT();
//
//        $leave = Leave::create([
//            "user_id" => User::first()->id,
//            "status_id" => $statusId,
//            "leave_type_id" => LeaveType::first()->id,
//            "start_date" => "2021-01-01 00:00:00",
//            "end_date" =>  "2021-01-02 00:00:00",
//            "attachment_name" => "text",
//            "attachment_path" => "text"
//        ]);
//
//        History::create([
//            "leave_id" => $leave->id,
//            "status_id" => $statusId,
//            "user_id" => User::first()->id,
//            "reason" => "reason",
//        ]);
//
//        $response = $this->get("/api/v1/leaves/".$leave->id."?withHistories=true", ['Authorization' => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(200);
//        $response->seeJsonStructure([
//            "message",
//            "data" => [
//                "id",
//                "user_id",
//                "status_id",
//                "leave_type_id",
//                "leave_type_sub_family_id",
//                "last_updater_id",
//                "start_date",
//                "end_date",
//                "duration",
//                "attachment_name",
//                "attachment_path",
//                "comment",
//                "comment",
//                "histories" => [
//                    "*" => [
//                        "reason",
//                        "created_at",
//                        "status" => [
//                            "tag",
//                            "name",
//                            "color",
//                            "class",
//                            "created_at",
//                        ],
//                        "user" => []
//                    ]
//                ],
//            ]
//        ]);
//
//        $lastStatusName = collect($response->response["data"]["histories"])->last()["status"]["name"];
//        $this->assertEquals("Refusée", $lastStatusName);
//    }
//
//    public function testShowLeaveWithHistoriesStatusCanceled(): void {
//
//        $statusId = $this->getStatusId("CANCELED");
//
//        $JWT = $this->getDummyJWT();
//
//        $leave = Leave::create([
//            "user_id" => User::first()->id,
//            "status_id" => $statusId,
//            "leave_type_id" => LeaveType::first()->id,
//            "start_date" => "2021-01-01 00:00:00",
//            "end_date" =>  "2021-01-02 00:00:00",
//            "attachment_name" => "text",
//            "attachment_path" => "text"
//        ]);
//
//        History::create([
//            "leave_id" => $leave->id,
//            "status_id" => $statusId,
//            "user_id" => User::first()->id,
//            "reason" => "reason",
//        ]);
//
//        $response = $this->get("/api/v1/leaves/".$leave->id."?withHistories=true", ['Authorization' => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(200);
//        $response->seeJsonStructure([
//            "message",
//            "data" => [
//                "id",
//                "user_id",
//                "status_id",
//                "leave_type_id",
//                "leave_type_sub_family_id",
//                "last_updater_id",
//                "start_date",
//                "end_date",
//                "duration",
//                "attachment_name",
//                "attachment_path",
//                "comment",
//                "comment",
//                "histories" => [
//                    "*" => [
//                        "reason",
//                        "created_at",
//                        "status" => [
//                            "tag",
//                            "name",
//                            "color",
//                            "class",
//                            "created_at",
//                        ],
//                        "user" => []
//                    ]
//                ],
//            ]
//        ]);
//
//        $lastStatusName = collect($response->response["data"]["histories"])->last()["status"]["name"];
//        $this->assertEquals("Annulé", $lastStatusName);
//    }
//
//    public function testShowLeaveWithHistoriesStatusValidated(): void {
//
//	    $statusId = $this->getStatusId("VALIDATED");
//
//        $JWT = $this->getDummyJWT();
//
//        $leave = Leave::create([
//            "user_id" => User::first()->id,
//            "status_id" => $statusId,
//            "leave_type_id" => LeaveType::first()->id,
//            "start_date" => "2021-01-01 00:00:00",
//            "end_date" =>  "2021-01-02 00:00:00",
//            "attachment_name" => "text",
//            "attachment_path" => "text"
//        ]);
//
//        History::create([
//            "leave_id" => $leave->id,
//            "status_id" => $statusId,
//            "user_id" => User::first()->id,
//            "reason" => "reason",
//        ]);
//
//        $response = $this->get("/api/v1/leaves/".$leave->id."?withHistories=true", ['Authorization' => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(200);
//        $response->seeJsonStructure([
//            "message",
//            "data" => [
//                "id",
//                "user_id",
//                "status_id",
//                "leave_type_id",
//                "leave_type_sub_family_id",
//                "last_updater_id",
//                "start_date",
//                "end_date",
//                "duration",
//                "attachment_name",
//                "attachment_path",
//                "comment",
//                "comment",
//                "histories" => [
//                    "*" => [
//                        "status" => [
//                            "tag",
//                            "name",
//                            "color",
//                        ],
//                        "user" => []
//                    ]
//                ],
//            ]
//        ]);
//
//        $lastStatus = collect($response->response["data"]["histories"])->last()["status"];
//        $this->assertEquals("WaitingPay", $lastStatus["name"]);
//        $this->assertEquals("AWAIT", $lastStatus["tag"]);
//    }
//
//    public function testShowLeaveWithHistoriesStatusSubmitted(): void {
//
//        $statusId = $this->getStatusId("SUBMITTED");
//
//        // un utilisateur qui à un manager
//        $user = User::whereHas('manager', function ($query) {
//            $query->whereIn("manager_id", User::all()->pluck("id"));
//        })->first();
//
//        $JWT = $this->getDummyJWT($user->id);
//
//        $leave = Leave::create([
//            "user_id" => $user->id,
//            "status_id" => $statusId,
//            "leave_type_id" => LeaveType::first()->id,
//            "start_date" => "2021-01-01 00:00:00",
//            "end_date" =>  "2021-01-02 00:00:00",
//            "attachment_name" => "text",
//            "attachment_path" => "text"
//        ]);
//
//        History::create([
//            "leave_id" => $leave->id,
//            "status_id" => $statusId,
//            "user_id" => $user->id,
//            "reason" => "reason",
//        ]);
//
//        $response = $this->get("/api/v1/leaves/".$leave->id."?withHistories=true", ['Authorization' => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(200);
//        $response->seeJsonStructure([
//            "message",
//            "data" => [
//                "id",
//                "user_id",
//                "status_id",
//                "leave_type_id",
//                "leave_type_sub_family_id",
//                "last_updater_id",
//                "start_date",
//                "end_date",
//                "duration",
//                "attachment_name",
//                "attachment_path",
//                "comment",
//                "comment",
//                "histories" => [
//                    "*" => [
//                        "status" => [
//                            "tag",
//                            "name",
//                            "color",
//                        ],
//                        "user" => [
//                            "firstname",
//                            "lastname",
//                        ]
//                    ]
//                ],
//            ]
//        ]);
//
//        $lastStatus = collect($response->response["data"]["histories"])->last()["status"];
//        $this->assertEquals("Waiting", $lastStatus["name"]);
//        $this->assertEquals("AWAIT", $lastStatus["tag"]);
//    }
//
//	/**
//	 * Test l'id du type de congés n'existe pas
//	 * @return void
//	 */
//	public function testShowIfIdNotExist(): void {
//
//        $JWT = $this->getDummyJWT();
//        $id = (Leave::all()->last()->id) +1;
//
//		$response = $this->get("/api/v1/leaves/" . $id, ['Authorization' => $JWT]);
//
//		$response->assertResponseStatus(404);
//		$response->seeJson([
//			"details" => "LeaveDontExist",
//		]);
//	}
//
//	/**
//	 * Test l'id du type de congés n'est pas un nombre
//	 * @return void
//	 */
//	public function testShowIfIdNotANumber(): void {
//
//        $JWT = $this->getDummyJWT();
//        $id = "azeezaeza";
//		$response = $this->get("/api/v1/leaves/" . $id, ['Authorization' => $JWT]);
//
//		$response->assertResponseStatus(500);
//	}
//
//	/**
//	 * Test l'id du type de congés est < 0
//	 * @return void
//	 */
//	public function testShowIfIdLessThan0(): void {
//
//        $JWT = $this->getDummyJWT();
//        $id = -582;
//		$response = $this->get("/api/v1/leaves/" . $id, ['Authorization' => $JWT]);
//
//		$response->assertResponseStatus(400);
//	}
}
