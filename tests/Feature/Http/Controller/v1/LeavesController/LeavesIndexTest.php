<?php

namespace Tests\Feature\Http\Controller\v1\LeavesController {

    use App\Http\Controllers\Api\v1\Leaves\LeavesController;
    use App\Models\Api\History;
    use App\Models\Api\Leave;
    use App\Models\Api\LeaveType;
    use App\Models\Api\Manager;
    use App\Models\Api\Profile;
    use App\Models\Api\Site;
    use App\Models\Api\Status;
    use App\Models\Api\User;
    use Carbon\Carbon;
    use Tests\TestCase;

    class LeavesIndexTest extends TestCase {
        /**
         * Teste si on récupère la bonne structure de donnée
         * @return void
         */
//        public function testShouldReturnAllLeaves(): void {
//
//            $JWT = $this->getDummyJWT();
//
//            $response = $this->get("/api/v1/leaves", ['Authorization' => $JWT]);
//
//            $response->assertResponseStatus(200);
//            $response->seeJsonStructure([
//                "message",
//                "data" => [
//                    "*" => [
//                        "id",
//                        "user_id",
//                        "status_id",
//                        "leave_type_id",
//                        "leave_type_sub_family_id",
//                        "last_updater_id",
//                        "start_date",
//                        "end_date",
//                        "duration",
//                        "attachment_name",
//                        "attachment_path",
//                        "comment",
//                        "comment",
//                    ]
//                ],
//                "current_page",
//                "next_page_url",
//                "last_page",
//                "total"
//            ]);
//        }
//
//        /**
//         * Teste si on récupère la bonne structure de donnée
//         * Utilisateur : Standard
//         * Parametre : leaveOwner = himself
//         * @return void
//         */
//        public function testShouldReturnAllLeavesStandardHimself(): void {
//            //Case User Standard
//            $JWT = $this->getDummyJWT(User::where('profile_id', '=', Profile::where('label', '=', 'STANDARD')->first()->id)->first()->id);
//
//            $response = $this->get("/api/v1/leaves?leaveOwner=himself", ['Authorization' => $JWT]);
//
//            $response->assertResponseStatus(200);
//            $response->seeJsonStructure([
//                "message",
//                "data" => [
//                    "*" => [
//                        "id",
//                        "user_id",
//                        "status_id",
//                        "leave_type_id",
//                        "leave_type_sub_family_id",
//                        "last_updater_id",
//                        "start_date",
//                        "end_date",
//                        "duration",
//                        "attachment_name",
//                        "attachment_path",
//                        "comment",
//                        "comment",
//                    ]
//                ],
//                "current_page",
//                "next_page_url",
//                "last_page",
//                "total"
//            ]);
//        }
//
//        /**
//         * Teste si on récupère la bonne structure de donnée
//         * Utilisateur : Standard
//         * Parametre : leaveOwner = validationToTreat
//         * @return void
//         */
//        public function testShouldReturnNoLeaveStandardValidationToTreat(): void {
//            //Case User Standard
//            $JWT = $this->getDummyJWT(User::where('profile_id', '=', Profile::where('label', '=', 'STANDARD')->first()->id)->first()->id);
//
//            $response = $this->get("/api/v1/leaves?leaveOwner=validationToTreat", ['Authorization' => $JWT]);
//
//            $response->assertResponseStatus(403);
//            $response->seeJson([
//                "message" => "ErrorGetLeave",
//                "details" => "NotManagerOrAdminProfil"
//            ]);
//        }
//
//        /**
//         * Teste si on récupère la bonne structure de donnée
//         * Utilisateur : Manager
//         * Parametre : leaveOwner = validationToTreat
//         * @return void
//         */
//        public function testShouldReturnAllLeavesManagerValidationToTreat(): void {
//            $submitted = Status::where('tag', '=', 'SUBMITTED')->first()->id;
//            //Case User Manager
//            $id = User::whereHas('leaves', function($q) use($submitted){
//                $q->where('current_validator_level', '=', 2)->where('status_id', '=', $submitted);
//            })
//                ->has('manager', '>', 1)->first()->manager()->wherePivot('level', '>', 1)->first()->id;
//            $JWT = $this->getDummyJWT($id);
//
//            $response = $this->get("/api/v1/leaves?leaveOwner=validationToTreat", ['Authorization' => $JWT]);
//
//            $response->assertResponseStatus(200);
//            $response->seeJson([
//                "status_id" => $submitted,
//                "last_updater_id" => $id
//            ]);
//            $response->seeJsonStructure([
//                "message",
//                "data" => [
//                    "*" => [
//                        "id",
//                        "user_id",
//                        "status_id",
//                        "leave_type_id",
//                        "leave_type_sub_family_id",
//                        "last_updater_id",
//                        "duration",
//                        "attachment_name",
//                        "attachment_path",
//                        "comment",
//                        "comment",
//                    ]
//                ],
//                "current_page",
//                "next_page_url",
//                "last_page",
//                "total"
//            ]);
//        }
//
//        /**
//         * Teste si on récupère la bonne structure de donnée
//         * Utilisateur : Manager
//         * Parametre : futureLeaves = true
//         * @return void
//         */
//        public function testShouldReturnAllLeavesManagerFutureLeaves(): void {
//            //Case User Manager
//            $id = User::has('manager')->first()->manager()->first()->id;
//            $JWT = $this->getDummyJWT($id);
//
//            // Create a Leave for User that have a end_date after now
//            Leave::create([
//                "user_id" => $id,
//                "status_id" => Status::first()->id,
//                "leave_type_id" => LeaveType::first()->id,
//                "start_date" => "2021-01-01 00:00:00",
//                "end_date" => Carbon::now()->addDays()->format('Y-m-d H:i:s'),
//                "attachment_name" => "text",
//                "attachment_path" => "text"
//            ]);
//
//            $response = $this->get("/api/v1/leaves?futureLeaves=1", ['Authorization' => $JWT]);
//
//            $response->assertResponseStatus(200);
//            $response->seeJsonStructure([
//                "message",
//                "data" => [
//                    "*" => [
//                        "id",
//                        "user_id",
//                        "status_id",
//                        "leave_type_id",
//                        "leave_type_sub_family_id",
//                        "last_updater_id",
//                        "start_date",
//                        "end_date",
//                        "duration",
//                        "attachment_name",
//                        "attachment_path",
//                        "comment",
//                        "comment",
//                    ]
//                ],
//                "current_page",
//                "next_page_url",
//                "last_page",
//                "total"
//            ]);
//            foreach ($response->response["data"] as $leave){
//                $this->assertTrue(Carbon::now()->lessThan(Carbon::parse($leave["end_date"])));
//            }
//        }
//
//        /**
//         * Teste si on récupère les bonnes données
//         * Utilisateur : Manager
//         * Parametre : start date & end date
//         * @return void
//         */
//        public function testShouldReturnAllLeavesManagerStartAndEndDate(): void {
//            //Case User Manager
//            $user = User::has('manager')->first()->manager()->first();
//            $JWT = $this->getDummyJWT($user->id);
//
//            $startDate = "2021-05-01";
//            $endDate = "2021-11-31";
//
//            $response = $this->get("/api/v1/leaves?startDate=".$startDate."&endDate=".$endDate."&leaveOwner=all", ['Authorization' => $JWT]);
//
//            // Assertions
//            $startDate = new Carbon($startDate);
//            $endDate = new Carbon($endDate);
//
//            foreach ($response->response["data"] as $leave) {
//                // les congés sont bien compris entre les deux dates demandées
//                $this->assertTrue($startDate->lessThan(Carbon::parse($leave["end_date"])));
//                $this->assertTrue($endDate->greaterThan(Carbon::parse($leave["start_date"])));
//                // les users des congés appartiennent bien au manager
//                $this->assertTrue(in_array(
//                    $leave["user_id"],
//                    Manager::where("manager_id", "=", $user->id)->get()->pluck("user_id")->toArray()
//                ));
//            }
//        }
//
//        /**
//         * Teste si on récupère les bonnes données
//         * Utilisateur : Manager
//         * Parametre : start date
//         * @return void
//         */
//        public function testShouldReturnAllLeavesManagerStartDate(): void {
//            //Case User Manager
//            $id = User::has('manager')->first()->manager()->first()->id;
//            $JWT = $this->getDummyJWT($id);
//
//            $startDate = "2021-02-24";
//            $response = $this->get("/api/v1/leaves?startDate=".$startDate, ['Authorization' => $JWT]);
//
//            $startDate = new Carbon($startDate);
//
//            foreach ($response->response["data"] as $leave){
//                $this->assertTrue($startDate->lessThan(Carbon::parse($leave["end_date"])));
//            }
//        }
//
//        /**
//         * Teste si on récupère les bonnes données
//         * Utilisateur : Manager
//         * Parametre : start date & end date
//         * @return void
//         */
//        public function testShouldReturnAllLeavesManagerEndDate(): void {
//            //Case User Manager
//            $id = User::has('manager')->first()->manager()->first()->id;
//            $JWT = $this->getDummyJWT($id);
//
//            $endDate = "2021-12-31";
//
//            $response = $this->get("/api/v1/leaves?endDate=".$endDate, ['Authorization' => $JWT]);
//
//            $endDate = new Carbon($endDate);
//
//            foreach ($response->response["data"] as $leave){
//                $this->assertTrue($endDate->greaterThan(Carbon::parse($leave["start_date"])));
//            }
//        }
//
//        /**
//         * Teste si on récupère la bonne structure de donnée
//         * Utilisateur : Standard
//         * Parametre : leaveOwner = validationTreated
//         * @return void
//         */
//        public function testShouldReturnNoLeaveStandardValidationTreated(): void {
//            //Case User Manager
//            $JWT = $this->getDummyJWT(User::where('profile_id', '=', Profile::where('label', '=', 'STANDARD')->first()->id)->first()->id);
//
//            $response = $this->get("/api/v1/leaves?leaveOwner=validationTreated", ['Authorization' => $JWT]);
//
//            $response->assertResponseStatus(403);
//            $response->seeJson([
//                "message" => "ErrorGetLeave",
//                "details" => "NotManagerOrAdminProfil"
//            ]);
//        }
//
//        /**
//         * Teste si on récupère la bonne structure de donnée
//         * Utilisateur : Manager
//         * Parametre : leaveOwner = validationTreated
//         * @return void
//         */
//        public function testShouldReturnAllLeavesManagerValidationTreated(): void {
//            $validated = Status::where('tag', '=', 'VALIDATED')->first()->id;
//            $submitted = Status::where('tag', '=', 'SUBMITTED')->first()->id;
//            $refused = Status::where('tag', '=', 'REFUSED')->first()->id;
//            $transmitted = Status::where('tag', '=', 'TRANSMITTED')->first()->id;
//            //Case User Manager and have history
//            $usersHaveManager = User::has('managers')->get();
//            $managersId = [];
//            for($i = 0; $i < count($usersHaveManager); $i++){
//                $managersId = array_merge($managersId, $usersHaveManager[$i]->manager()->get()->pluck('id')->toArray());
//            }
//            $id = History::whereIn('user_id', $managersId)->first()->user_id;
//            $JWT = $this->getDummyJWT($id);
//
//            $response = $this->get("/api/v1/leaves?leaveOwner=validationTreated", ['Authorization' => $JWT]);
//
//            $response->assertResponseStatus(200);
////        $response->seeJson([
////            "manager_id" => $id
////        ]);
//            foreach ($response->response['data'] as $leave) {
//                //Status is REFUSED or TRANSMITTED or VALIDATED
//                $this->assertTrue($leave['status_id'] == $validated || $leave['status_id'] == $refused || $leave['status_id'] == $transmitted);
//            }
//            $response->seeJsonStructure([
//                "message",
//                "data" => [
//                    "*" => [
//                        "id",
//                        "user_id",
//                        "status_id",
//                        "leave_type_id",
//                        "leave_type_sub_family_id",
//                        "last_updater_id",
//                        "start_date",
//                        "end_date",
//                        "duration",
//                        "attachment_name",
//                        "attachment_path",
//                        "comment",
//                        "comment",
//                    ]
//                ],
//                "current_page",
//                "next_page_url",
//                "last_page",
//                "total"
//            ]);
//        }
//
//        /**
//         * Teste si on récupère la bonne structure de donnée
//         * Utilisateur : Standard
//         * Parametre : leaveOwner = validationToTreatAndTreated
//         * @return void
//         */
//        public function testShouldReturnNoLeaveStandardValidationToTreatAndTreated(): void {
//            //Case User Manager
//            $JWT = $this->getDummyJWT(User::where('profile_id', '=', Profile::where('label', '=', 'STANDARD')->first()->id)->first()->id);
//
//            $response = $this->get("/api/v1/leaves?leaveOwner=validationToTreatAndTreated", ['Authorization' => $JWT]);
//
//            $response->assertResponseStatus(403);
//            $response->seeJson([
//                "message" => "ErrorGetLeave",
//                "details" => "NotManagerOrAdminProfil"
//            ]);
//        }
//
//        /**
//         * Teste si on récupère la bonne structure de donnée
//         * Utilisateur : Manager
//         * Parametre : leaveOwner = validationToTreatAndTreated
//         * @return void
//         */
//        public function testShouldReturnAllLeavesManagerToTreatAndTreated(): void {
//            //Case User Manager
//            $id = Manager::first()->manager_id;
//            $JWT = $this->getDummyJWT($id);
//
//            $response = $this->get("/api/v1/leaves?leaveOwner=validationToTreatAndTreated", ['Authorization' => $JWT]);
//
//            $response->assertResponseStatus(200);
////        $response->seeJson([
////            "manager_id" => $id,
////            "status_id" => 7
////        ]);
//            $response->seeJsonStructure([
//                "message",
//                "data" => [
//                    "*" => [
//                        "id",
//                        "user_id",
//                        "status_id",
//                        "leave_type_id",
//                        "leave_type_sub_family_id",
//                        "last_updater_id",
//                        "start_date",
//                        "end_date",
//                        "duration",
//                        "attachment_name",
//                        "attachment_path",
//                        "comment",
//                        "comment",
//                    ]
//                ]
//            ]);
//        }
//
//        /**
//         * Teste si on récupère la bonne structure de donnée
//         * Utilisateur : Standard
//         * Parametre : leaveOwner = payTreated
//         * @return void
//         */
//        public function testShouldReturnNoLeaveStandardPayTreated(): void {
//            //Case User Manager
//            $JWT = $this->getDummyJWT(User::where('profile_id', '=', Profile::where('label', '=', 'STANDARD')->first()->id)->first()->id);
//
//            $response = $this->get("/api/v1/leaves?leaveOwner=payTreated", ['Authorization' => $JWT]);
//
//            $response->assertResponseStatus(403);
//            $response->seeJson([
//                "message" => "ErrorGetLeave",
//                "details" => "NotManagerOrAdminProfil"
//            ]);
//        }
//
//        /**
//         * Teste si on récupère la bonne structure de donnée
//         * Utilisateur : Manager
//         * Parametre : leaveOwner = payTreated
//         * @return void
//         */
//        public function testShouldReturnAllLeavesManagerPayTreated(): void {
//            $validated = Status::where('tag', '=', 'VALIDATED')->first()->id;
//            $submitted = Status::where('tag', '=', 'SUBMITTED')->first()->id;
//            //Case User Manager
//            $id = Manager::first()->manager_id;
//            $JWT = $this->getDummyJWT($id);
//
//            $response = $this->get("/api/v1/leaves?leaveOwner=payTreated", ['Authorization' => $JWT]);
//
//            $response->assertResponseStatus(403);
//            $response->seeJson([
//                "message" => "ErrorGetLeave",
//                "details" => "MustBeAdmin"
//            ]);
//        }
//
//        /**
//         * Teste si on récupère la bonne structure de donnée
//         * Utilisateur : Standard
//         * Parametre : leaveOwner = payTransmitted
//         * @return void
//         */
//        public function testShouldReturnNoLeaveStandardPayTransmitted(): void {
//            //Case User Manager
//            $JWT = $this->getDummyJWT(User::where('profile_id', '=', Profile::where('label', '=', 'STANDARD')->first()->id)->first()->id);
//
//            $response = $this->get("/api/v1/leaves?leaveOwner=payTransmitted", ['Authorization' => $JWT]);
//
//            $response->assertResponseStatus(403);
//            $response->seeJson([
//                "message" => "ErrorGetLeave",
//                "details" => "NotManagerOrAdminProfil"
//            ]);
//        }
//
//        /**
//         * Teste si on récupère la bonne structure de donnée
//         * Utilisateur : Manager
//         * Parametre : leaveOwner = payTransmitted
//         * @return void
//         */
//        public function testShouldReturnAllLeavesManagerPayTransmitted(): void {
//            $transmitted = Status::where('tag', '=', 'TRANSMITTED')->first()->id;
//            //Case User Manager
//            $id = Manager::first()->manager_id;
//            $JWT = $this->getDummyJWT($id);
//
//            $response = $this->get("/api/v1/leaves?leaveOwner=payTransmitted", ['Authorization' => $JWT]);
//
//            $response->assertResponseStatus(403);
//            $response->seeJson([
//                "message" => "ErrorGetLeave",
//                "details" => "MustBeAdmin"
//            ]);
//        }
//
//        /**
//         * Teste si on récupère la bonne structure de donnée
//         * @return void
//         */
//        public function testShouldReturnAllLeavesUserSearch(): void {
//            $user = User::first();
//
//            $JWT = $this->getDummyJWT($user->id);
//
//            $response = $this->get("/api/v1/leaves?userSearch=".$user->firstname.",".$user->lastname, ['Authorization' => $JWT]);
//
//            $response->assertResponseStatus(200);
//            $response->seeJson([
//                "id" => $user->id
//            ]);
//        }
//
//        /**
//         * Teste si on récupère la bonne structure de donnée
//         * @return void
//         */
//        public function testShouldReturnAllLeavesUserSearchBis(): void {
//            $user = User::first();
//
//            $JWT = $this->getDummyJWT($user->id);
//
//            $response = $this->get("/api/v1/leaves?userSearch=".$user->lastname.",".$user->firstname, ['Authorization' => $JWT]);
//
//            $response->assertResponseStatus(200);
//            $response->seeJson([
//                "id" => $user->id
//            ]);
//        }
//
//        /**
//         * Teste si on récupère la bonne structure de donnée
//         * @return void
//         */
//        public function testShouldReturnNoLeaveUserSearch(): void {
//            $user = User::first();
//
//            $JWT = $this->getDummyJWT($user->id);
//
//            $response = $this->get("/api/v1/leaves?userSearch=".$user->lastname."s,".$user->firstname, ['Authorization' => $JWT]);
//
//            $response->assertResponseStatus(200);
//            $response->seeJson([
//                "data" => []
//            ]);
//        }
//
//        /**
//         * Teste si on récupère la bonne structure de donnée
//         * @return void
//         */
//        public function testShouldReturnAllLeavesSite(): void {
//            $user = User::first();
//
//            $JWT = $this->getDummyJWT($user->id);
//
//            $response = $this->get("/api/v1/leaves?site=".$user->site_id, ['Authorization' => $JWT]);
//
//            $response->assertResponseStatus(200);
//            $response->seeJson([
//                "id" => $user->id
//            ]);
//        }
//
//        /**
//         * Teste si on récupère la bonne structure de donnée
//         * @return void
//         */
//        public function testShouldReturnNoLeaveSite(): void {
//            $user = User::first();
//
//            $JWT = $this->getDummyJWT($user->id);
//
//            $response = $this->get("/api/v1/leaves?site=".(Site::where('id', '!=', $user->site_id)->first()->id), ['Authorization' => $JWT]);
//
//            $response->assertResponseStatus(200);
//            $response->seeJson([
//                "data" => []
//            ]);
//        }
//
//        /**
//         * Teste si on récupère la bonne structure de donnée
//         * @return void
//         */
//        public function testShouldReturnNoLeaveSiteNotExist(): void {
//            $JWT = $this->getDummyJWT();
//
//            $siteId = (Site::orderBy('id', 'desc')->first()->id)+1;
//
//            $response = $this->get("/api/v1/leaves?site=".$siteId, ['Authorization' => $JWT]);
//
//            $response->assertResponseStatus(404);
//            $response->seeJson([
//                "details" => "SiteNotFound",
//                "message" => "ErrorGetLeave"
//            ]);
//        }
//
//        public function testIndexLeaveWithLeaveTypeSoftDeleted(): void {
//
//            $id = User::has('manager')->first()->manager()->first()->id;
//            $JWT = $this->getDummyJWT($id);
//
//            LeaveType::query()->delete();
//
//            $response = $this->get("/api/v1/leaves?leaveOwner=validationToTreat&withLeaveTypeSubFamily=1", ['Authorization' => $JWT]);
//
//            $response->assertResponseOk();
//        }
//
//        public function testIndexLeaveCanCancel(): void {
//
//            $id = User::has('manager')->first()->manager()->first()->id;
//            $JWT = $this->getDummyJWT($id);
//
//            $response = $this->get("/api/v1/leaves?leaveOwner=canCancel", ['Authorization' => $JWT]);
//
//            // verifie que tous les congés renvoyés peuvent être annulés
//            $response->assertResponseStatus(200);
//            foreach ($response->response["data"] as $leave){
//                $leave = new Leave($leave);
//                $leaveController = new LeavesController();
//                $this->assertTrue($leaveController->verifyIfUserCanCancelLeave($leave)["result"]);
//            }
//
//            // verifie que tous les autres congés ne peuvent pas être annulés
//            $leaveCanCancel = Leave::whereNotIn("id", collect($response->response["data"])->pluck("id"));
//            foreach ($leaveCanCancel as $leave){
//                $leaveController = new LeavesController();
//                $this->assertFalse($leaveController->verifyIfUserCanCancelLeave($leave)["result"]);
//            }
//        }
    }
}
