<?php

namespace Tests\Feature\Http\Controller\v1\LeavesController;

use App\Models\Api\Client;
use App\Models\Api\Day;
use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\Profile;
use App\Models\Api\Site;
use App\Models\Api\Status;
use App\Models\Api\User;
use App\Models\Api\UserLeaveCount;
use Tests\TestCase;

class LeavesRefusedTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        $this->withoutMiddleware();
    }

    private function getStatuses()
    {
        return [
            'submitted' => Status::firstOrCreate(['tag' => 'SUBMITTED'], ['name' => 'Soumis à validation']),
            'validated' => Status::firstOrCreate(['tag' => 'VALIDATED'], ['name' => 'Validé']),
            'refused' => Status::firstOrCreate(['tag' => 'REFUSED'], ['name' => 'Refusée']),
        ];
    }

    private function createProfiles()
    {
        return [
            'admin' => Profile::firstOrCreate(['label' => 'ADMINISTRATEUR']),
            'manager' => Profile::firstOrCreate(['label' => 'ADMINISTRATEURMANAGER']),
            'standard' => Profile::firstOrCreate(['label' => 'STANDARD'])
        ];
    }

    private function createClient($scheme = 'VERTICAL', $levels = 1)
    {
        $client = Client::factory()->create([
            'validation_scheme' => $scheme,
            'number_managers_can_validate' => $levels,
        ]);
        $day = Day::firstOrCreate(['day_name' => 'MONDAY']);
        if (!$client->days()->where('day_id', $day->id)->exists()) {
            $client->days()->attach($day->id);
        }
        return $client;
    }

    private function createSite($client)
    {
        return Site::factory()->create(['client_id' => $client->id]);
    }

    private function createUsers($profiles, $site)
    {
        $admin = User::factory()->create([
            'profile_id' => $profiles['admin']->id,
            'site_id' => $site->id,
        ]);

        $manager = User::factory()->create([
            'profile_id' => $profiles['manager']->id,
            'site_id' => $site->id,
        ]);

        $collaborator = User::factory()->create([
            'profile_id' => $profiles['standard']->id,
            'site_id' => $site->id,
        ]);

        $collaborator->managers()->attach($manager->id, ['level' => 1]);

        return [
            'admin' => $admin,
            'manager' => $manager,
            'collaborator' => $collaborator
        ];
    }

    // *** Fin de la méthode createUsers ***

    private function createLeaveType($client)
    {
        return LeaveType::factory()->create([
            'client_id' => $client->id,
            'is_active' => true,
            'is_half_day' => true,
        ]);
    }

    private function createLeave($user, $statusId, $leaveTypeId)
    {
        $startDate = now()->addDays(5)->startOfDay();
        $endDate = now()->addDays(5)->endOfDay();

        return Leave::factory()->create([
            'user_id' => $user->id,
            'status_id' => $statusId,
            'leave_type_id' => $leaveTypeId,
            'duration' => 1,
            'current_validator_level' => 1,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);
    }

    private function createUserLeaveCount($userId, $leaveTypeId)
    {
        UserLeaveCount::updateOrCreate(
            ['user_id' => $userId, 'leave_type_id' => $leaveTypeId, 'is_last_year' => false],
            ['balance' => 10, 'acquired' => 10, 'taken' => 0]
        );
        UserLeaveCount::updateOrCreate(
            ['user_id' => $userId, 'leave_type_id' => $leaveTypeId, 'is_last_year' => true],
            ['balance' => 5, 'acquired' => 5, 'taken' => 0]
        );
    }

    /**
     * Test qu'un manager peut refuser un congé soumis par un collaborateur géré.
     */
    public function test_manager_can_refuse_leave()
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 1);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client);

        $users = $this->createUsers($profiles, $site);
        $collaborator = $users['collaborator'];
        $manager = $users['manager'];

        $this->createUserLeaveCount($collaborator->id, $leaveType->id);

        $leave = $this->createLeave($collaborator, $statuses['submitted']->id, $leaveType->id);
        $this->assertEquals('SUBMITTED', $leave->status->tag);

        $payload = [[
            'action' => 'REFUSED',
            'leave_id' => $leave->id,
            'reason' => 'Raison du refus de test'
        ]];
        $response = $this
            ->actingAs($manager, 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload);

        $response->assertStatus(200);
        $leave->refresh();
        $this->assertEquals('REFUSED', $leave->status->tag);
        $this->assertEquals(2, $leave->current_validator_level);
    }

    /**
     * Test qu'un manager de niveau N peut refuser un congé même si les managers N-1 ont validé.
     */
    public function test_higher_level_manager_can_refuse_leave_after_lower_validation()
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 3);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client);

        $collaborator = User::factory()->create([
            'profile_id' => $profiles['standard']->id,
            'site_id' => $site->id,
        ]);
        $manager1 = User::factory()->create([
            'profile_id' => $profiles['manager']->id,
            'site_id' => $site->id,
        ]);
        $manager2 = User::factory()->create([
            'profile_id' => $profiles['manager']->id,
            'site_id' => $site->id,
        ]);
        $manager3 = User::factory()->create([
            'profile_id' => $profiles['manager']->id,
            'site_id' => $site->id,
        ]);

        $collaborator->managers()->sync([
            $manager1->id => ['level' => 1],
            $manager2->id => ['level' => 2],
            $manager3->id => ['level' => 3],
        ]);

        $this->createUserLeaveCount($collaborator->id, $leaveType->id);
        $leave = $this->createLeave($collaborator, $statuses['submitted']->id, $leaveType->id);

        $payload1 = [['action' => 'VALIDATED', 'leave_id' => $leave->id]];
        $response1 = $this
            ->actingAs($manager1, 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload1);
        $response1->assertStatus(200);

        $leave->refresh();
        $this->assertEquals(2, $leave->current_validator_level);
        $this->assertEquals('SUBMITTED', $leave->status->tag);

        $payload2 = [[
            'action' => 'REFUSED',
            'leave_id' => $leave->id,
            'reason' => 'Refus au niveau 2'
        ]];
        $response2 = $this
            ->actingAs($manager2, 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload2);

        $response2->assertStatus(200);
        $leave->refresh();
        $this->assertEquals('REFUSED', $leave->status->tag);
        $this->assertEquals(4, $leave->current_validator_level);
    }
}
