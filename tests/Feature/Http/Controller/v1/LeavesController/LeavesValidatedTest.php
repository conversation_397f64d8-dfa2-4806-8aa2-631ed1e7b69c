<?php

namespace Tests\Feature\Http\Controller\v1\LeavesController;

use App\Models\Api\Client;
use App\Models\Api\Day;
use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\Profile;
use App\Models\Api\Site;
use App\Models\Api\Status;
use App\Models\Api\User;
use App\Models\Api\UserLeaveCount;
use App\Http\Middleware\CheckProfile;
use Tests\TestCase;

class LeavesValidatedTest extends TestCase
{

    protected function setUp(): void
    {
        parent::setUp();
        $this->withoutMiddleware(CheckProfile::class);
    }

    private function getStatuses(): array
    {
        return [
            'submitted' => Status::firstOrCreate(['tag' => 'SUBMITTED'], ['name' => 'Soumis à validation']),
            'validated' => Status::firstOrCreate(['tag' => 'VALIDATED'], ['name' => 'Validé']),
        ];
    }

    private function createProfiles(): array
    {
        return [
            'admin' => Profile::firstOrCreate(['label' => 'ADMINISTRATEUR']),
            'manager' => Profile::firstOrCreate(['label' => 'ADMINISTRATEURMANAGER']),
            'standard' => Profile::firstOrCreate(['label' => 'STANDARD'])
        ];
    }

    private function createClient(string $scheme = 'VERTICAL', int $levels = 1): Client
    {
        $client = Client::factory()->create([
            'validation_scheme' => $scheme,
            'number_managers_can_validate' => $levels,
        ]);
        $this->assertNotNull($client->uuid);

        $day = Day::firstOrCreate(['day_name' => 'MONDAY']);
        $client->days()->syncWithoutDetaching([$day->id]);

        return $client;
    }

    private function createSite(Client $client): Site
    {
        return Site::factory()->create(['client_id' => $client->id]);
    }

    private function createLeaveType(Client $client): LeaveType
    {
        return LeaveType::factory()->create([
            'client_id' => $client->id,
            'is_active' => true,
            'is_half_day' => true,
            'needs_count' => true,
            'can_exceed' => false,
        ]);
    }

    private function createUser(array $attributes = []): User
    {
        if (isset($attributes['site_id']) && !isset($attributes['client_uuid'])) {
            $site = Site::find($attributes['site_id']);
            if ($site?->client) {
                $attributes['client_uuid'] = $site->client->uuid;
            }
        } elseif (!isset($attributes['site_id'])) {
            $client = Client::factory()->create();
            $site = Site::factory()->create(['client_id' => $client->id]);
            $attributes['site_id'] = $site->id;
            $attributes['client_uuid'] = $client->uuid;
        }
        $user = User::factory()->state($attributes)->create();
        $this->assertNotNull($user->uuid);
        return $user;
    }


    private function createLeave(User $user, int $statusId, int $leaveTypeId, int $validatorLevel = 1): Leave
    {
        $startDate = now()->addDays(15)->startOfDay()->setHour(9);
        $endDate = now()->addDays(15)->startOfDay()->setHour(18);

        $leave = Leave::factory()->create([
            'user_id' => $user->id,
            'creator_id' => $user->id,
            'status_id' => $statusId,
            'leave_type_id' => $leaveTypeId,
            'duration' => 1,
            'current_validator_level' => $validatorLevel,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'n' => 1,
            'n1' => 0,
        ]);
        $this->assertDatabaseHas('leaves', ['id' => $leave->id]);
        return $leave;
    }

    private function createUserLeaveCount(int $userId, int $leaveTypeId): void
    {
        UserLeaveCount::updateOrCreate(
            ['user_id' => $userId, 'leave_type_id' => $leaveTypeId, 'is_last_year' => false],
            ['balance' => 10, 'acquired' => 10, 'taken' => 0]
        );
        UserLeaveCount::updateOrCreate(
            ['user_id' => $userId, 'leave_type_id' => $leaveTypeId, 'is_last_year' => true],
            ['balance' => 5, 'acquired' => 5, 'taken' => 0]
        );
    }

    /**
     * Test that a manager can validate a submitted leave in a single-level validation scheme.
     * The leave should become VALIDATED and the validator level should increase.
     */
    public function test_manager_can_validate_leave_single_level(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 1);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client);

        $collaborator = $this->createUser(['profile_id' => $profiles['standard']->id, 'site_id' => $site->id]);
        $manager = $this->createUser(['profile_id' => $profiles['manager']->id, 'site_id' => $site->id]);

        $collaborator->managers()->attach($manager->id, ['level' => 1]);
        $this->createUserLeaveCount($collaborator->id, $leaveType->id);

        $leave = $this->createLeave($collaborator, $statuses['submitted']->id, $leaveType->id, 1);

        $this->assertNotNull(Leave::find($leave->id));
        $this->assertEquals(1, $leave->current_validator_level);
        $this->assertEquals('SUBMITTED', $leave->status->tag);

        $payload = [['action' => 'VALIDATED', 'leave_id' => $leave->id]];

        $this->assertNotNull($manager->uuid);

        $response = $this
            ->actingAs($manager, 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload);

        $response->assertStatus(200);
        $leave->refresh();
        $this->assertEquals('VALIDATED', $leave->status->tag);
        $this->assertEquals(2, $leave->current_validator_level);
    }

    /**
     * Test the first validation step in a hierarchical scheme (3 levels).
     * Level 1 manager validates. Leave status should remain SUBMITTED, and level should advance to 2.
     */
    public function test_hierarchical_validation_step1_manager1_validates(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 3);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client);

        $collaborator = $this->createUser(['profile_id' => $profiles['standard']->id, 'site_id' => $site->id]);
        $manager1 = $this->createUser(['profile_id' => $profiles['manager']->id, 'site_id' => $site->id]);
        $manager2 = $this->createUser(['profile_id' => $profiles['manager']->id, 'site_id' => $site->id]);
        $manager3 = $this->createUser(['profile_id' => $profiles['manager']->id, 'site_id' => $site->id]);

        $collaborator->managers()->sync([$manager1->id => ['level' => 1], $manager2->id => ['level' => 2], $manager3->id => ['level' => 3]]);
        $this->createUserLeaveCount($collaborator->id, $leaveType->id);

        $leave = $this->createLeave($collaborator, $statuses['submitted']->id, $leaveType->id, 1);

        $this->assertNotNull(Leave::find($leave->id));
        $this->assertEquals(1, $leave->current_validator_level);
        $this->assertEquals('SUBMITTED', $leave->status->tag);

        $payload = [['action' => 'VALIDATED', 'leave_id' => $leave->id]];

        $this->assertNotNull($manager1->uuid);

        $response = $this
            ->actingAs($manager1, 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload);

        $response->assertStatus(200);
        $leave->refresh();
        $this->assertEquals(2, $leave->current_validator_level);
        $this->assertEquals('SUBMITTED', $leave->status->tag);
    }

    /**
     * Test the second validation step in a hierarchical scheme (3 levels).
     * Level 2 manager validates. Leave status should remain SUBMITTED, and level should advance to 3.
     */
    public function test_hierarchical_validation_step2_manager2_validates(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 3);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client);

        $collaborator = $this->createUser(['profile_id' => $profiles['standard']->id, 'site_id' => $site->id]);
        $manager1 = $this->createUser(['profile_id' => $profiles['manager']->id, 'site_id' => $site->id]);
        $manager2 = $this->createUser(['profile_id' => $profiles['manager']->id, 'site_id' => $site->id]);
        $manager3 = $this->createUser(['profile_id' => $profiles['manager']->id, 'site_id' => $site->id]);

        $collaborator->managers()->sync([$manager1->id => ['level' => 1], $manager2->id => ['level' => 2], $manager3->id => ['level' => 3]]);
        $this->createUserLeaveCount($collaborator->id, $leaveType->id);

        $leave = $this->createLeave($collaborator, $statuses['submitted']->id, $leaveType->id, 1);
        $leave->update(['current_validator_level' => 2]);

        $this->assertNotNull(Leave::find($leave->id));
        $this->assertEquals(2, $leave->current_validator_level);
        $this->assertEquals('SUBMITTED', $leave->status->tag);

        $payload = [['action' => 'VALIDATED', 'leave_id' => $leave->id]];

        $this->assertNotNull($manager2->uuid);

        $response = $this
            ->actingAs($manager2, 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload);

        $response->assertStatus(200);
        $leave->refresh();
        $this->assertEquals(3, $leave->current_validator_level);
        $this->assertEquals('SUBMITTED', $leave->status->tag);
    }

    /**
     * Test the third and final validation step in a hierarchical scheme (3 levels).
     * Level 3 manager validates. Leave status should become VALIDATED, and level should increase.
     */
    public function test_hierarchical_validation_step3_manager3_validates(): void
    {
        $statuses = $this->getStatuses();
        $profiles = $this->createProfiles();
        $client = $this->createClient('VERTICAL', 3);
        $site = $this->createSite($client);
        $leaveType = $this->createLeaveType($client);

        $collaborator = $this->createUser(['profile_id' => $profiles['standard']->id, 'site_id' => $site->id]);
        $manager1 = $this->createUser(['profile_id' => $profiles['manager']->id, 'site_id' => $site->id]);
        $manager2 = $this->createUser(['profile_id' => $profiles['manager']->id, 'site_id' => $site->id]);
        $manager3 = $this->createUser(['profile_id' => $profiles['manager']->id, 'site_id' => $site->id]);

        $collaborator->managers()->sync([$manager1->id => ['level' => 1], $manager2->id => ['level' => 2], $manager3->id => ['level' => 3]]);
        $this->createUserLeaveCount($collaborator->id, $leaveType->id);

        $leave = $this->createLeave($collaborator, $statuses['submitted']->id, $leaveType->id, 1);
        $leave->update(['current_validator_level' => 3]);

        $this->assertNotNull(Leave::find($leave->id));
        $this->assertEquals(3, $leave->current_validator_level);
        $this->assertEquals('SUBMITTED', $leave->status->tag);

        $payload = [['action' => 'VALIDATED', 'leave_id' => $leave->id]];

        $this->assertNotNull($manager3->uuid);

        $response = $this
            ->actingAs($manager3, 'jwt')
            ->putJson('/api/v1/leaves/mass-validation', $payload);

        $response->assertStatus(200);
        $leave->refresh();
        $this->assertEquals(4, $leave->current_validator_level);
        $this->assertEquals('VALIDATED', $leave->status->tag);
    }
}
