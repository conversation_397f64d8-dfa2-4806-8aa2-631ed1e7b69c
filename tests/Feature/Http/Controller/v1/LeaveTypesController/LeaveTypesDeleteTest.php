<?php

namespace Tests\Feature\Http\Controller\v1\LeaveTypesController;

use App\Models\Api\Client;
use App\Models\Api\LeaveType;
use App\Models\Api\User;
use Tests\TestCase;

class LeaveTypesDeleteTest extends TestCase {
	/**
	 * Teste si le type de congés est supprimé
	 * @return void
	 */
//	public function testDeleteIfDeleted(): void {
//
//        $JWT = $this->getDummyJWT();
//        $id = LeaveType::where("client_id", "=", User::first()->site->client_id)->first()->id;
//
//		$response = $this->delete("/api/v1/leave-types/". $id, [], ['Authorization' => $JWT]);
//
//		$response->assertResponseStatus(200);
//		$response->seeJsonEquals([
//			"message" => "SuccessDeleteLeaveType",
//			"data" => []
//		]);
//	}
//
//	/**
//	 * Test l'id du type de congés n'existe pas
//	 * @return void
//	 */
//	public function testDeleteIfIdNotExist(): void {
//		$id = (LeaveType::orderBy('id', 'desc')->first()->id)+1;
//		$response = $this->delete("/api/v1/leave-types/" . $id);
//
//		$response->assertResponseStatus(500);
//		$response->seeJsonEquals([
//			"details" => "ErrorGetLeaveTypeWithId ".$id,
//			"message" => "ErrorDeleteLeaveType"
//		]);
//	}
//
//	/**
//	 * Test l'id du type de congés n'est pas un nombre
//	 * @return void
//	 */
//	public function testDeleteIfIdNotANumber(): void {
//		$id = "azeezaeza";
//		$response = $this->delete("/api/v1/leave-types/" . $id);
//
//		$response->assertResponseStatus(500);
//	}
//
//	/**
//	 * Test l'id du type de congés est < 0
//	 * @return void
//	 */
//	public function testDeleteIfIdLessThan0(): void {
//		$id = -582;
//		$response = $this->delete("/api/v1/leave-types/" . $id);
//
//		$response->assertResponseStatus(400);
//	}
//
//	/**
//	 * Test si le type de congés ne m'appartient pas
//	 * @return void
//	 */
//	public function testDeleteIfNotMyLeaveType(): void {
//		$id = LeaveType::where('client_id','!=',Client::first()->id)->first()->id;
//	    $response = $this->delete("/api/v1/leave-types/".$id);
//
//		$response->assertResponseStatus(500);
//		$response->seeJsonEquals([
//			"details" => "ErrorDontOwnLeaveTypeWithId ".$id,
//			"message" => "ErrorDontOwnLeaveType"
//		]);
//	}
}
