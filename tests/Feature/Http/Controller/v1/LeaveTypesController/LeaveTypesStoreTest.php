<?php

namespace Tests\Feature\Http\Controller\v1\LeaveTypesController;

use App\Models\Api\LeaveType;
use App\Models\Api\User;
use Tests\TestCase;

class LeaveTypesStoreTest extends TestCase {
	/**
	 * Teste si le type de congés est enregistré
	 * @return void
	 */
//	public function testStoreLeaveType(): void{
//		$data = [
//			"name" => "Congés payés ".((LeaveType::orderBy('id', 'desc')->first()->id)+1),
//            "default_leave_value" => 10,
//			"is_active" => 1,
//			"is_deletable" => 1,
//			"is_half_day" => 0,
//			"is_attachment_required" => 0,
//			"is_ignore_by_export" => 0,
//			"is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 0,
//            "color" => "#000000",
//            "order_appearance" => 0
//		];
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT();
//
//		$response = $this->post("/api/v1/leave-types", $data, ['Authorization' => $JWT]);
//
//		$response->assertResponseStatus(200);
//		$response->seeInDatabase("leave_types", $data);
//		$response->seeInDatabase("user_leave_counts", [
//            "user_id" => User::first()->id,
//            "leave_type_id" => LeaveType::all()->last()->id,
//            "is_last_year" => 0,
//        ]);
//		$response->seeInDatabase("user_leave_counts", [
//            "user_id" => User::first()->id,
//            "leave_type_id" => LeaveType::all()->last()->id,
//            "is_last_year" => 1,
//        ]);
//	}
//
//	/**
//	 * Teste si le type de congés existe déjà
//	 * @return void
//	 */
//	public function testStoreItemExist(): void {
//		$data = [
//            "name" => LeaveType::first()->name,
//            "default_leave_value" => 10,
//            "is_active" => 1,
//            "is_deletable" => 1,
//            "is_half_day" => 0,
//            "is_attachment_required" => 0,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 0,
//            "color" => "#000000",
//            "order_appearance" => 0
//		];
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT();
//
//        $response = $this->post("/api/v1/leave-types", $data, ["Authorization" => $JWT]);
//
//		$response->assertResponseStatus(400);
//		$response->seeJsonEquals([
//			"details" => "LeaveTypeAlreadyExistWithName ".$data["name"],
//			"message" => "LeaveTypeAlreadyExist"
//		]);
//	}
}
