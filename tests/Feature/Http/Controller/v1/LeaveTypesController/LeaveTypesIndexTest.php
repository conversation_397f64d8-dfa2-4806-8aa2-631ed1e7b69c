<?php

namespace Tests\Feature\Http\Controller\v1\LeaveTypesController;

use App\Models\Api\Profile;
use App\Models\Api\User;
use Tests\TestCase;

class LeaveTypesIndexTest extends TestCase {
	/**
	 * Teste si on récupère la bonne structure de donnée
	 * @return void
	 */
//	public function testShouldReturnAllLeaveTypes(): void {
//        $JWT = $this->getDummyJWT();
//		$response = $this->get("/api/v1/leave-types", ['Authorization' => $JWT]);
//
//		$response->assertResponseStatus(200);
//		$this->seeJsonStructure([
//			"message",
//			"data" => [
//				"*" => [
//					"id",
//					"client_id",
//					"name",
//                    "default_leave_value",
//                    "is_active",
//                    "is_deletable",
//                    "is_half_day",
//                    "is_attachment_required",
//                    "is_ignore_by_export",
//                    "is_monthly",
//                    "start_date",
//                    "end_date",
//				]
//			],
//			"current_page",
//			"next_page_url",
//			"last_page",
//			"total"
//		]);
//	}
//
//    public function testShouldReturnAllLeaveTypesWithSubFamilies(): void {
//        $JWT = $this->getDummyJWT();
//        $response = $this->get("/api/v1/leave-types?withLeaveTypeSubFamilies=1", ['Authorization' => $JWT]);
//
//        $response->assertResponseStatus(200);
//        $response->seeJsonStructure([
//            "message",
//            "data" => [
//                "*" => [
//                    "id",
//                    "client_id",
//                    "name",
//                    "default_leave_value",
//                    "is_active",
//                    "is_deletable",
//                    "is_half_day",
//                    "is_attachment_required",
//                    "is_ignore_by_export",
//                    "is_monthly",
//                    "start_date",
//                    "end_date",
//                    "leave_type_sub_families" => [
//                        "*" => [
//                            "id",
//                            "leave_type_id",
//                            "name",
//                            "value",
//                            "deleted_at",
//                            "created_at",
//                            "updated_at"
//                        ]
//                    ]
//                ]
//            ],
//            "current_page",
//            "next_page_url",
//            "last_page",
//            "total"
//        ]);
//    }
//
//    /**
//     * Teste si on récupère la bonne structure de donnée
//     * Avec les LeaveTypes ayant is_active = 0
//     * @return void
//     */
//    public function testShouldReturnAllLeaveTypesWithIsNotActive(): void {
//        $adminProfiles = Profile::whereIn('label',['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->get()->pluck('id')->toArray();
//        $user = User::whereIn('profile_id', $adminProfiles)->first();
//        $JWT = $this->getDummyJWT($user->id);
//        $response = $this->get("/api/v1/leave-types?withIsNotActive=1", ['Authorization' => $JWT]);
//
//        $response->assertResponseStatus(200);
//        $this->seeJsonStructure([
//            "message",
//            "data" => [
//                "*" => [
//                    "id",
//                    "client_id",
//                    "name",
//                    "default_leave_value",
//                    "is_active",
//                    "is_deletable",
//                    "is_half_day",
//                    "is_attachment_required",
//                    "is_ignore_by_export",
//                    "is_monthly",
//                    "start_date",
//                    "end_date",
//                ]
//            ],
//            "current_page",
//            "next_page_url",
//            "last_page",
//            "total"
//        ]);
//    }
//
//    /**
//     * Teste si on récupère la bonne structure de donnée
//     * Avec les LeaveTypes ayant is_active = 0
//     * Sans être admin
//     * @return void
//     */
//    public function testShouldReturnAllLeaveTypesWithIsNotActiveNoAdmin(): void {
//        $adminProfiles = Profile::whereIn('label',['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->get()->pluck('id')->toArray();
//        $user = User::whereNotIn('profile_id', $adminProfiles)->first();
//        $JWT = $this->getDummyJWT($user->id);
//        $response = $this->get("/api/v1/leave-types?withIsNotActive=1", ['Authorization' => $JWT]);
//
//        $response->assertResponseStatus(403);
//        $this->seeJson([
//            "message" => "ErrorGetLeaveTypes",
//            "details" => "NotRightForGetLeaveType"
//        ]);
//    }
}
