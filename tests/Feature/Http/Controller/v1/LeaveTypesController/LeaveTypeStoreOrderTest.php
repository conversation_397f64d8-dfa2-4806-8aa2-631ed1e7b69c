<?php


namespace Tests\Feature\Http\Controller\v1\LeaveTypesController;


use App\Models\Api\Client;
use App\Models\Api\LeaveType;
use App\Models\Api\Profile;
use App\Models\Api\User;
use Tests\TestCase;

class LeaveTypeStoreOrderTest extends TestCase
{
//    private function verifyOrder($client_id){
//        $leaveTypes = LeaveType::where("client_id","=",$client_id)->where("order_appearance","!=",null)->where("order_appearance","!=",0)->orderBy("order_appearance","asc")->get();
//        for ($i = 1;$i-1 < count($leaveTypes);$i++){
//            $this->assertEquals($i,$leaveTypes[$i-1]->order_appearance);
//        }
//    }
//
//    private function createModels(){
//        $profileAdmin = Profile::whereIn('label', ['ADMINISTRATEUR','ADMINISTRATEURMANAGER'])->get()->pluck('id')->toArray();
//        $clientId = Client::first()->id;
//        $user = User::whereHas('site', function($q) use($clientId){
//            $q->where('client_id', '=', $clientId);
//        })->whereIn('profile_id', $profileAdmin)->first();
//
//        $min = LeaveType::where("client_id","=",$clientId)->get()->max("order_appearance")+1;
//
//        $newLeaveType = [
//            "client_id" => $clientId,
//            "name" => "test",
//            "is_monthly" => 1,
//            "leave_code" => "a",
//            "color" => "a",
//            "is_active" => 1,
//            "is_half_day" => 0,
//            "is_attachment_required" => 0,
//			"is_ignore_by_export" => 0,
//            "can_exceed" => 0,
//        ];
//        $newLeaveType2 = LeaveType::create([
//            "client_id" => $clientId,
//            "name" => "test2",
//            "is_monthly" => 1,
//            "leave_code" => "a",
//            "color" => "a",
//            "order_appearance" => $min
//        ]);
//        $newLeaveType3 = LeaveType::create([
//            "client_id" => $clientId,
//            "name" => "test3",
//            "is_monthly" => 1,
//            "leave_code" => "a",
//            "color" => "a",
//            "order_appearance" => $min+1
//        ]);
//        $max = LeaveType::where("client_id","=",$clientId)->get()->max("order_appearance");
//        return [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min, $max];
//    }
//
//
//    /**
//     * Teste si l'élément a été modifié
//     * Objectif Val = 4
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeOrderMin(): void{
//        [$clientId, $user, $data, $newLeaveType2, $newLeaveType3, $min] = $this->createModels();
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data["order_appearance"] = 1;
//
//        $response = $this->post("/api/v1/leave-types", $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $min;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$response->response["data"]["id"],"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min+1]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+2]);
//        $this->verifyOrder($clientId);
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * Objectif Val = 6
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeOrderMax(): void{
//        [$clientId, $user, $data, $newLeaveType2, $newLeaveType3, $min, $max] = $this->createModels();
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data["order_appearance"] = $max+1000;
//
//        $response = $this->post("/api/v1/leave-types", $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $max+1;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$response->response["data"]["id"],"order_appearance"=>$max+1]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+1]);
//        $this->verifyOrder($clientId);
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * Objectif Val = 5
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeOrderMiddle(): void{
//        [$clientId, $user, $data, $newLeaveType2, $newLeaveType3, $min] = $this->createModels();
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data["order_appearance"] = $min+1;
//
//        $response = $this->post("/api/v1/leave-types", $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $min+1;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$response->response["data"]["id"],"order_appearance"=>$min+1]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+2]);
//        $this->verifyOrder($clientId);
//    }
}
