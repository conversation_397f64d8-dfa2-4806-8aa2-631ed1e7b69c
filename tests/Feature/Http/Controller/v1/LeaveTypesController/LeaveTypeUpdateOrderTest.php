<?php


namespace Tests\Feature\Http\Controller\v1\LeaveTypesController;


use App\Models\Api\Client;
use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\Profile;
use App\Models\Api\User;
use Tests\TestCase;

class LeaveTypeUpdateOrderTest extends TestCase
{
//    private function verifyOrder($client_id){
//        $leaveTypes = LeaveType::where("client_id","=",$client_id)->where("order_appearance","!=",null)->where("order_appearance","!=",0)->orderBy("order_appearance","asc")->get();
//        for ($i = 1;$i-1 < count($leaveTypes);$i++){
//            $this->assertEquals($i,$leaveTypes[$i-1]->order_appearance);
//        }
//    }
//
//    private function createModels($createLeave = false, $firstShow = 1){
//        $profileAdmin = Profile::whereIn('label', ['ADMINISTRATEUR','ADMINISTRATEURMANAGER'])->get()->pluck('id')->toArray();
//        $clientId = Client::first()->id;
//        $user = User::whereHas('site', function($q) use($clientId){
//            $q->where('client_id', '=', $clientId);
//        })->whereIn('profile_id', $profileAdmin)->first();
//
//        $min = LeaveType::where("client_id","=",$clientId)->get()->max("order_appearance")+1;
//        $firstVal = $min;
//        if (!$firstShow){
//            $firstVal = 0;
//            $min--;
//        } elseif ($firstShow == 2){
//            $firstVal = $min+2;
//            $min--;
//        }
//
//        $newLeaveType = LeaveType::create([
//            "client_id" => $clientId,
//            "name" => "test",
//            "is_monthly" => 1,
//            "leave_code" => "a",
//            "color" => "a",
//            "order_appearance" => $firstVal
//        ]);
//        $newLeaveType2 = LeaveType::create([
//            "client_id" => $clientId,
//            "name" => "test2",
//            "is_monthly" => 1,
//            "leave_code" => "a",
//            "color" => "a",
//            "order_appearance" => $min+1
//        ]);
//        $newLeaveType3 = LeaveType::create([
//            "client_id" => $clientId,
//            "name" => "test3",
//            "is_monthly" => 1,
//            "leave_code" => "a",
//            "color" => "a",
//            "order_appearance" => $min+2
//        ]);
//        if ($createLeave) {
//            Leave::create([
//                "leave_type_id" => $newLeaveType->id,
//                "user_id" => $user->id,
//                "status_id" => 1,
//                "start_date" => Carbon::now(),
//                "end_date" => Carbon::now()->addDay()
//            ]);
//        }
//        $max = LeaveType::where("client_id","=",$clientId)->get()->max("order_appearance");
//
//        if (!$firstShow){
//            $min++;
//        } elseif ($firstShow == 2){
//            $min++;
//        }
//        return [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min, $max];
//    }
//
//
//    /**
//     * Teste si l'élément a été modifié
//     * First Val = 4
//     * Objectif Val = 4
//     * Has Leave = false
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeOrderMin(): void{
//        [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min] = $this->createModels();
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "name" => "test 1",
//            "is_active" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => 1
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $min;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType->id,"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min+1]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+2]);
//        $this->verifyOrder($clientId);
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * First Val = 4
//     * Objectif Val = 6
//     * Has Leave = false
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeOrderMax(): void{
//        [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min, $max] = $this->createModels();
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "name" => "test 1",
//            "is_active" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => $max+1000
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $max;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType->id,"order_appearance"=>$max]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+1]);
//        $this->verifyOrder($clientId);
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * First Val = 4
//     * Objectif Val = 5
//     * Has Leave = false
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeOrderMiddle(): void{
//        [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min] = $this->createModels();
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "name" => "test 1",
//            "is_active" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => $min+1
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $min+1;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType->id,"order_appearance"=>$min+1]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+2]);
//        $this->verifyOrder($clientId);
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * First Val = 4
//     * Objectif Val = 4
//     * Has Leave = true
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeHadLeaveOrderMin(): void{
//        [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min] = $this->createModels(true);
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "name" => "test 1",
//            "is_active" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => 1
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $min;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$response->response["data"]["id"],"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min+1]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+2]);
//        $this->verifyOrder($clientId);
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * First Val = 4
//     * Objectif Val = 6
//     * Has Leave = true
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeHadLeaveOrderMax(): void{
//        [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min, $max] = $this->createModels(true);
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "name" => "test 1",
//            "is_active" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => $max+1000
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $max;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$response->response["data"]["id"],"order_appearance"=>$max]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+1]);
//        $this->verifyOrder($clientId);
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * First Val = 4
//     * Objectif Val = 5
//     * Has Leave = true
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeHadLeaveOrderMiddle(): void{
//        [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min] = $this->createModels(true);
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "name" => "test 1",
//            "is_active" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => $min+1
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $min+1;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$response->response["data"]["id"],"order_appearance"=>$min+1]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+2]);
//        $this->verifyOrder($clientId);
//    }
//
//
//    /**
//     * Teste si l'élément a été modifié
//     * First Val = 0
//     * Objectif Val = 4
//     * Has Leave = false
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeVal0OrderMin(): void{
//        [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min] = $this->createModels(false,false);
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "name" => "test 1",
//            "is_active" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => 1
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $min;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType->id,"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min+1]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+2]);
//        $this->verifyOrder($clientId);
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * First Val = 0
//     * Objectif Val = 6
//     * Has Leave = false
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeVal0OrderMax(): void{
//        [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min, $max] = $this->createModels(false,false);
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "name" => "test 1",
//            "is_active" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => $max+1000
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $max+1;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType->id,"order_appearance"=>$max+1]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+1]);
//        $this->verifyOrder($clientId);
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * First Val = 0
//     * Objectif Val = 5
//     * Has Leave = false
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeVal0OrderMiddle(): void{
//        [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min] = $this->createModels(false,false);
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "name" => "test 1",
//            "is_active" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => $min+1
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $min+1;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType->id,"order_appearance"=>$min+1]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+2]);
//        $this->verifyOrder($clientId);
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * First Val = 0
//     * Objectif Val = 4
//     * Has Leave = true
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeVal0HadLeaveOrderMin(): void{
//        [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min] = $this->createModels(true,false);
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "name" => "test 1",
//            "is_active" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => 1
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $min;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$response->response["data"]["id"],"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min+1]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+2]);
//        $this->verifyOrder($clientId);
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * First Val = 0
//     * Objectif Val = 6
//     * Has Leave = true
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeVal0HadLeaveOrderMax(): void{
//        [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min, $max] = $this->createModels(true,false);
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "name" => "test 1",
//            "is_active" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => $max+1000
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $max+1;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$response->response["data"]["id"],"order_appearance"=>$max+1]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+1]);
//        $this->verifyOrder($clientId);
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * First Val = 0
//     * Objectif Val = 5
//     * Has Leave = true
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeVal0HadLeaveOrderMiddle(): void{
//        [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min] = $this->createModels(true,false);
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "name" => "test 1",
//            "is_active" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => $min+1
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $min+1;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$response->response["data"]["id"],"order_appearance"=>$min+1]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+2]);
//        $this->verifyOrder($clientId);
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * First Val = 6
//     * Objectif Val = 4
//     * Has Leave = false
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeValMaxOrderMin(): void{
//        [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min] = $this->createModels(false,2);
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "name" => "test 1",
//            "is_active" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => 1
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $min;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType->id,"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min+1]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+2]);
//        $this->verifyOrder($clientId);
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * First Val = 6
//     * Objectif Val = 6
//     * Has Leave = false
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeValMaxOrderMax(): void{
//        [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min, $max] = $this->createModels(false,2);
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "name" => "test 1",
//            "is_active" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => $max+1000
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $max;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType->id,"order_appearance"=>$max]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+1]);
//        $this->verifyOrder($clientId);
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * First Val = 6
//     * Objectif Val = 5
//     * Has Leave = false
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeValMaxOrderMiddle(): void{
//        [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min] = $this->createModels(false,2);
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "name" => "test 1",
//            "is_active" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => $min+1
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $min+1;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType->id,"order_appearance"=>$min+1]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+2]);
//        $this->verifyOrder($clientId);
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * First Val = 6
//     * Objectif Val = 4
//     * Has Leave = true
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeValMaxHadLeaveOrderMin(): void{
//        [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min] = $this->createModels(true,2);
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "name" => "test 1",
//            "is_active" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => 1
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $min;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$response->response["data"]["id"],"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min+1]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+2]);
//        $this->verifyOrder($clientId);
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * First Val = 6
//     * Objectif Val = 6
//     * Has Leave = true
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeValMaxHadLeaveOrderMax(): void{
//        [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min, $max] = $this->createModels(true,2);
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "name" => "test 1",
//            "is_active" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => $max+1000
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $max;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$response->response["data"]["id"],"order_appearance"=>$max]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+1]);
//        $this->verifyOrder($clientId);
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * First Val = 6
//     * Objectif Val = 5
//     * Has Leave = true
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeValMaxHadLeaveOrderMiddle(): void{
//        [$clientId, $user, $newLeaveType, $newLeaveType2, $newLeaveType3, $min] = $this->createModels(true,2);
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "name" => "test 1",
//            "is_active" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => $min+1
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $data["order_appearance"] = $min+1;
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_types", ["id"=>$response->response["data"]["id"],"order_appearance"=>$min+1]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType2->id,"order_appearance"=>$min]);
//        $this->seeInDatabase("leave_types", ["id"=>$newLeaveType3->id,"order_appearance"=>$min+2]);
//        $this->verifyOrder($clientId);
//    }
}
