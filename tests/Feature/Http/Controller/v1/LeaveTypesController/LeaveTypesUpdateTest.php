<?php

namespace Tests\Feature\Http\Controller\v1\LeaveTypesController;

use App\Models\Api\Client;
use App\Models\Api\LeaveType;
use App\Models\Api\LeaveTypeSubFamily;
use App\Models\Api\Profile;
use App\Models\Api\User;
use Tests\TestCase;

class LeaveTypesUpdateTest extends TestCase {
	/**
	 * Teste si l'élément a été modifié
	 * @return void
	 */
//	public function testUpdateItemUpdated(): void{
//		$id = LeaveType::where("is_deletable","=",false)->first()->id;
//		$profileAdmin = Profile::whereIn('label', ['ADMINISTRATEUR','ADMINISTRATEURMANAGER'])->get()->pluck('id')->toArray();
//		$clientId = Client::first()->id;
//		$user = User::whereHas('site', function($q) use($clientId){
//		    $q->where('client_id', '=', $clientId);
//        })->whereIn('profile_id', $profileAdmin)->first();
//		$subFamilies = LeaveTypeSubFamily::where('leave_type_id',$id)->get();
//		$name = "Congés payés ".((LeaveType::orderBy('id', 'desc')->first()->id)+1);
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//	    $response = $this->put("/api/v1/leave-types/".$id, [
//            "name" => $name,
//            "default_leave_value" => 10,
//            "is_active" => 1,
//            "is_deletable" => 1,
//            "is_pay" => 1,
//            "needs_count" => 0,
//            "is_half_day" => 0,
//            "is_attachment_required" => 0,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "leave_type_sub_families" => $subFamilies->toArray(),
//            "can_exceed" => 0,
//            "color" => "#000000",
//            "order_appearance" => 0
//		],[
//		    "Authorization" => $JWT
//        ]);
//		$response->assertResponseStatus(200);
//		$dataUpdated = [
//            "name" => $name,
//            "default_leave_value" => 10,
//            "is_active" => 1,
//            "is_half_day" => 0,
//            "is_attachment_required" => 0,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 0,
//            "color" => "#000000",
//            "order_appearance" => LeaveType::where("is_deletable","=",false)->first()->order_appearance
//        ];
//		if ($subFamilies->count() > 0){
//            $dataUpdated["leave_type_sub_families"] = $subFamilies->toArray();
//        }
//		// Can't change is_deletable, is_pay, needs_count
//		$response->seeJson($dataUpdated);
//	}
//
//    /**
//     * Teste si l'élément a été modifié
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveType(): void{
//        $profileAdmin = Profile::whereIn('label', ['ADMINISTRATEUR','ADMINISTRATEURMANAGER'])->get()->pluck('id')->toArray();
//        $clientId = Client::first()->id;
//        $user = User::whereHas('site', function($q) use($clientId){
//            $q->where('client_id', '=', $clientId);
//        })->whereIn('profile_id', $profileAdmin)->first();
//
//        $newLeaveType = LeaveType::create([
//            "client_id" => $clientId,
//            "name" => "test",
//            "is_monthly" => 1,
//            "leave_code" => "a",
//            "color" => "a",
//            "order_appearance" => 9
//        ]);
//        $subFamily = LeaveTypeSubFamily::create([
//            "leave_type_id" => $newLeaveType->id,
//            "name" => "hello",
//            "value" => 2
//        ]);
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "client_id" => 1000,
//            "last_update_id" => 1000,
//            "name" => "test 1",
//            "default_leave_value" => 10,
//            "is_active" => 0,
//            "is_deletable" => 0,
//            "is_pay" => 1,
//            "needs_count" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "start_date" => "2021-02-01",
//            "end_date" => "2021-03-01",
//            "leave_code" => "123",
//            "leave_type_sub_families" => [
//                [
//                    "id" => $subFamily->id,
//                    "name" => "coucou",
//                    "value" => 1.5
//                ]
//            ],
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => 0
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        unset($data["client_id"]);
//        unset($data["last_update_id"]);
//        unset($data["is_deletable"]);
//        unset($data["is_pay"]);
//        unset($data["needs_count"]);
//        $data["leave_type_sub_families"] = [LeaveTypeSubFamily::find($subFamily->id)->toArray()];
//        // Can't change is_deletable, is_pay, needs_count
//        $response->seeJson($data);
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeCreateSubFamily(): void{
//        $profileAdmin = Profile::whereIn('label', ['ADMINISTRATEUR','ADMINISTRATEURMANAGER'])->get()->pluck('id')->toArray();
//        $clientId = Client::first()->id;
//        $user = User::whereHas('site', function($q) use($clientId){
//            $q->where('client_id', '=', $clientId);
//        })->whereIn('profile_id', $profileAdmin)->first();
//
//        $newLeaveType = LeaveType::create([
//            "client_id" => $clientId,
//            "name" => "test",
//            "is_monthly" => 1,
//            "leave_code" => "a",
//            "color" => "a",
//            "order_appearance" => 9
//        ]);
//        $subFamily = LeaveTypeSubFamily::create([
//            "leave_type_id" => $newLeaveType->id,
//            "name" => "hello",
//            "value" => 2
//        ]);
//        $newSubFamily = [
//            "value" => 3.5,
//            "name" => "hello"
//        ];
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "client_id" => 1000,
//            "last_update_id" => 1000,
//            "name" => "test 1",
//            "default_leave_value" => 10,
//            "is_active" => 0,
//            "is_deletable" => 0,
//            "is_pay" => 1,
//            "needs_count" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "start_date" => "2021-02-01",
//            "end_date" => "2021-03-01",
//            "leave_code" => "123",
//            "leave_type_sub_families" => [
//                [
//                    "id" => $subFamily->id,
//                    "name" => "coucou",
//                    "value" => 1.5
//                ],
//                $newSubFamily,
//            ],
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => 0
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        unset($data["client_id"]);
//        unset($data["last_update_id"]);
//        unset($data["is_deletable"]);
//        unset($data["is_pay"]);
//        unset($data["needs_count"]);
//        unset($data["leave_type_sub_families"]);
//        //dd([$data["leave_type_sub_families"]==$response->response["data"]["leave_type_sub_families"],$data["leave_type_sub_families"],$response->response["data"]["leave_type_sub_families"]]);
//        // Can't change is_deletable, is_pay, needs_count
//        $response->seeJson($data);
//        $this->seeInDatabase("leave_type_sub_families",array_merge($newSubFamily,["leave_type_id"=>$newLeaveType->id]));
//    }
//
//    /**
//     * Teste si l'élément a été modifié
//     * @return void
//     */
//    public function testUpdateItemUpdatedNewLeaveTypeMinValues(): void{
//        $profileAdmin = Profile::whereIn('label', ['ADMINISTRATEUR','ADMINISTRATEURMANAGER'])->get()->pluck('id')->toArray();
//        $clientId = Client::first()->id;
//        $user = User::whereHas('site', function($q) use($clientId){
//            $q->where('client_id', '=', $clientId);
//        })->whereIn('profile_id', $profileAdmin)->first();
//
//        $newLeaveType = LeaveType::create([
//            "client_id" => $clientId,
//            "name" => "test",
//            "is_monthly" => 1,
//            "leave_code" => "a",
//            "color" => "a",
//            "order_appearance" => 9
//        ]);
//        $subFamily = LeaveTypeSubFamily::create([
//            "leave_type_id" => $newLeaveType->id,
//            "name" => "hello",
//            "value" => 2
//        ]);
//
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "name" => "test 1",
//            "is_active" => 0,
//            "is_half_day" => 1,
//            "is_attachment_required" => 1,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 1,
//            "color" => "#000000",
//            "order_appearance" => 0
//        ];
//
//        $response = $this->put("/api/v1/leave-types/".$newLeaveType->id, $data,[
//            "Authorization" => $JWT
//        ]);
//        $response->assertResponseStatus(200);
//        // Can't change is_deletable, is_pay, needs_count
//        $response->seeJson($data);
//    }
//
//	/**
//	 * Teste si l'élément n'existe pas
//	 * @return void
//	 */
//	public function testUpdateIfItemNotExist(): void {
//		$id = (LeaveType::orderBy('id', 'desc')->first()->id)+1;
//
//	    $response = $this->put("/api/v1/leave-types/".$id, [
//            "name" => "Congés payés ".((LeaveType::orderBy('id', 'desc')->first()->id)+1),
//            "default_leave_value" => 10,
//            "is_active" => 1,
//            "is_deletable" => 1,
//            "is_half_day" => 0,
//            "is_attachment_required" => 0,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "leave_type_sub_families" => LeaveTypeSubFamily::where('leave_type_id',$id)->get()->toArray(),
//            "can_exceed" => 0,
//            "color" => "#000000",
//            "order_appearance" => 0
//		]);
//
//		$response->assertResponseStatus(500);
//		$response->seeJson([
//		    "message" => "ErrorUpdateLeaveType",
//            "details" => "ErrorGetLeaveTypeWithId ".$id
//        ]);
//	}
//
//	/**
//	 * Teste si l'élément n'existe pas pour le client
//	 * @return void
//	 */
//	public function testUpdateIfItemNotExistForClient(): void {
//        $profileAdmin = Profile::whereIn('label', ['ADMINISTRATEUR','ADMINISTRATEURMANAGER'])->get()->pluck('id')->toArray();
//        $clientId = Client::first()->id;
//        $user = User::whereHas('site', function($q) use($clientId){
//            $q->where('client_id', '=', $clientId);
//        })->whereIn('profile_id', $profileAdmin)->first();
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//		$id = LeaveType::where('client_id','!=',$clientId)->first()->id;
//		$response = $this->put("/api/v1/leave-types/" . $id, [
//            "name" => "Congés payés ".((LeaveType::orderBy('id', 'desc')->first()->id)+1),
//            "default_leave_value" => 10,
//            "is_active" => 1,
//            "is_deletable" => 1,
//            "is_half_day" => 0,
//            "is_attachment_required" => 0,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "can_exceed" => 0,
//            "color" => "#000000",
//            "order_appearance" => 0
//		],[
//            "Authorization" => $JWT
//        ]);
//
//		$response->assertResponseStatus(500);
//		$response->seeJsonEquals([
//			"details" => "ErrorDontOwnLeaveTypeWithId ".$id,
//			"message" => "ErrorDontOwnLeaveType"
//		]);
//	}
//
//    /**
//     * Teste si client_id est modifié
//     * @return void
//     */
//    public function testUpdateIfClientIdIsChanged(): void {
//        $profileAdmin = Profile::whereIn('label', ['ADMINISTRATEUR','ADMINISTRATEURMANAGER'])->get()->pluck('id')->toArray();
//        $clientId = Client::first()->id;
//        $user = User::whereHas('site', function($q) use($clientId){
//            $q->where('client_id', '=', $clientId);
//        })->whereIn('profile_id', $profileAdmin)->first();
//        //Obtain JWT of user
//        $JWT = $this->getDummyJWT($user->id);
//
//        $id = LeaveType::where('client_id','!=',$clientId)->first()->id;
//        $response = $this->put("/api/v1/leave-types/" . $id, [
//            "name" => "Congés payés ".((LeaveType::orderBy('id', 'desc')->first()->id)+1),
//            "client_id" => $clientId+1,
//            "default_leave_value" => 10,
//            "is_active" => 1,
//            "is_deletable" => 1,
//            "is_half_day" => 0,
//            "is_attachment_required" => 0,
//			"is_ignore_by_export" => 0,
//            "is_monthly" => 0,
//            "leave_code" => "123",
//            "start_date" => "2018-05-16 15:35:26",
//            "end_date" => "2018-05-16 15:35:27",
//            "can_exceed" => 0,
//            "color" => "#000000",
//            "order_appearance" => 0
//        ],[
//            "Authorization" => $JWT
//        ]);
//
//        $response->assertResponseStatus(500);
//        $response->seeJsonEquals([
//            "details" => "ErrorDontOwnLeaveTypeWithId ".$id,
//            "message" => "ErrorDontOwnLeaveType"
//        ]);
//    }
}
