<?php

namespace Tests\Feature\Http\Controller\v1\LeaveTypesController;

use App\Models\Api\LeaveType;
use App\Models\Api\Profile;
use App\Models\Api\User;
use Tests\TestCase;

class LeaveTypesShowTest extends TestCase {
	/**
	 * Teste si on récupère la bonne structure de donnée
	 * @return void
	 */
//	public function testShouldReturnOneLeaveType(): void {
//
//        $JWT = $this->getDummyJWT();
//        $id = LeaveType::where("client_id", "=", User::first()->site->client_id)->where('is_active', '=', 1)->first()->id;
//
//		$response = $this->get("/api/v1/leave-types/".$id, ['Authorization' => $JWT]);
//
//		$response->assertResponseStatus(200);
//		$response->seeJsonStructure([
//			"message",
//			"data" => [
//                "id",
//                "client_id",
//                "name",
//                "default_leave_value",
//                "is_active",
//                "is_deletable",
//                "is_half_day",
//                "is_attachment_required",
//                "is_ignore_by_export",
//                "is_monthly",
//                "start_date",
//                "end_date",
//			]
//		]);
//	}
//
//	/**
//	 * Test l'id du type de congés n'existe pas
//	 * @return void
//	 */
//	public function testShowIfIdNotExist(): void {
//		$id = (LeaveType::all()->last()->id) +1;
//		$response = $this->get("/api/v1/leave-types/" . $id);
//
//		$response->assertResponseStatus(500);
//		$response->seeJsonEquals([
//			"details" => "ErrorGetLeaveTypeWithId ".$id,
//			"message" => "ErrorGetLeaveTypes"
//		]);
//	}
//
//	/**
//	 * Test l'id du type de congés n'est pas un nombre
//	 * @return void
//	 */
//	public function testShowIfIdNotANumber(): void {
//		$id = "azeezaeza";
//		$response = $this->get("/api/v1/leave-types/" . $id);
//
//		$response->assertResponseStatus(500);
//	}
//
//	/**
//	 * Test l'id du type de congés est < 0
//	 * @return void
//	 */
//	public function testShowIfIdLessThan0(): void {
//		$id = -582;
//		$response = $this->get("/api/v1/leave-types/" . $id);
//
//		$response->assertResponseStatus(400);
//	}
//
//    /**
//     * Teste si on récupère la bonne structure de donnée
//     * Avec les LeaveTypes ayant is_active = 0
//     * @return void
//     */
//    public function testShouldReturnLeaveTypesIsActiveFalse(): void {
//        $adminProfiles = Profile::whereIn('label',['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->get()->pluck('id')->toArray();
//        $user = User::whereIn('profile_id', $adminProfiles)->first();
//        $JWT = $this->getDummyJWT($user->id);
//
//        $leaveType = LeaveType::where("client_id", "=", $user->site->client_id)->first();
//        // Set is_active to 0
//        $leaveType->is_active = 0;
//        $leaveType->save();
//
//        $response = $this->get("/api/v1/leave-types/".$leaveType->id, ['Authorization' => $JWT]);
//
//        $response->assertResponseStatus(200);
//        $this->seeJsonStructure([
//            "message",
//            "data" => [
//                "id",
//                "client_id",
//                "name",
//                "default_leave_value",
//                "is_active",
//                "is_deletable",
//                "is_half_day",
//                "is_attachment_required",
//                "is_ignore_by_export",
//                "is_monthly",
//                "start_date",
//                "end_date",
//            ]
//        ]);
//    }
//
//    /**
//     * Teste si on récupère la bonne structure de donnée
//     * Avec les LeaveTypes ayant is_active = 0
//     * Sans être admin
//     * @return void
//     */
//    public function testShouldReturnLeaveTypesIsActiveFalseNoAdmin(): void {
//        $adminProfiles = Profile::whereIn('label',['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->get()->pluck('id')->toArray();
//        $user = User::whereNotIn('profile_id', $adminProfiles)->first();
//
//        $leaveType = LeaveType::where("client_id", "=", $user->site->client_id)->first();
//        // Set is_active to 0
//        $leaveType->is_active = 0;
//        $leaveType->save();
//
//        $JWT = $this->getDummyJWT($user->id);
//        $response = $this->get("/api/v1/leave-types/".$leaveType->id, ['Authorization' => $JWT]);
//
//        $response->assertResponseStatus(403);
//        $this->seeJson([
//            "message" => "ErrorGetLeaveTypes",
//            "details" => "NotRightForGetLeaveType"
//        ]);
//    }
}
