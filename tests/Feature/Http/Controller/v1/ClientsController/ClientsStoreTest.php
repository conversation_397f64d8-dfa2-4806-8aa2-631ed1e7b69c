<?php


namespace Tests\Feature\Http\Controller\v1\ClientsController;

use App\Models\Api\Client;
use Tests\TestCase;


class ClientsStoreTest extends TestCase
{
//    public function testStoreClientItemSaved(){
//        $JWT = $this->getDummyJWT();
//        $faker = \Faker\Factory::create();
//        $data = [
//            "name" => "clientName",
//            "uuid" => $faker->uuid
//        ];
//        $response = $this->post("/api/internal/clients", $data,
//            ['Authorization' => $JWT]
//        );
//        $response->assertResponseOk();
//        $response->seeJson(array_merge([
//            "message" => "SuccessStoreCustomer",
//        ],$data));
//    }
//
//    public function testStoreClientItemRestored(){
//        $JWT = $this->getDummyJWT();
//        $faker = \Faker\Factory::create();
//        $data = [
//            "name" => "clientName",
//            "uuid" => $faker->uuid
//        ];
//        // Create client
//        $client = Client::create($data);
//        // soft delete client
//        $client->delete();
//        $response2 = $this->post("/api/internal/clients", $data,
//            ['Authorization' => $JWT]
//        );
//        $response2->assertResponseOk();
//        $response2->seeJson(array_merge([
//            "message" => "SuccessStoreCustomer",
//        ],$data));
//    }
//
//    public function testStoreClientItemAlreadyExist(){
//        $JWT = $this->getDummyJWT();
//        $faker = \Faker\Factory::create();
//        $data = [
//            "name" => "clientName",
//            "uuid" => $faker->uuid
//        ];
//        // Create client
//        Client::create($data);
//        $response2 = $this->post("/api/internal/clients", $data,
//            ['Authorization' => $JWT]
//        );
//        $response2->assertResponseStatus(400);
//        $response2->seeJson([
//            "message" => "ErrorStoreCustomer",
//            "details" => "ClientAlreadyExist"
//        ]);
//    }
}
