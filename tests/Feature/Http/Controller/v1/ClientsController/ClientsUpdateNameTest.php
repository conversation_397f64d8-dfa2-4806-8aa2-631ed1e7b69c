<?php

namespace Tests\Feature\Http\Controller\v1\ClientsController;

use App\Models\Api\Client;
use App\Models\Api\User;
use Tests\TestCase;

class ClientsUpdateNameTest extends TestCase {
	/**
	 * Teste si l'élément a été modifié
	 * @return void
	 */
//	public function testUpdateClientNameItemUpdated(): void{
//
//        $JWT = $this->getDummyJWT();
//        $data = [
//            "name" => "clientName"
//        ];
//        $response = $this->put("/api/internal/clients/".Client::first()->uuid, $data,
//            ['Authorization' => $JWT]
//        );
//
//		$response->assertResponseStatus(200);
//		$response->seeJson([
//		    "message" => "SuccessUpdateCustomer",
//            "name" => $data["name"]
//        ]);
//	}
//
//    public function testUpdateClientNameItemNotBelongToUser(): void{
//	    $client = Client::first();
//        $user = User::whereHas('site', function($q) use($client){
//            $q->where('client_id', '!=', $client->id);
//        })->first();
//        $JWT = $this->getDummyJWT($user->id);
//        $data = [
//            "name" => "clientName"
//        ];
//        $response = $this->put("/api/internal/clients/".$client->uuid, $data,
//            ['Authorization' => $JWT]
//        );
//
//        $response->assertResponseStatus(400);
//        $response->seeJson([
//            "message" => "ErrorUpdateCustomer",
//            "details" => "ErrorUpdateOtherCustomer" . $client->id
//        ]);
//    }
//
//    public function testUpdateClientNameItemNotExist(): void{
//
//        $JWT = $this->getDummyJWT();
//        $data = [
//            "name" => "clientName"
//        ];
//        $response = $this->put("/api/internal/clients/abc", $data,
//            ['Authorization' => $JWT]
//        );
//
//        $response->assertResponseStatus(404);
//        $response->seeJson([
//            "message" => "ErrorUpdateCustomer",
//            "details" => "ClientNotExist"
//        ]);
//    }
}
