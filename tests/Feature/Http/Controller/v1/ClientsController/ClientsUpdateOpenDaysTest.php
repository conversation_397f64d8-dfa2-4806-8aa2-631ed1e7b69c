<?php


namespace Tests\Feature\Http\Controller\v1\ClientsController;


use App\Models\Api\User;
use Tests\TestCase;

class ClientsUpdateOpenDaysTest extends TestCase
{
    /**
     * Teste si l'élément a été modifié
     * @return void
     */
//    public function testUpdateOpenDays(): void{
//        $user = User::whereIn('profile_id',$this->getProfilsAdmin())->first();
//
//        $JWT = $this->getDummyJWT($user->id);
//
//        $response = $this->put("/api/v1/clients/open-days",[
//            "monday" => 1,
//            "tuesday" => 1,
//            "wednesday" => 1,
//            "thursday" => 1,
//            "friday" => 1,
//            "saturday" => 1,
//            "sunday" => 1,
//        ],
//            ['Authorization' => $JWT]
//        );
//        $openDays = ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"];
//
//        $response->assertResponseStatus(200);
//        $response->seeJsonStructure([
//            "message",
//            "data" => []
//        ]);
//        $this->assertEquals($openDays,$response->response['data']);
//    }

    /**
     * Teste si l'élément a été modifié
     * @return void
     */
//    public function testUpdateOpenDaysBis(): void{
//        $user = User::whereIn('profile_id',$this->getProfilsAdmin())->first();
//
//        $JWT = $this->getDummyJWT($user->id);
//
//        $response = $this->put("/api/v1/clients/open-days",[
//            "tuesday" => 1,
//            "wednesday" => 1,
//            "thursday" => 1,
//            "friday" => 1,
//            "saturday" => 1,
//            "sunday" => 0,
//        ],
//            ['Authorization' => $JWT]
//        );
//        $openDays = ["TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY"];
//
//        $response->assertResponseStatus(200);
//        $response->seeJsonStructure([
//            "message",
//            "data" => []
//        ]);
//        $this->assertEquals($openDays,$response->response['data']);
//    }

    /**
     * Teste si l'élément a été modifié
     * @return void
     */
//    public function testUpdateOpenDaysWrongType(): void{
//        $user = User::whereIn('profile_id',$this->getProfilsAdmin())->first();
//
//        $JWT = $this->getDummyJWT($user->id);
//
//        $response = $this->put("/api/v1/clients/open-days",[
//            "monday" => "abc",
//            "tuesday" => 1,
//            "wednesday" => 1,
//            "thursday" => 1,
//            "friday" => 1,
//            "saturday" => 1,
//            "sunday" => 1,
//        ],
//            ['Authorization' => $JWT]
//        );
//
//        $response->assertResponseStatus(422);
//        $this->seeJson([
//            "monday MustBeBool",
//            "message" => "ErrorUpdateBusinessDay"
//        ]);
//    }
}
