<?php


namespace Tests\Feature\Http\Controller\v1\ClientsController;


use App\Models\Api\Client;
use App\Models\Api\User;
use Tests\TestCase;

class ClientsUpdateTest extends TestCase
{
    /**
     * Teste si l'élément a été modifié
     * @return void
     */
//    public function testUpdateItemUpdated(): void{
//
//        $JWT = $this->getDummyJWT();
//        $data = [
//            "validation_scheme" => "HORIZONTAL",
//            "count_public_holidays" => true,
//        ];
//        $response = $this->put("/api/v1/clients/".Client::first()->id, $data,
//            ['Authorization' => $JWT]
//        );
//        $data["leave_start_date"] = "2020-05-15 00:00:00";
//        $response->assertResponseStatus(200);
//        $response->seeJson([
//            "validation_scheme" => $data["validation_scheme"],
//            "count_public_holidays" => $data["count_public_holidays"]
//        ]);
//    }

    /**
     * Teste si l'élément a été modifié
     * @return void
     */
//    public function testUpdateItemUpdatedValidationScheme(): void{
//
//        $JWT = $this->getDummyJWT();
//        $data = [
//            "validation_scheme" => "HORIZONTAL"
//        ];
//        $response = $this->put("/api/v1/clients/".Client::first()->id, $data,
//            ['Authorization' => $JWT]
//        );
//        $response->assertResponseStatus(200);
//        $response->seeJson($data);
//    }

//    public function testUpdateItemUpdatedCountPublicHolidays(): void{
//
//        $JWT = $this->getDummyJWT();
//        $data = [
//            "count_public_holidays" => true,
//        ];
//        $response = $this->put("/api/v1/clients/".Client::first()->id, $data,
//            ['Authorization' => $JWT]
//        );
//        $response->assertResponseStatus(200);
//        $response->seeJson($data);
//    }

    /**
     * Teste si l'élément n'existe pas
     * @return void
     */
//    public function testUpdateIfItemNotExist(): void{
//
//        $JWT = $this->getDummyJWT();
//        $id = (Client::all()->last()->id) +1;
//
//        $response = $this->put("/api/v1/clients/".$id, [
//            "validation_scheme" => "HORIZONTAL",
//            "count_public_holidays" => true,
//        ],
//            ['Authorization' => $JWT]
//        );
//
//        $response->assertResponseStatus(400);
//    }

    /**
     * Teste si l'élément n'existe pas pour le client
     * @return void
     */
//    public function testUpdateIfItemNotExistForClient(): void{
//
//        $JWT = $this->getDummyJWT();
//        $id = User::join("sites","users.site_id","=","sites.id")->where("client_id", "!=", User::first()->site->client_id)->first()->id;
//
//        $response = $this->put("/api/v1/clients/" . $id,
//            [
//                "validation_scheme" => "HORIZONTAL",
//                "count_public_holidays" => true,
//            ],
//            ['Authorization' => $JWT]
//        );
//
//        $response->assertResponseStatus(400);
//        $response->seeJsonEquals([
//            "details" => "ErrorUpdateOtherCustomer" . $id,
//            "message" => "ErrorUpdateCustomer"
//        ]);
//    }
}
