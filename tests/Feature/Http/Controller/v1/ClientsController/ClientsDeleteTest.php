<?php


namespace Tests\Feature\Http\Controller\v1\ClientsController;

use App\Models\Api\Client;
use App\Models\Api\User;
use Tests\TestCase;

class ClientsDeleteTest extends TestCase
{
    /**
     * Teste si l'élément a été supprimé
     * @return void
     */
//    public function testDestroyClientItemDeleted(): void{
//
//        $JWT = $this->getDummyJWT();
//        $response = $this->delete("/api/internal/clients/".Client::first()->uuid, [],
//            ['Authorization' => $JWT]
//        );
//
//        $response->assertResponseStatus(200);
//        $response->seeJson([
//            "message" => "SuccessDeleteCustomer"
//        ]);
//    }
//
//    public function testDestroyClientItemNotBelongToUser(): void{
//        $client = Client::first();
//        $user = User::whereHas('site', function($q) use($client){
//            $q->where('client_id', '!=', $client->id);
//        })->first();
//        $JWT = $this->getDummyJWT($user->id);
//        $response = $this->delete("/api/internal/clients/".$client->uuid, [],
//            ['Authorization' => $JWT]
//        );
//
//        $response->assertResponseStatus(400);
//        $response->seeJson([
//            "message" => "ErrorDeleteCustomer",
//            "details" => "ErrorUpdateOtherCustomer" . $client->id
//        ]);
//    }
//
//    public function testDestroyClientItemNotExist(): void{
//
//        $JWT = $this->getDummyJWT();
//        $response = $this->delete("/api/internal/clients/abc", [],
//            ['Authorization' => $JWT]
//        );
//
//        $response->assertResponseStatus(404);
//        $response->seeJson([
//            "message" => "ErrorDeleteCustomer",
//            "details" => "ClientNotExist"
//        ]);
//    }
}
