<?php


namespace Tests\Feature\Http\Controller\v1\ClientsController;


use App\Models\Api\User;
use Tests\TestCase;

class ClientsGetOpenDaysTest extends TestCase
{
    /**
     * Teste si l'élément a été modifié
     * @return void
     */
//    public function testGetClientOpenDays(): void{
//        $user = User::first();
//
//        $JWT = $this->getDummyJWT($user->id);
//
//        $response = $this->get("/api/v1/clients/open-days",
//            ['Authorization' => $JWT]
//        );
//        $openDays = $user->site->client->days()->get()->pluck('day_name')->toArray();
//
//        $intOpenDays = [];
//        foreach ($openDays as $day){
//            $intOpenDays[] =  date("N",strtotime($day));
//        }
//
//        $response->assertResponseStatus(200);
//        $response->seeJsonStructure([
//            "message",
//            "data" => []
//        ]);
//        $this->assertEquals($intOpenDays,$response->response['data']);
//    }
}
