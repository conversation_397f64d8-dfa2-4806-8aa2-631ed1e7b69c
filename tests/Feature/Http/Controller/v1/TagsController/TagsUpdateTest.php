<?php

namespace Tests\Feature\Http\Controller\v1\TagsController;

use App\Models\Api\Tag;
use App\Models\Api\User;
use Tests\TestCase;

class TagsUpdateTest extends TestCase {

//    public function testUpdateTag(): void{
//
//        $user = User::first();
//        $JWT = $this->getDummyJWT($user->id);
//        $tag = Tag::where("user_id", $user->id)->first();
//
//        $data = [
//            "label" => "label"
//        ];
//
//        // Request
//        $response = $this->put("/api/v1/tags/". $tag->id, $data, ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->seeJsonContains([
//            "message" => "SuccessUpdateTag",
//            "id" => $tag->id,
//            "label" => $data["label"],
//            "user_id" => $tag->user_id,
//        ]);
//        $response->assertResponseStatus(200);
//    }
//
//    public function testUpdateTagDontBelongToUser(): void{
//
//        $user = User::first();
//        $JWT = $this->getDummyJWT($user->id);
//        $tag = Tag::where("user_id", '!=', $user->id)->first();
//
//        $data = [
//            "label" => "label"
//        ];
//
//        // Request
//        $response = $this->put("/api/v1/tags/". $tag->id, $data, ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(403);
//    }
//
//    public function testUpdateTagInvalideData(): void{
//
//        $JWT = $this->getDummyJWT();
//        $tag = Tag::where("user_id", User::first()->id)->first();
//
//        $data = [
//            "label" => 2
//        ];
//
//        // Request
//        $response = $this->put("/api/v1/tags/". $tag->id, $data, ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(422);
//        $response->seeJsonEquals([
//            "message" => "ErrorUpdateTag",
//            "details" => "Label MustBeString"
//        ]);
//    }
//
//    public function testUpdateTagDontExist(): void{
//
//        $user = User::first();
//        $JWT = $this->getDummyJWT($user->id);
//        $tagId = Tag::all()->last()->id+1;
//
//        $data = [
//            "label" => "label"
//        ];
//
//        // Request
//        $response = $this->put("/api/v1/tags/". $tagId, $data, ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(404);
//        $response->seeJsonEquals([
//            "message" => "ErrorUpdateTag",
//            "details" => "TagDontExist",
//        ]);
//    }
}
