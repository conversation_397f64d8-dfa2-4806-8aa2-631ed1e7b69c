<?php

namespace Tests\Feature\Http\Controller\v1\TagsController;

use App\Models\Api\Tag;
use App\Models\Api\User;
use App\Models\Api\UserTag;
use Tests\TestCase;

class TagsDetachTest extends TestCase {

//    public function testDetachTags(): void
//    {
//        $user = User::whereHas('profile', function ($q) {
//            $q->where("label", "STANDARDMANAGER");
//        })->first();
//        $JWT = $this->getDummyJWT($user->id);
//
//        $user2 = User::all()[1];
//
//        $tag = Tag::create([
//            "label" => "l",
//            "user_id" => $user->id,
//        ]);
//
//        $data = [
//            "user_id" => $user2->id,
//            "tag_id" => $tag->id,
//        ];
//
//        UserTag::create($data);
//
//        // Request
//        $response = $this->post("/api/v1/tags/detach", $data, ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(200);
//        $response->seeJsonContains([
//            "message" => "SuccessRemoveUserFromTag",
//            "user_id" => $data["user_id"],
//            "tag_id" => $data["tag_id"],
//        ]);
//    }
//
//    public function testDetachTagsDontBelongsToUser(): void
//    {
//        $user = User::first();
//        $JWT = $this->getDummyJWT($user->id);
//        $tag = Tag::where("user_id", "!=", $user->id)->first();
//
//        $data = [
//            "user_id" => $user->id,
//            "tag_id" => $tag->id,
//        ];
//
//        // Request
//        $response = $this->post("/api/v1/tags/detach", $data, ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(403);
//        $response->seeJsonEquals([
//            "message" => "ErrorRemoveUserFromTag",
//            "details" => "NotAccessToTag",
//        ]);
//    }
//
//    public function testDetachTagsRelationDontExist(): void
//    {
//        $user = User::first();
//        $JWT = $this->getDummyJWT($user->id);
//
//        $tag = Tag::create([
//            "label" => "l",
//            "user_id" => $user->id,
//        ]);
//
//        $data = [
//            "user_id" => $user->id,
//            "tag_id" => $tag->id,
//        ];
//
//        // Request
//        $response = $this->post("/api/v1/tags/detach", $data, ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(404);
//        $response->seeJsonEquals([
//            "message" => "ErrorRemoveUserFromTag",
//            "details" => "RelationDontExist",
//        ]);
//    }
}
