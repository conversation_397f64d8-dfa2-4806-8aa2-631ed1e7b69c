<?php

namespace Tests\Feature\Http\Controller\v1\TagsController;

use App\Models\Api\Tag;
use App\Models\Api\User;
use Tests\TestCase;

class TagsShowTest extends TestCase {

//    public function testShowTags(): void
//    {
//        $JWT = $this->getDummyJWT();
//        $tag = Tag::where("user_id", User::first()->id)->first();
//
//        // Request
//        $response = $this->get("/api/v1/tags/" . $tag->id, ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(200);
//        $response->seeJsonContains([
//            "message" => "SuccessGetTag",
//            "id" => $tag->id,
//            "label" => $tag->label,
//            "user_id" => $tag->user_id,
//        ]);
//    }
}
