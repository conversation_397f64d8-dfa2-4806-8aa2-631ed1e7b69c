<?php

namespace Tests\Feature\Http\Controller\v1\TagsController;

use App\Models\Api\Tag;
use App\Models\Api\User;
use App\Models\Api\UserTag;
use Tests\TestCase;

class TagsAttachTest extends TestCase {

//    public function testAttachTags(): void
//    {
//        $user = User::first();
//        $JWT = $this->getDummyJWT($user->id);
//
//        $user2 = User::whereHas("client", function ($q) use ($user) {
//            $q->where("clients.id", "=", $user->client->id);
//        })->first();
//
//        $tag = Tag::create([
//            "label" => "l",
//            "user_id" => $user->id,
//        ]);
//
//        $data = [
//            "user_id" => $user2->id,
//            "tag_id" => $tag->id,
//        ];
//
//        // Request
//        $response = $this->post("/api/v1/tags/attach", $data, ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(200);
//        $response->seeJsonContains([
//            "message" => "SuccessAddUserToTag",
//            "user_id" => $data["user_id"],
//            "tag_id" => $data["tag_id"],
//        ]);
//    }
//
//    public function testAttachTagsDontBelongsToUser(): void
//    {
//        $user = User::first();
//        $JWT = $this->getDummyJWT($user->id);
//        $tag = Tag::where("user_id", "!=", $user->id)->first();
//
//        $data = [
//            "user_id" => $user->id,
//            "tag_id" => $tag->id,
//        ];
//
//        // Request
//        $response = $this->post("/api/v1/tags/attach", $data, ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(403);
//        $response->seeJsonEquals([
//            "message" => "ErrorAddUserToTag",
//            "details" => "NotAccessToTag",
//        ]);
//    }
//
//    public function testAttachTagsRelationAlreadyExist(): void
//    {
//        $user = User::first();
//        $JWT = $this->getDummyJWT($user->id);
//
//        $tag = Tag::create([
//            "label" => "l",
//            "user_id" => $user->id,
//        ]);
//
//        $data = [
//            "user_id" => $user->id,
//            "tag_id" => $tag->id,
//        ];
//
//        UserTag::create($data);
//
//        // Request
//        $response = $this->post("/api/v1/tags/attach", $data, ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(400);
//        $response->seeJsonEquals([
//            "message" => "ErrorAddUserToTag",
//            "details" => "RelationAlreadyExist",
//        ]);
//    }
}
