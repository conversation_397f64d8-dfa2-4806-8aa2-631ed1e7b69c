<?php

namespace Tests\Feature\Http\Controller\v1\TagsController;

use App\Models\Api\User;
use App\Models\Api\UserTag;
use Tests\TestCase;

class TagsMultipleAttachDetachTest extends TestCase {

//    public function testAttachTagsClassic(): void
//    {
//        $user = User::first();
//        $JWT = $this->getDummyJWT($user->id);
//
//        $tagAttachedIds = UserTag::where("user_id", $user->id)->get()->pluck("tag_id")->toArray();
//        $tagNotAttachedIds = UserTag::where("user_id", "!=", $user->id)
//            ->whereNotIn("tag_id", $tagAttachedIds)
//            ->get()->pluck("tag_id")->unique()->toArray();
//
//        $data = [
//            "user_id" => $user->id,
//            "attach" => $tagNotAttachedIds,
//            "detach" => $tagAttachedIds,
//        ];
//
//        // Request
//        $response = $this->post("/api/v1/tags/multipleAttachDetach", $data, ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->seeJsonContains([
//            "message" => "SuccessUpdateUserTag",
//        ]);
//        $response->assertResponseStatus(200);
//
//        foreach ($tagAttachedIds as $tagAttachedId) {
//            $userTagDeleted = UserTag::withTrashed()
//                ->where("user_id", $user->id)
//                ->where("tag_id", $tagAttachedId)
//                ->whereNotNull('deleted_at')
//                ->get();
//            $this->assertCount(1, $userTagDeleted);
//        }
//
//        foreach ($tagNotAttachedIds as $tagNotAttachedId) {
//            $response->seeInDatabase("user_tags", [
//                "user_id" => $user->id,
//                "tag_id" => $tagNotAttachedId,
//            ]);
//        }
//    }
//
//    public function testAttachTagsAlreadyExist(): void
//    {
//        $user = User::first();
//        $JWT = $this->getDummyJWT($user->id);
//
//        $tagAttachedIds = UserTag::where("user_id", $user->id)->get()->pluck("tag_id")->toArray();
//        $tagNotAttachedIds = UserTag::where("user_id", "!=", $user->id)
//            ->whereNotIn("tag_id", $tagAttachedIds)
//            ->get()->pluck("tag_id")->unique()->toArray();
//
//        $data = [
//            "user_id" => $user->id,
//            "attach" => $tagAttachedIds,
//            "detach" => $tagNotAttachedIds,
//        ];
//
//        // Request
//        $response = $this->post("/api/v1/tags/multipleAttachDetach", $data, ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->seeJsonContains([
//            "message" => "SuccessUpdateUserTag",
//        ]);
//        $response->assertResponseStatus(200);
//
//        foreach ($tagAttachedIds as $tagAttachedId) {
//            $response->seeInDatabase("user_tags", [
//                "user_id" => $user->id,
//                "tag_id" => $tagAttachedId,
//            ]);
//        }
//
//        foreach ($tagNotAttachedIds as $tagNotAttachedId) {
//            $response->notSeeInDatabase("user_tags", [
//                "user_id" => $user->id,
//                "tag_id" => $tagNotAttachedId,
//            ]);
//        }
//    }
}
