<?php

namespace Tests\Feature\Http\Controller\v1\TagsController;

use Tests\TestCase;

class TagsIndexTest extends TestCase {


//	public function testIndexTags(): void{
//
//        $JWT = $this->getDummyJWT();
//
//	    // Request
//		$response = $this->get("/api/v1/tags", ["Authorization" => $JWT]);
//
//		// Assertions
//		$response->assertResponseStatus(200);
//		$response->seeJsonStructure([
//			"message",
//			"data" => [
//				"*" => [
//					"id",
//					"label",
//					"user_id",
//					"deleted_at",
//					"created_at",
//					"updated_at"
//				]
//			],
//			"current_page",
//			"next_page_url",
//			"last_page",
//			"total"
//		]);
//	}
//
//    public function testIndexTagsOnlyLabels(): void {
//
//        $JWT = $this->getDummyJWT();
//
//	    // Request
//        $response = $this->get("/api/v1/tags?onlyLabel=1", ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(200);
//        $response->seeJsonStructure([
//            "message",
//            "data",
//            "current_page",
//            "next_page_url",
//            "last_page",
//            "total"
//        ]);
//    }
}
