<?php

namespace Tests\Feature\Http\Controller\v1\TagsController;

use App\Models\Api\User;
use Tests\TestCase;

class TagsStoreTest extends TestCase {

//    public function testStoreTag(): void{
//
//        $user = User::first();
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "label" => "label"
//        ];
//
//        // Request
//        $response = $this->post("/api/v1/tags/", $data, ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(200);
//        $response->seeJsonContains([
//            "message" => "SuccessStoreTag",
//            "label" => $data["label"],
//            "user_id" => $user->id,
//        ]);
//    }
//
//    public function testStoreTag2(): void{
//
//        $user = User::first();
//        $JWT = $this->getDummyJWT($user->id);
//
//        $data = [
//            "label" => "label",
//        ];
//
//        // Request
//        $response = $this->post("/api/v1/tags/", $data, ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(200);
//        $response->seeJsonContains([
//            "message" => "SuccessStoreTag",
//            "label" => $data["label"],
//            "user_id" => $user->id,
//        ]);
//    }
}
