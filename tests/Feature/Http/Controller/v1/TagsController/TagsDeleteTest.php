<?php

namespace Tests\Feature\Http\Controller\v1\TagsController;

use App\Models\Api\Tag;
use App\Models\Api\User;
use Tests\TestCase;

class TagsDeleteTest extends TestCase {

//    public function testDeleteTag(): void{
//
//        $user = User::first();
//        $JWT = $this->getDummyJWT($user->id);
//        $tag = Tag::where("user_id", $user->id)->first();
//
//        // Request
//        $response = $this->delete("/api/v1/tags/". $tag->id, [], ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(200);
//        $response->seeJsonEquals([
//            "message" => "SuccessDeleteTag",
//            "data" => []
//        ]);
//    }
//
//    public function testDeleteTagDontExist(): void{
//
//        $user = User::first();
//        $JWT = $this->getDummyJWT($user->id);
//        $tagId = Tag::all()->last()->id+1;
//
//        // Request
//        $response = $this->delete("/api/v1/tags/". $tagId, [], ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(404);
//        $response->seeJsonEquals([
//            "message" => "ErrorDeleteTag",
//            "details" => "TagDontExist",
//        ]);
//    }
//
//    public function testDeleteTagDontBelongToUser(): void{
//
//        $user = User::first();
//        $JWT = $this->getDummyJWT($user->id);
//        $tag = Tag::where("user_id", "!=", $user->id)->first();
//
//        // Request
//        $response = $this->delete("/api/v1/tags/". $tag->id, [], ["Authorization" => $JWT]);
//
//        // Assertions
//        $response->assertResponseStatus(403);
//        $response->seeJsonEquals([
//            "message" => "ErrorDeleteTag",
//            "details" => "NotAccessToTag"
//        ]);
//    }

}
