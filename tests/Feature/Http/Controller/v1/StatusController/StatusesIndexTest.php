<?php

namespace Tests\Feature\Http\Controller\v1\StatusController;

//use App\Models\Api\SiteDatabase;
use Tests\TestCase;

class StatusesIndexTest extends TestCase {

	/**
	 * Teste si on récupère la bonne structure de donnée
	 * @return void
	 */
//	public function testShouldReturnAllStatuses(): void{
//		$response = $this->get("/api/v1/statuses");
//
//		$response->assertResponseStatus(200);
//		$response->seeJsonStructure([
//			"message",
//			"data" => [
//				"*" => [
//					"id",
//					"tag",
//					"name",
//					"color",
//					"class",
//					"deleted_at",
//					"created_at",
//					"updated_at"
//				]
//			],
//			"current_page",
//			"next_page_url",
//			"last_page",
//			"total"
//		]);
//	}

	/**
	 * Teste si on récupère la bonne structure de donnée with site databases
	 * @return void
	 */
//	public function testShouldReturnAllSitesWithOrder(): void{
//		$response = $this->get("/api/v1/statuses?order=name,asc");
//
//		$response->assertResponseStatus(200);
//		$response->seeJsonStructure([
//			"message",
//			"data" => [
//				"*" => [
//                    "id",
//                    "tag",
//                    "name",
//                    "color",
//                    "class",
//                    "deleted_at",
//                    "created_at",
//                    "updated_at"
//				]
//			],
//			"current_page",
//			"next_page_url",
//			"last_page",
//			"total"
//		]);
//	}
}
