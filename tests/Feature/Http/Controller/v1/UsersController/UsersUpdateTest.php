<?php

namespace Tests\Feature\Http\Controller\v1\UsersController;

use App\Models\Api\User;
use Tests\TestCase;

class UsersUpdateTest extends TestCase {
    /**
     * Teste si l'élément a été modifié
     * @return void
     */
//    public function testUpdateLeaves(): void{
//
//        $JWT = $this->getDummyJWT();
//
//        $data = [
//            "profile_id" => 1,
//            "site_id" => 1,
//            "uuid" => "a840bb04-e754-5f05-8ddb-f6d93287f136",
//            "crm_uuid" => "95E77E68-84D5-EA11-8117-00155D085053",
//            "client_uuid" => "9780e9b8-3619-56e0-a8bc-9a9aa827316e",
//            "firstname" => "CLIENT 1",
//            "lastname" => "USER",
//            "email" => "<EMAIL>",
//            "fcm_token" => null,
//            "picture_path" => null,
//            "license_path" => null,
//            "matricule" => "azefgFE",
//            "enter_date" => "2017-08-01",
//        ];
//
//        $this->put("/api/v1/users/1", $data, ['Authorization' => $JWT]);
//
//        $this->assertResponseStatus(200);
//
//    }

    /**
     * Teste si l'élément n'existe pas
     * @return void
     */
//    public function testUpdateIfItemNotExist(): void {
//
//        $JWT = $this->getDummyJWT();
//        $id = (User::all()->last()->id) +1;
//
//        $response = $this->put("/api/v1/users/".$id, [
//            "profile_id" => 1,
//            "site_id" => 1,
//            "uuid" => "a840bb04-e754-5f05-8ddb-f6d93287f136",
//            "crm_uuid" => "95E77E68-84D5-EA11-8117-00155D085053",
//            "client_uuid" => "9780e9b8-3619-56e0-a8bc-9a9aa827316e",
//            "firstname" => "CLIENT 1",
//            "lastname" => "USER",
//            "email" => "<EMAIL>",
//            "fcm_token" => null,
//            "picture_path" => null,
//            "license_path" => null,
//            "matricule" => "azefgFE",
//            "enter_date" => "2017-08-01",
//        ],
//            ['Authorization' => $JWT]
//        );
//
//        $response->seeJsonEquals([
//            "details" => "ErrorUserDontExist",
//            "message" => "ErrorUpdateUser",
//        ]);
//
//        $response->assertResponseStatus(500);
//    }

    /**
     * Teste si l'élément n'existe pas pour le client
     * @return void
     */
//    public function testUpdateIfItemNotExistForClient(): void {
//
//        $JWT = $this->getDummyJWT();
//        $id = User::join("sites","users.site_id","=","sites.id")->where("client_id", "!=", User::first()->site->client_id)->first()->id;
//
//        $response = $this->put("/api/v1/users/" . $id,
//            [
//                "uuid" => "a840bb04-e754-5f05-8ddb-f6d93287f136",
//                "crm_uuid" => "95E77E68-84D5-EA11-8117-00155D085053",
//                "client_uuid" => "9780e9b8-3619-56e0-a8bc-9a9aa827316e",
//                "firstname" => "CLIENT 1",
//                "lastname" => "USER",
//                "email" => "<EMAIL>",
//                "fcm_token" => null,
//                "picture_path" => null,
//                "license_path" => null,
//                "matricule" => "azefgFE",
//                "enter_date" => "2017-08-01",
//            ],
//            ['Authorization' => $JWT]
//        );
//
//        $response->assertResponseStatus(500);
//        $response->seeJsonEquals([
//            "details" => "ErrorDontOwnUser",
//            "message" => "ErrorUpdateUser"
//        ]);
//    }
}
