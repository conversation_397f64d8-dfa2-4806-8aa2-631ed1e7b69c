<?php

namespace Tests\Feature\Http\Controller\v1\UsersController;

use App\Models\Api\User;
use Tests\TestCase;

class UsersCurrentTest extends TestCase {

//    public function testGetCurrentUser(): void{
//
//        $JWT = $this->getDummyJWT();
//
//        // Request
//        $response = $this->get("api/v1/users/current", ['Authorization' => $JWT]);
//
//        // Assertions
//        $this->assertResponseStatus(200);
//
//        $this->assertEquals(User::first()->email, $response->response->original["data"]["email"]);
//
//        $this->seeJson([
//            "message" => "SuccessGetCurrentUser",
//        ]);
//
//        $this->seeJsonStructure([
//            "message",
//            "data" => [
//                "id",
//                "profile_id",
//                "site_id",
//                "uuid",
//                "crm_uuid",
//                "client_uuid",
//                "firstname",
//                "lastname",
//                "email",
//                "fcm_token",
//                "picture_path",
//                "license_path",
//                "matricule",
//                "enter_date"
//            ],
//        ]);
//    }
}
