<?php

namespace Tests\Feature\Http\Controller\v1\UsersController;

use Tests\TestCase;

class UsersIndexTest extends TestCase {
    /**
     * Teste si l'élément a été modifié
     * @return void
     */
//    public function testIndexLeaves(): void{
//
//        $this->get("/api/v1/users");
//
//        $this->assertResponseStatus(200);
//        $this->seeJsonStructure([
//            "message",
//            "data" => [
//                "*" => [
//                    "id",
//                    "profile_id",
//                    "site_id",
//                    "uuid",
//                    "crm_uuid",
//                    "client_uuid",
//                    "firstname",
//                    "lastname",
//                    "email",
//                    "fcm_token",
//                    "picture_path",
//                    "license_path",
//                    "matricule",
//                    "enter_date"
//                ]
//            ],
//            "current_page",
//            "next_page_url",
//            "last_page",
//            "total"
//        ]);
//    }
//
//    public function testIndexLeavesWithUserLeaveCounts(): void{
//
//        $this->get("/api/v1/users?withUserLeaveCounts=1");
//
//        $this->assertResponseStatus(200);
//        $this->seeJsonStructure([
//            "message",
//            "data" => [
//                "*" => [
//                    "id",
//                    "profile_id",
//                    "site_id",
//                    "uuid",
//                    "crm_uuid",
//                    "client_uuid",
//                    "firstname",
//                    "lastname",
//                    "email",
//                    "fcm_token",
//                    "picture_path",
//                    "license_path",
//                    "matricule",
//                    "enter_date",
//                    "user_leave_counts" => [
//                        "*" => [
//                            "id",
//                            "user_id",
//                            "leave_type_id",
//                            "acquired",
//                            "taken",
//                            "balance",
//                        ]
//                    ]
//                ]
//            ],
//            "current_page",
//            "next_page_url",
//            "last_page",
//            "total"
//        ]);
//    }

}
