<?php

namespace Tests\Feature\Http\Controller;

use App\Models\Api\Client;
use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\Profile;
use App\Models\Api\Site;
use App\Models\Api\Status;
use App\Models\Api\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Str;
use Tests\TestCase;


class TeamPlanningTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
    }

    protected function createUserWithRelations(array $userAttributes = [], array $clientAttributes = [], array $siteAttributes = []): User
    {
        $client = Client::factory()->create($clientAttributes);
        $site = Site::factory()->create(array_merge(['client_id' => $client->getKey()], $siteAttributes));

        $weekdays = ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY'];
        foreach ($weekdays as $day) {
            $dayModel = \App\Models\Api\Day::firstOrCreate(['day_name' => $day]);
            $client->days()->syncWithoutDetaching([$dayModel->getKey()]);
        }

        $defaultAttributes = [
            'site_id' => $site->getKey(),
            'profile_id' => Profile::firstOrCreate(['label' => 'STANDARD'])->getKey(),
            'client_uuid' => $client->uuid
        ];

        $user = User::factory()->create(array_merge($defaultAttributes, $userAttributes));
        $user->load('site');

        return $user;
    }


    private function createManager()
    {
        $managerProfile = Profile::firstOrCreate(['label' => 'STANDARDMANAGER']);

        $client = Client::factory()->create();
        $site = Site::factory()->create(['client_id' => $client->getKey()]);

        return User::factory()->create([
            'profile_id' => $managerProfile->getKey(),
            'site_id' => $site->getKey(),
            'client_uuid' => $client->uuid
        ]);
    }


    private function createCollaborator()
    {
        $collaboratorProfile = Profile::firstOrCreate(['label' => 'STANDARD']);

        $client = Client::factory()->create();
        $site = Site::factory()->create(['client_id' => $client->getKey()]);

        return User::factory()->create([
            'profile_id' => $collaboratorProfile->getKey(),
            'site_id' => $site->getKey(),
            'client_uuid' => $client->uuid
        ]);
    }


    private function createManagerRelation($manager, $collaborator, $level = 1)
    {
        if ($level === 1) {
            $collaborator->manager_id = $manager->getKey();
            $collaborator->save();
        }

        $collaborator->managers()->attach($manager->getKey(), ['level' => $level]);
    }


    private function createLeaveType($client)
    {
        return LeaveType::factory()->create([
            'client_id' => $client->getKey(),
            'is_active' => true,
        ]);
    }

    private function createLeave($user, $statusId, $leaveTypeId, $validatorLevel = 1, $startDate = null, $endDate = null)
    {
        if (!$startDate) {
            $startDate = Carbon::now()->startOfMonth()->addDays(5);
        }

        if (!$endDate) {
            $endDate = Carbon::now()->startOfMonth()->addDays(10);
        }

        $duration = 5;
        if ($user->site && $user->site->client && $user->site->client->days) {
            $daysOpen = $user->site->client->days()->get()->pluck('day_name')->toArray();
            if (!empty($daysOpen)) {
                $duration = (Carbon::parse($startDate)->diffInHoursFiltered(function(Carbon $date) use($daysOpen) {
                        return in_array(strtoupper($date->dayName), $daysOpen);
                    }, Carbon::parse($endDate))) / 24;
            }
        }

        $leave = new Leave([
            'user_id' => $user->getKey(),
            'creator_id' => $user->getKey(),
            'status_id' => $statusId,
            'leave_type_id' => $leaveTypeId,
            'current_validator_level' => $validatorLevel,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'duration' => $duration,
            'n1' => $duration,
            'comment' => 'Test leave',
        ]);

        $leave->save();
        return $leave;
    }


    private function getStatuses()
    {
        return [
            'submitted' => Status::firstOrCreate(['tag' => 'SUBMITTED'], ['name' => 'Soumis à validation']),
            'validated' => Status::firstOrCreate(['tag' => 'VALIDATED'], ['name' => 'Validé']),
            'refused' => Status::firstOrCreate(['tag' => 'REFUSED'], ['name' => 'Refusée']),
        ];
    }

    private function getDateFilters($startDate, $endDate)
    {
        return [
            [
                'field' => 'start_date',
                'operator' => '>=',
                'value' => $startDate instanceof Carbon ? $startDate->format('Y-m-d') : $startDate
            ],
            [
                'field' => 'end_date',
                'operator' => '<=',
                'value' => $endDate instanceof Carbon ? $endDate->format('Y-m-d') : $endDate
            ]
        ];
    }

    private function getMonthFilters($monthOffset = 0)
    {
        $date = Carbon::now()->addMonths($monthOffset);
        return $this->getDateFilters(
            $date->copy()->startOfMonth(),
            $date->copy()->endOfMonth()
        );
    }


    public function test_team_planning_shows_pending_validation_requests()
    {
        $manager = $this->createManager();
        $collaborator = $this->createCollaborator();
        $this->createManagerRelation($manager, $collaborator);
        $leaveType = $this->createLeaveType($collaborator->site->client);
        $statuses = $this->getStatuses();

        $leave = $this->createLeave(
            $collaborator,
            $statuses['submitted']->getKey(),
            $leaveType->getKey()
        );

        $this->actingAs($manager, 'jwt');
        $response = $this->postJson('/api/v1/leaves/search', [
            'scope' => 'waitingForUserValidation'
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure(['data']);

        $responseData = $response->json('data');
        $this->assertNotEmpty($responseData);

        $leaveIds = collect($responseData)->pluck('id')->toArray();
        $this->assertContains($leave->getKey(), $leaveIds, 'Created request is not present in the response');
    }


    public function test_team_planning_shows_validated_requests_for_specific_period()
    {
        $manager = $this->createManager();
        $collaborator1 = $this->createCollaborator();
        $collaborator2 = $this->createCollaborator();
        $this->createManagerRelation($manager, $collaborator1);
        $this->createManagerRelation($manager, $collaborator2);
        $leaveType = $this->createLeaveType($collaborator1->site->client);
        $statuses = $this->getStatuses();

        $startDate1 = Carbon::now()->startOfMonth()->addDays(5);
        $endDate1 = Carbon::now()->startOfMonth()->addDays(10);
        $leave1 = $this->createLeave($collaborator1, $statuses['validated']->getKey(), $leaveType->getKey(), 2, $startDate1, $endDate1);

        $startDate2 = Carbon::now()->startOfMonth()->addDays(15);
        $endDate2 = Carbon::now()->startOfMonth()->addDays(20);
        $leave2 = $this->createLeave($collaborator2, $statuses['validated']->getKey(), $leaveType->getKey(), 2, $startDate2, $endDate2);

        $this->actingAs($manager, 'jwt');
        $response = $this->postJson('/api/v1/leaves/search', [
            'scope' => 'validated',
            'filters' => $this->getDateFilters($startDate1, $endDate1)
        ]);

        $response->assertStatus(200);
        $responseData = $response->json('data');
        $this->assertNotEmpty($responseData);

        $leaveIds = collect($responseData)->pluck('id')->toArray();
        $this->assertContains($leave1->getKey(), $leaveIds, 'Leave 1 was not found in the response');
        $this->assertNotContains($leave2->getKey(), $leaveIds, 'Leave 2 was found in the response but should not be');

        $matchingLeave = collect($responseData)->firstWhere('id', $leave1->getKey());
        $this->assertEquals($collaborator1->getKey(), $matchingLeave['user_id']);
        $this->assertEquals($leaveType->getKey(), $matchingLeave['leave_type_id']);
        $this->assertEquals($statuses['validated']->getKey(), $matchingLeave['status_id']);

        $leaveStartDate = Carbon::parse($matchingLeave['start_date'])->format('Y-m-d');
        $leaveEndDate = Carbon::parse($matchingLeave['end_date'])->format('Y-m-d');
        $this->assertEquals($startDate1->format('Y-m-d'), $leaveStartDate);
        $this->assertEquals($endDate1->format('Y-m-d'), $leaveEndDate);
    }


    public function test_team_planning_shows_other_validated_requests_for_current_month()
    {
        $manager = $this->createManager();
        $collaborator1 = $this->createCollaborator();
        $collaborator2 = $this->createCollaborator();
        $this->createManagerRelation($manager, $collaborator1);
        $this->createManagerRelation($manager, $collaborator2);
        $leaveType = $this->createLeaveType($collaborator1->site->client);
        $statuses = $this->getStatuses();

        $leave1 = $this->createLeave(
            $collaborator1,
            $statuses['validated']->getKey(),
            $leaveType->getKey(),
            2,
            Carbon::now()->startOfMonth()->addDays(5),
            Carbon::now()->startOfMonth()->addDays(10)
        );

        $leave2 = $this->createLeave(
            $collaborator2,
            $statuses['validated']->getKey(),
            $leaveType->getKey(),
            2,
            Carbon::now()->startOfMonth()->addDays(15),
            Carbon::now()->startOfMonth()->addDays(20)
        );

        $this->actingAs($manager, 'jwt');
        $response = $this->postJson('/api/v1/leaves/search', [
            'scope' => 'validated',
            'filters' => $this->getMonthFilters()
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure(['data']);

        $responseData = $response->json('data');
        if (!empty($responseData)) {
            $leaveIds = collect($responseData)->pluck('id')->toArray();
            $this->assertContains($leave1->getKey(), $leaveIds, 'Leave 1 was not found in the response');
            $this->assertContains($leave2->getKey(), $leaveIds, 'Leave 2 was not found in the response');
        }
    }


    public function test_team_planning_allows_navigation_between_months()
    {
        $manager = $this->createManager();
        $collaborator = $this->createCollaborator();
        $this->createManagerRelation($manager, $collaborator);
        $leaveType = $this->createLeaveType($collaborator->site->client);
        $statuses = $this->getStatuses();

        $leave1 = $this->createLeave(
            $collaborator,
            $statuses['validated']->getKey(),
            $leaveType->getKey(),
            2,
            Carbon::now()->startOfMonth()->addDays(5),
            Carbon::now()->startOfMonth()->addDays(10)
        );

        $leave2 = $this->createLeave(
            $collaborator,
            $statuses['validated']->getKey(),
            $leaveType->getKey(),
            2,
            Carbon::now()->addMonth()->startOfMonth()->addDays(5),
            Carbon::now()->addMonth()->startOfMonth()->addDays(10)
        );

        $this->actingAs($manager, 'jwt');

        $response1 = $this->postJson('/api/v1/leaves/search', [
            'scope' => 'validated',
            'filters' => $this->getMonthFilters(0)
        ]);

        $response2 = $this->postJson('/api/v1/leaves/search', [
            'scope' => 'validated',
            'filters' => $this->getMonthFilters(1)
        ]);

        $response1->assertStatus(200);
        $response2->assertStatus(200);
        $response1->assertJsonStructure(['data']);
        $response2->assertJsonStructure(['data']);

        $responseData1 = $response1->json('data');
        $responseData2 = $response2->json('data');

        if (!empty($responseData1)) {
            $leaveIds1 = collect($responseData1)->pluck('id')->toArray();
            $this->assertContains($leave1->getKey(), $leaveIds1, 'Leave 1 was not found in current month response');
            $this->assertNotContains($leave2->getKey(), $leaveIds1, 'Leave 2 was found in current month response but should not be');
        }

        if (!empty($responseData2)) {
            $leaveIds2 = collect($responseData2)->pluck('id')->toArray();
            $this->assertContains($leave2->getKey(), $leaveIds2, 'Leave 2 was not found in next month response');
            $this->assertNotContains($leave1->getKey(), $leaveIds2, 'Leave 1 was found in next month response but should not be');
        }
    }


    public function test_team_planning_shows_pending_validation_requests_for_specific_month()
    {
        $manager = $this->createManager();
        $collaborator = $this->createCollaborator();
        $this->createManagerRelation($manager, $collaborator);
        $leaveType = $this->createLeaveType($collaborator->site->client);
        $statuses = $this->getStatuses();

        $leave1 = $this->createLeave(
            $collaborator,
            $statuses['submitted']->getKey(),
            $leaveType->getKey(),
            1,
            Carbon::now()->startOfMonth()->addDays(5),
            Carbon::now()->startOfMonth()->addDays(10)
        );

        $leave2 = $this->createLeave(
            $collaborator,
            $statuses['submitted']->getKey(),
            $leaveType->getKey(),
            1,
            Carbon::now()->addMonth()->startOfMonth()->addDays(5),
            Carbon::now()->addMonth()->startOfMonth()->addDays(10)
        );

        $this->actingAs($manager, 'jwt');
        $response1 = $this->postJson('/api/v1/leaves/search', [
            'scope' => 'waitingForUserValidation',
            'filters' => $this->getMonthFilters(0)
        ]);

        $response1->assertStatus(200);
        $responseData1 = $response1->json('data');
        $this->assertNotEmpty($responseData1, 'Response for current month does not contain any data');

        $leaveIds1 = collect($responseData1)->pluck('id')->toArray();
        $this->assertContains($leave1->getKey(), $leaveIds1, 'Current month request is not present in the response');

        $matchingLeave = collect($responseData1)->firstWhere('id', $leave1->getKey());
        $this->assertEquals($collaborator->getKey(), $matchingLeave['user_id']);
        $this->assertEquals($leaveType->getKey(), $matchingLeave['leave_type_id']);
        $this->assertEquals($statuses['submitted']->getKey(), $matchingLeave['status_id']);
        $this->assertEquals(1, $matchingLeave['current_validator_level']);
        $this->assertEquals(5, $matchingLeave['duration']);
        $this->assertEquals('Test leave', $matchingLeave['comment']);

        $response2 = $this->postJson('/api/v1/leaves/search', [
            'scope' => 'waitingForUserValidation',
            'filters' => $this->getMonthFilters(1)
        ]);

        $response2->assertStatus(200);
        $responseData2 = $response2->json('data');
        $this->assertNotEmpty($responseData2, 'Response for next month does not contain any data');

        $leaveIds2 = collect($responseData2)->pluck('id')->toArray();
        $this->assertContains($leave2->getKey(), $leaveIds2, 'Next month request is not present in the response');

        $this->assertNotContains($leave1->getKey(), $leaveIds2, 'Current month leave was found in next month response');
        $this->assertNotContains($leave2->getKey(), $leaveIds1, 'Next month leave was found in current month response');
    }
}
