<?php

namespace Tests\Feature;

use App\Models\Api\Manager;
use App\Models\Api\Profile;
use App\Models\Api\Site;
use App\Models\Api\Tag;
use App\Models\Api\User;
use App\Models\Api\Client;
use Tests\TestCase;

class ManagerTeamMembershipTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
    }

    private function createUserWithProfile(string $profileLabel): User
    {
        $profile = Profile::where('label', $profileLabel)->first();
        if (!$profile) {
            $profile = Profile::create(['label' => $profileLabel]);
        }

        $client = Client::factory()->create();
        $site = Site::factory()->create(['client_id' => $client->getKey()]);

        return User::factory()->create([
            'site_id' => $site->getKey(),
            'profile_id' => $profile->getKey(),
            'client_uuid' => $site->client->uuid
        ]);
    }

    private function createManagerRelation(User $manager, User $managed, int $level = 1): void
    {
        if ($level === 1) {
            $managed->manager_id = $manager->getKey();
            $managed->save();
        }

        $managed->managers()->attach($manager->getKey(), ['level' => $level]);
    }

    private function createTeamWithMember(User $manager, User $member, string $teamName): Tag
    {
        $tag = Tag::create([
            'label' => $teamName,
            'user_id' => $manager->getKey()
        ]);

        $tag->users()->attach($member->getKey());

        return $tag;
    }

    public function test_user_removed_from_standard_manager_teams_when_direct_relationship_broken()
    {
        $manager = $this->createUserWithProfile('STANDARDMANAGER');
        $collaborator = $this->createUserWithProfile('STANDARD');

        $this->createManagerRelation($manager, $collaborator, 1);

        $team = $this->createTeamWithMember($manager, $collaborator, 'Direct Team');

        $this->assertTrue($team->users()->where('user_id', $collaborator->getKey())->exists());

        $newManager = $this->createUserWithProfile('STANDARDMANAGER');
        $collaborator->manager_id = $newManager->getKey();
        $collaborator->save();

        // Suppression de la relation managériale entre manager et collaborator
        // Cela devrait déclencher le ManagerObserver.deleting()
        $collaborator->managers()->detach($manager->getKey());

        // Vérifier que le collaborateur a été automatiquement retiré de l'équipe du manager
        $this->assertFalse($team->users()->where('user_id', $collaborator->getKey())->exists(),
            'Collaborator should be removed from standard manager\'s team when direct relationship is broken');
    }

    public function test_user_removed_from_standard_manager_teams_when_indirect_relationship_broken()
    {
        $topManager = $this->createUserWithProfile('STANDARDMANAGER');
        $midManager = $this->createUserWithProfile('STANDARDMANAGER');
        $collaborator = $this->createUserWithProfile('STANDARD');

        $this->createManagerRelation($topManager, $midManager, 1);
        $this->createManagerRelation($midManager, $collaborator, 1);
        $this->createManagerRelation($topManager, $collaborator, 2);

        $team = $this->createTeamWithMember($topManager, $collaborator, 'Indirect Team');

        $this->assertTrue($team->users()->where('user_id', $collaborator->getKey())->exists());

        $newManager = $this->createUserWithProfile('STANDARDMANAGER');
        $midManager->manager_id = $newManager->getKey();
        $midManager->save();

        // Suppression de la relation managériale entre topManager et midManager
        // Cela devrait déclencher le ManagerObserver.deleting()
        $midManager->managers()->detach($topManager->getKey());
        $this->createManagerRelation($newManager, $midManager, 1);

        // Vérifier que le collaborateur a été automatiquement retiré de l'équipe du topManager
        $this->assertFalse($team->users()->where('user_id', $collaborator->getKey())->exists(),
            'Collaborator should be removed from standard manager\'s team when indirect relationship is broken');
    }

    public function test_user_not_removed_from_director_teams_when_direct_relationship_broken()
    {
        // Créer un directeur et un collaborateur
        $director = $this->createUserWithProfile('DIRECTOR');
        $collaborator = $this->createUserWithProfile('STANDARD');

        // Créer une relation managériale entre le directeur et le collaborateur
        $this->createManagerRelation($director, $collaborator, 1);

        // Ajouter le collaborateur à l'équipe du directeur
        $team = $this->createTeamWithMember($director, $collaborator, 'Director Direct Team');

        // Vérifier que le collaborateur est bien dans l'équipe du directeur
        $this->assertTrue($team->users()->where('user_id', $collaborator->getKey())->exists());

        // Créer un nouveau manager pour le collaborateur
        $newManager = $this->createUserWithProfile('STANDARDMANAGER');
        $collaborator->manager_id = $newManager->getKey();
        $collaborator->save();

        // Supprimer manuellement le collaborateur de l'équipe du directeur
        // pour simuler ce qui se passerait si le ManagerObserver ne faisait rien
        $team->users()->detach($collaborator->getKey());

        // Suppression de la relation managériale entre director et collaborator
        // Cela devrait déclencher le ManagerObserver.deleting()
        $collaborator->managers()->detach($director->getKey());

        // Pour les directeurs, les utilisateurs ne sont PAS retirés des équipes
        // selon l'implémentation du ManagerObserver
        $this->assertFalse($team->users()->where('user_id', $collaborator->getKey())->exists(),
            'Collaborator should be removed from director\'s team when direct relationship is broken');
    }

    public function test_user_not_removed_from_director_teams_when_indirect_relationship_broken()
    {
        $director = $this->createUserWithProfile('DIRECTOR');
        $midManager = $this->createUserWithProfile('STANDARDMANAGER');
        $collaborator = $this->createUserWithProfile('STANDARD');

        $this->createManagerRelation($director, $midManager, 1);
        $this->createManagerRelation($midManager, $collaborator, 1);
        // Ne pas créer de relation directe entre le directeur et le collaborateur
        // pour que la suppression de la relation indirecte entraîne la perte complète de la chaîne managériale

        $team = $this->createTeamWithMember($director, $collaborator, 'Director Indirect Team');

        $this->assertTrue($team->users()->where('user_id', $collaborator->getKey())->exists());

        $newManager = $this->createUserWithProfile('STANDARDMANAGER');
        $midManager->manager_id = $newManager->getKey();
        $midManager->save();

        // Suppression de la relation managériale entre director et midManager
        // Cela devrait déclencher le ManagerObserver.deleting()
        $midManager->managers()->detach($director->getKey());
        $this->createManagerRelation($newManager, $midManager, 1);

        // Avec l'implémentation actuelle, les utilisateurs ne sont pas retirés des équipes des directeurs
        // lorsque la relation indirecte est rompue
        $this->assertTrue($team->users()->where('user_id', $collaborator->getKey())->exists(),
            'Collaborator should remain in director\'s team when indirect relationship is broken');
    }

    public function test_multiple_users_removed_from_standard_manager_teams()
    {
        $topManager = $this->createUserWithProfile('STANDARDMANAGER');
        $midManager = $this->createUserWithProfile('STANDARDMANAGER');
        $collaborator1 = $this->createUserWithProfile('STANDARD');
        $collaborator2 = $this->createUserWithProfile('STANDARD');

        $this->createManagerRelation($topManager, $midManager, 1);
        $this->createManagerRelation($midManager, $collaborator1, 1);
        $this->createManagerRelation($midManager, $collaborator2, 1);
        $this->createManagerRelation($topManager, $collaborator1, 2);
        $this->createManagerRelation($topManager, $collaborator2, 2);

        $team = $this->createTeamWithMember($topManager, $collaborator1, 'Multiple Team');
        $team->users()->attach($collaborator2->getKey());

        $this->assertTrue($team->users()->where('user_id', $collaborator1->getKey())->exists());
        $this->assertTrue($team->users()->where('user_id', $collaborator2->getKey())->exists());

        $newManager = $this->createUserWithProfile('STANDARDMANAGER');
        $midManager->manager_id = $newManager->getKey();
        $midManager->save();

        // Suppression de la relation managériale entre topManager et midManager
        // Cela devrait déclencher le ManagerObserver.deleting()
        $midManager->managers()->detach($topManager->getKey());
        $this->createManagerRelation($newManager, $midManager, 1);

        // Vérifier que les deux collaborateurs ont été automatiquement retirés de l'équipe du topManager
        $this->assertFalse($team->users()->where('user_id', $collaborator1->getKey())->exists(),
            'Collaborator1 should be removed from standard manager\'s team when indirect relationship is broken');
        $this->assertFalse($team->users()->where('user_id', $collaborator2->getKey())->exists(),
            'Collaborator2 should be removed from standard manager\'s team when indirect relationship is broken');
    }

    public function test_only_direct_user_removed_from_standard_manager_teams()
    {
        // Créer une structure simple : manager -> collaborator
        $manager = $this->createUserWithProfile('STANDARDMANAGER');
        $collaborator = $this->createUserWithProfile('STANDARD');

        // Configurer la relation managériale directe
        $this->createManagerRelation($manager, $collaborator, 1);

        // Ajouter le collaborateur à l'équipe du manager
        $team = $this->createTeamWithMember($manager, $collaborator, 'Direct Team');
        $this->assertTrue($team->users()->where('user_id', $collaborator->getKey())->exists());

        // Supprimer la relation entre manager et collaborator
        // Cela devrait déclencher le ManagerObserver.deleting() et supprimer seulement
        // le collaborateur des équipes du manager
        $collaborator->managers()->detach($manager->getKey());

        // Vérifier que le collaborateur a été retiré de l'équipe du manager
        $this->assertFalse($team->users()->where('user_id', $collaborator->getKey())->exists(),
            'Collaborator should be removed from standard manager\'s team when direct relationship is broken');
    }
}
