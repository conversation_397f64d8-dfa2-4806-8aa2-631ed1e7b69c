<?php
namespace Tests;

use App\Models\Api\Profile;
use App\Models\Api\Status;
use Illuminate\Contracts\Console\Kernel;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\RefreshDatabaseState;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Mockery;
use Tests\CreatesApplication;

abstract class TestCase extends BaseTestCase {
//	use WithoutMiddleware;
//	use DatabaseTransactions;
    use CreatesApplication, RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        Mockery::getConfiguration()->allowMockingNonExistentMethods(true);
        $toolsMock = Mockery::mock('alias:App\Lib\Tools');
        $toolsMock->shouldReceive('getCurrentUserWithUuid')
            ->andReturnUsing(fn() => auth()->user());
    }

    protected function getProfilsAdmin() {
	    return Profile::whereIn('label', ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->get()->pluck('id')->toArray();
    }

    protected function getStatusId($status) {
	    return Status::where("tag", "=", $status)->first()->id;
    }

    protected function refreshTestDatabase()
    {
        if (! RefreshDatabaseState::$migrated) {
            $this->artisan('migrate', ['--path' => __DIR__.'/../database/migrations', '--realpath' => true]);

            $this->app[Kernel::class]->setArtisan(null);

            RefreshDatabaseState::$migrated = true;
        }

        $this->beginDatabaseTransaction();
    }
}
