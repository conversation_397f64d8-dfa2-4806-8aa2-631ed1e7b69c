<?php

namespace Tests\Unit;

use App\Http\Controllers\Api\v1\Leaves\LeavesController;
use App\Lib\Tools;
use App\Models\Api\Client;
use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\Profile;
use App\Models\Api\Site;
use App\Models\Api\Status;
use App\Models\Api\User;
use App\Models\Api\UserLeaveCount;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Str;
use Mockery;
use Tests\TestCase;

class LeaveCounterRecalculationTest extends TestCase
{
    use DatabaseTransactions;

    protected $toolsMock;
    protected $controller;
    protected $user;
    protected $leaveType;
    protected $statuses;

    protected function setUp(): void
    {
        parent::setUp();

        $this->toolsMock = Mockery::mock(Tools::class);
        $this->toolsMock->shouldReceive('getCurrentUserWithUuid')
            ->andReturnUsing(fn() => auth()->user());

        app()->instance(Tools::class, $this->toolsMock);

        $this->controller = new LeavesController();
        $this->user = $this->createUserWithRelations();
        $this->leaveType = $this->createLeaveType();
        $this->statuses = $this->createStatuses();
        
        $this->setProperty($this->controller, 'currentUser', $this->user);
        $this->setProperty($this->controller, 'clientId', $this->user->site->client_id);
    }

    public function test_recalc_leaves_distribution_with_no_previous_year_counter(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, false, true, 10);

        $leave1 = $this->createLeave(3.0, Carbon::create(2024, 4, 15));
        $leave2 = $this->createLeave(2.0, Carbon::create(2024, 7, 10));

        $this->callRecalcLeavesDistribution();

        $leave1->refresh();
        $leave2->refresh();

        $this->assertEquals(3.0, $leave1->n);
        $this->assertEquals(0, $leave1->n1);
        $this->assertEquals(0, $leave1->out_day);

        $this->assertEquals(2.0, $leave2->n);
        $this->assertEquals(0, $leave2->n1);
        $this->assertEquals(0, $leave2->out_day);
    }

    public function test_recalc_leaves_distribution_with_previous_year_counter(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 10, 5);

        Carbon::setTestNow(Carbon::create(2024, 8, 1));

        $leave1 = $this->createLeave(3.0, Carbon::create(2024, 4, 15));

        $this->callRecalcLeavesDistribution();

        $leave1->refresh();

        $this->assertEquals(0, $leave1->n);
        $this->assertEquals(3.0, $leave1->n1);
    }

    public function test_recalc_leaves_distribution_with_insufficient_n1_balance(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 10, 2);

        Carbon::setTestNow(Carbon::create(2024, 8, 1));

        $leave1 = $this->createLeave(3.0, Carbon::create(2024, 4, 15));
        $leave2 = $this->createLeave(2.0, Carbon::create(2024, 5, 10));

        $this->callRecalcLeavesDistribution();

        $leave1->refresh();
        $leave2->refresh();

        $this->assertEquals(1.0, $leave1->n);
        $this->assertEquals(2.0, $leave1->n1);

        $this->assertEquals(2.0, $leave2->n);
        $this->assertEquals(0, $leave2->n1);
    }

    public function test_recalc_leaves_distribution_with_negative_balance(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 5, 2);

        Carbon::setTestNow(Carbon::create(2024, 8, 1));

        $leave1 = $this->createLeave(4.0, Carbon::create(2024, 4, 15));
        $leave2 = $this->createLeave(3.0, Carbon::create(2025, 7, 10));

        $this->callRecalcLeavesDistribution();

        $leave1->refresh();
        $leave2->refresh();

        $this->assertEquals(2.0, $leave1->n);
        $this->assertEquals(2.0, $leave1->n1);

        $this->assertEquals(3.0, $leave2->n);
        $this->assertEquals(0, $leave2->n1);
        $this->assertEquals(0, $leave2->out_day);
    }

    public function test_update_user_leave_count_without_previous_year(): void
    {
        $leaveTypeNoPay = LeaveType::factory()->create([
            'client_id' => $this->user->site->client_id,
            'needs_count' => true,
            'is_pay' => false
        ]);

        $this->createUserLeaveCount($this->user->id, $leaveTypeNoPay->id, false, true, 10);

        $initialCount = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $leaveTypeNoPay->id)
            ->where('is_last_year', false)
            ->first();

        $this->controller->updateUserLeaveCount(
            $leaveTypeNoPay->id,
            3.0,
            $this->user->id,
            null,
            3.0
        );

        $updatedCount = $initialCount->fresh();
        $this->assertEquals(3.0, $updatedCount->taken);
        $this->assertEquals(7.0, $updatedCount->balance);
    }

    public function test_update_user_leave_count_with_previous_year(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 10, 5);

        $nCount = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $this->leaveType->id)
            ->where('is_last_year', false)
            ->first();

        $n1Count = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $this->leaveType->id)
            ->where('is_last_year', true)
            ->first();

        $this->controller->updateUserLeaveCount(
            $this->leaveType->id,
            4.0,
            $this->user->id,
            2.0,
            2.0
        );

        $nCount->refresh();
        $n1Count->refresh();

        $this->assertEquals(2.0, $nCount->taken);
        $this->assertEquals(8.0, $nCount->balance);
        $this->assertEquals(2.0, $n1Count->taken);
        $this->assertEquals(3.0, $n1Count->balance);
    }

    public function test_recalc_with_multiple_leave_types(): void
    {
        $leaveType2 = LeaveType::factory()->create([
            'client_id' => $this->user->site->client_id,
            'needs_count' => true,
            'is_pay' => true
        ]);

        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 10, 5);
        $this->createUserLeaveCount($this->user->id, $leaveType2->id, true, true, 8, 3);

        Carbon::setTestNow(Carbon::create(2024, 8, 1));

        $leave1 = $this->createLeave(2.0, Carbon::create(2024, 4, 15), $this->leaveType->id);
        $leave2 = $this->createLeave(1.5, Carbon::create(2024, 5, 10), $leaveType2->id);

        $this->callRecalcLeavesDistribution($this->leaveType->id);
        $this->callRecalcLeavesDistribution($leaveType2->id);

        $leave1->refresh();
        $leave2->refresh();

        $this->assertEquals(0, $leave1->n);
        $this->assertEquals(2.0, $leave1->n1);

        $this->assertEquals(0, $leave2->n);
        $this->assertEquals(1.5, $leave2->n1);
    }

    public function test_recalc_with_transmitted_leaves_excluded(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 10, 5);

        Carbon::setTestNow(Carbon::create(2024, 8, 1));

        $submittedLeave = $this->createLeave(2.0, Carbon::create(2024, 4, 15), $this->leaveType->id, 'SUBMITTED');
        $validatedLeave = $this->createLeave(1.5, Carbon::create(2024, 5, 10), $this->leaveType->id, 'VALIDATED');
        $transmittedLeave = $this->createLeave(1.0, Carbon::create(2024, 6, 15), $this->leaveType->id, 'TRANSMITTED');

        $this->callRecalcLeavesDistribution();

        $submittedLeave->refresh();
        $validatedLeave->refresh();
        $transmittedLeave->refresh();

        $this->assertEquals(0, $submittedLeave->n);
        $this->assertEquals(2.0, $submittedLeave->n1);

        $this->assertEquals(0, $validatedLeave->n);
        $this->assertEquals(1.5, $validatedLeave->n1);

        $this->assertEquals(0, $transmittedLeave->n);
        $this->assertEquals(0, $transmittedLeave->n1);
    }

    public function test_recalc_with_empty_leave_collection(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 10, 5);

        $this->callRecalcLeavesDistribution();

        $nCount = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $this->leaveType->id)
            ->where('is_last_year', false)
            ->first();

        $n1Count = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $this->leaveType->id)
            ->where('is_last_year', true)
            ->first();

        $this->assertEquals(10, $nCount->balance);
        $this->assertEquals(5, $n1Count->balance);
    }

    private function createUserWithRelations(): User
    {
        $client = Client::factory()->create();
        $site = Site::factory()->create(['client_id' => $client->getKey()]);
        $profile = Profile::firstOrCreate(['label' => 'ADMINISTRATEUR']);

        $user = new User([
            'site_id' => $site->getKey(),
            'profile_id' => $profile->getKey(),
            'uuid' => Str::uuid(),
            'client_uuid' => $site->client?->uuid,
            'firstname' => fake()->firstName,
            'lastname' => fake()->lastName,
            'email' => fake()->companyEmail,
            'picture_path' => fake()->word.'/'.fake()->word,
            'license_path' => fake()->word.'/'.fake()->word,
            'matricule' => fake()->randomNumber(5, true),
            'can_receive_mails' => fake()->boolean
        ]);

        $user->save();
        $user->load('site');

        return $user;
    }

    private function createLeaveType(): LeaveType
    {
        return LeaveType::factory()->create([
            'client_id' => $this->user->site->client_id,
            'needs_count' => true,
            'is_pay' => true
        ]);
    }

    private function createStatuses(): array
    {
        return [
            'submitted' => Status::firstOrCreate(['tag' => 'SUBMITTED'], ['name' => 'Soumis']),
            'validated' => Status::firstOrCreate(['tag' => 'VALIDATED'], ['name' => 'Validé']),
            'transmitted' => Status::firstOrCreate(['tag' => 'TRANSMITTED'], ['name' => 'Transmis']),
        ];
    }

    private function createUserLeaveCount(int $userId, int $leaveTypeId, bool $createN1 = true, bool $createN = true, float $nBalance = 10, float $n1Balance = 5): void
    {
        if ($createN) {
            UserLeaveCount::updateOrCreate(
                ['user_id' => $userId, 'leave_type_id' => $leaveTypeId, 'is_last_year' => false],
                ['balance' => $nBalance, 'acquired' => $nBalance, 'taken' => 0]
            );
        }

        if ($createN1) {
            UserLeaveCount::updateOrCreate(
                ['user_id' => $userId, 'leave_type_id' => $leaveTypeId, 'is_last_year' => true],
                ['balance' => $n1Balance, 'acquired' => $n1Balance, 'taken' => 0]
            );
        }
    }

    private function createLeave(float $duration, Carbon $startDate, int $leaveTypeId = null, string $statusTag = 'VALIDATED'): Leave
    {
        $leaveTypeId = $leaveTypeId ?? $this->leaveType->id;
        $status = $this->statuses[strtolower($statusTag)];

        return Leave::factory()->create([
            'user_id' => $this->user->id,
            'leave_type_id' => $leaveTypeId,
            'status_id' => $status->id,
            'duration' => $duration,
            'start_date' => $startDate,
            'end_date' => $startDate->copy()->addDays($duration - 1),
            'n' => 0,
            'n1' => 0
        ]);
    }

    private function callRecalcLeavesDistribution(int $leaveTypeId = null): void
    {
        $leaveTypeId = $leaveTypeId ?? $this->leaveType->id;
        
        $reflection = new \ReflectionClass($this->controller);
        $method = $reflection->getMethod('recalcLeavesDistribution');
        $method->invoke($this->controller, $this->user->id, $leaveTypeId);
    }

    public function test_recalc_with_simple_leave_distribution(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 10, 5);

        Carbon::setTestNow(Carbon::create(2024, 8, 1));

        $leave = $this->createLeave(5.0, Carbon::create(2024, 4, 15));

        $this->callRecalcLeavesDistribution();

        $leave->refresh();
        $this->assertEquals(0, $leave->n);
        $this->assertEquals(5.0, $leave->n1);
    }

    public function test_update_user_leave_count_with_zero_values(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 10, 5);

        $this->controller->updateUserLeaveCount(
            $this->leaveType->id,
            0.0,
            $this->user->id,
            0.0,
            0.0
        );

        $nCount = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $this->leaveType->id)
            ->where('is_last_year', false)
            ->first();

        $n1Count = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $this->leaveType->id)
            ->where('is_last_year', true)
            ->first();

        $this->assertEquals(0.0, $nCount->taken);
        $this->assertEquals(10.0, $nCount->balance);
        $this->assertEquals(0.0, $n1Count->taken);
        $this->assertEquals(5.0, $n1Count->balance);
    }

    public function test_recalc_with_fractional_balances(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 7.5, 2.25);

        Carbon::setTestNow(Carbon::create(2024, 8, 1));

        $leave1 = $this->createLeave(1.5, Carbon::create(2024, 4, 15));

        $this->callRecalcLeavesDistribution();

        $leave1->refresh();

        $this->assertEquals(0, $leave1->n);
        $this->assertEquals(1.5, $leave1->n1);
    }

    private function setProperty($object, $property, $value)
    {
        $reflection = new \ReflectionClass($object);
        $property = $reflection->getProperty($property);
        $property->setValue($object, $value);
        return $object;
    }
}
