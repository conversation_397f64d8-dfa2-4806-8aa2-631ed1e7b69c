<?php

namespace Tests\Unit;

use App\Http\Controllers\Api\v1\Leaves\LeavesController;
use App\Models\Api\Client;
use App\Models\Api\LeaveType;
use App\Models\Api\Profile;
use App\Models\Api\Site;
use App\Models\Api\User;
use App\Models\Api\UserLeaveCount;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class UpdateUserLeaveCountTest extends TestCase
{
    use DatabaseTransactions;

    protected $controller;
    protected $user;
    protected $leaveType;

    protected function setUp(): void
    {
        parent::setUp();

        $this->controller = new LeavesController();
        $this->user = $this->createUserWithRelations();
        $this->leaveType = $this->createLeaveType();
    }

    public function test_update_user_leave_count_without_previous_year(): void
    {
        $leaveTypeNoPay = LeaveType::factory()->create([
            'client_id' => $this->user->site->client_id,
            'needs_count' => true,
            'is_pay' => false
        ]);
        
        $this->createUserLeaveCount($this->user->id, $leaveTypeNoPay->id, false, true, 10);

        $initialCount = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $leaveTypeNoPay->id)
            ->where('is_last_year', false)
            ->first();

        $this->controller->updateUserLeaveCount(
            $leaveTypeNoPay->id,
            3.0,
            $this->user->id,
            null,
            3.0
        );

        $updatedCount = $initialCount->fresh();
        $this->assertEquals(3.0, $updatedCount->taken);
        $this->assertEquals(7.0, $updatedCount->balance);
    }

    public function test_update_user_leave_count_with_previous_year(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 10, 5);

        $nCount = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $this->leaveType->id)
            ->where('is_last_year', false)
            ->first();

        $n1Count = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $this->leaveType->id)
            ->where('is_last_year', true)
            ->first();

        $this->controller->updateUserLeaveCount(
            $this->leaveType->id,
            4.0,
            $this->user->id,
            2.0,
            2.0
        );

        $nCount->refresh();
        $n1Count->refresh();

        $this->assertEquals(2.0, $nCount->taken);
        $this->assertEquals(8.0, $nCount->balance);
        $this->assertEquals(2.0, $n1Count->taken);
        $this->assertEquals(3.0, $n1Count->balance);
    }

    public function test_update_user_leave_count_with_zero_values(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 10, 5);

        $this->controller->updateUserLeaveCount(
            $this->leaveType->id,
            0.0,
            $this->user->id,
            0.0,
            0.0
        );

        $nCount = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $this->leaveType->id)
            ->where('is_last_year', false)
            ->first();

        $n1Count = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $this->leaveType->id)
            ->where('is_last_year', true)
            ->first();

        $this->assertEquals(0.0, $nCount->taken);
        $this->assertEquals(10.0, $nCount->balance);
        $this->assertEquals(0.0, $n1Count->taken);
        $this->assertEquals(5.0, $n1Count->balance);
    }

    public function test_update_user_leave_count_with_fractional_values(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 7.5, 2.25);

        $this->controller->updateUserLeaveCount(
            $this->leaveType->id,
            3.75,
            $this->user->id,
            1.5,
            2.25
        );

        $nCount = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $this->leaveType->id)
            ->where('is_last_year', false)
            ->first();

        $n1Count = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $this->leaveType->id)
            ->where('is_last_year', true)
            ->first();

        $this->assertEquals(2.25, $nCount->taken);
        $this->assertEquals(5.25, $nCount->balance);
        $this->assertEquals(1.5, $n1Count->taken);
        $this->assertEquals(0.75, $n1Count->balance);
    }

    public function test_update_user_leave_count_with_large_values(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 10, 5);

        $this->controller->updateUserLeaveCount(
            $this->leaveType->id,
            20.0,
            $this->user->id,
            5.0,
            15.0
        );

        $nCount = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $this->leaveType->id)
            ->where('is_last_year', false)
            ->first();

        $n1Count = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $this->leaveType->id)
            ->where('is_last_year', true)
            ->first();

        $this->assertEquals(15.0, $nCount->taken);
        $this->assertEquals(-5.0, $nCount->balance);
        $this->assertEquals(5.0, $n1Count->taken);
        $this->assertEquals(0.0, $n1Count->balance);
    }

    public function test_update_user_leave_count_multiple_calls_accumulate(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 10, 5);

        // Premier appel
        $this->controller->updateUserLeaveCount(
            $this->leaveType->id,
            3.0,
            $this->user->id,
            1.0,
            2.0
        );

        // Deuxième appel
        $this->controller->updateUserLeaveCount(
            $this->leaveType->id,
            2.5,
            $this->user->id,
            1.5,
            1.0
        );

        $nCount = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $this->leaveType->id)
            ->where('is_last_year', false)
            ->first();

        $n1Count = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $this->leaveType->id)
            ->where('is_last_year', true)
            ->first();

        // Vérification que les valeurs s'accumulent
        $this->assertEquals(3.0, $nCount->taken); // 2.0 + 1.0
        $this->assertEquals(7.0, $nCount->balance); // 10 - 3.0
        $this->assertEquals(2.5, $n1Count->taken); // 1.0 + 1.5
        $this->assertEquals(2.5, $n1Count->balance); // 5 - 2.5
    }

    public function test_update_user_leave_count_only_n_when_no_n1_counter(): void
    {
        $leaveTypeNoPay = LeaveType::factory()->create([
            'client_id' => $this->user->site->client_id,
            'needs_count' => true,
            'is_pay' => false
        ]);
        
        // Créer seulement le compteur N (pas de N-1)
        UserLeaveCount::updateOrCreate(
            ['user_id' => $this->user->id, 'leave_type_id' => $leaveTypeNoPay->id, 'is_last_year' => false],
            ['balance' => 15, 'acquired' => 15, 'taken' => 0]
        );

        $this->controller->updateUserLeaveCount(
            $leaveTypeNoPay->id,
            8.0,
            $this->user->id,
            null,
            8.0
        );

        $nCount = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $leaveTypeNoPay->id)
            ->where('is_last_year', false)
            ->first();

        // Vérifier qu'aucun compteur N-1 n'a été créé
        $n1Count = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $leaveTypeNoPay->id)
            ->where('is_last_year', true)
            ->first();

        $this->assertEquals(8.0, $nCount->taken);
        $this->assertEquals(7.0, $nCount->balance);
        $this->assertNull($n1Count);
    }

    public function test_update_user_leave_count_with_null_n1_value(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 10, 5);

        $this->controller->updateUserLeaveCount(
            $this->leaveType->id,
            4.0,
            $this->user->id,
            null,
            4.0
        );

        $nCount = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $this->leaveType->id)
            ->where('is_last_year', false)
            ->first();

        $n1Count = UserLeaveCount::where('user_id', $this->user->id)
            ->where('leave_type_id', $this->leaveType->id)
            ->where('is_last_year', true)
            ->first();

        $this->assertEquals(4.0, $nCount->taken);
        $this->assertEquals(6.0, $nCount->balance);
        $this->assertEquals(0.0, $n1Count->taken); // N'a pas changé
        $this->assertEquals(5.0, $n1Count->balance); // N'a pas changé
    }

    private function createUserWithRelations(): User
    {
        $client = Client::factory()->create();
        $site = Site::factory()->create(['client_id' => $client->getKey()]);
        $profile = Profile::firstOrCreate(['label' => 'ADMINISTRATEUR']);

        $user = User::factory()->create([
            'site_id' => $site->getKey(),
            'profile_id' => $profile->getKey(),
            'client_uuid' => $site->client?->uuid,
        ]);

        $user->load('site');

        return $user;
    }

    private function createLeaveType(): LeaveType
    {
        return LeaveType::factory()->create([
            'client_id' => $this->user->site->client_id,
            'needs_count' => true,
            'is_pay' => true
        ]);
    }

    private function createUserLeaveCount(int $userId, int $leaveTypeId, bool $createN1 = true, bool $createN = true, float $nBalance = 10, float $n1Balance = 5): void
    {
        if ($createN) {
            UserLeaveCount::updateOrCreate(
                ['user_id' => $userId, 'leave_type_id' => $leaveTypeId, 'is_last_year' => false],
                ['balance' => $nBalance, 'acquired' => $nBalance, 'taken' => 0]
            );
        }

        if ($createN1) {
            UserLeaveCount::updateOrCreate(
                ['user_id' => $userId, 'leave_type_id' => $leaveTypeId, 'is_last_year' => true],
                ['balance' => $n1Balance, 'acquired' => $n1Balance, 'taken' => 0]
            );
        }
    }


}
