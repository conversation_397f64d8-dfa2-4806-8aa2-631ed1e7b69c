<?php

namespace Tests\Unit;

use App\Http\Controllers\Api\v1\Leaves\LeavesController;
use App\Models\Api\Client;
use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\User;
use App\Models\Api\Site;
use App\Models\Api\Profile;
use Carbon\Carbon;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Mockery;
use Tests\TestCase;

class LeaveTest extends TestCase
{
    public function testSameStartAndEndDates(): void
    {
        $client = Client::factory()->create();
        $site = Site::factory()->create(['client_id' => $client->id]);
        $user = User::factory()->create(['site_id' => $site->id]);
        $leaveType = LeaveType::factory()->create(['client_id' => $client->id]);
        $date = Carbon::create(2019, 5, 3);

        $leave = Leave::factory()->create([
            'user_id' => $user->id,
            'leave_type_id' => $leaveType->id,
            'start_date' => $date,
            'end_date' => $date,
        ]);

        $this->assertEquals($leave->start_date, $leave->end_date);
    }
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    protected function setProperty($object, $property, $value)
    {
        $reflection = new \ReflectionClass($object);
        $property = $reflection->getProperty($property);
        $property->setAccessible(true);
        $property->setValue($object, $value);
        return $object;
    }

    public function testEndDateIsBeforeStartDate(): void
    {
        $client = Client::factory()->create();
        $site = Site::factory()->create(['client_id' => $client->id]);
        $profile = Profile::firstOrCreate(['label' => 'ADMINISTRATEUR']);
        $user = User::factory()->create([
            'site_id' => $site->id,
            'profile_id' => $profile->id,
        ]);
        $user->load('site');

        $leaveType = LeaveType::factory()->create([
            'client_id' => $client->id,
            'is_attachment_required' => false,
            'needs_count' => false
        ]);

        $startDate = Carbon::create(2025, 6, 4);
        $endDate = Carbon::create(2025, 6, 3);

        $payload = [
            'users' => [$user->id],
            'leave_type_id' => $leaveType->id,
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate->toDateString(),
            'leave_type_sub_family_id' => null,
            'comment' => 'Test commentaire',
            'duration' => 1,
            'usersNotStoreLeave' => [],
        ];
        $requestData = ['data' => json_encode($payload)];

        Mockery::getConfiguration()->allowMockingNonExistentMethods(true);
        $toolsMock = Mockery::mock('alias:App\Lib\Tools');
        $toolsMock->shouldReceive('getCurrentUserWithUuid')
            ->andReturnUsing(fn() => auth()->user());

        $controller = new LeavesController();
        $this->setProperty($controller, 'currentUser', $user);
        $this->setProperty($controller, 'clientId', $user->site->client_id);

        $request = new Request([], $requestData);
        $this->actingAs($user, 'jwt');

        $response = $controller->store($request);

        $this->assertEquals(400, $response->getStatusCode());
        $responseData = $response->getData(true);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertEquals(__('warnings.ErrorStartDateAboveEndDate'), $responseData['message']);
    }
    public function testStoreLeave(): void
    {
        $client = Client::factory()->create();
        $site = Site::factory()->create(['client_id' => $client->id]);
        $profile = Profile::firstOrCreate(['label' => 'ADMINISTRATEUR']);
        $user = User::factory()->create([
            'site_id' => $site->id,
            'profile_id' => $profile->id,
        ]);
        $user->load('site');

        $leaveType = LeaveType::factory()->create([
            'client_id' => $client->id,
            'is_attachment_required' => false,
            'needs_count' => false
        ]);

        $startDate = Carbon::create(2025, 6, 4);
        $endDate = Carbon::create(2025, 6, 5);

        $payload = [
            'users' => [$user->id],
            'leave_type_id' => $leaveType->id,
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate->toDateString(),
            'leave_type_sub_family_id' => null,
            'comment' => 'Test commentaire',
            'duration' => 1,
            'usersNotStoreLeave' => [],
        ];
        $requestData = ['data' => json_encode($payload)];

        Mockery::getConfiguration()->allowMockingNonExistentMethods(true);
        $toolsMock = Mockery::mock('alias:App\Lib\Tools');
        $toolsMock->shouldReceive('getCurrentUserWithUuid')
            ->andReturnUsing(fn() => auth()->user());

        $controller = new LeavesController();
        $this->setProperty($controller, 'currentUser', $user);
        $this->setProperty($controller, 'clientId', $user->site->client_id);

        $request = new Request([], $requestData);
        $this->actingAs($user, 'jwt');

        $response = $controller->store($request);
        $this->assertEquals(200, $response->getStatusCode());
        $responseData = $response->getData(true);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertEquals([__('messages.SuccessStoreLeave')], $responseData['message']);
    }

    public function testNeedAttachmentCantJustifyLaterError(): void
    {
        $client = Client::factory()->create();
        $site = Site::factory()->create(['client_id' => $client->id]);
        $profile = Profile::firstOrCreate(['label' => 'ADMINISTRATEUR']);
        $user = User::factory()->create([
            'site_id' => $site->id,
            'profile_id' => $profile->id,
        ]);
        $user->load('site');

        $leaveType = LeaveType::factory()->create([
            'client_id' => $client->id,
            'is_attachment_required' => true,
            'needs_count' => false,
            'can_justify_later' => false,
        ]);
        $startDate = Carbon::create(2025, 6, 4);
        $endDate = Carbon::create(2025, 6, 5);

        $payload = [
            'users' => [$user->id],
            'leave_type_id' => $leaveType->id,
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate->toDateString(),
            'leave_type_sub_family_id' => null,
            'comment' => 'Test commentaire',
            'duration' => 1,
            'usersNotStoreLeave' => [],
        ];
        $requestData = ['data' => json_encode($payload)];

        Mockery::getConfiguration()->allowMockingNonExistentMethods(true);
        $toolsMock = Mockery::mock('alias:App\Lib\Tools');
        $toolsMock->shouldReceive('getCurrentUserWithUuid')
            ->andReturnUsing(fn() => auth()->user());

        $controller = new LeavesController();
        $this->setProperty($controller, 'currentUser', $user);
        $this->setProperty($controller, 'clientId', $user->site->client_id);

        $request = new Request([], $requestData);
        $this->actingAs($user, 'jwt');

        $response = $controller->store($request);
        $this->assertEquals(400, $response->getStatusCode());
        $responseData = $response->getData(true);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertEquals(__('warnings.FileRequired'), $responseData['message']);
    }
    public function testDontNeedAttachment(): void
    {
        $client = Client::factory()->create();
        $site = Site::factory()->create(['client_id' => $client->id]);
        $profile = Profile::firstOrCreate(['label' => 'ADMINISTRATEUR']);
        $user = User::factory()->create([
            'site_id' => $site->id,
            'profile_id' => $profile->id,
        ]);
        $user->load('site');

        $leaveType = LeaveType::factory()->create([
            'client_id' => $client->id,
            'is_attachment_required' => false,
            'needs_count' => false,

        ]);
        $startDate = Carbon::create(2025, 6, 4);
        $endDate = Carbon::create(2025, 6, 5);

        $payload = [
            'users' => [$user->id],
            'leave_type_id' => $leaveType->id,
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate->toDateString(),
            'leave_type_sub_family_id' => null,
            'comment' => 'Test commentaire',
            'duration' => 1,
            'usersNotStoreLeave' => [],
        ];
        $requestData = ['data' => json_encode($payload)];

        Mockery::getConfiguration()->allowMockingNonExistentMethods(true);
        $toolsMock = Mockery::mock('alias:App\Lib\Tools');
        $toolsMock->shouldReceive('getCurrentUserWithUuid')
            ->andReturnUsing(fn() => auth()->user());

        $controller = new LeavesController();
        $this->setProperty($controller, 'currentUser', $user);
        $this->setProperty($controller, 'clientId', $user->site->client_id);

        $request = new Request([], $requestData);
        $this->actingAs($user, 'jwt');

        $response = $controller->store($request);
        $this->assertEquals(200, $response->getStatusCode());
        $responseData = $response->getData(true);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertEquals([__('messages.SuccessStoreLeave')], $responseData['message']);
        $this->assertDatabaseHas('leaves', [
            'user_id' => $user->id,
            'leave_type_id' => $leaveType->id,
            'attachment_name' => null,
            'attachment_path' => null,
        ]);
        $leaveData = $responseData['data'];
        $this->assertArrayHasKey('attachment_name', $leaveData);
        $this->assertNull($leaveData['attachment_name']);
        $this->assertArrayHasKey('attachment_path', $leaveData);
        $this->assertNull($leaveData['attachment_path']);
    }
    public function testNeedAttachmentCanJustifyLater(): void
    {
        $client = Client::factory()->create();
        $site = Site::factory()->create(['client_id' => $client->id]);
        $profile = Profile::firstOrCreate(['label' => 'ADMINISTRATEUR']);
        $user = User::factory()->create([
            'site_id' => $site->id,
            'profile_id' => $profile->id,
        ]);
        $user->load('site');

        $leaveType = LeaveType::factory()->create([
            'client_id' => $client->id,
            'is_attachment_required' => true,
            'needs_count' => false,
            'can_justify_later'=>true
        ]);
        $startDate = Carbon::create(2025, 6, 4);
        $endDate = Carbon::create(2025, 6, 5);

        $payload = [
            'users' => [$user->id],
            'leave_type_id' => $leaveType->id,
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate->toDateString(),
            'leave_type_sub_family_id' => null,
            'comment' => 'Test commentaire',
            'duration' => 1,
            'usersNotStoreLeave' => [],
        ];
        $requestData = ['data' => json_encode($payload)];

        Mockery::getConfiguration()->allowMockingNonExistentMethods(true);
        $toolsMock = Mockery::mock('alias:App\Lib\Tools');
        $toolsMock->shouldReceive('getCurrentUserWithUuid')
            ->andReturnUsing(fn() => auth()->user());

        $controller = new LeavesController();
        $this->setProperty($controller, 'currentUser', $user);
        $this->setProperty($controller, 'clientId', $user->site->client_id);

        $request = new Request([], $requestData);
        $this->actingAs($user, 'jwt');

        $response = $controller->store($request);
        $this->assertEquals(200, $response->getStatusCode());
        $responseData = $response->getData(true);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertEquals([__('messages.SuccessStoreLeave')], $responseData['message']);
        $this->assertDatabaseHas('leaves', [
            'user_id' => $user->id,
            'leave_type_id' => $leaveType->id,
            'attachment_name' => null,
            'attachment_path' => null,
        ]);
        $leaveData = $responseData['data'];
        $this->assertArrayHasKey('attachment_name', $leaveData);
        $this->assertNull($leaveData['attachment_name']);
        $this->assertArrayHasKey('attachment_path', $leaveData);
        $this->assertNull($leaveData['attachment_path']);
    }
    public function testAttachmentStore(): void
    {
        $client = Client::factory()->create();
        $site = Site::factory()->create(['client_id' => $client->id]);
        $profile = Profile::firstOrCreate(['label' => 'ADMINISTRATEUR']);
        $user = User::factory()->create(['site_id' => $site->id, 'profile_id' => $profile->id]);
        $user->load('site');

        $leaveType = LeaveType::factory()->create([
            'client_id' => $client->id,
            'is_attachment_required' => true,
            'needs_count' => false
        ]);

        $startDate = Carbon::create(2025, 7, 1);
        $endDate = Carbon::create(2025, 7, 2);

        $payload = [
            'users' => [$user->id],
            'leave_type_id' => $leaveType->id,
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate->toDateString(),
            'leave_type_sub_family_id' => null,
            'comment' => 'Test avec fichier',
            'duration' => 1,
            'usersNotStoreLeave' => [],
        ];
        $requestData = ['data' => json_encode($payload)];

        $fakeFile = UploadedFile::fake()->create('document.pdf', 1)->mimeType('application/pdf');

        Mockery::getConfiguration()->allowMockingNonExistentMethods(true);
        $toolsMock = Mockery::mock('alias:App\Lib\Tools');
        $toolsMock->shouldReceive('getCurrentUserWithUuid')
            ->andReturnUsing(fn() => auth()->user());

        $controller = new LeavesController();
        $this->setProperty($controller, 'currentUser', $user);
        $this->setProperty($controller, 'clientId', $user->site->client_id);

        Storage::fake('s3');

        $request = new Request([], $requestData, [], [], ['attachment' => $fakeFile]);
        $this->actingAs($user, 'jwt');

        $response = $controller->store($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $responseData = $response->getData(true);
        $this->assertEquals([__('messages.SuccessStoreLeave')], $responseData['message']);

        $createdLeave = Leave::where('user_id', $user->id)
            ->where('leave_type_id', $leaveType->id)
            ->orderBy('id', 'desc')
            ->first();

        $this->assertNotNull($createdLeave->attachment_name);
        $this->assertNotNull($createdLeave->attachment_path);
        $this->assertEquals($fakeFile->getClientOriginalName(), $createdLeave->attachment_name);
        $this->assertStringStartsWith('leave_attachment/leaveId-', $createdLeave->attachment_path);
        $this->assertStringEndsWith('.pdf', $createdLeave->attachment_path);

        Storage::disk('s3')->assertExists($createdLeave->attachment_path);

        $leaveData = $responseData['data'];
        $this->assertNotNull($leaveData);
        $this->assertEquals($createdLeave->attachment_name, $leaveData['attachment_name'] ?? null);
        $this->assertEquals($createdLeave->attachment_path, $leaveData['attachment_path'] ?? null);
    }
}
