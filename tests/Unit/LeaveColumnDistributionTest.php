<?php

namespace Tests\Unit;

use App\Http\Controllers\Api\v1\Leaves\LeavesController;
use App\Lib\Tools;
use App\Models\Api\Client;
use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\Profile;
use App\Models\Api\Site;
use App\Models\Api\Status;
use App\Models\Api\User;
use App\Models\Api\UserLeaveCount;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Str;
use Mockery;
use Tests\TestCase;

class LeaveColumnDistributionTest extends TestCase
{
    use DatabaseTransactions;

    protected $toolsMock;
    protected $controller;
    protected $user;
    protected $leaveType;

    protected function setUp(): void
    {
        parent::setUp();

        $this->toolsMock = Mockery::mock(Tools::class);
        $this->toolsMock->shouldReceive('getCurrentUserWithUuid')
            ->andReturnUsing(fn() => auth()->user());

        app()->instance(Tools::class, $this->toolsMock);

        $this->controller = new LeavesController();
        $this->user = $this->createUserWithRelations();
        $this->leaveType = $this->createLeaveType();
        
        $this->setProperty($this->controller, 'currentUser', $this->user);
        $this->setProperty($this->controller, 'clientId', $this->user->site->client_id);
    }

    public function test_distribution_when_no_previous_year_counter_exists(): void
    {
        $leaveTypeNoPay = LeaveType::factory()->create([
            'client_id' => $this->user->site->client_id,
            'needs_count' => true,
            'is_pay' => false
        ]);

        $this->createUserLeaveCount($this->user->id, $leaveTypeNoPay->id, false, true);

        $startDate = Carbon::create(2024, 3, 15);
        $duration = 5.0;

        [$n, $n1, $outDay, $hasAlert] = $this->controller->updateNandN1(
            $startDate,
            $leaveTypeNoPay->id,
            $duration,
            $this->user->id,
            false,
            1,
            $errors
        );

        $this->assertEquals($duration, $n);
        $this->assertNull($n1);
        $this->assertEquals(0, $outDay);
        $this->assertFalse($hasAlert);
    }

    public function test_distribution_when_leave_starts_after_june(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true);

        Carbon::setTestNow(Carbon::create(2024, 8, 1));
        $startDate = Carbon::create(2025, 7, 15);
        $duration = 3.0;

        [$n, $n1, $outDay, $hasAlert] = $this->controller->updateNandN1(
            $startDate,
            $this->leaveType->id,
            $duration,
            $this->user->id,
            false,
            1,
            $errors
        );

        $this->assertEquals($duration, $n);
        $this->assertNull($n1);
        $this->assertEquals(0, $outDay);
        $this->assertFalse($hasAlert);
    }

    public function test_distribution_when_leave_starts_before_june_with_sufficient_n1_balance(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true);

        Carbon::setTestNow(Carbon::create(2024, 8, 1));
        $startDate = Carbon::create(2024, 4, 15);
        $duration = 3.0;

        [$n, $n1, $outDay, $hasAlert] = $this->controller->updateNandN1(
            $startDate,
            $this->leaveType->id,
            $duration,
            $this->user->id,
            false,
            1,
            $errors
        );

        $this->assertNull($n);
        $this->assertEquals($duration, $n1);
        $this->assertEquals(0, $outDay);
        $this->assertFalse($hasAlert);
    }

    public function test_distribution_when_leave_starts_before_june_with_insufficient_n1_balance(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 10, 2);

        Carbon::setTestNow(Carbon::create(2024, 8, 1));
        $startDate = Carbon::create(2024, 4, 15);
        $duration = 5.0;

        [$n, $n1, $outDay, $hasAlert] = $this->controller->updateNandN1(
            $startDate,
            $this->leaveType->id,
            $duration,
            $this->user->id,
            false,
            1,
            $errors
        );

        $this->assertEquals(3.0, $n);
        $this->assertEquals(2.0, $n1);
        $this->assertEquals(0, $outDay);
        $this->assertTrue($hasAlert);
    }

    public function test_distribution_with_existing_future_leaves(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 10, 5);

        $this->createExistingLeave(2.0, 1.0);

        Carbon::setTestNow(Carbon::create(2024, 8, 1));
        $startDate = Carbon::create(2024, 4, 15);
        $duration = 3.0;

        [$n, $n1, $outDay, $hasAlert] = $this->controller->updateNandN1(
            $startDate,
            $this->leaveType->id,
            $duration,
            $this->user->id,
            false,
            1,
            $errors
        );

        $this->assertNull($n);
        $this->assertEquals(3.0, $n1);
        $this->assertEquals(0, $outDay);
        $this->assertFalse($hasAlert);
    }

    public function test_distribution_edge_case_exactly_on_june_boundary(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true);

        Carbon::setTestNow(Carbon::create(2024, 8, 1));
        $startDate = Carbon::create(2025, 6, 1);
        $duration = 2.0;

        [$n, $n1, $outDay, $hasAlert] = $this->controller->updateNandN1(
            $startDate,
            $this->leaveType->id,
            $duration,
            $this->user->id,
            false,
            1,
            $errors
        );

        $this->assertNull($n);
        $this->assertEquals($duration, $n1);
        $this->assertEquals(0, $outDay);
        $this->assertFalse($hasAlert);
    }

    public function test_distribution_with_zero_duration(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true);

        $startDate = Carbon::create(2024, 4, 15);
        $duration = 0.0;

        [$n, $n1, $outDay, $hasAlert] = $this->controller->updateNandN1(
            $startDate,
            $this->leaveType->id,
            $duration,
            $this->user->id,
            false,
            1,
            $errors
        );

        $this->assertNull($n);
        $this->assertEquals(0.0, $n1);
        $this->assertEquals(0, $outDay);
        $this->assertFalse($hasAlert);
    }

    public function test_distribution_with_fractional_duration(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 10, 2.5);

        Carbon::setTestNow(Carbon::create(2024, 8, 1));
        $startDate = Carbon::create(2024, 4, 15);
        $duration = 3.5;

        [$n, $n1, $outDay, $hasAlert] = $this->controller->updateNandN1(
            $startDate,
            $this->leaveType->id,
            $duration,
            $this->user->id,
            false,
            1,
            $errors
        );

        $this->assertEquals(1.0, $n);
        $this->assertEquals(2.5, $n1);
        $this->assertEquals(0, $outDay);
        $this->assertTrue($hasAlert);
    }

    private function createUserWithRelations(array $userAttributes = []): User
    {
        $client = Client::factory()->create();
        $site = Site::factory()->create(['client_id' => $client->getKey()]);
        $profile = Profile::firstOrCreate(['label' => 'ADMINISTRATEUR']);

        $user = new User(array_merge([
            'site_id' => $site->getKey(),
            'profile_id' => $profile->getKey(),
            'uuid' => Str::uuid(),
            'client_uuid' => $site->client?->uuid,
            'firstname' => fake()->firstName,
            'lastname' => fake()->lastName,
            'email' => fake()->companyEmail,
            'picture_path' => fake()->word.'/'.fake()->word,
            'license_path' => fake()->word.'/'.fake()->word,
            'matricule' => fake()->randomNumber(5, true),
            'can_receive_mails' => fake()->boolean
        ], $userAttributes));

        $user->save();
        $user->load('site');

        return $user;
    }

    private function createLeaveType(): LeaveType
    {
        return LeaveType::factory()->create([
            'client_id' => $this->user->site->client_id,
            'needs_count' => true,
            'is_pay' => true
        ]);
    }

    private function createUserLeaveCount(int $userId, int $leaveTypeId, bool $createN1 = true, bool $createN = true, float $nBalance = 10, float $n1Balance = 5): void
    {
        if ($createN) {
            UserLeaveCount::updateOrCreate(
                ['user_id' => $userId, 'leave_type_id' => $leaveTypeId, 'is_last_year' => false],
                ['balance' => $nBalance, 'acquired' => $nBalance, 'taken' => 0]
            );
        }

        if ($createN1) {
            UserLeaveCount::updateOrCreate(
                ['user_id' => $userId, 'leave_type_id' => $leaveTypeId, 'is_last_year' => true],
                ['balance' => $n1Balance, 'acquired' => $n1Balance, 'taken' => 0]
            );
        }
    }

    private function createExistingLeave(float $n, float $n1): Leave
    {
        $status = Status::firstOrCreate(['tag' => 'VALIDATED'], ['name' => 'Validé']);
        
        return Leave::factory()->create([
            'user_id' => $this->user->id,
            'leave_type_id' => $this->leaveType->id,
            'status_id' => $status->id,
            'n' => $n,
            'n1' => $n1,
            'duration' => $n + $n1
        ]);
    }

    public function test_distribution_with_large_duration(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true, 10, 5);

        Carbon::setTestNow(Carbon::create(2024, 8, 1));
        $startDate = Carbon::create(2024, 4, 15);
        $duration = 20.0;

        [$n, $n1, $outDay, $hasAlert] = $this->controller->updateNandN1(
            $startDate,
            $this->leaveType->id,
            $duration,
            $this->user->id,
            false,
            1,
            $errors
        );

        $this->assertEquals(15.0, $n);
        $this->assertEquals(5.0, $n1);
        $this->assertEquals(-5, $outDay);
        $this->assertTrue($hasAlert);
    }

    public function test_distribution_when_current_time_is_before_june(): void
    {
        $this->createUserLeaveCount($this->user->id, $this->leaveType->id, true, true);

        Carbon::setTestNow(Carbon::create(2024, 3, 1));
        $startDate = Carbon::create(2024, 7, 15);
        $duration = 3.0;

        [$n, $n1, $outDay, $hasAlert] = $this->controller->updateNandN1(
            $startDate,
            $this->leaveType->id,
            $duration,
            $this->user->id,
            false,
            1,
            $errors
        );

        $this->assertEquals($duration, $n);
        $this->assertNull($n1);
        $this->assertEquals(0, $outDay);
        $this->assertFalse($hasAlert);
    }

    public function test_distribution_with_out_day_calculation(): void
    {
        $leaveTypeNoPay = LeaveType::factory()->create([
            'client_id' => $this->user->site->client_id,
            'needs_count' => true,
            'is_pay' => false
        ]);

        $this->createUserLeaveCount($this->user->id, $leaveTypeNoPay->id, false, true, 3);

        $startDate = Carbon::create(2024, 4, 15);
        $duration = 5.0;

        [$n, $n1, $outDay, $hasAlert] = $this->controller->updateNandN1(
            $startDate,
            $leaveTypeNoPay->id,
            $duration,
            $this->user->id,
            false,
            1,
            $errors
        );

        $this->assertEquals($duration, $n);
        $this->assertNull($n1);
        $this->assertEquals(-2, $outDay);
        $this->assertFalse($hasAlert);
    }

    private function setProperty($object, $property, $value)
    {
        $reflection = new \ReflectionClass($object);
        $property = $reflection->getProperty($property);
        $property->setValue($object, $value);
        return $object;
    }
}
