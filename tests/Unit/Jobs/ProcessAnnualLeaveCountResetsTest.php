<?php

namespace Tests\Unit\Jobs;

use App\Jobs\ProcessAnnualLeaveCountResets;
use App\Models\Api\Client;
use App\Models\Api\LeaveType;
use App\Models\Api\Profile;
use App\Models\Api\Site;
use App\Models\Api\UserLeaveCount;
use App\Models\Api\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Tests\TestCase;

class ProcessAnnualLeaveCountResetsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
    }

    /** @test */
    public function it_resets_counters_for_leave_types_marked_for_auto_reset()
    {
        $client = Client::create(['name' => 'Test Client ' . Str::random(5), 'uuid' => Str::uuid()]);
        $profile = Profile::create(['label' => 'ADMINISTRATEUR']);
        $site = Site::create(['client_id' => $client->getKey(), 'name' => 'Test Site ' . Str::random(5), 'country' => 'France', 'country_alpha' => 'FR']);
        $user = User::create(['profile_id' => $profile->getKey(), 'site_id' => $site->getKey(), 'uuid' => Str::uuid(), 'firstname' => 'Test', 'lastname' => 'User', 'email' => 'testuser' . Str::random(5) . '@example.com']);

        $ltToReset1 = LeaveType::create(['name' => 'CP Auto Reset 1', 'client_id' => $client->getKey(), 'auto_resets_annually' => true, 'leave_code' => 'CP_AUTO_1_' . Str::random(3), 'is_monthly' => false, 'color' => '#FF0000', 'order_appearance' => 1, 'needs_count' => true]);
        $ltToReset2 = LeaveType::create(['name' => 'CP Auto Reset 2', 'client_id' => $client->getKey(), 'auto_resets_annually' => true, 'leave_code' => 'CP_AUTO_2_' . Str::random(3), 'is_monthly' => false, 'color' => '#00FF00', 'order_appearance' => 2, 'needs_count' => true]);
        $ltNoReset = LeaveType::create(['name' => 'CP No Reset', 'client_id' => $client->getKey(), 'auto_resets_annually' => false, 'leave_code' => 'CP_NO_RESET_' . Str::random(3), 'is_monthly' => false, 'color' => '#0000FF', 'order_appearance' => 3, 'needs_count' => true]);

        $initialRhUpdate1 = Carbon::now()->subDay()->startOfSecond();
        $counter1User1 = UserLeaveCount::create(['user_id' => $user->getKey(), 'leave_type_id' => $ltToReset1->getKey(), 'is_last_year' => false, 'acquired' => 25, 'taken' => 5, 'balance' => 20, 'rh_update' => $initialRhUpdate1]);

        $initialRhUpdate2 = Carbon::now()->subDay()->startOfSecond();
        $counter2User1 = UserLeaveCount::create(['user_id' => $user->getKey(), 'leave_type_id' => $ltToReset2->getKey(), 'is_last_year' => false, 'acquired' => 10, 'taken' => 10, 'balance' => 0, 'rh_update' => $initialRhUpdate2]);

        $initialRhUpdateNoReset = Carbon::now()->subDay()->startOfSecond();
        $counterNoResetUser1 = UserLeaveCount::create(['user_id' => $user->getKey(), 'leave_type_id' => $ltNoReset->getKey(), 'is_last_year' => false, 'acquired' => 15, 'taken' => 3, 'balance' => 12, 'rh_update' => $initialRhUpdateNoReset]);

        $job = new ProcessAnnualLeaveCountResets();
        $job->handle();

        $updatedCounter1 = $counter1User1->fresh();
        $this->assertEquals(0, $updatedCounter1->taken);
        $this->assertEquals(25, $updatedCounter1->balance);
        $this->assertTrue(Carbon::parse($updatedCounter1->rh_update)->gt($initialRhUpdate1));

        $updatedCounter2 = $counter2User1->fresh();
        $this->assertEquals(0, $updatedCounter2->taken);
        $this->assertEquals(10, $updatedCounter2->balance);
        $this->assertTrue(Carbon::parse($updatedCounter2->rh_update)->gt($initialRhUpdate2));

        $updatedCounterNoReset = $counterNoResetUser1->fresh();
        $this->assertEquals(3, $updatedCounterNoReset->taken);
        $this->assertEquals(12, $updatedCounterNoReset->balance);
        $this->assertEquals($initialRhUpdateNoReset->timestamp, Carbon::parse($updatedCounterNoReset->rh_update)->timestamp);
    }

    /** @test */
    public function it_does_nothing_if_no_leave_types_are_marked_for_auto_reset()
    {
        $client = Client::create(['name' => 'Test Client B ' . Str::random(5), 'uuid' => Str::uuid()]);
        $profile = Profile::create(['label' => 'STANDARD']);
        $site = Site::create(['client_id' => $client->getKey(), 'name' => 'Test Site B ' . Str::random(5), 'country' => 'UK', 'country_alpha' => 'GB']);
        $user = User::create(['profile_id' => $profile->getKey(), 'site_id' => $site->getKey(), 'uuid' => Str::uuid(), 'firstname' => 'TestB', 'lastname' => 'UserB', 'email' => 'testuserb' . Str::random(5) . '@example.com']);

        $ltNoReset = LeaveType::create(['name' => 'CP No Reset Only', 'client_id' => $client->getKey(), 'auto_resets_annually' => false, 'leave_code' => 'CP_NO_RESET_ONLY_' . Str::random(3), 'is_monthly' => false, 'color' => '#ABCDEF', 'order_appearance' => 1, 'needs_count' => true]);

        $initialRhUpdate = Carbon::now()->subMonths(2)->startOfSecond();
        $counterBefore = UserLeaveCount::create(['user_id' => $user->getKey(), 'leave_type_id' => $ltNoReset->getKey(), 'is_last_year' => false, 'acquired' => 20, 'taken' => 5, 'balance' => 15, 'rh_update' => $initialRhUpdate]);

        Log::spy();

        $job = new ProcessAnnualLeaveCountResets();
        $job->handle();

        $updatedCounter = $counterBefore->fresh();
        $this->assertEquals(5, $updatedCounter->taken);
        $this->assertEquals(15, $updatedCounter->balance);
        $this->assertEquals($initialRhUpdate->timestamp, Carbon::parse($updatedCounter->rh_update)->timestamp);
    }

    /** @test */
    public function annual_reset_job_is_triggered_by_scheduler_on_june_1st_at_01am()
    {
        $client = Client::create(['name' => 'Test Client for Schedule', 'uuid' => Str::uuid()]);
        $profile = Profile::create(['label' => 'STANDARD']);
        $site = Site::create(['client_id' => $client->getKey(), 'name' => 'Site for Schedule', 'country' => 'FR', 'country_alpha' => 'FR']);
        $user = User::create(['profile_id' => $profile->getKey(), 'site_id' => $site->getKey(), 'uuid' => Str::uuid(), 'firstname' => 'Scheduled', 'lastname' => 'User', 'email' => '<EMAIL>']);
        $leaveTypeToReset = LeaveType::create(['name' => 'Scheduled CP Reset', 'client_id' => $client->getKey(), 'auto_resets_annually' => true, 'leave_code' => 'SCHED_CP', 'is_monthly' => false, 'color' => '#111111', 'order_appearance' => 1, 'needs_count' => true]);

        $initialRhUpdate = Carbon::now()->subYear();
        $counter = UserLeaveCount::create(['user_id' => $user->getKey(), 'leave_type_id' => $leaveTypeToReset->getKey(), 'is_last_year' => false, 'acquired' => 30, 'taken' => 10, 'balance' => 20, 'rh_update' => $initialRhUpdate]);

        $targetDate = Carbon::create(2025, 6, 1, 1, 0, 0, 'Europe/Paris');
        Carbon::setTestNow($targetDate);

        $this->artisan('schedule:run');

        $updatedCounter = $counter->fresh();
        $this->assertEquals(0, $updatedCounter->taken, "Le champ 'taken' aurait dû être réinitialisé à 0.");
        $this->assertEquals(30, $updatedCounter->balance, "Le champ 'balance' aurait dû être réinitialisé à la valeur 'acquired'.");
    }

    /** @test */
    public function annual_reset_job_is_not_triggered_by_scheduler_on_june_1st_at_02am()
    {
        $client = Client::create(['name' => 'Test Client No Trigger', 'uuid' => Str::uuid()]);
        $leaveTypeToReset = LeaveType::create(['name' => 'Scheduled CP No Trigger', 'client_id' => $client->getKey(), 'auto_resets_annually' => true, 'leave_code' => 'SCHED_CP_NT', 'is_monthly' => false, 'color' => '#222222', 'order_appearance' => 1, 'needs_count' => true]);
        $profile = Profile::create(['label' => 'STANDARD']);
        $site = Site::create(['client_id' => $client->getKey(), 'name' => 'Site No Trigger', 'country' => 'FR', 'country_alpha' => 'FR']);
        $user = User::create(['profile_id' => $profile->getKey(), 'site_id' => $site->getKey(), 'uuid' => Str::uuid(), 'firstname' => 'NoTrigger', 'lastname' => 'User', 'email' => '<EMAIL>']);

        $exactInitialRhUpdate = Carbon::now('UTC')->subYear()->startOfSecond();
        $counterNotToChange = UserLeaveCount::create(['user_id' => $user->getKey(), 'leave_type_id' => $leaveTypeToReset->getKey(), 'is_last_year' => false, 'acquired' => 25, 'taken' => 5, 'balance' => 20, 'rh_update' => $exactInitialRhUpdate]);

        $targetDateForScheduler = Carbon::create(2025, 6, 1, 2, 0, 0, 'Europe/Paris');
        Carbon::setTestNow($targetDateForScheduler);

        $this->artisan('schedule:run');

        $counterAfterRun = $counterNotToChange->fresh();
        $this->assertEquals(5, $counterAfterRun->taken, "Le champ 'taken' ne devrait pas avoir changé.");
        $this->assertEquals(20, $counterAfterRun->balance, "Le champ 'balance' ne devrait pas avoir changé.");
        $this->assertEquals($exactInitialRhUpdate->timestamp, Carbon::parse($counterAfterRun->rh_update)->timestamp, "Le rh_update ne devrait pas avoir changé.");
    }
}
