<?php

use App\Http\Controllers\Api\v1\Clients\ClientsController;
use App\Http\Controllers\Api\v1\Documentation\DocumentationController;
use App\Http\Controllers\Api\v1\Export\ExportHistoriesController;
use App\Http\Controllers\Api\v1\Leaves\LeavesController;
use App\Http\Controllers\Api\v1\Leaves\LeaveTypesController;
use App\Http\Controllers\Api\v1\Mobile\ApplicationCodesController;
use App\Http\Controllers\Api\v1\Sites\SitesController;
use App\Http\Controllers\Api\v1\Statuses\StatusController;
use App\Http\Controllers\Api\v1\Tags\TagsController;
use App\Http\Controllers\Api\v1\Teams\TeamsController;
use App\Http\Controllers\Api\v1\Users\UserLeaveCountsController;
use App\Http\Controllers\Api\v1\Users\UsersController;
use App\Http\Controllers\ClientHolidaysController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\DirectorUserController;
use App\Http\Controllers\InternalController;
use App\Http\Controllers\OrionHolidaysController;
use App\Http\Controllers\OrionLeavesController;
use App\Http\Controllers\OrionLeaveTypesController;
use App\Http\Controllers\OrionSitesController;
use App\Http\Controllers\OrionTagsController;
use App\Http\Controllers\OrionUserLeavesCountController;
use App\Http\Controllers\OrionClientsController;
use App\Http\Controllers\OrionSupportDocumentController;
use App\Http\Controllers\OrionUsersController;
use App\Http\Controllers\SiteHolidaysController;
use Illuminate\Support\Facades\Route;
use Orion\Facades\Orion;

Route::get("/", function(){
    return response()->json("ok");
});

Route::group(['prefix' => '/internal', 'middleware' => ['internal.com']], function () {
    Route::get('/tables', [Controller::class, 'checkDatabaseTables']);

    //User
    Route::get('/get/users', [UsersController::class, 'indexInternal']);
    Route::get('/get/users/{clientUuid}', [UsersController::class, 'show']);
    Route::post('/post/user', [UsersController::class, 'store']);
    Route::post('/update/user/{uuid}', [UsersController::class, 'updateInternal']);
    Route::delete('/delete/user/{uuid}', [UsersController::class, 'destroy']);

    //Profiles
    Route::get('/get/profiles', [UsersController::class, 'profiles'] );

    //Clients
    Route::get('/clients/{uuid}', [ClientsController::class, 'getByUuid']);
    Route::post('/clients', [ClientsController::class, 'store']);
    Route::put('/clients/{uuid}', [ClientsController::class, 'updateName']);
    Route::delete('/clients/{uuid}', [ClientsController::class, 'destroy']);

    Route::get('/fresh', [InternalController::class, 'fresh'])->middleware('demo-env');
});

Route::group(['middleware' => 'verify.webhook.signature'], function () {
    Route::post('synchronize-data/model', [\App\Http\Controllers\SynchronizeDataController::class, 'synchronizeModel']);
    Route::post('synchronize-data/roles', [\App\Http\Controllers\SynchronizeDataController::class, 'synchronizeRole']);
});

//use ($router)
Route::group(['prefix' => '/v1', 'middleware' => ["auth:jwt"]], function () {

    //use ($router)
    Route::group(['middleware' => ['check.profile']], function () {

        Route::post("/leave-types", [LeaveTypesController::class, 'store'])->name('store.LeaveTypesController');
        Route::put("/leave-types/{id}", [LeaveTypesController::class, 'update'])->name('update.LeaveTypesController');
        Route::delete("/leave-types/{id}", [LeaveTypesController::class, 'destroy'])->name('destroy.LeaveTypesController');

        Route::put("/sites/{id}", [SitesController::class, 'update'])->name('update.SitesController');

        Route::get("/clients/leave-date", [ClientsController::class, 'getClientLeaveDate'])->name('leave-date.ClientsController');
        Route::put("/clients/open-days", [ClientsController::class, 'updateClientOpenDays'])->name('update-open-days.ClientsController');
        Route::put("/clients/count-public-holidays", [ClientsController::class, 'updateClientCountPublicHolidays'])->name('update-count-public-holidays.ClientsController');
        Route::put("/clients/count-pentecost", [ClientsController::class, 'updateClientCountPentecost'])->name('update-count-pentecost.ClientsController');
        Route::put("/clients/{id}", [ClientsController::class, 'update'])->name('update.ClientsController');

        Route::put("/leaves/mass-validation", [LeavesController::class, 'massValidation'])->name('validation.LeavesController');
        Route::post('/import-leaves', [LeavesController::class, 'import'])->name('import.LeavesController');
        Route::post("/export-leaves", [LeavesController::class, 'export'])->name('export.LeavesController');
        Route::post("/export-leaves-model", [LeavesController::class, 'exportModel'])->name('exportModel.LeavesController');

        Route::get("/teams", [TeamsController::class, 'index'])->name('index.TeamsController');

        Route::get("/leave-team-count", [UserLeaveCountsController::class, 'getTeamCounts'])->name('team-count.UserLeaveCountsController');

        Route::put("/leave-count/{id}", [UserLeaveCountsController::class, 'update'])->name('update.UserLeaveCountsController');

        Route::get('export-histories/{exportId}/download', [ExportHistoriesController::class, 'downloadExport'])
            ->name('downloadExport.ExportHistoriesController')
            ->where('exportId', '[0-9]+');

        Route::post("/export-leave-count", [UserLeaveCountsController::class, 'export'])->name('export.UserLeaveCountsController');

        Route::post('/import-user-leave-count', [UserLeaveCountsController::class, 'import'])->name('import.UserLeaveCountsController');
    });
    // pas de check profile
    Route::get("/clients/current", [ClientsController::class, 'current']);

    Route::get("/clients/open-days", [ClientsController::class, 'getClientOpenDays']);
    Route::get("/clients/validation-scheme", [ClientsController::class, 'getValidationScheme']);
    Route::get("/clients/count-public-holidays", [ClientsController::class, 'getClientCountPublicHolidays']);
    Route::get("/clients/count-pentecost", [ClientsController::class, 'getClientCountPentecost']);

    //TODO Supprimer quand le front utilise le support Document
    Route::get("/documentation", [DocumentationController::class, 'index']);
    Route::get("/documentation/{id}", [DocumentationController::class, 'show']);

    Route::get("/leave-types", [LeaveTypesController::class, 'index']);
    Route::get("/leave-types/{id}", [LeaveTypesController::class, 'show']);

    Route::get("/sites", [SitesController::class, 'index']);
    Route::get("/sites/{id}", [SitesController::class, 'show']);

    Route::get("/users", [UsersController::class, 'index']);
    Route::post("/users", [UsersController::class, 'index']);
    Route::get("/users/current", [UsersController::class, 'current']);
    Route::get("/users/logout", [UsersController::class, 'logout']);
    Route::put("/users/{id}", [UsersController::class, 'update']);
    Route::put("/users/current/open-days", [UsersController::class, 'updateClientOpenDays']);
    Route::post('/users/{user}/fcm-token', [UsersController::class, 'updateFcmToken']);

    Route::get("/leaves/public-holidays", [LeavesController::class, 'getPublicHolidays']);
    Route::get("/leaves/holidays", [LeavesController::class, 'getHolidays']);
    Route::get("/leaves/{id}", [LeavesController::class, 'show']);
    Route::get("/leaves/{id}/attachment", [LeavesController::class, 'attachment']);
    Route::post("/leaves/{leave}/attachment", [LeavesController::class, 'setAttachment']);
    Route::post("/leaves", [LeavesController::class, 'store']);
    Route::put("/leaves-cancel", [LeavesController::class, 'cancelLeave']);
    Route::put("/leaves-refuse-cancel/{leave}", [LeavesController::class, 'refuseCancelLeave']);
    Route::post("/leaves-callback-manager/{leave}", [LeavesController::class, 'callbackManagerLeave']);

    Route::get("/leave-count", [UserLeaveCountsController::class, 'getCounts']);
    Route::get("/statuses", [StatusController::class, 'index']);

    // Tags
    Route::get("/tags", [TagsController::class, 'index']);
    Route::get("/tags/users/{id}", [TagsController::class, 'getUsersInTag']);
    Route::get("/tags/{id}", [TagsController::class, 'show']);
    Route::put("/tags/{id}", [TagsController::class, 'update']);
    Route::post("/tags", [TagsController::class, 'store']);
    Route::post("/tags/attach", [TagsController::class, 'attach']);
    Route::post("/tags/detach", [TagsController::class, 'detach']);
    Route::post("/tags/multipleAttachDetach", [TagsController::class, 'multipleAttachDetach']);
    Route::delete("/tags/{id}", [TagsController::class, 'delete']);

    //TODO Remove function when new system notification is up
    Route::post("/token", [UsersController::class, 'updateFcmToken']);

    // QR Code
    Route::get("/application-code", [ApplicationCodesController::class, 'getCurrentUserQrs']);

    Orion::resource('leaves', OrionLeavesController::class)->only('search', 'destroy');
    Orion::resource('users', OrionUsersController::class)->only('search');
    Orion::resource('sites', OrionSitesController::class)->only('search');
    Orion::resource('tags', OrionTagsController::class)->only('search', 'batchUpdate', 'batchDelete');
    Orion::resource('leave-types', OrionLeaveTypesController::class)->only('search');
    Orion::resource('user-leaves-count', OrionUserLeavesCountController::class)->only('search', 'batchUpdate');
    Orion::resource('clients', OrionClientsController::class)->only('search');
    Orion::resource('support-documents', OrionSupportDocumentController::class);
    Orion::resource('export-histories', ExportHistoriesController::class)
        ->only(['index', 'show', 'destroy', 'search']);

    // Seed demo
    Route::get('/clients/seed-demo', [ClientsController::class, 'seedDemo'])->middleware('verify.env:local,rd,rc,demo');

    Route::post("/director/attach", [DirectorUserController::class, 'attach']);
    Route::post("/director/detach", [DirectorUserController::class, 'detach']);

    Route::post('/statistics/portal/leaves-by-type', [\App\Http\Controllers\StatisticsController::class, 'leavesByType']);
    Route::post('/statistics/portal/leaves-per-users', [\App\Http\Controllers\StatisticsController::class, 'leavesPerUsers']);
    Route::post('/statistics/portal/real-time-leaves-consumption', [\App\Http\Controllers\StatisticsController::class, 'realTimeLeavesConsumption']);

    Route::put('/sites/open-days/{site}', [OrionSitesController::class, 'updateSiteOpenDays']);

    Orion::belongsToManyResource('sites', 'holidays', SiteHolidaysController::class)->only('attach', 'detach', 'sync');
    Orion::belongsToManyResource('clients', 'holidays', ClientHolidaysController::class)->only('attach', 'detach', 'sync');
    Orion::resource('holidays', OrionHolidaysController::class)->only('search', 'store', 'destroy');
    Route::post('holidays/create-holidays-with-country', [\App\Http\Controllers\OrionHolidaysController::class, 'createHolidaysWithCountry']);

    Route::get('/leave-types/week/activity', [OrionLeaveTypesController::class, 'getWeekActivity']);
    Route::get('/leave-types/days/activity', [OrionLeaveTypesController::class, 'getActivityByDays']);


    Route::post('/sites/create-settings/{site}', [OrionSitesController::class, 'createSettings']);
    Route::delete('/sites/remove-settings/{site}', [OrionSitesController::class, 'removeSettings']);

    Route::post('/tags/all-users/', [OrionTagsController::class, 'allTagsWithUsers']);

    //Media
    Orion::resource('media', \App\Http\Controllers\Api\v1\MediaController::class);
    Route::get('media/{media}/download', [\App\Http\Controllers\Api\v1\MediaController::class, 'download'])->name('media.download');
    Route::get('/users-without-leave', [UserLeaveCountsController::class, 'usersWithoutLeave']);
});

Route::get('minio/{path}', [\App\Http\Controllers\Api\v1\MinioController::class, 'getFile'])
    ->where('path', '.*')
    ->name('minio.get-file');
