<?php

namespace App\Jobs;

use App\Models\Api\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class StructureManagementJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private User $user;
    /**
     * Create a new job instance.
     */
    public function __construct(User $user)
    {
        $this->user = $user;
        $this->user->managers()->detach();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $lastManagerLevel = $this->user->number_managers_can_validate ?? $this->user->loadMissing('site.client')->site->client->number_managers_can_validate;

        $recursiveManagers =
            DB::select(
                "
                    with recursive cte as (
                        select id, manager_id, is_level_one_manager, 0 as level
                        from users
                        where id = :id
                        union all
                        select u.id, u.manager_id, u.is_level_one_manager, cte.level + 1
                        from cte
                        inner join users u on u.id = cte.manager_id
                    )
                    select id, is_level_one_manager, level
                    from cte
                    where level <= :level and ((is_level_one_manager = 0 and level > 0) or (is_level_one_manager = 1 and level < 2));
                ", ['id' => $this->user->getKey(), 'level' => $lastManagerLevel]
            );

        $this->user->managers()->attach(
            collect($recursiveManagers)
                ->mapWithKeys(function ($manager) {
                    return [$manager->id => ['level' => $manager->level]];
                })
                ->toArray()
        );

        $recursiveManaged =
            DB::select(
                "
                    with recursive cte (id, manager_id, level) as (
                        select id, manager_id, 0 as level
                        from users
                        where id = :id
                        union
                        select u.id, u.manager_id, level + 1
                        from users u
                        inner join cte on u.manager_id = cte.id
                    )
                    select id, level from cte where level > 0;
                ", ['id' => $this->user->getKey()]
            );

        User::withTrashed()
            ->whereIn('id', array_column($recursiveManaged, 'id'))
            ->each(function (User $managed) {
                StructureManagementJob::dispatch($managed);
            }, 10);
    }

    /**
     * Get the unique ID for the job.
     */
    public function uniqueId(): string
    {
        return $this->user->getKey();
    }

}
