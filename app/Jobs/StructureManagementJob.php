<?php

namespace App\Jobs;

use App\Models\Api\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StructureManagementJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private User $user;
    /**
     * Create a new job instance.
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $this->user->managers()->detach();

        Log::info('StructureManagement called for user '.$this->user->getKey().' on queue '.$this->queue.' and connection '.$this->connection);
        $lastManagerLevel = $this->user->number_managers_can_validate ?? $this->user->loadMissing('site.client')->site->client->number_managers_can_validate;

        $recursiveManagers =
            DB::select(
                "
                    with recursive cte as (
                        select id, manager_id, is_level_one_manager, 0 as level
                        from users
                        where id = :id
                        union all
                        select u.id, u.manager_id, u.is_level_one_manager, cte.level + 1
                        from cte
                        inner join users u on u.id = cte.manager_id
                    )
                    select id, is_level_one_manager, level
                    from cte
                    where level <= :level and ((is_level_one_manager = 0 and level > 0) or (is_level_one_manager = 1 and level < 2));
                ", ['id' => $this->user->getKey(), 'level' => $lastManagerLevel]
            );

        $this->user->managers()->attach(
            collect($recursiveManagers)
                ->mapWithKeys(function ($manager) {
                    return [$manager->id => ['level' => $manager->level]];
                })
                ->toArray()
        );

        $recursiveManaged =
            DB::select(
                "
                    with recursive cte (id, manager_id, level) as (
                        select id, manager_id, 0 as level
                        from users
                        where id = :id
                        union
                        select u.id, u.manager_id, level + 1
                        from users u
                        inner join cte on u.manager_id = cte.id
                    )
                    select id, level from cte where level > 0;
                ", ['id' => $this->user->getKey()]
            );

        User::withTrashed()
            ->whereIn('id', array_column($recursiveManaged, 'id'))
            ->each(function (User $managed) {
                StructureManagementJob::dispatch($managed);
            }, 10);
    }

    public function middleware(): array
    {
        return [(new WithoutOverlapping($this->user->getKey()))->releaseAfter(5)];
    }
}
