<?php

namespace App\Jobs;

use App\Models\Api\Leave;
use App\Models\Api\Status;
use App\Models\Api\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class LeaveValidateIfManagerDeletedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public User $user
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Leave::query()
            ->select('leaves.*')
            ->join('users', 'leaves.user_id', 'users.id')
            ->join('sites', 'sites.id', 'users.site_id')
            ->join('clients', 'clients.id', 'sites.client_id')
            ->join('statuses', 'leaves.status_id', 'statuses.id')
            ->where('statuses.tag', 'SUBMITTED')
            ->whereColumn(
                'leaves.current_validator_level',
                '>',
                DB::raw('COALESCE(users.number_managers_can_validate, clients.number_managers_can_validate)')
            )
            ->where('users.id', $this->user->getKey())
            ->update([
                'status_id' => Status::query()->where('tag', 'VALIDATED')->first()->getKey()
            ]);
    }
}
