<?php

namespace App\Jobs;

use App\Models\Api\LeaveType;
use App\Models\Api\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Queue\SerializesModels;

class StructureSiteUserLeaveCountJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private User $user;
    private $leavesTypes;
    /**
     * Create a new job instance.
     */
    public function __construct(User $user)
    {
        $this->user = $user;
        $this->leavesTypes = LeaveType::where([['site_id', $this->user->site_id], ['client_id', $this->user->site->client_id]])->get();
        if ($this->leavesTypes->isEmpty()) {
            $this->leavesTypes = LeaveType::where('client_id', $this->user->site->client_id)->whereNull('site_id')->get();
        }
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        foreach ($this->user->user_leave_counts as $user_leave_count) {
            $user_leave_count->leave_type_id = $this->leavesTypes->firstWhere('leave_code', $user_leave_count->leave_type->leave_code)->getKey() ?? null;
            $user_leave_count->save();
        }

        foreach ($this->user->leaves()->where('leave_type_id', $user_leave_count->leave_type_id)->get() as $leave) {
            $leave->leave_type_id = $this->leavesTypes->firstWhere('leave_code', $leave->leave_type->leave_code)->getKey() ?? null;
            $leave->save();
        }
    }

    /**
     * Get the unique ID for the job.
     */
    public function uniqueId(): int
    {
        return $this->user->getKey();
    }
}
