<?php

namespace App\Jobs;

use App\Models\Api\Leave;
use App\Models\Api\Status;
use App\Notifications\UserReceiveNotificationPush;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class JustifyLeaveReminder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $leaves = Leave::select('leaves.*')
            ->join('leave_types', 'leave_types.id', 'leaves.leave_type_id')
            ->where('leave_types.can_justify_later', true)
            ->where(function ($query){
                $query->whereNull('attachment_name')
                    ->orWhereNull('attachment_path');
            })
            ->whereIn('leaves.status_id', Status::whereIn('tag', ['SUBMITTED', 'VALIDATED'])->get()->pluck('id'))
            ->get();

        $leaves->each(function ($leave){
            if (!is_null($leave->user->fcm_token)) {
                $leave->user->notify(new UserReceiveNotificationPush([
                    'leave' => $leave,
                    'title' => __('notifications.fcm.title.title_leaves_no_attachment', [], $leave->user->language),
                    'message' => __('notifications.fcm.body.leave_waiting_an_attachment', [], $leave->user->language),
                    'url' => "/leave/{$leave->getKey()}",
                    'page' => 'myLeaves',
                ]));
            }
        });
    }
}
