<?php

namespace App\Jobs;

use App\Models\Api\LeaveType;
use App\Models\Api\UserLeaveCount;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class ProcessAnnualLeaveCountResets implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct()
    {
        //
    }

    public function handle(): void
    {
        $leaveTypesForReset = LeaveType::where('auto_resets_annually', true)->get();

        if ($leaveTypesForReset->isEmpty()) {
            return;
        }

        foreach ($leaveTypesForReset as $leaveType) {
            $updatedUserLeaveCounts = UserLeaveCount::where('leave_type_id', $leaveType->id)
                ->where('is_last_year', false)
                ->update([
                    'taken' => 0,
                    'balance' => DB::raw('acquired'),
                    'rh_update' => Carbon::now()
                ]);
                }
    }
}
