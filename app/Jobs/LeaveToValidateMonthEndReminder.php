<?php

namespace App\Jobs;

use App\Models\Api\Status;
use App\Models\Api\User;
use App\Notifications\LeaveToValidateReminderMonthEndNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;

class LeaveToValidateMonthEndReminder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $managers = User::select('users.*')
            ->join('managers', 'managers.manager_id', 'users.id')
            ->whereExists(function (Builder $query) {
                $query->select(DB::raw(1))
                    ->from('leaves')
                    ->whereColumn('leaves.user_id', 'managers.managed_id')
                    ->whereColumn('leaves.current_validator_level', 'managers.level')
                    ->where('leaves.status_id', Status::where('tag', '=', 'SUBMITTED')->sole()->getKey());
            })
            ->where('users.can_receive_absentees_reminder_mails', true)
            ->get();

        Notification::send($managers, new LeaveToValidateReminderMonthEndNotification());
    }
}
