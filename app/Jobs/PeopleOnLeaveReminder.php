<?php

namespace App\Jobs;

use App\Models\Api\Leave;
use App\Models\Api\Status;
use App\Models\Api\User;
use App\Notifications\PeopleOnLeaveReminderNotification;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;

class PeopleOnLeaveReminder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $managers = User::select('users.*')
            ->join('sites', 'sites.id', 'users.site_id')
            ->join('clients', 'clients.id', 'sites.client_id')
            ->join('managers', function (JoinClause $join) {
                $join->on('managers.manager_id', 'users.id')
                    ->whereColumn('level', '<=', 'clients.number_managers_can_validate');
            })
            ->whereExists(function (Builder $query) {
                $query->select(DB::raw(1))
                    ->from('leaves')
                    ->whereColumn('leaves.user_id', 'managers.managed_id')
                    ->whereIn('leaves.status_id', [Status::where('tag', '=', 'VALIDATED')->sole()->getKey(), Status::where('tag', '=', 'TRANSMITTED')->sole()->getKey()])
                    ->where(function (Builder $query){
                        $query->whereBetween('leaves.start_date', [Carbon::today()->startOfDay(), Carbon::today()->addWeek()->subSecond()])
                            ->orWhereBetween('leaves.end_date', [Carbon::today()->startOfDay(), Carbon::today()->addWeek()->subSecond()])
                            ->orWhere(function ($query){
                                $query->where('leaves.start_date', '<', Carbon::today()->startOfDay())
                                    ->where('leaves.end_date', '>', Carbon::today()->addWeek()->subSecond());
                            });
                    });
            })
            ->where('users.can_receive_absentees_reminder_mails', true)
            ->groupBy('users.id')
            ->get();

        Notification::send($managers, new PeopleOnLeaveReminderNotification());
    }
}
