<?php

namespace App\Jobs;

use App\Models\Api\LeaveType;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class IncrementUserLeaveCounts implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        LeaveType::where('is_auto_increment_active', '=', true)
            ->whereNull('deleted_at')
            ->chunk(50, function (Collection $leaveTypes) {
                $leaveTypes->each(function (LeaveType $leaveType){
                    $leaveType->user_leave_counts()
                        ->whereNull('deleted_at')
                        ->when('is_pay', function ($query){
                            $query->where('is_last_year', false);
                        })
                        ->chunk(50, function (Collection $counts) use ($leaveType) {
                           $counts->each(function ($count) use ($leaveType) {
                               $count->update([
                                   'acquired' => $count->acquired + $leaveType->increment_days_number,
                                   'balance' => $count->balance + $leaveType->increment_days_number,
                               ]);
                           });
                        });
                });
            });
    }
}
