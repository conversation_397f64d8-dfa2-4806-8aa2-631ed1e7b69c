<?php

namespace App\Jobs;

use App\Models\Api\Leave;
use App\Models\Api\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class LeaveBackToDirectManagerJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public User $user
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Leave::query()
            ->select('leaves.*')
            ->join('users', 'leaves.user_id', 'users.id')
            ->join('statuses', 'leaves.status_id', 'statuses.id')
            ->where('statuses.tag', 'SUBMITTED')
            ->where('leaves.current_validator_level', '>', 1)
            ->where('users.id', $this->user->getKey())
            ->update([
                'current_validator_level' => 1
            ]);
    }
}
