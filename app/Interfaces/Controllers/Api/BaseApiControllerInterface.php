<?php


namespace App\Interfaces\Controllers\Api;


use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

interface BaseApiControllerInterface {
	/**
	 * Get all occurence of this class
	 *
	 * @param Request $request
	 *
	 * @return JsonResponse
	 */
	public function index(Request $request): JsonResponse;

	/**
	 * Get one by id of this class
	 *
	 * @param Request $request
	 * @param int $id
	 *
	 * @return JsonResponse
	 */
	public function show(Request $request, int $id): JsonResponse;

	/**
	 * Create new occurence of this class
	 *
	 * @param Request $request
	 *
	 * @return JsonResponse
	 */
	public function store(Request $request): JsonResponse;

	/**
	 * Update one by id of this classe
	 *
	 * @param Request $request
	 * @param int $id
	 *
	 * @return JsonResponse
	 */
	public function update(Request $request, int $id): JsonResponse;

	/**
	 * Delete one by id of this class
	 *
	 * @param int $id
	 *
	 * @return JsonResponse
	 */
	public function destroy(int $id): JsonResponse;
}
