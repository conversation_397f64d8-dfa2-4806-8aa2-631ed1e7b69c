<?php

namespace App\Observers;

use App\Models\Api\Holiday;
use App\Models\Api\Site;

class SiteObserver
{
    /**
     * Handle the User "created" event.
     *
     * @param Site $site
     * @return void
     */
    public function created(Site $site)
    {
        //
    }

    /**
     * Handle the User "updated" event.
     *
     * @param Site $site
     * @return void
     */
    public function updated(Site $site)
    {
        if ($site->isDirty('country_alpha') && $site->holidays()->exists()) {
            $site->holidays()->sync(Holiday::where('country_code', $site->country_alpha)->pluck('id'));
        }
    }

    /**
     * Handle the User "deleted" event.
     *
     * @param Site $site
     * @return void
     */
    public function deleting(Site $site)
    {
        //
    }

    /**
     * Handle the User "restored" event.
     *
     * @param Site $site
     * @return void
     */
    public function restored(Site $site)
    {
        //
    }

    /**
     * Handle the User "force deleted" event.
     *
     * @param Site $site
     * @return void
     */
    public function forceDeleted(Site $site)
    {
        //
    }
}
