<?php

namespace App\Observers;

use App\Models\Api\SupportDocument;

class SupportDocumentObserver
{
    /**
     * Handle the User "created" event.
     *
     * @param SupportDocument $document
     * @return void
     */
    public function created(SupportDocument $document)
    {
        //
    }

    /**
     * Handle the SupportDocument "updated" event.
     *
     * @param SupportDocument $document
     * @return void
     */
    public function updated(SupportDocument $document)
    {
        //
    }

    /**
     * Handle the SupportDocument "deleted" event.
     *
     * @param SupportDocument $document
     * @return void
     */
    public function deleting(SupportDocument $document)
    {
        $document->sites()->detach();
    }

    /**
     * Handle the SupportDocument "restored" event.
     *
     * @param SupportDocument $document
     * @return void
     */
    public function restored(SupportDocument $document)
    {
        //
    }

    /**
     * Handle the User "force deleted" event.
     *
     * @param SupportDocument $document
     * @return void
     */
    public function forceDeleted(SupportDocument $document)
    {
        //
    }
}
