<?php

namespace App\Observers;

use App\Models\Api\Manager;

class ManagerObserver
{
    /**
     * Handle the User "created" event.
     *
     * @param Manager $manager
     * @return void
     */
    public function created(Manager $manager)
    {
        //
    }

    /**
     * Handle the User "updated" event.
     *
     * @param Manager $manager
     * @return void
     */
    public function updated(Manager $manager)
    {
        //
    }

    /**
     * Handle the User "deleted" event.
     *
     * @param Manager $manager
     * @return void
     */
    public function deleting(Manager $manager)
    {
        //
    }

    /**
     * Handle the User "restored" event.
     *
     * @param Manager $manager
     * @return void
     */
    public function restored(Manager $manager)
    {
        //
    }

    /**
     * Handle the User "force deleted" event.
     *
     * @param Manager $manager
     * @return void
     */
    public function forceDeleted(Manager $manager)
    {
        //
    }
}
