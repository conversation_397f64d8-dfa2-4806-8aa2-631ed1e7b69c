<?php

namespace App\Observers;

use App\Models\Api\Manager;
use App\Models\Api\Profile;
use App\Models\Api\Tag;
use App\Models\Api\User;

class ManagerObserver
{
    /**
     * Handle the User "created" event.
     *
     * @param Manager $manager
     * @return void
     */
    public function created(Manager $manager)
    {
        //
    }

    /**
     * Handle the User "updated" event.
     *
     * @param Manager $manager
     * @return void
     */
    public function updated(Manager $manager)
    {
        //
    }

    /**
     * Handle the User "deleted" event.
     *
     * @param Manager $manager
     * @return void
     */
    public function deleting(Manager $manager)
    {
        if (User::find($manager->manager_id)->profile_id !== Profile::where('label', 'DIRECTOR')->first()->getKey()) {
            $managedUser = User::find($manager->managed_id);

            // Supprimer seulement l'utilisateur géré (pas récursivement)
            Tag::where('user_id', $manager->manager_id)->each(function ($tag) use ($managedUser) {
                $tag->users()->where('user_id', $managedUser->id)->detach();
            });
        }
    }

    /**
     * Handle the User "restored" event.
     *
     * @param Manager $manager
     * @return void
     */
    public function restored(Manager $manager)
    {
        //
    }

    /**
     * Handle the User "force deleted" event.
     *
     * @param Manager $manager
     * @return void
     */
    public function forceDeleted(Manager $manager)
    {
        //
    }
}
