<?php

namespace App\Observers;

use App\Models\Api\Manager;
use App\Models\Api\Profile;
use App\Models\Api\Tag;
use App\Models\Api\User;

class ManagerObserver
{
    /**
     * Handle the User "created" event.
     *
     * @param Manager $manager
     * @return void
     */
    public function created(Manager $manager)
    {
        //
    }

    /**
     * Handle the User "updated" event.
     *
     * @param Manager $manager
     * @return void
     */
    public function updated(Manager $manager)
    {
        //
    }

    /**
     * Handle the User "deleted" event.
     *
     * @param Manager $manager
     * @return void
     */
    public function deleting(Manager $manager)
    {
        if (User::find($manager->manager_id)->profile_id !== Profile::where('label', 'DIRECTOR')->first()->getKey()) {
            $managedUser = User::find($manager->managed_id);

            $affectedUsers = [$managedUser->getKey()];
            $managedUser->findAllManagedUsers($affectedUsers);

            Tag::where('user_id', $manager->manager_id)->each(function ($tag) use ($affectedUsers) {
                $tag->users()->whereIn('user_id', $affectedUsers)->detach();
            });
        }
    }

    /**
     * Handle the User "restored" event.
     *
     * @param Manager $manager
     * @return void
     */
    public function restored(Manager $manager)
    {
        //
    }

    /**
     * Handle the User "force deleted" event.
     *
     * @param Manager $manager
     * @return void
     */
    public function forceDeleted(Manager $manager)
    {
        //
    }
}
