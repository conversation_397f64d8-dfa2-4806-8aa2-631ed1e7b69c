<?php

namespace App\Observers;

use App\Events\DetachUserDirected;
use App\Jobs\LeaveBackToDirectManagerJob;
use App\Jobs\LeaveValidateIfManagerDeletedJob;
use App\Jobs\StructureManagementJob;
use App\Models\Api\User;
use Illuminate\Support\Facades\Log;

class UserObserver
{
    /**
     * Handle the User "created" event.
     *
     * @param User $user
     * @return void
     */
    public function created(User $user)
    {
        if ($user->directManager()->exists()) {
            StructureManagementJob::dispatch($user);
        }
    }

    /**
     * Handle the User "updated" event.
     *
     * @param User $user
     * @return void
     */
    public function updated(User $user)
    {
        if ($user->isDirty('manager_id', 'number_managers_can_validate')) {
            StructureManagementJob::dispatch($user);

            if ($user->isDirty('manager_id')) {
                LeaveBackToDirectManagerJob::dispatch($user);

                $user->tags()->where('tags.user_id', $user->getOriginal('manager_id'))->each(function ($tag) use($user){
                    $user->tags()->detach($tag->getKey());
                });
            }

            if ($user->isDirty('number_managers_can_validate')) {
                LeaveValidateIfManagerDeletedJob::dispatch($user);
            }
        }
    }

    /**
     * Handle the User "deleted" event.
     *
     * @param User $user
     * @return void
     */
    public function deleting(User $user)
    {
        DetachUserDirected::dispatch($user);

        $user->directors()->detach();
    }

    /**
     * Handle the User "restored" event.
     *
     * @param User $user
     * @return void
     */
    public function restored(User $user)
    {
        //
    }

    /**
     * Handle the User "force deleted" event.
     *
     * @param User $user
     * @return void
     */
    public function forceDeleted(User $user)
    {
        //
    }
}
