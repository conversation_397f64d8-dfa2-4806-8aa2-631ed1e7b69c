<?php

namespace App\Console;

use App\Jobs\IncrementUserLeaveCounts;
use App\Jobs\JustifyLeaveReminder;
use App\Jobs\LeaveToValidateAfterCreatedAtReminder;
use App\Jobs\LeaveToValidateBeforeStartDateReminder;
use App\Jobs\LeaveToValidateMonthEndReminder;
use App\Jobs\PeopleOnLeaveReminder;
use App\Jobs\ProcessAnnualLeaveCountResets;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
//    /**
//     * The Artisan commands provided by your application.
//     *
//     * @var array
//     */
//    protected $commands = [
//        //
//    ];

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('model:prune')->daily();
        $schedule->job(new LeaveToValidateBeforeStartDateReminder())->dailyAt('08:00');
        $schedule->job(new LeaveToValidateAfterCreatedAtReminder())->dailyAt('08:00');
        $schedule->job(new PeopleOnLeaveReminder())->mondays()->at('08:00');
        $schedule->job(new JustifyLeaveReminder())->dailyAt('08:00');
        $schedule->job(new LeaveToValidateMonthEndReminder())->lastDayOfMonth();
        $schedule->job(new IncrementUserLeaveCounts())->monthly();
        $schedule->job(new ProcessAnnualLeaveCountResets)->yearlyOn(6, 1, '01:00');
    }

    /**
     * Get the timezone that should be used by default for scheduled events.
     */
    protected function scheduleTimezone(): \DateTimeZone|string|null
    {
        return 'Europe/Paris';
    }
}
