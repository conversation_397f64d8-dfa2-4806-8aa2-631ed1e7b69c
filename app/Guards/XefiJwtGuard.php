<?php

namespace App\Guards;

use App\Utils\Jwt\JWT;
use Illuminate\Auth\Events\Authenticated;
use Illuminate\Auth\GuardHelpers;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\UserProvider;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class XefiJwtGuard implements Guard
{
    use GuardHelpers;

    /**
     * The name of the guard. Typically "web".
     *
     * Corresponds to guard name in authentication configuration.
     *
     * @var string
     */
    protected $name;

    /**
     * The event dispatcher instance.
     *
     * @var \Illuminate\Contracts\Events\Dispatcher
     */
    protected $events;

    /**
     * The request instance.
     *
     * @var \Illuminate\Http\Request
     */
    protected $request;

    /**
     * The name of the query string item from the request containing the API token.
     *
     * @var string
     */
    protected $inputKey;

    /**
     * The name of the token "column" in persistent storage.
     *
     * @var string
     */
    protected $storageKey;

    /**
     * Create a new authentication guard.
     *
     * @param  string  $inputKey
     * @param  string  $storageKey
     */
    public function __construct(
        $name,
        UserProvider $provider,
        Request $request,
        $inputKey = 'uuid',
        $storageKey = 'id'
    ) {
        $this->name = $name;
        $this->request = $request;
        $this->provider = $provider;
        $this->inputKey = $inputKey;
        $this->storageKey = $storageKey;
    }

    public function user()
    {
        // If we've already retrieved the user for the current request we can just
        // return it back immediately. We do not want to fetch the user data on
        // every call to this method because that would be tremendously slow.
        if (! is_null($this->user)) {
            return $this->user;
        }

        $jwt = new JWT($this->request);

        if (! is_null($jwt->userId) && ! $jwt->isExpired() && $this->user = $this->provider->retrieveByCredentials(['uuid' => $jwt->userId])) {
            $this->fireAuthenticatedEvent($this->user);
        }

        if ($this->request->headers->has('authorization') && Str::contains($this->request->headers->get('authorization'), 'Bearer')) {
            return Auth::guard('api')->user();
        }

        return $this->user;
    }

    public function validate(array $credentials = [])
    {
        if (empty($credentials[$this->inputKey])) {
            return false;
        }

        $credentials = [$this->storageKey => $credentials[$this->inputKey]];

        if ($this->provider->retrieveById($credentials)) {
            return true;
        }

        return false;
    }

    /**
     * Log the given user ID into the application.
     *
     * @param  mixed  $id
     * @param  bool  $remember
     * @return Authenticatable|false
     */
    public function loginUsingId($id, $remember = false)
    {
        if (! is_null($user = $this->provider->retrieveById($id))) {
            $this->user = $user;

            return $user;
        }

        return false;
    }

    /**
     * Fire the authenticated event if the dispatcher is set.
     *
     * @param  Authenticatable  $user
     * @return void
     */
    protected function fireAuthenticatedEvent($user)
    {
        if (isset($this->events)) {
            $this->events->dispatch(new Authenticated(
                $this->name, $user
            ));
        }
    }

    /**
     * Set the event dispatcher instance.
     *
     * @return void
     */
    public function setDispatcher(Dispatcher $events)
    {
        $this->events = $events;
    }
}
