<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

class ExcelProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        HeadingRowFormatter::extend('unique', function ($value) {
            static $headerCounts = [];

            if (!isset($headerCounts[$value])) {
                $headerCounts[$value] = 0;
            }

            $headerCounts[$value]++;

            return $headerCounts[$value] > 1
                ? Str::slug($value, '_') . "_$headerCounts[$value]"
                : Str::slug($value, '_');
        });
    }
}
