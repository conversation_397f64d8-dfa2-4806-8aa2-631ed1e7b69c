<?php

namespace App\Providers;

use App\Events\DetachUserDirected;
use App\Events\HolidayCreated;
use App\Listeners\AttachHoliday;
use App\Listeners\CalculateHierarchicalManagerListener;
use App\Listeners\ChangeLeavesForUserOnSiteChange;
use App\Listeners\DetachUserDirectedListener;
use App\Listeners\CalculateBalanceUserLeaveCount;
use App\Models\Api\Manager;
use App\Models\Api\Site;
use App\Models\Api\SupportDocument;
use App\Models\Api\User;
use App\Observers\ManagerObserver;
use App\Observers\SiteObserver;
use App\Observers\SupportDocumentObserver;
use App\Observers\UserObserver;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        DetachUserDirected::class => [
            DetachUserDirectedListener::class
        ],
        HolidayCreated::class => [
            AttachHoliday::class
        ],
        'eloquent.updated: App\Models\Api\User' => [
            ChangeLeavesForUserOnSiteChange::class,
            CalculateHierarchicalManagerListener::class
        ],
        'eloquent.creating: App\Models\Api\UserLeaveCount' => [
            CalculateBalanceUserLeaveCount::class,
        ],
        'eloquent.updating: App\Models\Api\UserLeaveCount' => [
            CalculateBalanceUserLeaveCount::class,
        ],
    ];

    protected $observers = [
        User::class => [UserObserver::class],
        Site::class => [SiteObserver::class],
        Manager::class => [ManagerObserver::class],
        SupportDocument::class => [SupportDocumentObserver::class],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();
    }
}
