<?php

namespace App\Providers;

use App\Excel\Formatters\UniqueFormatter;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\ServiceProvider;
use Laravel\Telescope\Telescope;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        if ($this->app->environment('local')) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }
    }

    public function boot()
    {
        Http::macro('publicHolidays', function () {
            return Http::baseUrl(config('external-apis.publicHolidays.base_url'))
                ->acceptJson();
        });
    }
}
