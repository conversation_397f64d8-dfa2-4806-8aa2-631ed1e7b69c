<?php

namespace App\Http\Resources;

use App\Models\Api\Leave;
use App\Models\Api\Status;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        if (Str::contains($request->get('include'), 'days')){
            return array_merge($this->resource->toArray(),[
                'days' => $this->resource->days->pluck('id')->toArray()
            ]);
        }
        if (Str::contains($request->get('include'), 'user_leave_counts')){
            $this->resource->user_leave_counts->each(function($count){

                $leaves = Leave::query()->whereIn('status_id',Status::whereIn('tag', ['VALIDATED','SUBMITTED'])->get('id')->toArray())->where('user_id',$count->user_id)->where('leave_type_id',$count->leave_type_id)->get();

                $count->futureLeaves = $leaves->sum(function ($leave){
                    if ($this->resource->is_last_year){
                        return $leave->n1;
                    }else{
                        return $leave->n;
                    }
                });
            });
        }
        return $this->resource;
    }
}
