<?php

namespace App\Http\Resources;

use App\Models\Api\Leave;
use App\Models\Api\Status;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

class UserLeaveCountResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        if (Str::contains($request->get('include'), 'futureLeaves')){
            $leaves = Leave::query()->whereIn('status_id',Status::whereIn('tag', ['VALIDATED','SUBMITTED'])->get('id')->toArray())->where('user_id',$this->resource->user_id)->where('leave_type_id',$this->resource->leave_type_id)->get();

            $futureLeaves = $leaves->sum(function ($leave){
                if ($this->resource->is_last_year){
                    return $leave->n1;
                }else{
                    return $leave->n;
                }
            });

            return array_merge($this->resource->toArray(),[
                'futureLeaves' => $futureLeaves
            ]);
        }
        return $this->resource;
    }
}
