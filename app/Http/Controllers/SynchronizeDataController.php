<?php

namespace App\Http\Controllers;

use App\Facades\SynchronizeData;
use App\Models\Api\Profile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SynchronizeDataController extends Controller
{
    public function synchronizeModel(Request $request): void
    {
        Log::info('Conges : Start synchro model.');
        $method = $request->input('method'); // $method = 'save' | 'delete'
        $driver = Str::lower(Str::pluralStudly($request->input('model_class')));

        $model = SynchronizeData::driver($driver)->run($method, $request->input('model'));

        Log::info('CONGES : Fin synchro model. ' .$request->input('model_class') .' : ' .$model->getKey() ?? '');
    }

    public function synchronizeRole(Request $request): void
    {
        Log::info('Conges : Start synchro utilisateur.');
        $method = $request->input('method'); // $method = 'attach' | 'detach'

        $userData = $request->input('model_from_attached');
        $userData['profile_id'] = Profile::where('label', $request->input('model_to_attached')['label'])->first()->id;

        $user = SynchronizeData::driver('users')->run($method === 'attach' ? 'save' : 'delete', $userData);

        Log::info('CONGES : Fin synchro utilisateur : ' .$user->getKey() ?? '');
    }
}
