<?php

namespace App\Http\Controllers;

use App\Events\HolidayCreated;
use App\Http\Requests\HolidayRequest;
use App\Lib\Holidays;
use App\Lib\Tools;
use App\Models\Api\Holiday;
use App\Models\Api\Leave;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Orion\Concerns\DisableAuthorization;
use Orion\Http\Controllers\Controller;
use Orion\Http\Requests\Request;

class OrionHolidaysController extends Controller
{
    use DisableAuthorization;

    protected $model = Holiday::class;

    protected $request = HolidayRequest::class;

    public function includes(): array
    {
        return ['sites', 'clients'];
    }

    public function filterableBy(): array
    {
        return ['name', 'year', 'country_code', 'id', 'clients.id', 'sites.id'];
    }

    public function sortableBy(): array
    {
        return ['date'];
    }

    public function aggregates(): array
    {
        return [
            'clients', 'sites'
        ];
    }

    protected function buildFetchQuery(Request $request, array $requestedRelations): Builder
    {
        $query = parent::buildFetchQuery($request, $requestedRelations);
        $currentUser = Tools::getCurrentUserWithUuid();
        $site = $currentUser->site;
        $client = $site->client;

        $query->when($site->holidays()->exists(), function ($query) use ($currentUser) {
            $query->leftjoin('site_holiday as sh', function ($join) use ($currentUser) {
                $join->on('sh.holiday_id', 'holidays.id')
                    ->where('sh.site_id', $currentUser->site_id);
            });
        })->when($client->holidays()->exists() && !$site->holidays()->exists(), function ($query) use ($currentUser) {
            $query->leftjoin('client_holiday as ch', function ($join) use ($currentUser) {
                $join->on('ch.holiday_id', 'holidays.id')
                    ->where('ch.client_id', $currentUser->site->client_id);
            });
        });

        return $query;
    }

    protected function beforeStore(Request $request, Model $entity)
    {
        $entity->is_manual = true;
        $entity->year = Carbon::create($request->input('date'))->format('Y');
    }

    protected function afterStore(Request $request, Model $entity)
    {
        HolidayCreated::dispatch(
            Holiday::find($entity->getKey()),
            $request->input()
        );
    }

    protected function beforeDestroy(Request $request, Model $entity)
    {
        abort_if(
            Leave::query()->where('start_date', '<=', $entity->date)->where('end_date', '>=', $entity->date)->exists(),
            422,
            __('validation.custom.leave_already_taken'),
        );
    }

    public function createHolidaysWithCountry(Request $request)
    {
        $countryCode = $request->input('country_code');
        $countrySubCode = $request->input('country_subcode');
        $startdate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $holidaysYears = Holidays::getPublicHolidaysByCountryCode($startdate, $endDate, $countryCode, $countrySubCode);

        foreach ($holidaysYears as $holidaysYear) {
            foreach ($holidaysYear as $holiday) {
                Holiday::firstOrCreate([
                    'year' => Carbon::parse($holiday['date'])->format('Y'),
                    'name' => $holiday['name'],
                    'country_code' => $holiday['countryCode'],
                    'date' => Carbon::parse($holiday['date']),
                ]);
            }
        }

        return (__('messages.SuccessStoreHoliday'));
    }
}
