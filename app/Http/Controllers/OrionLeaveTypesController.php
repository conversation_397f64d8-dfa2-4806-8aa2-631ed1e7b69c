<?php

namespace App\Http\Controllers;

use App\Lib\Tools;
use App\Models\Api\LeaveType;
use App\Models\Api\LeaveTypeCategory;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Orion\Concerns\DisableAuthorization;
use Orion\Http\Controllers\Controller;
use Orion\Http\Requests\Request;

class OrionLeaveTypesController extends Controller
{
    use DisableAuthorization;

    protected $model = LeaveType::class;

    protected function buildFetchQuery(Request $request, array $requestedRelations): Builder
    {
        $query = parent::buildFetchQuery($request, $requestedRelations);
        $currentUser = Tools::getCurrentUserWithUuid();

        if (!$request->get('scope') || $request->get('scope') !== 'settings') {
            $query->where(function ($query) use ($currentUser) {
                $query->where('leave_types.site_id', $currentUser->site_id)
                    ->orWhere(function ($query) use ($currentUser) {
                        $query->where('leave_types.client_id', $currentUser->site->client_id)
                            ->whereNull('leave_types.site_id')
                            ->whereRaw("(SELECT COUNT(*) FROM leave_types WHERE site_id = $currentUser->site_id AND deleted_at is null) < 1");
                    });
            });
        }

        $query->where('leave_types.client_id', $currentUser->client->getKey());

        if ($request->get('scope') === 'leavesTypesName') {
            $query->select('leave_types.name')
                ->orderBy('leave_types.name')
                ->groupBy('leave_types.name');
        } else {
            $query->orderBy('leave_type_category_id')
                ->orderBy('is_deletable');
        }

        return $query;
    }

    public function includes(): array
    {
        return ['client', 'leave_type_sub_families', 'site', 'leaveTypeCategory'];
    }

    public function filterableBy(): array
    {
        return ['is_active', 'client_id', 'needs_count', 'client.uuid', 'id', 'site_id', 'leaveTypeCategory.slug', 'name', "is_take_leave", 'leave_type_category_id'];
    }

    public function searchableBy(): array
    {
        return ['name'];
    }

    public function sortableBy(): array
    {
        return ['name', 'order_appearance'];
    }

    public function getWeekActivity(Request $request)
    {
        $currentUser = Tools::getCurrentUserWithUuid();

        $startOfMonth = Carbon::createFromFormat('m-Y', $request->input('monthyear'))->startOfMonth();
        $endOfMonth = Carbon::createFromFormat('m-Y', $request->input('monthyear'))->endOfMonth();

        if ($endOfMonth->isSaturday()) {
            $endOfMonth = $endOfMonth->subDay();
        } elseif ($endOfMonth->isSunday()) {
            $endOfMonth = $endOfMonth->subDays(2);
        }

        if ($startOfMonth->isWeekend()) {
            $startOfWeek = $startOfMonth->next(CarbonInterface::MONDAY);
        } else {
            $startOfWeek = $startOfMonth->copy();
        }

        $leaveTypesData = array();

        while ($startOfWeek->lessThanOrEqualTo($endOfMonth)) {
            $endOfWeek = $startOfWeek->copy()->endOfWeek(CarbonInterface::FRIDAY);

            if ($endOfWeek->greaterThan($endOfMonth)) {
                $endOfWeek = $endOfMonth;
            }

            $leaveTypes = LeaveType::query()
                ->select('leave_types.color')
                ->addSelect(DB::raw("$startOfWeek->isoWeek as week_number"))
                ->where('leave_types.client_id', $currentUser->client->getKey())
                ->join('leaves', function ($join) use ($endOfWeek, $startOfWeek, $currentUser) {
                    $join->on('leaves.leave_type_id', 'leave_types.id')
                        ->join('statuses', 'statuses.id', 'leaves.status_id')
                        ->whereNotIn('statuses.tag', ['REFUSED', 'CANCELED', 'SUBMITTED_TO_CANCELLATION'])
                        ->where(fn($builder) => $builder->whereDate('leaves.start_date', '<=', $endOfWeek)->whereDate('leaves.end_date', '>=', $startOfWeek))
                        ->whereNull('leaves.deleted_at');
                })
                ->join('users', 'users.id', 'leaves.user_id')
                ->join('managers as manager_managed_user_no_one_else', function ($join) use ($currentUser) {
                    $join->on('manager_managed_user_no_one_else.managed_id', 'users.id')
                        ->where('manager_managed_user_no_one_else.manager_id', $currentUser->id);
                })
                ->whereNotNull('manager_managed_user_no_one_else.manager_id')
                ->whereNull('leave_types.deleted_at')
                ->groupBy('leave_types.id')
                ->get();

            $leaveTypesData = array_merge($leaveTypesData, $leaveTypes->toArray());

            $startOfWeek = $startOfWeek->copy()->addWeek()->startOfWeek(CarbonInterface::MONDAY);
        }

        return response()->json(['data' => $leaveTypesData]);
    }
    public function getActivityByDays(Request $request)
    {
        $currentUser = Tools::getCurrentUserWithUuid();

        $startDate = Carbon::createFromFormat('Y-m-d', $request->input('startDate'));
        $endDate = Carbon::createFromFormat('Y-m-d', $request->input('endDate'));

        $leaveTypesData = array();

        while ($startDate->lessThanOrEqualTo($endDate)) {
            $leaveTypes = LeaveType::query()
                ->select('leave_types.name as type', 'leave_types.color')
                ->where('leave_types.client_id', $currentUser->client->getKey())
                ->join('leaves', function ($join) use ($startDate, $currentUser) {
                    $join->on('leaves.leave_type_id', 'leave_types.id')
                        ->join('statuses', 'statuses.id', 'leaves.status_id')
                        ->whereNotIn('statuses.tag', ['REFUSED', 'CANCELED', 'SUBMITTED_TO_CANCELLATION'])
                        ->where(fn($builder) => $builder->whereDate('leaves.start_date', '<=', $startDate)->whereDate('leaves.end_date', '>=', $startDate))
                        ->whereNull('leaves.deleted_at');
                })
                ->join('users', 'users.id', 'leaves.user_id')
                ->join('managers as manager_managed_user_no_one_else', function ($join) use ($currentUser) {
                    $join->on('manager_managed_user_no_one_else.managed_id', 'users.id')
                        ->where('manager_managed_user_no_one_else.manager_id', $currentUser->id);
                })
                ->whereNotNull('manager_managed_user_no_one_else.manager_id')
                ->whereNull('leave_types.deleted_at')
                ->groupBy('leave_types.id')
                ->get();

            $leaveTypesData[$startDate->year.'-'.$startDate->month.'-'.$startDate->day] = $leaveTypes->toArray();

            $startDate = $startDate->copy()->addDay();
        }

        return response()->json(['data' => $leaveTypesData]);
    }
}
