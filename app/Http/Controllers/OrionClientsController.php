<?php

namespace App\Http\Controllers;

use App\Models\Api\Client;
use Illuminate\Database\Eloquent\Builder;
use App\Lib\Tools;
use Orion\Concerns\DisableAuthorization;
use Orion\Http\Controllers\Controller;

class OrionClientsController extends Controller
{
    use DisableAuthorization;

    protected $model = Client::class;

    public function includes(): array
    {
        return ['days', 'leave_types', 'holidays', 'leave_types.*'];
    }

    public function filterableBy(): array
    {
        return ['leave_types.site_id', 'holidays.year'];
    }

    protected function buildFetchQuery(\Orion\Http\Requests\Request $request, array $requestedRelations): Builder
    {
        $query = parent::buildFetchQuery($request,$requestedRelations);
        $currentUser = Tools::getCurrentUserWithUuid();

        $query->where('clients.id', $currentUser->site->client_id);

        return $query;
    }
}
