<?php

namespace App\Http\Controllers;

use App\Http\Requests\SupportDocumentRequest;
use App\Models\Api\SupportDocument;
use App\Utils\AddFileMedia;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Orion\Concerns\DisableAuthorization;
use Orion\Http\Controllers\Controller;
use Orion\Http\Requests\Request;

class OrionSupportDocumentController extends Controller
{
    use DisableAuthorization;

    protected $model = SupportDocument::class;

    protected $relation = 'creator';

    protected $request = SupportDocumentRequest::class;

    public function filterableBy(): array
    {
        return ['creator_id',];
    }

    public function sortableBy(): array
    {
        return ['name'];
    }

    public function alwaysIncludes(): array
    {
        return ['media'];
    }

    public function includes() : array
    {
        return ['sites'];
    }

    protected function buildIndexFetchQuery(Request $request, array $requestedRelations): Builder
    {
        $query = parent::buildIndexFetchQuery($request, $requestedRelations);

        return \App\Utils\PermissionQueries\SupportDocument::make()->implementQuery($query);
    }

    protected function beforeStore(Request $request, Model $entity)
    {
        AddFileMedia::storeMedia($request, $entity);
    }

    protected function afterStore(Request $request, Model $entity)
    {
        if ($request->has("allSites")) {
            $sitesId = Auth::user()->site->client->sites->pluck('id')->toArray();
        } else {
            $sitesId = $request->input('sites');
        }

        $entity->sites()->sync($sitesId);
    }

    protected function beforeUpdate(Request $request, Model $entity)
    {
        AddFileMedia::storeMedia($request, $entity);
    }

    protected function afterUpdate(Request $request, Model $entity)
    {
        $sitesId = $request->input('sites');

        $entity->sites()->sync($sitesId);
    }
}
