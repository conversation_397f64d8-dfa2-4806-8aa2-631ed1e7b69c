<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Routing\Controller as BaseController;

class Controller extends BaseController {
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

	/**
	 * Convertie une date en anglais en français
	 *
	 * @param string $date
	 * @param string $delimiter
	 * @param string $separator
	 *
	 * @return string
	 */
	protected function convertDateEnToFr(string $date, string $delimiter = "-", string $separator = "/"): string {
		$dateExpl = explode($delimiter, $date);
		$date = $dateExpl[0] . $separator . $dateExpl[1] . $separator . $dateExpl[2];

		return date("d" . $separator . "m" . $separator . "Y", strtotime($date));
	}

	/**
	 * Convertie une date en français en anglais
	 *
	 * @param string $date
	 * @param string $delimiter
	 * @param string $separator
	 *
	 * @return string
	 */
	protected function convertDateFrToEn(string $date, string $delimiter = "-", string $separator = "-"): string {
		$dateExpl = explode($delimiter, $date);
		$date = $dateExpl[2] . $separator . $dateExpl[1] . $separator . $dateExpl[0];

		return date("Y" . $separator . "m" . $separator . "d", strtotime($date));
	}

    public function checkDatabaseTables() :JsonResponse
    {
        $tables = DB::select('SHOW tables');
        $db = "Tables_in_".env('DB_DATABASE');
        $i = 0;
        $responses = [];
        foreach($tables as $key => $table) {
            if ($table->$db === 'clients') {
                $i++;
                $responses[] = $table->$db;
            }
        }
        if ($i > 0) {
            $response = $responses;
            $code = 200;
        } else {
            $response = __('warning.ClientTableNotFound');
            $code = 404;
        }
        return response()->json($response, $code);
    }
}
