<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Api\Application;
use App\Models\Api\User;
use Xefi\InternalCom\InternalComLib;
use Illuminate\Support\Facades\Request;
use Exception;

class UserController extends Controller
{
    private $clientIdClaim;

    public function __construct()
    {
        $this->clientIdClaim = Env('CLIENT_ID_JWT_CLAIM');
    }

    public function show($clientId)
    {
        try {
            $clientUsers = User::where('client_id', $clientId)->get();
            $clientsArr = [];
            foreach($clientUsers as $user) {
                $clientsArr[$user['uuid']] = $user;
            }
        } catch (Exception $exception) {
            return response()->json(['error' => $exception], 500);
        }
        return response()->json($clientsArr, 200);
    }

    public function store()
    {
        $data = [
            'uuid' => Request::json('uuid'),
            'client_id' => Request::json('client_id'),
            'profile_id' => Request::json('profile_id'),
            'db_host_crm' => Request::json('db_host_crm'),
            'db_database_crm' => Request::json('db_database_crm'),
        ];

        try {
            User::create($data);
        } catch (Exception $exception) {
            return response()->json(['error' => $exception], 500);
        }
        return response()->json(['success' => 'utilisateur ajouté avec succès'], 200);
    }

    public function destroy($uuid)
    {

        try {
            User::where('uuid', $uuid)->delete();
        } catch (Exception $exception) {
            return response()->json($exception);
        }
        return response()->json(['succes' => 'Lútilisateur a bien été supprimé'], 200);
    }

    public function update($uuid) {
        $data = Request::json();
        $userToUpdate = User::where('uuid', $uuid)->first();
        try {
            $userToUpdate['profile_id'] = $data->get('profile_id');
            $userToUpdate->save();
        } catch (Exception $exception) {
            return response()->json(['error' => $exception], 500);
        }
        return response()->json($userToUpdate, 200);
    }

    public function me()
    {
        $portal = Application::where('name', 'portail')->first();
        $comLib = new InternalComLib($portal);
        $me = $comLib->get('auth/me', ['Authorization' => Request::header("Authorization")]);
        $appProfile = User::where('uuid', $me['body']['uuid'])->first();
        $me['body']['appProfil'] = $appProfile['profile_id'];

        return response()->json($me['body']);
    }

    public function logout()
    {
        $jwt = Request::header('Authorization');
        try {
            $portal = Application::where('name', 'portail')->first();
            $comLib = new InternalComLib($portal);
            $comLib->get('auth/logout', ['Authorization' => $jwt]);
        } catch (Exception $ex) {
            return response()->json($ex->getMessage(), 500);
        }
        return response()->json('Deconnecté avec succès', 200);
    }

    // Si besoin de la crm \\
    /*
    public function updateCrm($clientId) {
        $data = Request::json()->all();

        try {
            $users = User::where('client_id', $clientId)->get()->all();
            foreach($users as $user) {
                $user->db_host_crm = $data['db_host_crm'];
                $user->db_database_crm = $data['db_database_crm'];
                $user->save();
            }
            $message = 'Tous les utilisateurs ont été mis à jour';
            $code = 200;
        } catch(Exception $ex) {
            $message = $ex->getMessage();
            $code = 404;
        }


        return response()->json($message, $code);
    }
    */
}
