<?php

namespace App\Http\Controllers;

use App\Models\Api\Client;
use App\Models\Api\Holiday;
use Illuminate\Support\Arr;
use Orion\Concerns\DisableAuthorization;
use Orion\Http\Controllers\RelationController;
use Orion\Http\Requests\Request;

class ClientHolidaysController extends RelationController
{
    use DisableAuthorization;

    protected $model = Client::class;

    protected $relation = 'holidays';

    public function sync(Request $request, ...$args)
    {
        $client = Client::find(head($args));
        $holidays = $request->get('resources');
        $firstYear = Holiday::find(Arr::first($holidays))->year;

        $syncResult = $client->holidays()->syncWithoutDetaching($holidays);

        $client->holidays()->whereNotIn('id', $holidays)->where('year', $firstYear)->each(function ($holiday) use (&$syncResult) {
            $holiday->pivot->delete();
            $syncResult['detached'][] = $holiday->getKey();
        });

        return response()->json($syncResult);
    }
}
