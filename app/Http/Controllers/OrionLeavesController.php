<?php

namespace App\Http\Controllers;

use App\Lib\Tools;
use App\Models\Api\History;
use App\Models\Api\Leave;
use App\Utils\Traits\FormatHistoriesTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Str;
use Orion\Concerns\DisableAuthorization;
use Orion\Http\Controllers\Controller;
use Orion\Http\Requests\Request;

class OrionLeavesController extends Controller
{
    use DisableAuthorization, FormatHistoriesTrait;

    protected $model = Leave::class;

    public function filterableBy(): array
    {
        return ['id', 'user_id', 'user.site_id', 'user.matricule', 'user.enter_date', 'status.id', 'status.tag', 'start_date', 'end_date', 'leave_type.id', 'leave_type.name',
            'duration', 'updated_at', 'user.site.name', 'user.uuid', 'leave_type.leave_type_category_id', 'user.tags.id', 'leave_type.is_active'
        ];
    }

    public function includes(): array
    {
        return ['user', 'user.validators', 'status', 'leave_type', 'leave_type.leaveTypeCategory', 'leave_type_sub_family', 'user.site', 'histories', 'histories.status', 'histories.user', 'user.user_leave_counts', 'user.user_leave_counts.leave_type', 'creator'];
    }

    public function searchableBy(): array
    {
        return ['user.lastname', 'user.firstname'];
    }

    public function sortableBy(): array
    {
        return ['leave_type.name', 'duration', 'status.name', 'start_date', 'updated_at', 'user.site.name', 'user.lastname', 'user.matricule'];
    }

    public function exposedScopes(): array
    {
        return ['tagsUserLeave','waitingForUserValidation', 'alreadyTreatedByUser', 'waitingForUserValidationOnDifferentLevel', 'waitingForUserCancelation', 'weekActivity', 'planningMobile'];
    }

    protected function buildFetchQuery(\Orion\Http\Requests\Request $request, array $requestedRelations): Builder
    {
        $query = parent::buildFetchQuery($request, $requestedRelations);

        $currentUser = Tools::getCurrentUserWithUuid();

        if (in_array(strtolower($currentUser->profile->label), ['administrateur', 'administrateurmanager'])) {
            $query->select('leaves.*')
                ->leftJoin('users as us', 'us.id', 'leaves.user_id')
                ->join('sites as s', 's.id', 'us.site_id')
                ->join('clients', 'clients.id', 's.client_id')
                ->where('clients.id', $currentUser->site->client->id);
        } else {
            $query->select('leaves.*')
                ->leftJoin('users as us', 'us.id', 'leaves.user_id')
                ->leftJoin('managers as manager_managed_user_no_one_else', function ($join) use ($currentUser) {
                    $join->on('manager_managed_user_no_one_else.managed_id', 'us.id')
                        ->where('manager_managed_user_no_one_else.manager_id', $currentUser->id);
                })
                ->leftJoin('director_user as du', function (JoinClause $joinQuery) use ($currentUser) {
                    $joinQuery->on('du.user_id', 'us.id')->where('du.director_id', $currentUser->id);
                })
                ->where(function (Builder $whereQuery) use ($currentUser) {
                    $whereQuery
                        ->where('leaves.user_id', $currentUser->id)
                        ->orWhereNotNull('manager_managed_user_no_one_else.manager_id')
                        ->orWhereNotNull('du.director_id');
                });
        }

        if ($search = $request->input('search-users.value')) {
            $query->where(function (Builder $query) use ($search) {
                $query->whereRaw("CONCAT(us.firstname, ' ', us.lastname) like ?", "%{$search}%")
                    ->orWhereRaw("CONCAT(us.lastname, ' ', us.firstname) like ?", "%{$search}%");
            });
        }

        $query->when($request->has('sortSiteName') && in_array($request->input('sortSiteName'), ['asc', 'desc']), function () use ($request, $query) {
            $query->join('sites as so', 'so.id', 'us.site_id')
                ->orderBy('so.name', $request->input('sortSiteName'));
        });

        return $query;
    }

    protected function afterIndex(Request $request, $entities)
{
    if ($request->has('include') && Str::contains($request->input('include'), 'histories')) {
        $entities = $entities->map(function ($entity) {
            return $this->formatHistories($entity);
        });
    }

    return $entities;
}

    public function newLeaveSearchAndCounts(Request $request)
    {
        $filters = $request->input('filters', []);
        $currentUser = auth()->user();

        $scopes = [
            'alreadyTreatedByUser' => __('messages.AlreadyTreatedLeavesCount'),
            'waitingForUserValidation' => __('messages.WaitingValidationLeavesCount'),
            'waitingForUserCancelation' => __('messages.WaitingCancelationLeavesCount'),
        ];

        $meta = [];

        foreach ($scopes as $scope => $translationKey) {
            $query = Leave::query()->leftJoin('users as us', 'us.id', 'leaves.user_id')->$scope($currentUser->getKey());
            $meta[$translationKey] = $this->applyFilters($query, $filters, $currentUser)->count();
        }

        $query = $this->applyFilters(Leave::query()->leftJoin('users as us', 'us.id', 'leaves.user_id'), $filters, $currentUser);

        return response()->json([
            'data' => $query->get(),
            'meta' => $meta,
        ]);
    }

    private function applyFilters(Builder $query, array $filters, $currentUser): Builder
    {
        $startDate = null;
        $endDate = null;

        if (in_array(strtolower($currentUser->profile->label), ['administrateur', 'administrateurmanager'])) {
            $query->select('leaves.*')
                ->join('sites as s', 's.id', 'us.site_id')
                ->join('clients', 'clients.id', 's.client_id')
                ->where('clients.id', $currentUser->site->client->id);
        } else {
            $query->select('leaves.*')
                ->leftJoin('managers as manager_managed_user_no_one_else', function ($join) use ($currentUser) {
                    $join->on('manager_managed_user_no_one_else.managed_id', 'us.id')
                        ->where('manager_managed_user_no_one_else.manager_id', $currentUser->id);
                })
                ->leftJoin('director_user as du', function ($joinQuery) use ($currentUser) {
                    $joinQuery->on('du.user_id', 'us.id')->where('du.director_id', $currentUser->id);
                })
                ->where(function ($whereQuery) {
                    $whereQuery
                        ->orWhereNotNull('manager_managed_user_no_one_else.manager_id')
                        ->orWhereNotNull('du.director_id');
                });
        }

        if ($search = request()->input('searchUsersValue')) {
            $query->where(function (Builder $query) use ($search) {
                $query->whereRaw("CONCAT(us.firstname, ' ', us.lastname) like ?", ["%{$search}%"])
                    ->orWhereRaw("CONCAT(us.lastname, ' ', us.firstname) like ?", ["%{$search}%"]);
            });
        }

        $query->when(request()->has('sortSiteName') && in_array(request()->input('sortSiteName'), ['asc', 'desc']), function ($query) {
            $query->join('sites as so', 'so.id', 'us.site_id')
                ->orderBy('so.name', request()->input('sortSiteName'));
        });

        foreach ($filters as $filter) {
            $field = $filter['field'];
            $operator = $filter['operator'];
            $value = $filter['value'];

            switch ($field) {
                case 'user_id':
                    if ($operator === 'in') {
                        $query->where('us.id', $value);
                    }
                    break;
                case 'leave_type.id':
                    if ($operator === 'in') {
                        $query->where('leaves.leave_type_id', $value);
                    }
                    break;
                case 'user.site_id':
                    if ($operator === 'in') {
                        $query->where('us.site_id', $value);
                    }
                    break;
                case 'start_date':
                    if ($operator === '>=') {
                        $startDate = $value;
                    }
                    break;
                case 'end_date':
                    if ($operator === '<=') {
                        $endDate = $value;
                    }
                    break;
            }
        }

        if ($startDate && $endDate) {
            $query->where(function ($query) use ($startDate, $endDate) {
                $query->where('leaves.start_date', '>=', $startDate)
                    ->where('leaves.end_date', '<=', $endDate);
            });
        }

        return $query;
    }

    public function getFetchQuery(Request $request, $requestedRelations = [])
    {
        $action = $request->route()->getAction();
        $action['controller'] = Str::replace(explode('@', $action['controller'])[1], 'search', $action['controller']);
        $request->route()->setAction($action);
        return $this->buildFetchQuery($request, $requestedRelations);
    }

    public function updateFutureLeaves()
    {
        $currentUser = Tools::getCurrentUserWithUuid();

        $leaves = Leave::select('leaves.*')
            ->join('leave_types', 'leave_types.id', 'leaves.leave_type_id')
            ->join('users', 'users.id', 'leaves.user_id')
            ->join('sites', 'sites.id', 'users.site_id')
            ->join('clients', 'clients.id', 'sites.client_id')
            ->where('leave_types.is_pay', 1)
            ->where('clients.id', $currentUser->load('site.client')->site->client->getKey())
            ->whereIn('status_id', [1, 2])
            ->where('leaves.start_date', '>=', Carbon::now()->month(6)->startOfMonth())
            ->get();

        $leaves->each(function ($leave) use ($currentUser) {
            $leave->n1 = $leave->n;
            $leave->n = 0;
            $leave->save();

            History::create([
                'leave_id' => $leave->getKey(),
                'status_id' => $leave->status_id,
                'user_id' => $currentUser->getKey(),
                'reason' => 'Changement des futureLeaves'
            ]);
        });

        return response()->json(['message' => __('messages.UpdateSuccess')]);
    }
}
