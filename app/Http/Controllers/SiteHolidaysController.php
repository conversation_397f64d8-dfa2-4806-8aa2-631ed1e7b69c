<?php

namespace App\Http\Controllers;

use App\Models\Api\Holiday;
use App\Models\Api\Site;
use Illuminate\Support\Arr;
use Orion\Concerns\DisableAuthorization;
use Orion\Http\Controllers\RelationController;
use Orion\Http\Requests\Request;

class SiteHolidaysController extends RelationController
{
    use DisableAuthorization;

    protected $model = Site::class;

    protected $relation = 'holidays';

    public function sync(Request $request, ...$args)
    {
        $site = Site::find(head($args));
        $holidays = $request->get('resources');
        $firstYear = Holiday::find(Arr::first($holidays))->year;

        foreach ($holidays as $key => $holiday) {
            if (Holiday::find($holiday)->country_code !== $site->country_alpha){
                Arr::forget($holidays, $key);
            }
        }

        $syncResult = $site->holidays()->syncWithoutDetaching($holidays);

        $site->holidays()->whereNotIn('id', $holidays)->where('year', $firstYear)->each(function ($holiday) use (&$syncResult) {
            $holiday->pivot->delete();
            $syncResult['detached'][] = $holiday->getKey();
        });

        return response()->json($syncResult);
    }
}
