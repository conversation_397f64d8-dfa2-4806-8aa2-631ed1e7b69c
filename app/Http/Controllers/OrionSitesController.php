<?php

namespace App\Http\Controllers;

use App\Models\Api\Day;
use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\Site;
use App\Models\Api\UserLeaveCount;
use Illuminate\Database\Eloquent\Builder;
use App\Lib\Tools;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Orion\Concerns\DisableAuthorization;
use Orion\Http\Controllers\Controller;

class OrionSitesController extends Controller
{
    use DisableAuthorization;

    protected $model = Site::class;

    public function includes(): array
    {
        return ['days', 'leave_types.leave_type_sub_families', 'holidays', 'leave_types.*', 'leave_types.leaveTypeCategory'];
    }

    public function filterableBy(): array
    {
        return ['name', 'id', 'holidays.year'];
    }

    public function searchableBy(): array
    {
        return ['name'];
    }

    public function sortableBy(): array
    {
        return ['name'];
    }

    public function exposedScopes(): array
    {
        return ['withDifferentClientSettings', 'withoutDifferentClientSettings'];
    }

    protected function buildFetchQuery(\Orion\Http\Requests\Request $request, array $requestedRelations): Builder
    {
        $query = parent::buildFetchQuery($request, $requestedRelations);
        $currentUser = Tools::getCurrentUserWithUuid();

        $query->select('sites.*')
            ->join('clients', 'clients.id', 'sites.client_id')
            ->where('clients.id', $currentUser->site->client->id);

        return $query;
    }

    /**
     * Mise à jour des jours ouvrés du site
     *
     * @param Request $request
     * @return JsonResponse
     * @throws Exception
     */

    public function updateSiteOpenDays(Request $request, Site $site): JsonResponse
    {
        $data = $this->validate($request, [
            'monday' => 'boolean',
            'tuesday' => 'boolean',
            'wednesday' => 'boolean',
            'thursday' => 'boolean',
            'friday' => 'boolean',
            'saturday' => 'boolean',
            'sunday' => "boolean",
        ]);

        abort_if($site->client->getKey() !== Tools::getCurrentUserWithUuid()->client->getKey(), 403);

        // Detach all
        $site->days()->detach();
        // Attach days in data
        if (!empty($data)) {
            foreach ($data as $day_name => $bool) {
                if ($bool) {
                    $day = Day::where('day_name', '=', strtoupper($day_name))->first();
                    $site->days()->attach($day);
                }
            }
        }

        // Get days open
        $days = $site->days()->get();

        if (is_null($days)) {
            $openDays = [];
        } else {
            $openDays = $days->pluck('day_name')->toArray();
        }

        return response()->json(['data' => $openDays]);
    }

    public function createSettings(Site $site): JsonResponse
    {
        $client = Tools::getCurrentUserWithUuid()->site->client;

        abort_if($site->client_id !== $client->getKey(), 403, __('warnings.UnauthorizedAction'));
        abort_if($site->leave_types()->exists() || $site->days()->exists() || $site->holidays()->exists(), 403,  __('warnings.SettingsAlreadyExists'));

        $client->leave_types()->whereNull('site_id')->each(function ($leaveType) use ($client, $site) {
           $lt = $leaveType->replicate();
           $lt->site_id = $site->getKey();
           $lt->save();

           Leave::query()
               ->select('leaves.*')
               ->join('users', 'users.id', 'leaves.user_id')
               ->whereNull('users.deleted_at')
               ->where('users.site_id', $site->getKey())
               ->where('leaves.leave_type_id',$leaveType->getKey())
               ->chunk(100, function ($leaves) use ($lt) {
                   $leaves->each(function ($leave) use ($lt) {
                      $leave->leave_type_id = $lt->getKey();
                      $leave->save();
                   });
               });

           UserLeaveCount::query()
               ->select('user_leave_counts.*')
               ->join('users', 'users.id', 'user_leave_counts.user_id')
               ->whereNull('users.deleted_at')
               ->where('users.site_id', $site->getKey())
               ->where('user_leave_counts.leave_type_id', $leaveType->getKey())
               ->chunk(100, function ($ulcs) use ($lt) {
                   $ulcs->each(function ($ulc) use ($lt) {
                      $ulc->leave_type_id = $lt->getKey();
                      $ulc->save();
                   });
               });
        });
        $site->days()->sync($client->days()->pluck('id'));
        $site->holidays()->sync($client->holidays()->pluck('id'));

        return response()->json(['site' => $site->load('leave_types.leave_type_sub_families', 'days', 'holidays'), 'message' => __('messages.CreateSettingsSuccess')]);
    }

    public function removeSettings(Site $site): JsonResponse
    {
        $client = Tools::getCurrentUserWithUuid()->site->client;

        abort_if($site->client_id !== $client->getKey(), 403, __('warnings.UnauthorizedAction'));

        $site->leave_types()->each(function ($leaveType) use ($client, $site) {
            $clientLt = LeaveType::where('leave_code', $leaveType->leave_code)->where('client_id', $client->getKey())->whereNull('site_id')->first();

            if($clientLt) {
                Leave::query()
                    ->select('leaves.*')
                    ->join('users', 'users.id', 'leaves.user_id')
                    ->whereNull('users.deleted_at')
                    ->where('users.site_id', $site->getKey())
                    ->where('leaves.leave_type_id', $leaveType->getKey())
                    ->chunk(100, function ($leaves) use ($clientLt) {
                        $leaves->each(function ($leave) use ($clientLt) {
                            $leave->leave_type_id = $clientLt->getKey();
                            $leave->save();
                        });
                    });

                UserLeaveCount::query()
                    ->select('user_leave_counts.*')
                    ->join('users', 'users.id', 'user_leave_counts.user_id')
                    ->whereNull('users.deleted_at')
                    ->where('users.site_id', $site->getKey())
                    ->where('user_leave_counts.leave_type_id', $leaveType->getKey())
                    ->chunk(100, function ($ulcs) use ($clientLt) {
                        $ulcs->each(function ($ulc) use ($clientLt) {
                            $ulc->leave_type_id = $clientLt->getKey();
                            $ulc->save();
                        });
                    });
            }

           $leaveType->delete();
        });

        $site->days()->sync([]);
        $site->holidays()->sync([]);

        return response()->json(['message' =>__('messages.RemoveSettingsSuccess')]);
    }
}
