<?php

namespace App\Http\Controllers;

use App\Lib\Tools;
use App\Models\Api\Site;
use App\Models\Api\Tag;
use App\Models\Api\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\Auth;
use Orion\Concerns\DisableAuthorization;
use Orion\Http\Controllers\Controller;
use Orion\Http\Requests\Request;


class OrionTagsController extends Controller
{
    use DisableAuthorization;

    protected $model = Tag::class;

    /**
     * The attributes that are used for filtering.
     *
     * @return array
     */
    public function filterableBy(): array
    {
        return ['user_id', 'label', 'id'];
    }

    /**
     * The attributes that are used for searching.
     *
     * @return array
     */
    public function searchableBy(): array
    {
        return ['label'];
    }

    /**
     * The attributes that are used for sortable.
     *
     * @return array
     */
    public function sortableBy(): array
    {
        return ['label'];
    }

    /**
     * The attributes that are used for includes.
     *
     * @return array
     */
    public function includes(): array
    {
        return ['user', 'users'];
    }

    public function exposedScopes(): array
    {
        return [
            'withUsersOnly',
        ];
    }

    protected function buildIndexFetchQuery(Request $request, array $requestedRelations): Builder
    {
        $query = parent::buildIndexFetchQuery($request, $requestedRelations);
        $currentUser = Tools::getCurrentUserWithUuid();

        $query->select('tags.*')
            ->join('users', 'users.id', 'tags.user_id')
            ->join('sites', 'sites.id', 'users.site_id')
            ->join('clients', 'clients.id', 'sites.client_id')
            ->where('clients.id', $currentUser->load('site.client')->site->client->getKey())
            ->join('profiles', 'profiles.id', 'users.profile_id')
            ->where('tags.user_id', $currentUser->getKey());

        return $query;
    }

    public function beforeBatchUpdate(Request $request)
    {
        $currentUser = Tools::getCurrentUserWithUuid();

        foreach ($request->get('resources') as $key => $resource) {
            if (Tag::where('label', $resource['label'])->where('user_id', $currentUser->id)->where('id', '!=', $key)->exists()) {
                return response(['message' => __('warnings.ErrorBatchUpdateTags'), 'details' => __('warnings.TagAlreadyExists')], 400);
            }
        }
    }

    public function allTagsWithUsers(Request $request)
    {
        $currentUser = Auth::user();

        if (strtolower($currentUser->profile->label) === 'administrateur') {
            $users = User::select('users.*')
                ->join('sites as s', 's.id', 'users.site_id')
                ->where('s.client_id', $currentUser->site->client_id)
                ->orderBy('lastname')
                ->paginate($request->query('limit'), ['*'], 'page', $request->query('page'));

            $tags['users'] = $users;
        }else {
            $totalCollaborators = [];
            $tags['tags'] = Tag::select('tags.*')->whereHas('users', function ($query) use ($currentUser) {
                $query->where('tags.user_id', $currentUser->getKey());
            })
                ->with('users', function ($query) {
                    $query->orderBy('users.lastname');
                })
                ->withCount('users')
                ->orderBy('label')
                ->paginate($request->query('limit'), ['*'], 'page', $request->query('page'));

            foreach ($tags['tags'] as $tag) {
                $tag->users()->each(function ($user) use (&$totalCollaborators){
                    if (!in_array($user->getKey(), $totalCollaborators)) {
                        $totalCollaborators[] = $user->getKey();
                    }
                });
            }

            $users = User::leftJoin('managers as manager_managed_user_no_one_else', function (JoinClause $joinQuery) use ($currentUser) {
                $joinQuery->on('manager_managed_user_no_one_else.managed_id', 'users.id')
                    ->where('manager_managed_user_no_one_else.manager_id', $currentUser->getKey());
            })
                ->when(strtolower($currentUser->profile->label) === 'director', function ($query) use ($currentUser) {
                    $query->leftJoin('director_user as du', function (JoinClause $joinQuery) use ($currentUser) {
                        $joinQuery->on('du.user_id', 'users.id')->where('du.director_id', $currentUser->getKey());
                    });
                })
                ->where(function (Builder $whereQuery) use ($currentUser) {
                    $whereQuery->whereNotNull('manager_managed_user_no_one_else.manager_id')
                        ->when(strtolower($currentUser->profile->label) === 'director', function ($query) {
                            $query->orWhere(function ($orWhereQuery) {
                                $orWhereQuery->whereNotNull('du.director_id');
                            });
                        });
                })
                ->where(function (Builder $whereQuery) use ($currentUser) {
                    $whereQuery->whereDoesntHave('tags')
                        ->orWhereHas('tags', function (Builder $whereQuery) use ($currentUser) {
                            $whereQuery->whereNotIn('user_tags.user_id', function ($query) use ($currentUser) {
                                $query->select('ut2.user_id')
                                    ->from('user_tags as ut2')
                                    ->join('tags as t2', 't2.id', 'ut2.tag_id')
                                    ->where('t2.user_id', $currentUser->getKey());
                            });
                        });
                })
                ->orderBy('lastname')
                ->get();

            foreach ($users as $user) {
                if (!in_array($user->getKey(), $totalCollaborators)) {
                    $totalCollaborators[] = $user->getKey();
                }
            }

            if (strtolower($currentUser->profile->label) === 'administrateurmanager') {
                $usersNotManaged = User::select('users.*')
                    ->join('sites as s', 's.id', 'users.site_id')
                    ->where('s.client_id', $currentUser->site->client_id)
                    ->whereDoesntHave('managers', function ($query) use ($currentUser){
                        $query->where('managers.manager_id', $currentUser->id);
                    })
                    ->orderBy('lastname')
                    ->get();

                foreach ($usersNotManaged as $userNotManaged) {
                    if (!in_array($userNotManaged->getKey(), $totalCollaborators)) {
                        $totalCollaborators[] = $userNotManaged->getKey();
                    }
                }
            }


            if ((int)$request->query('page') === 1) {
                $tags['tags'][] = Tag::query()->hydrate([['id' => -1, 'label' => __('messages.UserWithNoTags'), 'users' => $users, 'users_count' => $users->count()]])->first();
                if (strtolower($currentUser->profile->label) === 'administrateurmanager') {
                    $tags['tags'][] = Tag::query()->hydrate([['id' => -2, 'label' => __('messages.UserNotManaged'), 'users' => $usersNotManaged, 'users_count' => $usersNotManaged->count()]])->first();
                }
            }
            $tags['totalCollaborators'] = count($totalCollaborators);
        }
        return $tags;
    }
}
