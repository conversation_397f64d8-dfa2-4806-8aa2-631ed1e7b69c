<?php

namespace App\Http\Controllers;

use App\Events\DetachUserDirected;
use App\Lib\Tools;
use App\Models\Api\User;
use Illuminate\Http\Request;

class DirectorUserController extends Controller
{
    public function attach(Request $request)
    {
        $currentUser = Tools::getCurrentUserWithUuid();

        foreach ($request->input()['resources'] as $resource) {
            abort_if(User::find($resource)->site->client_id !== $currentUser->site->client_id, 422, __('messages.UserNotInClient'));
        }

        $currentUser->usersDirected()->attach($request->input()['resources']);

        return response()->json(['message' => __('messages.SuccessAttachUsersToDirector')]);

    }

    public function detach(Request $request)
    {

        //TODO: check if user is director of the users he wants to detach (if not throw error)
        $currentUser = Tools::getCurrentUserWithUuid();

        $currentUser->usersDirected()->detach($request->input('resources'));

        DetachUserDirected::dispatch($request->input('resources'), $currentUser);

        return response()->json(['message' => __('messages.SuccessAttachUsersToDirector')]);
    }
}
