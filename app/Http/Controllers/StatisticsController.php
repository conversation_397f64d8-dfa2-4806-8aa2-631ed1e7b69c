<?php

namespace App\Http\Controllers;

use App\Http\Requests\LeavesByTypeRequest;
use App\Http\Requests\Statistics\LeavesPerUsersRequest;
use App\Lib\Tools;
use App\Models\Api\Leave;
use App\Models\Api\User;
use App\Utils\Statistics\StatisticsBuilder;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class StatisticsController extends Controller
{
    public function realTimeLeavesConsumption(Request $request): JsonResponse
    {
        $currentUser = Tools::getCurrentUserWithUuid();
        $clientId = $currentUser->site->client_id;

        if (!$currentUser->canSeeStatistics()) {
            return response()->json(
                ['message' => __('warnings.BadProfile')],
                403
            );
        }

        $stats = DB::table('leave_types as lt')
            ->select(
                DB::raw('MAX(lt.id) as leave_type_id'),
                'ulc.is_last_year as is_last_year',
                'lt.name',
                DB::raw('SUM(ulc.acquired) as acquired'),
                DB::raw('ROUND(SUM(ulc.taken), 2) as taken'),
                DB::raw('SUM(l.duration) as future'),
                DB::raw('MAX(lt.color) as color'),
            )
            ->join('user_leave_counts as ulc', 'ulc.leave_type_id', 'lt.id')
            ->join('leaves as l', function ($join) {
                $join->on('l.user_id', 'ulc.user_id')
                    ->whereColumn('l.leave_type_id', 'lt.id');
            })
            ->join('users', 'users.id', 'ulc.user_id')
            ->join('statuses as s', function (JoinClause $joinQuery) {
                $joinQuery->on('s.id', 'l.status_id')
                    ->whereIn('s.tag', ['VALIDATED', 'SUBMITTED']);
            })
            ->join('sites as site', 'site.id', 'users.site_id')
            ->when($request->input('site'), function ($query) use ($request) {
                $query->where('site.uuid', $request->input('site'));
            })
            ->when(!in_array($currentUser->profile->label, ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER', 'DIRECTOR']),
                fn(Builder $whenQuery) => StatisticsBuilder::applyManagerQuery($whenQuery, $currentUser)
            )
            ->where('lt.client_id', $clientId)
            ->where('lt.is_active', 1)
            ->where('lt.needs_count', 1)
            ->groupBy('lt.name', 'ulc.is_last_year')
            ->get();

        return response()->json([
            'data' => $stats
        ]);
    }

    public function leavesByType(LeavesByTypeRequest $request): JsonResponse
    {
        $currentUser = Tools::getCurrentUserWithUuid();
        $clientId = $currentUser->site->client_id;
        $stats = [];

        if (!$currentUser->canSeeStatistics()) {
            return response()->json(
                ['message' => __('warnings.BadProfile')],
                403
            );
        }

        $leaves = DB::table('leaves as l')
            ->select('l.*', 'lt.name as lt_name', 'lt.color as lt_color')
            ->join('leave_types as lt', 'lt.id', 'l.leave_type_id')
            ->join('users', 'users.id', 'l.user_id')
            ->join('sites as s', 's.id', 'users.site_id')
            ->join('statuses', 'statuses.id', 'l.status_id')
            ->where('lt.client_id', $clientId)
            ->whereIn('statuses.tag', ['VALIDATED', 'SUBMITTED', 'TRANSMITTED'])
            ->when($leaveTypeId = $request->validated('leave_type_id'), function ($query) use ($leaveTypeId) {
                $query->where('l.leave_type_id', $leaveTypeId);
            })
            ->when($siteId = $request->validated('site'), function ($query) use ($siteId) {
                $query->where('s.uuid', $siteId);
            })
            ->when($userId = $request->validated('user_id'), function (Builder $query) use ($userId) {
                $query->where('users.uuid', $userId);
            })
            ->when(!$request->validated('start_date') && !$request->validated('end_date'), function (Builder $query) {
                $query->where(function ($query) {
                    $query->where(function (Builder $query) {
                        $query->whereBetween('l.start_date', [Carbon::now()->subYear()->startOfMonth(), Carbon::now()->endOfMonth()]);
                    })->orWhere(function (Builder $query) {
                        $query->whereBetween('l.end_date', [Carbon::now()->subYear()->startOfMonth(), Carbon::now()->endOfMonth()]);
                    })->orWhere(function ($query) {
                        $query->where('l.start_date', '<=', Carbon::now()->subYear()->startOfMonth())
                            ->where('l.end_date', '>=', Carbon::now()->endOfMonth());
                    });
                });
            })
            ->when(
                $request->validated('start_date') && $request->validated('end_date'),
                fn(Builder $query) => StatisticsBuilder::applyDateRange(
                    $query,
                    'l',
                    ['start_date' => $request->validated('start_date'), 'end_date' => $request->validated('end_date')]
                )
            )
            ->when(!in_array($currentUser->profile->label, ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER', 'DIRECTOR']),
                fn(Builder $whenQuery) => StatisticsBuilder::applyManagerQuery($whenQuery, $currentUser)
            )
            ->orderBy('l.start_date')
            ->get();

        $period = $request->validated('period');
        $offset = $request->input('offset', 0);

        $carbonPeriod = $this->generatePeriod($offset, $period);

        switch ($period) {
            case 'year':
                $carbonPeriod->forEach(function (Carbon $date) use (&$stats, $leaves) {
                    $stats[$date->year] = $leaves->filter(function ($leave) use ($date) {
                        $leaveModel = Leave::query()->hydrate([$leave])->first();

                        return Arr::get($leaveModel->leave_days_distribution, $date->year);
                    })
                        ->map(function ($leave) use ($date) {
                            $leaveModel = Leave::query()->hydrate([$leave])->first();

                            return [
                                'name' => $leaveModel->lt_name,
                                'amount' => count($leaveModel->leave_days_distribution->get($date->year)),
                                'color' => $leaveModel->lt_color,
                            ];
                        })
                        ->groupBy('name')
                        ->map(fn($group) => ['name' => $group->first()['name'], 'amount' => $group->sum('amount'), 'color' => $group->first()['color']])
                        ->values();
                });
                break;
            case 'month':
                $carbonPeriod->forEach(function (Carbon $date) use (&$stats, $leaves) {
                    $stats["$date->year-$date->month"] = $leaves->filter(function ($leave) use ($date) {
                        $leaveModel = Leave::query()->hydrate([$leave])->first();

                        $distributionOnYear = Arr::get($leaveModel->leave_days_distribution, $date->year) ?? [];

                        return Arr::where($distributionOnYear, fn($value) => $value['month'] === $date->format('m'));
                    })
                        ->map(function ($leave) use ($date) {
                            $leaveModel = Leave::query()->hydrate([$leave])->first();

                            return [
                                'name' => $leaveModel->lt_name,
                                'amount' => collect($leaveModel->leave_days_distribution[$date->year])->filter(fn($value) => $value['month'] === $date->format('m'))->count(),
                                'color' => $leaveModel->lt_color,
                            ];
                        })
                        ->groupBy('name')
                        ->map(fn($group) => ['name' => $group->first()['name'], 'amount' => $group->sum('amount'), 'color' => $group->first()['color']])
                        ->values();
                });
                break;
            case 'week':
                $carbonPeriod->forEach(function (Carbon $date) use (&$stats, $leaves) {
                    $stats["{$date->startOfWeek()->year}-$date->isoWeek"] = $leaves->filter(function ($leave) use ($date) {
                        $leaveModel = Leave::query()->hydrate([$leave])->first();

                        $distributionOnYear = Arr::get($leaveModel->leave_days_distribution, $date->startOfWeek()->year) ?? [];

                        return Arr::where($distributionOnYear, fn($value) => $value['week'] === $date->isoWeek);
                    })
                        ->map(function ($leave) use ($date) {
                            $leaveModel = Leave::query()->hydrate([$leave])->first();

                            return [
                                'name' => $leaveModel->lt_name,
                                'amount' => collect($leaveModel->leave_days_distribution[$date->startOfWeek()->year])->filter(fn($value) => $value['week'] === $date->isoWeek)->count(),
                                'color' => $leaveModel->lt_color,
                            ];
                        })
                        ->groupBy('name')
                        ->map(fn($group) => ['name' => $group->first()['name'], 'amount' => $group->sum('amount'), 'color' => $group->first()['color']])
                        ->values();
                });
                break;
            default:
                $periodStartDate = Carbon::parse($request->validated('start_date'))->format('Y-m-d');
                $periodEndDate = Carbon::parse($request->validated('end_date'))->format('Y-m-d');

                $stats["$periodStartDate - $periodEndDate"] = $leaves->map(function ($leave) use ($periodStartDate, $periodEndDate) {
                    $leaveModel = Leave::query()->hydrate([$leave])->first();

                    $filteredDistribution = $leaveModel->leave_days_distribution
                        ->map(function ($distributions, $year) use ($periodStartDate, $periodEndDate) {
                            return collect($distributions)
                                ->filter(function ($distribution) use ($year, $periodStartDate, $periodEndDate) {
                                    $carbonDistribution = Carbon::create($year, $distribution['month'], $distribution['day']);

                                    return $carbonDistribution->isBetween($periodEndDate, $periodStartDate);
                                });
                        })->values()->toArray();

                    return [
                        'name' => $leaveModel->lt_name,
                        'amount' => count(array_merge(...$filteredDistribution)),
                        'color' => $leaveModel->lt_color,
                    ];
                })
                    ->groupBy('name')
                    ->map(fn($group) => ['name' => $group->first()['name'], 'amount' => $group->sum('amount'), 'color' => $group->first()['color']])
                    ->values();
        }

        return response()->json(['data' => $stats]);
    }

    public function leavesPerUsers(LeavesPerUsersRequest $request): JsonResponse
    {
        $currentUser = Tools::getCurrentUserWithUuid();
        $clientId = $currentUser->site->client_id;
        $sorts = $request->input('sorts', []);

        if (!$currentUser->canSeeStatistics()) {
            return response()->json(
                ['message' => __('warnings.BadProfile')],
                403
            );
        }

        $leavesPerUsers = DB::table('leaves')
            ->select(
                'users.id as user_id',
                'users.lastname as lastname',
                'users.firstname as firstname',
                'leave_types.name as leave_type',
                'leaves.duration as duration',
                'leaves.start_date',
                'leaves.end_date',
                'sites.name as site'
            )
            ->join('leave_types', 'leave_types.id', 'leaves.leave_type_id')
            ->join('statuses', function (JoinClause $joinQuery) {
                $joinQuery->on('statuses.id', 'leaves.status_id')
                    ->whereIn('statuses.tag', ['VALIDATED', 'TRANSMITTED']);
            })
            ->join('users', 'users.id', 'leaves.user_id')
            ->join('sites', 'sites.id', 'users.site_id')
            ->when($search = $request->validated('search'), function (Builder $whenQuery) use ($search) {
                $whenQuery->where(function (Builder $whereQuery) use ($search) {
                    $whereQuery->where(DB::raw("CONCAT(users.lastname, ' ', users.firstname)"), 'like', "%$search%")
                        ->orWhere(DB::raw("CONCAT(users.firstname, ' ', users.lastname)"), 'like', "%$search%");
                });
            })
            ->when(
                $period = $request->validated('period'),
                fn(Builder $whenQuery) => StatisticsBuilder::applyDateRange($whenQuery, 'leaves', $period)
            )
            ->when($leaveTypeIds = $request->validated('leave_type_ids'), function (Builder $query) use ($leaveTypeIds) {
                $query->whereIn('leave_types.id', $leaveTypeIds);
            })
            ->when($siteId = $request->validated('site'), function (Builder $query) use ($siteId) {
                $query->where('sites.uuid', $siteId);
            })
            ->when($userId = $request->validated('user_id'), function (Builder $query) use ($userId) {
                $query->where('users.uuid', $userId);
            })
            ->when(!in_array($currentUser->profile->label, ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER', 'DIRECTOR']),
                fn(Builder $whenQuery) => StatisticsBuilder::applyManagerQuery($whenQuery, $currentUser)
            )
            ->where('leave_types.client_id', $clientId)
            ->orderByRaw('leaves.start_date DESC,users.lastname, users.firstname')
            ->groupBy('leaves.id');

        StatisticsBuilder::applySorts($leavesPerUsers, $sorts);

        return response()->json(
            $leavesPerUsers->paginate(20)
        );
    }

    private function generatePeriod(int $offset, string $type = null): ?CarbonPeriod
    {
        return match ($type) {
            'year' => CarbonPeriod::create(
                Carbon::now()->addYear()->subYears(5 + $offset),
                "1 year",
                Carbon::now()->addYear()->subYears($offset)
            ),
            'month' => CarbonPeriod::create(
                Carbon::now()->addMonths(6)->subMonths(12 + $offset),
                "1 month",
                Carbon::now()->addMonths(6)->subMonths($offset)
            ),
            'week' => CarbonPeriod::create(
                Carbon::now()->addWeeks(5)->subWeeks(11 + $offset),
                "1 week",
                Carbon::now()->addWeeks(5)->subWeeks($offset)
            ),
            default => null,
        };
    }
}
