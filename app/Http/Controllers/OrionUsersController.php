<?php

namespace App\Http\Controllers;

use Illuminate\Database\Eloquent\Builder;
use App\Lib\Tools;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Str;
use Orion\Concerns\DisableAuthorization;
use App\Models\Api\User;
use Orion\Http\Controllers\Controller;
use Orion\Http\Requests\Request;

class OrionUsersController extends Controller
{
    use DisableAuthorization;

    protected $model = User::class;

    /**
     * The attributes that are used for filtering.
     *
     * @return array
     */
    public function filterableBy(): array
    {
        return [
            'tags.id', 'id', 'site.id', 'managers.manager_id', 'matricule', 'enter_date', 'lastname', 'user_leave_counts.leave_type',
            'user_leave_counts.balance', 'directors.director_id'
        ];
    }

    /**
     * The attributes that are used for searching.
     *
     * @return array
     */
    public function searchableBy(): array
    {
        return ['firstname', 'lastname', 'site.name'];
    }

    /**
     * The attributes that are used for sortable.
     *
     * @return array
     */
    public function sortableBy(): array
    {
        return ['roles.name', 'lastname', 'firstname', 'matricule', 'enter_date', 'site.name', 'site_id'];
    }

    /**
     * The attributes that are used for includes.
     *
     * @return array
     */
    public function includes(): array
    {
        return [
            'tags', 'managers', 'user_leave_counts', 'user_leave_counts.leave_type', 'site', 'user_tags', 'managed', 'leaves', 'leaves.status', 'days', 'client',
            'usersDirected'
        ];
    }

    public function exposedScopes(): array
    {
        return ['viewDirectManagedUsers', 'viewUndirectManagedUsers'];
    }

    public function aggregates(): array
    {
        return ['directors'];
    }

    protected function buildFetchQuery(Request $request, array $requestedRelations): Builder
    {
        $query = parent::buildFetchQuery($request, $requestedRelations);

        $currentUser = Tools::getCurrentUserWithUuid();

        if ($search = $request->input('search-users.value')) {

            $query->where(function (Builder $query) use ($search) {
                $query->whereRaw("CONCAT(users.firstname, ' ', users.lastname) like ?", "%{$search}%")
                    ->orWhereRaw("CONCAT(users.lastname, ' ', users.firstname) like ?", "%{$search}%");
            });
        }

        if ($request->has('planningTags')) {
            $query->select('users.*', 't.id as tag_id', 't.label as tag_label')
                ->leftJoin('user_tags as ut', 'ut.user_id', 'users.id')
                ->leftJoin('tags as t', 't.id', 'ut.tag_id')
                ->where('t.user_id', $currentUser->id)
                ->orderBy('t.label');
        } elseif ($request->has('searchDirectors')) {
            $query->select('users.*')
                ->join('sites as s', 's.id', 'users.site_id')
                ->leftJoin('managers as man', function ($join) use ($currentUser) {
                    $join->on('man.managed_id', 'users.id')
                        ->where('man.manager_id', $currentUser->id);
                })
                ->leftJoin('managers as man2', 'man2.managed_id', 'man.managed_id')
                ->leftJoin('director_user', 'director_user.user_id', 'users.id')
                ->where('s.client_id', $currentUser->site->client->id)
                ->where('users.id', '!=', $currentUser->id)
                ->whereNot('s.name', 'HOLDING GALAXITY')
                ->whereNull('man.managed_id')
                ->whereNull('director_user.director_id');
        } else {
            $query->select('users.*');

            if (in_array(strtolower($currentUser->profile->label), ['administrateur', 'administrateurmanager'])) {
                $query->join('sites as s', 's.id', 'users.site_id')
                    ->where('s.client_id', $currentUser->site->client->id);
            } else {
                $query->leftJoin('managers as manager_managed_user_no_one_else', function (JoinClause $joinQuery) use ($currentUser) {
                    $joinQuery->on('manager_managed_user_no_one_else.managed_id', 'users.id')
                        ->where('manager_managed_user_no_one_else.manager_id', $currentUser->id);
                })
                ->when(strtolower($currentUser->profile->label) === 'director', function ($query) use ($currentUser) {
                    $query->leftJoin('director_user as du', function (JoinClause $joinQuery) use ($currentUser) {
                        $joinQuery->on('du.user_id', 'users.id')->where('du.director_id', $currentUser->id);
                    });
                })
                ->where(function (Builder $whereQuery) use ($request, $currentUser) {
                    $whereQuery->whereNotNull('manager_managed_user_no_one_else.manager_id')
                        ->when(strtolower($currentUser->profile->label) === 'director', function ($query) {
                            $query->orWhere(function ($orWhereQuery) {
                                $orWhereQuery->whereNotNull('du.director_id');
                            });
                        });
                        $whereQuery->orWhere('users.id', $currentUser->id);
                });
            }
        }

        $aggregates = $request->input('aggregates', []);

        foreach ($aggregates as $aggregate) {
            $query->withAggregate(
                [
                    $aggregate['relation'] => function ($query) use ($request) {
                        $this->getQueryBuilder()->applyFiltersToQuery($query, $request);
                    }
                ], $aggregate['field'] ?? '*', $aggregate['type']
            );
        }

        return $query;
    }

    public function getFetchQuery(Request $request, $requestedRelations = [])
    {
        $action = $request->route()->getAction();
        $action['controller'] = Str::replace(explode('@', $action['controller'])[1], 'search', $action['controller']);
        $request->route()->setAction($action);

        $query = parent::buildFetchQuery($request, $requestedRelations);
        $query->select('users.*');

        return $query;
    }
}
