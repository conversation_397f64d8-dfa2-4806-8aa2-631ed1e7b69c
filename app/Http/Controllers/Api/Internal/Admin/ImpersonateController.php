<?php

namespace App\Http\Controllers\Api\Internal\Admin;

use Xefi\JWTAuth\Exceptions\JWTParsingException;
use App\Interfaces\Controllers\ImpersonateControllerInterface;
use Xefi\JWTAuth\Lib\AKT;
use App\Lib\ImpersonateJWT;
use Xefi\JWTAuth\Lib\JWT;
use App\Models\Api\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;

class ImpersonateController implements ImpersonateControllerInterface
{
    /**
     * @inheritDoc
     */
    public function initImpersonation($idUser)
    {
    }

    /**
     * @inheritDoc
     */
    public function endImpersonation()
    {
        $userJWT = Request::header("ImpersonatedJWT");
        try {
            $JWT = (new ImpersonateJWT())->parseToken($userJWT);
        } catch (JWTParsingException $exception) {
            return response()->json(["error" => __('warnings.InternalError')], 500);
        }
        $JWT->setUser(User::where("uuid", $JWT->extractCustomClaim("uuid"))->first());
        $JWT->destroy(true);

        return response()->json(["Ok"], 200);
    }
}
