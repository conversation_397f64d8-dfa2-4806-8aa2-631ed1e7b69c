<?php


namespace App\Http\Controllers\Api\Internal\User;


use Xefi\InternalCom\InternalComLib;
use App\Models\Api\Application;
use App\Models\Api\User;

class UserController
{
    public function show($clientId)
    {
        try {
            $clientUsers = User::where('client_id', $clientId)->get();
            $clientsArr = [];
            foreach($clientUsers as $user) {
                $clientsArr[$user['uuid']] = $user;
            }
        } catch (\Exception $exception) {
            return response()->json(['error' => $exception], 500);
        }
        return response()->json($clientsArr, 200);
    }

    public function store()
    {
        $data = [
            'uuid' => Request::json('uuid'),
            'client_id' => Request::json('client_id'),
            'profile_id' => Request::json('profile_id'),
            'db_host_crm' => Request::json('db_host_crm'),
            'db_database_crm' => Request::json('db_database_crm'),
        ];

        try {
            User::create($data);
        } catch (\Exception $exception) {
            return response()->json(['error' => $exception], 500);
        }
        return response()->json(['success' => __('warnings.AddUsersSuccess')], 200);
    }

    public function destroy($uuid)
    {

        try {
            User::where('uuid', $uuid)->delete();
        } catch (\Exception $exception) {
            return response()->json($exception);
        }
        return response()->json(['succes' => __('warnings.DellUsersSuccess')], 200);
    }

    public function update($uuid) {
        $data = Request::json();
        $userToUpdate = User::where('uuid', $uuid)->first();
        try {
            $userToUpdate['profile_id'] = $data->get('profile_id');
            $userToUpdate->save();
        } catch (\Exception $exception) {
            return response()->json(['error' => $exception], 500);
        }
        return response()->json($userToUpdate, 200);
    }

    public function logout()
    {
        $jwt = Request::header('Authorization');
        try {
            $portal = Application::where('name', 'portail')->first();
            $comLib = new InternalComLib($portal);
            $comLib->get('auth/logout', ['Authorization' => $jwt]);
        } catch (\Exception $ex) {
            return response()->json($ex->getMessage(), 500);
        }
        return response()->json(__('warnings.LogoutSuccess', 200));
    }
}
