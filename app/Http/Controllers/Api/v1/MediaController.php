<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Requests\MediaRequest;
use App\Utils\Traits\Orion\GivesGatesToResponse;
use Illuminate\Support\Facades\Storage;
use Orion\Concerns\DisableAuthorization;
use Orion\Http\Controllers\Controller;
use Orion\Http\Requests\Request;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class MediaController extends Controller
{
    use DisableAuthorization, GivesGatesToResponse;

    protected $model = Media::class;

    protected $request = MediaRequest::class;

    public function download(Media $media)
    {
        $path = $media->id.'/'.$media->file_name;
        $contents = Storage::disk($media->disk)->get($path);

        return response($contents, 200, [
            'Content-Type' => $media->mime_type,
            'Content-Disposition' => 'attachment; filename="'.$media->file_name.'"',
        ]);
    }

    public function store(Request $request)
    {
        $entity = $request->input('model_type')::find($request->input('model_id'));
        $name = $request->input('name');

        if ($request->has('media_documentation')) {
            foreach ($request->file('media_documentation') as $media) {
                $entity->addMedia($media)->usingName($name)->toMediaCollection($request->input('collection_name'));
            }
        }
    }

    public function update(Request $request, ...$args)
    {
        $entity = $request->input('model_type')::find($request->input('model_id'));
        $name = $request->input('name');

        if ($request->has('media_documentation')) {
            $entity->deleteMedia($args);
            foreach ($request->file('media_documentation') as $media) {
                $entity->addMedia($media)->usingName($name)->toMediaCollection($request->input('collection_name'));
            }
        } else {
            $media = Media::find($args);
            $media->update(['name' => $name, 'file_name' => $name]);
        }
    }
}
