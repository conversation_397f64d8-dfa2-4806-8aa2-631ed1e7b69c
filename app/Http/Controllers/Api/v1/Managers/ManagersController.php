<?php

namespace App\Http\Controllers\Api\v1\Managers;

use App\Http\Controllers\Api\v1\Controller;
use App\Interfaces\Controllers\Api\BaseApiControllerInterface;
use App\Lib\ApiHelper;
use App\Models\Api\Manager;
use App\Models\Api\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Throwable;

class ManagersController extends Controller implements BaseApiControllerInterface
{

    #region Public Functions

    public function index(Request $request): JsonResponse
    {
        try {
            throw new Exception(__('warnings.ErrorGetManager'),  405);
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetManager');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    public function show(Request $request, int $id): JsonResponse
    {
        try {
            throw new Exception(__('warnings.ErrorGetManager'));
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetManager');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    /**
     * Ajout d'un manager pour un utilisateur
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            throw new Exception(__('warnings.ErrorStoreManager'));
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorStoreManager');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    /**
     * Modification d'un manager pour un utilisateur
     *
     * @param Request $request
     * @param int $id
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            throw new Exception(__('warnings.ErrorUpdateManager'));
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateManager');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    /**
     * Suppression d'un manager pour un utilisateur
     *
     * @param int $id
     *
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            throw new Exception(__('warnings.ErrorDeleteManager'));
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorDeleteManager');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    #endregion
}
