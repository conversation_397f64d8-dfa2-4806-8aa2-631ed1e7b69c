<?php

namespace App\Http\Controllers\Api\v1\Tags;

use App\Http\Controllers\Api\v1\Controller;
use App\Lib\ApiHelper;
use App\Models\Api\Tag;
use App\Models\Api\User;
use App\Models\Api\UserTag;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class TagsController extends Controller
{

    private array $validationUpdate = [
        "label" => "filled|string",
    ];

    private array $validationStore = [
        "label" => "required|string",
    ];

    private array $validationAttachDetach = [
        "user_id" => "required|integer",
        "tag_id" => "required|integer",
    ];

    private array $validationMultipleAttachDetach = [
        "user_id" => "required|integer",
        "attach" => "nullable|array",
        "detach" => "nullable|array",
    ];

    /**
     * Vérifie que l'utilisateur connecté à access au tag
     *
     * @param $tag
     * @param bool $verifAdmin
     * @return bool
     * @throws Exception
     */
    private function canAccessTag($tag): bool
    {
        // Le tag appartient à l'utilisateur connecté
        if ($tag->user_id == $this->currentUser->id)
            return true;

        throw new Exception(__('warnings.NotAccessToTag'), 403);
    }

    /**
     * Récupération des tags
     *
     * @param Request $request
     * @return JsonResponse
     * @throws Exception
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $tagModel = Tag::query()->where("user_id", $this->currentUser->id);

            // Pagination des données
            $this->responseData = ApiHelper::defaultModelBuilder($tagModel,
                $request->get("orders", "id,asc"),
                $request->get("limit", null),
                $request->get("paginate", 0)
            );

            // Add an empty value
            if ($request->query("withEmptyValue")) {
                $emptyValue = [
                    "id" => 0,
                    "label" => "---"
                ];
                $this->responseData["data"] = $this->responseData["data"]->toArray();
                array_unshift($this->responseData["data"], $emptyValue);
                $this->responseData["data"] = collect($this->responseData["data"]);
            }

            // Récupère uniquement les labels
            if ($request->query("onlyLabel") == true) {
                $this->responseData["data"] = $this->responseData["data"]->pluck("label");
            }

            $this->getResponse = true;
            $this->responseMessage = __('warnings.SuccessGetTags');
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetTags');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    /**
     * Récupère les users appartenant à un tag
     * @param $id
     * @return JsonResponse
     */
    public function getUsersInTag($id): JsonResponse
    {
        try {
            // Vérification de l'id
            $this->basicIdVerification($id);

            $userTag = UserTag::where("tag_id", $id)->get()->pluck("user_id");
            $users = User::whereIn("id", $userTag)->get();

            $this->responseMessage = __('warnings.SuccessGetUser');
            $this->responseData = $users->toArray();

        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetUsers');
            $this->responseCode = $exception->getCode();
        }
        return $this->formattedResponse();
    }

    /**
     * Récupération d'un tag
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            // Vérification de l'id
            $this->basicIdVerification($id);

            // Récupération du site
            $tag = Tag::find($id);

            if (is_null($tag)) {
                throw new Exception(__('warnings.TagDontExist'),  404);
            }

            $this->canAccessTag($tag);

            $this->responseMessage = __('warnings.SuccessGetTag');
            $this->responseData = $tag->toArray();
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetTag');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    /**
     * Modification du tag passé en paramètre
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            // Vérifications
            $this->basicIdVerification($id);
            $data = $this->validate($request, $this->validationUpdate, $this->messages());

            // Récupération du tag
            $tag = Tag::find($id);
            if (is_null($tag)) {
                throw new Exception(__('warnings.TagDontExist'),  404);
            }

            $this->canAccessTag($tag);

            if (Tag::where('label', $tag['label'])->where('user_id', $tag['user_id'])->where('id', '!=', $id)->exists()) {
                throw new Exception('TagAlreadyExists', 400);
            } else {
                // Mise à jour
                $tag->update($data);
                $this->responseMessage = __('warnings.SuccessUpdateTag');
                $this->responseData = $tag->toArray();
            }

        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag()->unique();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateTag');
            $this->responseCode = $exception->status;
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateTag');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    /**
     * Création d'un tag
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            // Validation des données reçu en POST
            $tag = $this->validate($request, $this->validationStore, $this->messages());

            $tag["user_id"] = $this->currentUser->id;

            if (Tag::where('label', $tag['label'])->where('user_id', $tag['user_id'])->exists()) {
                throw new Exception('TagAlreadyExists', 400);
            } else {
                Tag::create($tag);
                $this->responseMessage = __('warnings.SuccessStoreTag');
                $this->responseData = $tag;
            }

        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag()->unique();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorStoreTag');
            $this->responseCode = $exception->status;
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorStoreTag');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    /**
     * Suppression du tag passé en paramètre
     *
     * @param int $id
     * @return JsonResponse
     */
    public function delete(int $id): JsonResponse
    {
        try {
            // Vérifications de l'id
            $this->basicIdVerification($id);

            // Récupération du tag
            $tag = Tag::find($id);
            if (is_null($tag)) {
                throw new Exception(__('warnings.TagDontExist'),404);
            }

            $this->canAccessTag($tag);

            // Suppression
            UserTag::query()->where('tag_id', $tag->getKey())->delete();
            $tag->delete();

            $this->responseMessage = __('warnings.SuccessDeleteTag');
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorDeleteTag');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    /**
     * Attach un user à un tag
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function attach(Request $request): JsonResponse
    {
        try {
            $data = $this->verifAttachDetach($request);

            $userTag = UserTag::where([
                ["user_id", $data["user_id"]],
                ["tag_id", $data["tag_id"]],
            ])->first();
            if (!is_null($userTag)) throw new Exception(__('warnings.RelationAlreadyExist'),  400);

            // Attach
            UserTag::create([
                "user_id" => $data["user_id"],
                "tag_id" => $data["tag_id"],
            ]);

            $this->responseMessage = __('warnings.SuccessAddUserToTag');
            $this->responseData = $data;

        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag()->unique();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorAddUserToTag');
            $this->responseCode = $exception->status;
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorAddUserToTag');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    /**
     * Detach un user d'un tag
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function detach(Request $request): JsonResponse
    {
        try {
            $data = $this->verifAttachDetach($request);

            // Detach
            $userTag = UserTag::where([
                ["user_id", $data["user_id"]],
                ["tag_id", $data["tag_id"]],
            ])->delete();
            if (!$userTag) throw new Exception(__('warnings.RelationDontExist'), 404);

            $this->responseMessage = __('warnings.SuccessRemoveUserFromTag');
            $this->responseData = $data;

        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag()->unique();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorRemoveUserFromTag');
            $this->responseCode = $exception->status;
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorRemoveUserFromTag');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    /**
     * Vérifie les données lié aux attach et detach et renvoie un array
     *
     * @param $request
     * @return array
     * @throws ValidationException
     * @throws Exception
     */
    public function verifAttachDetach($request): array
    {
        // Validation des données reçu
        $data = $this->validate($request, $this->validationAttachDetach, $this->messages());

        // Vérifications de l'existence des données
        $user = User::find($data["user_id"]);
        if (is_null($user))
            throw new Exception(__('warnings.UserDontExist'),  404);

        $tag = Tag::find($data["tag_id"]);
        if (is_null($tag))
            throw new Exception(__('warnings.TagDontExist'),  404);

        // Check if current user can access data
        $this->canAccessTag($tag);

        return $data;
    }

    /**
     * Attach et Detach des users à un tag
     * @param $request
     * @return JsonResponse
     */
    public function multipleAttachDetach(Request $request): JsonResponse
    {
        try {
            // Validation des données reçu
            $data = $this->validate($request, $this->validationMultipleAttachDetach, $this->messages());

            // Detach
            foreach ($data["detach"] as $tagId) {
                try {
                    UserTag::where([
                        ["user_id", $data["user_id"]],
                        ["tag_id", $tagId],
                    ])->delete();
                } catch (Exception $e) {
                }
            }
            // Attach
            foreach ($data["attach"] as $tagId) {
                try {
                    $userTag = UserTag::where([
                        ["user_id", $data["user_id"]],
                        ["tag_id", $tagId],
                    ])->first();

                    if (!$userTag) {
                        UserTag::create([
                            "user_id" => $data["user_id"],
                            "tag_id" => $tagId,
                        ]);
                    }
                } catch (Exception $e) {
                }
            }

            $this->responseMessage = __('warnings.SuccessUpdateUserTag');
            $this->responseData = [];

        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag()->unique();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateUserTag');
            $this->responseCode = $exception->status;
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateUserTag');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }
}
