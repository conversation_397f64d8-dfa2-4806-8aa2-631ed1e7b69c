<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Lib\ApiHelper;
use App\Models\Api\Alert;
use App\Models\Api\Client;
use App\Models\Api\History;
use App\Models\Api\Leave;
use App\Models\Api\Profile;
use App\Models\Api\Site;
use App\Models\Api\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Storage;

/**
 * @OA\Info(
 *    title="Api Congés BACKEND",
 *    version="v1"
 *  )
 */
class Controller extends BaseController
{
    public $currentUser;
    public ApiHelper $apiHelper;
    public $clientId;


    public function messages()
    {
        return [
            "integer" => ":attribute " . __('warnings.MustBeInteger'),
            "exists" => ":attribute " . __("warnings.Exists"),
            "unique" => ":attribute " . __("warnings.Unique"),
            "array" => ":attribute " . __("warnings.Array"),
            "required" => ":attribute " . __("warnings.IsRequired"),
            "in" => ":attribute " . __("warnings.MustBeEnum") . " :values.",
            "string" => ":attribute " . __("warnings.MustBeString"),
            "boolean" => ":attribute " . __("warnings.MustBeBool"),
            "filled" => ":attribute " . __("warnings.MustBeNotEmpty"),
            "numeric" => ":attribute " . __("warnings.MustBeNumber"),
            "email" => ":attribute " . __("warnings.MustBeEmail"),
            "uuid" => ":attribute " . __("warnings.MustBeUUID"),
            "date" => ":attribute " . __("warnings.MustBeDate"),
            "after" => ":attribute " . __("warnings.MustBeDateAfter") . " :date.",
            "before" => ":attribute " . __("warnings.MustBeDateBefore") . " :date.",
            "date_format" => ":attribute " . __("warnings.MustRespectFormat") . " :format"
        ];
    }

    protected array $defaultIndexFilters = [
        "orders" => "nullable|string",
        "paginate" => "nullable|boolean",
        "limit" => "nullable|integer|min:1",
        "page" => "nullable|integer|min:1"
    ];

    protected array $defaultContentTypeMapper = [
        "pdf" => "application/pdf",
        "jpg" => "image/jpeg",
        "jpeg" => "image/jpeg",
        "png" => "image/png"
    ];

    public int $numberManagersCanValidate;

    public function __construct()
    {
        $this->apiHelper = new ApiHelper();
        if (auth()->check()) {
            $this->currentUser = Auth::user();
            $this->clientId = $this->currentUser->site->client_id;
            $this->numberManagersCanValidate = $this->currentUser->number_managers_can_validate ?? $this->getCurrentClient()->number_managers_can_validate;
        }

        if (Request::header('InternalAkt') === env('INTERNAL_AKT')) {
            abort(404, __('warnings.UserNotFound'));
        }
    }

    protected function getCurrentClient()
    {
        return Client::whereId($this->getCurrentSite()->client_id)->first();
    }

    protected function getCurrentSite()
    {
        return Site::where("client_id", "=", $this->clientId)->first();
    }

    private function verifyCanSendMail($mail, $cc)
    {
        $user = User::where('email', '=', $mail)->first();
        if (!$user->can_receive_mails) {
            $mail = null;
        }
        $ccToReturn = [];
        foreach ($cc as $mailCC) {
            $user = User::where('email', '=', $mailCC)->first();
            if ($user->can_receive_mails) {
                array_push($ccToReturn, $mailCC);
            }
        }
        return [$mail, $ccToReturn];
    }

    /**
     * Returns the response of an appointment
     * @return JsonResponse
     */
    protected function formattedResponse(): JsonResponse
    {
        if (!is_numeric($this->responseCode)) {
            $this->responseCode = 500;
        }

        return response()->json($this->apiHelper
            ->responseFormatter(
                $this->checkHttpStatus($this->responseCode),
                $this->responseData,
                $this->responseMessage,
                $this->responseDetails,
                $this->getResponse
            ),
            $this->checkHttpStatus($this->responseCode)
        );
    }

    /**
     * Check if passed HttpStatus is valid: a three-digit number, between 200 and 599, otherwise return a 500 status
     *
     * @param int|null $httpStatus
     *
     * @return int
     */
    protected function checkHttpStatus(int $httpStatus = null): int
    {
        return (!empty($httpStatus) and preg_match('/^[2-5][0-9]{2}$/', $httpStatus)) ? $httpStatus : 500;
    }

    /**
     * Vérification du paramètre ID que l'on reçoit
     *
     * @param int $id
     *
     * @return void
     * @throws Exception
     */
    protected function basicIdVerification(int $id = null): void
    {
        if ($id) {
            // Si l'id est inférieur où égal à 0
            if ($id <= 0) {
                throw new Exception(__('warnings.ErrorIdAbove'),  400);
            }

            // Si l'id n'est pas un nombre
            if (!is_numeric($id)) {
                throw new Exception(__('warnings.ErrorIdNumber'),  400);
            }
        }
    }

    /**
     * Upload un fichier sur le serveur
     *
     * @param UploadedFile $file
     * @param string $path
     *
     * @return array
     * @throws Exception
     */
    protected function fileUpload(UploadedFile $file, string $path)
    {
        if ($file) {
            $fileSizeInBytes = $file->getSize();

            if ($fileSizeInBytes !== false && $fileSizeInBytes < (5 * 1024 * 1024)) {

                $fileMimeType = $file->getMimeType();

                if (!$fileMimeType) {
                    throw new Exception('Impossible de déterminer le type MIME du fichier.', 415);
                }

                if (in_array($fileMimeType, ["image/jpeg", "image/png", "application/pdf"])) {

                    $filePath = $file->path();
                    if (!$filePath) {
                        throw new Exception('Impossible d\'obtenir le chemin du fichier temporaire.', 500);
                    }

                    if (Storage::disk('s3')->put($path, file_get_contents($filePath))) {
                        return [
                            "path" => $path,
                            "name" => $file->getClientOriginalName()
                        ];
                    } else {
                        throw new Exception(__('warnings.ErrorStoreFile'), 400);
                    }
                } else {
                    $this->responseMessage = __('warnings.ErrorFileType');
                    throw new Exception(__('warnings.ErrorFileType'), 415);
                }
            } else {
                $this->responseMessage = __('warnings.ErrorFileSize');
                throw new Exception(__('warnings.ErrorFileSize'), 413);
            }
        } else {
            $this->responseMessage = __('warnings.ErrorFileDontExist');
            throw new Exception(__('warnings.ErrorFileDontExist'), 400);
        }
    }

    /**
     * Envoi un mail
     *
     * @param string $view
     * @param string $mail
     * @param string $subject
     * @param array $cc
     *
     * @throws Exception
     */
    protected function sendMail(string $view, string $mail, string $subject, array $data = [], array $cc = [])
    {
        try {
            [$mail, $cc] = $this->verifyCanSendMail($mail, $cc);


            if (!is_null($mail)) {
                Mail::send($view, $data, function ($message) use ($mail, $subject, $cc) {
                    $message->to($mail)->cc($cc)->subject($subject);
                });
            }
        } catch (Exception $exception) {
            throw new Exception($exception->getMessage());
        }
    }


    protected function getLastManager($user = null)
    {
        if ($user == null) {
            return $this->currentUser->orderBy("level", "DESC")->first();
        } else {
            return $user->managers()->where('level', $this->numberManagersCanValidate)->first();
        }
    }

    protected function getFirstManager($user = null)
    {
        if ($user == null) {
            return $this->currentUser->managers()->orderBy("level", "ASC")->first();
        } else {
            return $user->managers()->orderBy("level", "ASC")->first();
        }
    }

    /**
     * Envoi une alerte
     * @param User $user
     * @param Leave $leave
     * @param array $managers
     * @param string $title
     * @param string $body
     * @param int|null $statusId
     * @param string $status
     *
     * @throws Exception
     */
    protected function sendAlert(User $user, Leave $leave, array $managers, string $title, string $body, int $statusId = null, string $status = "UNREAD")
    {
        foreach ($managers as $managerId) {
            $alert = new Alert();
            $alert->user_id = $user->id;
            $alert->leave_id = $leave->id;
            $alert->status_id = $statusId ? $statusId : $leave->status_id;
            $alert->user_leave_id = $managerId;
            $alert->title = $title;
            $alert->body = $body;
            $alert->status = $status;

            if (!$alert->save()) {
                throw new Exception(__('warnings.ErrorSendAlert'));
            }
        }
    }

    /**
     * Envoi à l'historique
     * @param int $leaveId
     * @param int $leaveUserId
     * @param int $leaveStatusId
     * @param string $reason
     *
     * @throws Exception
     */
    protected function sendToHistory(int $leaveId, int $currentUserId, int $leaveStatusId, string $reason = ""): void
    {
        try {
            $history = new History();
            $history->leave_id = $leaveId;
            $history->user_id = $currentUserId;
            $history->status_id = $leaveStatusId;
            $history->reason = $reason;

            if (!$history->save()) {
                throw new Exception(__('warnings.ErrorSaveHistoric'));
            }
        } catch (Exception $exception) {
            throw new Exception($exception->getMessage());
        }
    }

    /**
     * Vérifie si l'utilisateur connecté est un administrateur
     * @return bool
     */
    public function isAdmin(): bool
    {
        $profilsAdmin = Profile::whereIn('label', ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->get()->pluck('id')->toArray();
        $isAdmin = in_array($this->currentUser->profile_id, $profilsAdmin);
        return $isAdmin;
    }
}
