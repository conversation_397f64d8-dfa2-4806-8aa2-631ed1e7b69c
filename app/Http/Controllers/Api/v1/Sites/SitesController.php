<?php

namespace App\Http\Controllers\Api\v1\Sites;

use App\Http\Controllers\Api\v1\Controller;
use App\Interfaces\Controllers\Api\BaseApiControllerInterface;
use App\Lib\ApiHelper;
use App\Models\Api\Documentation;
use App\Models\Api\Site;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class SitesController extends Controller {
	private array $config = [
	];

	#region Private Functions

	/**
	 * Vérifie si le site appartient pas à l'utilisateur
	 *
	 * @param Site $site
	 *
	 * @throws Exception
	 */
	private function checkSiteBelongsToUser(Site $site = null): void{
		if($site && $site->client_id !== $this->clientId){
			$this->responseMessage = __('warnings.ErrorDontOwnSite');
			throw new Exception(__('warnings.ErrorDontOwnSite'));
		}
	}

	/**
	 * Vérifie si le site existe
	 *
	 * @param int  $id
	 * @param Site $site
	 *
	 * @throws Exception
	 */
	private function checkSiteIfExist(int $id, Site $site = null): void{
		if(is_null($site)){
			throw new Exception(__('warnings.ErrorGetSite'));
		}
	}

	#endregion

	#region Public Functions

	/**
	 * Récupération des sites liés au Client
	 *
	 * @param Request $request
	 *
	 * @return JsonResponse
	 */
	public function index(Request $request): JsonResponse{
		try{
		    $this->validate($request,array_merge([
		        "withDocumentations" => "boolean"
            ], $this->defaultIndexFilters), $this->messages());
			$siteModel = Site::query()->where("client_id", "=", $this->clientId);
            // Si on veut les documentations en plus
            if($request->query("withDocumentations")){
                $siteModel->with("documentations");
            }
			// Pagination des données
			$this->responseData = ApiHelper::defaultModelBuilder($siteModel, $request->get("orders", "name,asc"),
				$request->get("limit", null), $request->get("paginate", 0));

            // Add an empty value
            if($request->query("withEmptyValue")) {
                $emptyValue = [
                    "id" => 0,
                    "name" => "---"
                ];
                $this->responseData["data"] = $this->responseData["data"]->toArray();
                array_unshift($this->responseData["data"], $emptyValue);
            }

			$this->getResponse = true;
			$this->responseMessage = __('warnings.SuccessGetSites');
		} catch(ValidationException $exception){
            $this->responseDetails = $exception->validator->getMessageBag();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ValidationErrors');
            $this->responseCode = $exception->status;
        } catch(Exception $exception){
			$this->responseDetails = $exception->getMessage();
			$this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetSite');
			$this->responseCode = $exception->getCode();
		}

		return $this->formattedResponse();
	}

	/**
	 * Récupération d'un site
	 *
	 * @param Request $request
	 * @param int     $id
	 *
	 * @return JsonResponse
	 */
	public function show(Request $request, int $id): JsonResponse{
		try{
			// Vérification de l'id
			$this->basicIdVerification($id);
			// Init model
			$siteModel = Site::query();
            // Si on veut les documentations en plus
            if($request->query("withDocumentations")){
                $siteModel->with("documentations");
            }
			// Récupération du site passé en paramètre
			$site = $siteModel->find($id);
            // Vérifie si le site existe
            $this->checkSiteIfExist($id, $site);
			// Vérifie si le site appartient pas à l'utilisateur
			$this->checkSiteBelongsToUser($site);

			$this->responseMessage = __('warnings.SuccessGetSite');
			$this->responseData = $site->toArray();
		} catch(Exception $exception){
			$this->responseDetails = $exception->getMessage();
			$this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetSite');
			$this->responseCode = $exception->getCode();
		}

		return $this->formattedResponse();
	}

	/**
	 * Modification du site passé en paramètre
	 *
	 * @param Request $request
	 * @param int     $id
	 *
	 * @return JsonResponse
	 */
	public function update(Request $request, int $id): JsonResponse{
		try{
			// Vérification de l'id
			$this->basicIdVerification($id);
			// Validation des données reçu en POST
			$data = $this->validate($request, $this->config, $this->messages());
			// Récupération du site liés au client par id
			$site = Site::find($id);
            // Vérifie si un site existe déjà pour cet id
            $this->checkSiteIfExist($id, $site);
			// Vérifie si le site appartient pas à l'utilisateur
			$this->checkSiteBelongsToUser($site);

            // Si erreur dans la modification du type de congés
            if(!$site->save()){
                throw new Exception(__('warnings.ErrorUpdateSite'));
            }

			$this->responseMessage = __('warnings.SuccessUpdateSite');
			$this->responseData = $site->toArray();
		} catch(ValidationException $exception){
			$this->responseDetails = $exception->validator->getMessageBag();
			$this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateSite');
			$this->responseCode = $exception->status;
		} catch(Exception $exception){
			$this->responseDetails = $exception->getMessage();
			$this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateSite');
			$this->responseCode = $exception->getCode();
		}

		return $this->formattedResponse();
	}
	#endregion
}
