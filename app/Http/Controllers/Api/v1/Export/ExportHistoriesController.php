<?php

namespace App\Http\Controllers\Api\v1\Export;

use App\Lib\ApiHelper;
use App\Models\Api\User;
use Illuminate\Support\Facades\Log;
use Orion\Http\Controllers\Controller;
use Orion\Http\Requests\Request;
use Orion\Concerns\DisableAuthorization;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\StreamedResponse;
use App\Models\Api\ExportHistory;
use Exception;
use Illuminate\Support\Carbon;

class ExportHistoriesController extends Controller
{
    use DisableAuthorization;

    protected $model = ExportHistory::class;

    public function alwaysIncludes(): array
    {
        return ['user', 'user.site'];
    }

    public function filterableBy(): array
    {
        return [
            'id',
            'file_name',
            'user_id',
            'created_at',
            'type'
        ];
    }

    public function sortableBy(): array
    {
        return ['created_at', 'id', 'file_name', 'user.lastname'];
    }

    public function defaultSort(): array
    {
        return [
            'created_at' => 'desc',
        ];
    }

    protected function buildFetchQuery(Request $request, array $requestedRelations): Builder
    {
        $query = parent::buildFetchQuery($request, $requestedRelations);
        $currentUser = Auth::user();

        if ($currentUser && $currentUser->site && $currentUser->site->client_id) {
            $clientId = $currentUser->site->client_id;

            $query->whereHas('user.site', function (Builder $q) use ($clientId) {
                $q->where('client_id', $clientId);
            });

            $oneYearAgo = Carbon::now()->subYear();
            $query->where('export_histories.created_at', '>=', $oneYearAgo);

            $query->with('user');

        } else {
            $query->whereRaw('1 = 0');
        }
        return $query;
    }

    protected function performDestroy(Model $entity): void
    {
        /** @var ExportHistory $entity */
        /** @var User|null $currentUser */
        $currentUser = Auth::user();

        $entity->loadMissing('user.site');

        if (!$currentUser || !$currentUser->site || $entity->user?->site?->client_id !== $currentUser->site->client_id) {
            abort(403, __('messages.ErrorForbidden'));
        }

        $clientId = $currentUser->site->client_id;
        try {
            $path = 'exports/' . $clientId . '/';
            if ($entity->file_name) {
                Storage::disk('s3')->delete($path . $entity->file_name);
            }
        } catch (Exception $e) {
            report("Échec de la suppression du fichier S3 {$entity->file_name} pour ExportHistory ID {$entity->id}: " . $e->getMessage());
        }

        parent::performDestroy($entity);
    }

    public function downloadExport(Request $request, int $exportId): StreamedResponse|JsonResponse
    {
        try {

            $exportHistory = ExportHistory::find($exportId);

            if (empty($exportHistory)) {

                throw new \Exception(__('warnings.ExportNotFound'), 404);
            }

            $currentUser = Auth::user();

            if (!$currentUser || !$currentUser->site) {
                throw new \Exception("Impossible de déterminer le contexte utilisateur.", 500);
            }
            $clientId = $currentUser->site->client_id;
            $path = 'exports/' . $clientId . '/';
            $fullPath = $path . $exportHistory->file_name;

            return ApiHelper::downloadExport($fullPath);

        } catch (\Exception $exception) {

            Log::error("Erreur downloadExport (ID: {$exportId}): " . $exception->getMessage());

            $responseCode = $exception->getCode();
            if (!is_int($responseCode) || $responseCode < 400 || $responseCode >= 600) {
                $responseCode = 500;
            }

            return response()->json([
                'message' => __('warnings.ErrorGetExportHistory'),
                'details' => $exception->getMessage(),
            ], $responseCode);
        }
    }
}
