<?php


namespace App\Http\Controllers\Api\v1\Leaves;


use App\DTO\ExportLeavesDTO;
use App\Excel\Exports\LeavesExport;
use App\Excel\Exports\LeavesModelImport;
use App\Excel\Imports\ImportLeaveAbs;
use App\Http\Controllers\Api\v1\Controller;
use App\Http\Requests\ExportRequest;
use App\Lib\ApiHelper;
use App\Lib\Holidays;
use App\Lib\Tools;
use App\Models\Api\Client;
use App\Models\Api\ExportHistory;
use App\Models\Api\History;
use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\LeaveTypeCategory;
use App\Models\Api\LeaveTypeSubFamily;
use App\Models\Api\Manager;
use App\Models\Api\Profile;
use App\Models\Api\Status;
use App\Models\Api\User;
use App\Models\Api\UserLeaveCount;
use App\Notifications\LeaveAccepted;
use App\Notifications\LeaveCancelled;
use App\Notifications\LeaveInformation;
use App\Notifications\LeaveRefused;
use App\Notifications\LeaveTransmitted;
use App\Notifications\UserReceiveNotificationPush;
use App\Utils\Traits\FormatHistoriesTrait;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Facades\Excel;
use Throwable;

class LeavesController extends Controller
{
    use FormatHistoriesTrait;

    public array $configValidation = [
        "*.action" => "required|in:VALIDATED,REFUSED,TRANSMITTED",
        "*.leave_id" => "required|integer",
        "*.reason" => "string",
    ];

    /**
     * Récupère les compteurs n et n-1 d'un congé de l'utilisateur connecté
     *
     * @param int $leaveTypeId
     * @param null $userId
     * @param null $users
     * @param array $errors
     * @return array
     * @throws Exception
     */
    private function getUserLeaveCount(int $leaveTypeId, $userId = null, $users = null, &$errors = []): array
    {
        $userId = $userId ?? $this->currentUser->getKey();

        $userLeaveCountSQL = UserLeaveCount::where("user_id", "=", $userId)
            ->where("leave_type_id", $leaveTypeId)
            ->orderBy("is_last_year", "asc") // n first
            ->get();

        $leaveType = LeaveType::withTrashed()->find($leaveTypeId);
        try {
            $userLeaveCount = [
                "n" => $userLeaveCountSQL[0]
            ];
        } catch (\Exception $e) {
            if ($users > 1) {
                $errors[$userId]['user'] = User::find($userId)->firstname . ' ' . User::find($userId)->lastname;
                $errors[$userId]['message'] = __('warnings.NoLeaveCounter');
            } else {
                throw new Exception(__('warnings.NoLeaveCounter'), 400);
            }
        }

        if (isset($errors[$userId])) {
            $userLeaveCount = [];
        } else {
            if ($leaveType->is_pay) {
                $userLeaveCount = array_merge($userLeaveCount, ["n-1" => $userLeaveCountSQL[1]]);
            }
        }

        return $userLeaveCount;
    }

    /**
     * Renvoie le niveau de validation du congé ainsi que le manager suivant
     * @param $leave
     * @param null $manager
     * @return Model|null
     */
    private function getNextManager($leave, $manager = null)
    {
        if ($manager->managed()->where('managers.managed_id', $leave->user->getKey())->first() && $manager->managed()->where('managers.managed_id', $leave->user->getKey())->first()->pivot->level <= $this->numberManagersCanValidate) {
            $nextManager = User::query()
                ->join('managers', 'managers.manager_id', 'users.id')
                ->where('managers.managed_id', $leave->user->getKey())
                ->where('managers.level', $manager->managed()->where('id', $leave->user->getKey())->first()->pivot->level + 1)
                ->first() ?? null;
        } else {
            $nextManager = null;
        }

        return $nextManager;
    }

    /**
     * Renvoie les derniers manager associé à la leave
     * @param $leave
     * @return Collection
     */
    private function getPreviousCurrentValidatorLevelAndManager($leave): Collection
    {
        $userLeave = User::withTrashed()->find($leave->user_id);
        $level = $leave->status->tag !== 'SUBMITTED' ? $leave->current_validator_level - 1 : $leave->current_validator_level;

        return $userLeave->managers()->where('level', '<=', $level)->get();
    }

    /**
     * Vérifications basique d'un congé envoyé en POST
     * @param array $data
     * @param array $usersId
     * @param array $errors
     * @throws Exception
     */
    private function basicLeaveVerifications(array $data, $usersId = [], &$errors = []): void
    {
        try {
            // Vérifie si le type de congé existe et appartient au client de l'utilisateur qui enregistre le congé
            $this->verifyLeaveTypeIfExistAndBelongToClient($data["leave_type_id"]);
            // Vérifie si la sous famille existe pour le type de congé
            $this->verifyLeaveTypeSubFamilyIfExistForLeaveType($data["leave_type_id"], $data["leave_type_sub_family_id"]);
            // Vérifie que les dates soit dans le format jj/mm/aaaa
            $this->verifyStartAndEndDateFormat($data["start_date"], $data["end_date"]);
            // Vérifie que start_date > now et que start_date <= end_date et que start_date et end_date soit compris dans la periode des congés du client
            $this->verifyStartAndEndDate($data["start_date"], $data["end_date"]);
            // Vérifie qu'un congé n'est pas déjà présent pour la période spécifiée
            foreach ($usersId as $userId) {
                $user = User::find($userId);
                $this->verifyLeaveAlreadyExist($data["start_date"], $data["end_date"], $user, count($usersId), $errors);
            }
            // Vérifie durée
            $this->verifyDurationData($data["duration"] ?? null, $data["leave_type_id"]);
        } catch (Exception $exception) {
            throw new Exception($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Vérifie que l'utilisateur possède suffisamment de jours de congé
     *
     * @param $leave
     * @param $users
     * @param $errors
     * @throws Exception
     */
    private function verifIfUserAsEnoughDayAvailable($leave, $users, &$errors)
    {
        $userLeaveCount = $this->getUserLeaveCount($leave->leave_type_id, $leave->user_id, $users, $errors);

        if (!empty($userLeaveCount)) {
            $leaves = Leave::query()->whereIn('status_id', Status::whereIn('tag', ['VALIDATED', 'SUBMITTED'])->get("id")->toArray())->where('user_id', $leave->user_id)->where('leave_type_id', $leave->leave_type_id)->get();
            $futureNLeaves = $leaves->sum('n');
            $futureN1Leaves = $leaves->sum('n1');
            // Nombre de jours de congé restant
            $totalBalance = array_sum(collect($userLeaveCount)->pluck('balance')->toArray());
            $newBalance = $totalBalance - ($leave->duration + $futureNLeaves + $futureN1Leaves);

            if ($newBalance < 0) {
                if ($users > 1) {
                    $errors[$leave->user->getKey()]['user'] = $leave->user->firstname . ' ' . $leave->user->lastname;
                    $errors[$leave->user->getKey()]['message'] = __('warnings.NotEnoughLeaveDay');
                } else {
                    throw new Exception(__('warnings.NotEnoughLeaveDay'));
                }
            }
        }
    }

    /**
     * Vérifie qu'un congé n'est pas déjà présent pour la période spécifiée
     *
     * @param string $startDate
     * @param string $endDate
     * @param $user
     * @param $usersCount
     * @param $errors
     * @throws Exception
     */
    private function verifyLeaveAlreadyExist(string $startDate, string $endDate, $user, $usersCount, &$errors): void
    {
        $status = Status::whereIn("tag", ["CANCELED", "REFUSED"])->get("id");
        $leave = Leave::where("user_id", "=", $user ? $user->id : $this->currentUser->getKey())
            ->whereNotIn("status_id", $status->toArray())
            ->where('start_date', '<=', $endDate)
            ->where('end_date', '>=', $startDate)
            ->get()->toArray();

        if (!empty($leave)) {
            if ($usersCount > 1) {
                $errors[$user->getKey()]['user'] = $user->firstname . ' ' . $user->lastname;
                $errors[$user->getKey()]['message'] = __('warnings.LeaveAlreadyExisteForDate');
            } else {
                throw new Exception(__('warnings.LeaveAlreadyExisteForDate'), 400);
            }
        }
    }

    private function verifyDurationData($duration, $leave_type_id)
    {
        // Vérifie que la durée a été renseignée
        if (is_null($duration)) {
            throw new Exception(__('warnings.DurationRequired'), 422);
        }
        // Vérifie durée est un nombre
        if (!is_float($duration) && !is_int($duration)) {
            throw new Exception(__('warnings.DurationMustBeANumber'), 422);
        }
        //Vérifie durée est positive
        if ($duration <= 0) {
            throw new Exception(__('warnings.DurationMustBePositive'), 422);
        }
        // Vérifie sa valeur
        // Check if duration is an integer or is like X.5
        $whole = floor($duration);
        $fraction = $duration - $whole;
        if ($fraction != (0) && $fraction != (1 / 2)) {
            throw new Exception(__('warnings.ErrorLeaveDuration'), 400);
        } elseif ($fraction == (1 / 2) && !(LeaveType::find($leave_type_id)->is_half_day)) { // If LeaveType does not permit half day duration
            throw new Exception(__('warnings.ErrorLeaveDurationHalfDayNotPermit'), 400);
        }
    }

    /**
     * Vérifie si le type de congé existe et appartient au client de l'utilisateur qui enregistre le congé
     * @param int|null $leaveTypeId
     *
     * @throws Exception
     */
    private function verifyLeaveTypeIfExistAndBelongToClient(int $leaveTypeId = null): void
    {
        // Vérifie que l'id du type de congés est envoyé
        if (!$leaveTypeId) {
            throw new Exception(__('warnings.GetLeaveType'));
        }

        $leaveTypeExit = LeaveType::find($leaveTypeId);

        // Vérifie que le type de congé existe
        if (!$leaveTypeExit) {
            throw new Exception(__('warnings.ErrorLeaveTypeDontExist'), 404);
        }

        // Vérifie que le type de congé appartient au client de l'utilisateur
        if ($leaveTypeExit->client_id !== $this->clientId) {
            throw new Exception(__('warnings.ErrorLeaveTypeDontBelongToClient'), 403);
        }
    }

    /**
     * Vérifie si la sous famille existe pour le type de congé
     * @param int|null $leaveTypeId
     * @param int|null $leaveTypeSubFamilyId
     *
     * @throws Exception
     */
    private function verifyLeaveTypeSubFamilyIfExistForLeaveType(int $leaveTypeId = null, int $leaveTypeSubFamilyId = null): void
    {
        if ($leaveTypeSubFamilyId) {
            $leaveTypeSubFamilyExit = LeaveTypeSubFamily::find($leaveTypeSubFamilyId);

            // Vérifie que la sous famille existe
            if (!$leaveTypeSubFamilyExit) {
                throw new Exception(__('warnings.ErrorLeaveTypeSubFamilyDontExist'), 404);
            }

            // Vérifie que la sous famille correspondant au type de congé envoyé
            if ($leaveTypeSubFamilyExit->leave_type_id !== $leaveTypeId) {
                throw new Exception(__('warnings.ErrorLeaveTypeSubFamilyDontBelongToLeave'));
            }
        }
    }

    /**
     * check si le current user et manager
     *
     * @param $leave
     * @return bool
     */
    private function currentUserIsManager($leave): bool
    {
        $userLeave = User::find($leave->user_id);
        $managers = $userLeave->managers()->pluck('managers.manager_id')->toArray();
        $isManager = in_array($this->currentUser->getKey(), $managers);

        return $isManager;
    }

    /**
     * Vérifie que les dates soit dans le format jj/mm/aaaa
     * @param string|null $startDate
     * @param string|null $endDate
     *
     * @throws Exception
     */
    private function verifyStartAndEndDateFormat(string $startDate = null, string $endDate = null): void
    {
        if (!preg_match("/\d{4}-\d{2}-\d{2}/", $startDate)) {
            $this->responseMessage = __('warnings.ErrorStartDateFormat');
            throw new Exception(__('warnings.ErrorStartDateFormat'), 400);
        }

        if (!preg_match("/\d{4}-\d{2}-\d{2}/", $endDate)) {
            $this->responseMessage = __('warnings.ErrorEndDateFormat');
            throw new Exception(__('warnings.ErrorEndDateFormat'), 400);
        }
    }

    /**
     * Vérifie que start_date > now et que start_date <= end_date et que start_date et end_date soit compris dans la periode des congés du client
     * @param string|null $startDate
     * @param string|null $endDate
     *
     * @throws Exception
     */
    private function verifyStartAndEndDate(string $startDate = null, string $endDate = null): void
    {
        if (!$startDate) {
            throw new Exception(__('warnings.ErrorStartDateRequired'));
        }

        if (!$endDate) {
            throw new Exception(__('warnings.ErrorEndDateRequired'));
        }

        if ($startDate > $endDate) {
            $this->responseMessage = __('warnings.ErrorStartDateAboveEndDate');
            throw new Exception(__('warnings.ErrorStartDateAboveEndDate'), 400);
        }


        if ($startDate == $endDate) {
            throw new Exception(__('warnings.ErrorSameDate'), 400);
        }
    }

    /**
     * Vérifie si le congés n'aappartient pas à l'utilisateur
     *
     * @param Leave $leave
     *
     * @throws Exception
     */
    private function verifyIfLeaveBelongsToUser($leave = null, bool $isManageur = null): void
    {
        if (!(($leave && $isManageur) || $this->isAdmin() || $leave->user_id == $this->currentUser->getKey())) {
            throw new Exception(__('warnings.DontOwnLeave'), 403);
        }
    }

    /**
     * Vérifie si le congés existe
     *
     * @param int $id
     * @param Leave $leave
     *
     * @throws Exception
     */
    private function verifyIfLeaveIfExist(int $id, $leave = null): void
    {
        if (is_null($leave)) {
            throw new Exception(__('warnings.LeaveDontExist'), 404);
        }
    }

    /**
     * Get the open days of current client
     * @return array
     */
    private function getClientOpenDays(): array
    {
        $client = Client::find($this->clientId);
        $days = $client->days()->get();
        if (is_null($days)) {
            return [];
        }
        return $days->pluck('day_name')->toArray();
    }

    /**
     * Vérification que le user a le droit de valider le congé
     * @param Leave $leave
     *
     * @return array|null
     * @throws Exception
     */
    private function verifyIfUserCanRefuseLeave(Leave $leave): ?array
    {
        $userLeave = User::query()->withTrashed()->find($leave->user_id);
        // Si utilisateur courant est l'utilisateur du congé et est son propre manager
        $isOwnManager = $userLeave->managers()->exists() && $userLeave->is($this->currentUser);

        $managerExist = $userLeave->managers()->where('managers.manager_id', $this->currentUser->getKey())->exists();

        // Vérification si utilisateur courant est admin
        $boolCurrentUserAdmin =
            Profile::query()->whereIn('label', ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->where('id', $this->currentUser->profile_id) &&
            $this->currentUser->site->client_id == $userLeave->site->client_id;

        // Status du congé
        $status = Status::query()->find($leave->status_id);
        /*
        * Evol le 19 Août 2021
        * L'acceptation du refus/annulation d'un TRANSMITTED doit être possible mais seulement par un admin
        * En effet nous avons des cas où des congés sont transmis en paie légèrement en amont de leur consommation et son finalement à annuler (ex: un arrêt maladie survient pendant les congés de la personne)
        */
        $boolStatusSubCor = in_array($status->tag, ['SUBMITTED', 'SUBMITTED_TO_CANCELLATION']);
        $boolStatusValidated = in_array($status->tag, ['VALIDATED', 'SUBMITTED_TO_CANCELLATION']) || ($status->tag === 'TRANSMITTED' && $boolCurrentUserAdmin);
        // Admin remplace Manager

        $admin = null;
        $manager = null;
        $adminReplaceManager = false;

        if ($boolStatusValidated) {
            if (!$boolCurrentUserAdmin) throw new Exception(__('warnings.NotAdminFromUser'));

            $admin = $this->currentUser;
        } elseif ($managerExist) {
            $manager = $this->currentUser;
        } elseif ($boolCurrentUserAdmin && $boolStatusSubCor) {
            // Admin refuse à la place du manager si le statut est correct
            $admin = $this->currentUser;
            $adminReplaceManager = true;
        } elseif ($isOwnManager) {
            $manager = $userLeave;
        } else {
            throw new Exception(__('warnings.NotAdminOrManagerFromUser'));
        }

        if ((!$boolStatusSubCor && $manager && !$boolCurrentUserAdmin) || (!$boolStatusValidated && !$adminReplaceManager && !$manager)) {
            throw new Exception(__('warnings.CantRefuseLeaveWrongStatus'));
        }

        return compact('admin', 'manager', 'adminReplaceManager');
    }

    /**
     * Vérification que le user a le droit d'annuler le congé
     * @param Leave $leave
     *
     * @return array
     * @throws Exception
     */
    public function verifyIfUserCanCancelLeave(Leave $leave)
    {
        // format response
        $response = [];
        $response["result"] = true;
        $response["msg"] = "";

        // get infos
        $userLeave = User::withTrashed()->find($leave->user_id);
        $status = Status::find($leave->status_id);
        $managers = $userLeave->validators()->pluck('managers.manager_id')->toArray();
        $indirectManagers = $userLeave->managers;
        $profilsAdmin = Profile::whereIn('label', ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->get()->pluck('id')->toArray();

        // find user type
        $isCollaborateur = $leave->user_id == $this->currentUser->getKey();
        $isManager = in_array($this->currentUser->getKey(), $managers);
        $isAdmin = in_array($this->currentUser->profile_id, $profilsAdmin);
        $isIndirectManager = $indirectManagers->contains($this->currentUser);

        /* Vérifications */

        // Si le congé est déjà transmis en paie
        /*
        * Evol le 19 Août 2021
        * L'acceptation du refus/annulation d'un TRANSMITTED doit être possible mais seulement par un admin
        * En effet nous avons des cas où des congés sont transmis en paie légèrement en amont de leur consommation et sont finalement à annuler (ex: un arrêt maladie survient pendant les congés de la personne)
        */
        if ($status->tag == "TRANSMITTED" && !$isAdmin) {
            $response["result"] = false;
            $response["msg"] = __('messages.CantCancelLeaveTransmitted');
            return $response;
        } else if ($status->tag == "REFUSED") { // Refusé
            $response["result"] = false;
            $response["msg"] = __('messages.CantCancelLeaveRefused');
            return $response;
        } else if ($status->tag == "CANCELED") { // Annulé
            $response["result"] = false;
            $response["msg"] = __('messages.LeaveCanceled');
            return $response;
        } else if ($status->tag == "VALIDATED" && $isCollaborateur) {
            $response["result"] = false;
            $response["msg"] = __('messages.CantCancelLeaveValidated');
            $response['send_mail'] = true;
            return $response;
        }

        // Si l'utilisateur ne possède pas ce congé et qu'il n'est pas responsable de l'utilisateur du congé
        if (!($isCollaborateur || $isManager || $isAdmin || $isIndirectManager)) {
            $response["result"] = false;
            $response["msg"] = __('messages.DontOwnLeaveOrManagerUser');
            return $response;
        }

        // Si le congé a déjà été validée par un manager
        if ($leave->current_validator_level != ($this->getFirstManager()["pivot"]["level"] ?? 1) && $isCollaborateur) {
            $response["result"] = false;
            $response["send_mail"] = true;

            return $response;
        }

        if ($isCollaborateur && $leave->status->tag === 'SUBMITTED') {
            $response["result"] = false;
            $response["send_mail"] = true;

            return $response;
        }

        // Si l'user n'appartient pas à l'admin
        if ($isAdmin && $this->currentUser->site->client_id !== $userLeave->site->client_id) {
            $response["result"] = false;
            $response["msg"] = __('messages.NotAdminFromUser');
            return $response;
        }

        if ($isAdmin && $status->tag === "SUBMITTED_TO_CANCELLATION" && $leave->user_id === $this->currentUser->getKey()) {
            $response["result"] = false;
            $response["msg"] = __('messages.CantCancelAdminOwnLeave');
            return $response;
        }

        // Si c'est le dernier manager qui a validé la leave
        $lastActionHistory = History::where("leave_id", "=", $leave->getKey())->orderBy('created_at', 'desc')->first();
        if ($this->getCurrentClient()->validation_scheme == 'VERTICAL' && $lastActionHistory->user_id != $this->currentUser->getKey() && !$isAdmin && !$isIndirectManager) {
            $response["result"] = false;
            $response["msg"] = __('messages.OtherAdminValidated');
            $manager = Manager::where('managed_id', '=', $userLeave->id)->where('manager_id', '=', $this->currentUser->getKey())->first();
            if ($leave->current_validator_level == $manager->level) {
                $response["msg"] = __('messages.CantCancelLeaveNotValidated');
            }
            return $response;
        }

        // verify status
        $boolCollaborateur = $status->tag == "SUBMITTED" && $isCollaborateur;
        $boolCollaborateurNotHaveManager = $status->tag == "VALIDATED" && $isCollaborateur && empty($this->getFirstManager());
        $boolManager = $status->tag == $isManager;
        $lastManager = $this->getLastManager($userLeave);
        $boolIsLastManager = $status->tag == "VALIDATED" && !empty($lastManager) && $lastManager->id == $this->currentUser->getKey();

        $response["result"] = $boolManager || $boolCollaborateur || $boolCollaborateurNotHaveManager || $boolIsLastManager || $isAdmin || $isIndirectManager;

        return $response;
    }

    /**
     * Vérification que le user a le droit de transmettre en paie le congé
     * @param Client $client
     * @param Leave $leave
     *
     * @return User|null
     * @throws Exception
     * @throws Exception
     */
    private function verifyIfUserCanTransmitLeave(Client $client, Leave $leave): ?User
    {
        $userLeave = User::withTrashed()->find($leave->user_id);
        $adminExist = User::where('users.id', '=', $this->currentUser->getKey())
            ->whereHas('site', function ($query) use ($userLeave) {
                $query->where('client_id', '=', $userLeave->site->client_id);
            })->first();
        // Si l'admin n'existe pas
        if (!$adminExist) {
            throw new Exception(__('messages.NotAdminFromUser'));
        }

        $status = Status::find($leave->status_id);

        // Si le congé n'a pas le bon status
        if ($status->tag !== "VALIDATED" && $status->tag !== "SUBMITTED" && $status->tag !== "SUBMITTED_TO_CANCELLATION") {
            throw new Exception(__('messages.ErrorTransmisLeaveWrongStatus'));
        }

        // Verification Schema VERTICAL
        if ($client->validation_scheme == "VERTICAL") {
            $lastManager = $userLeave->managers()
                ->where('level', $this->numberManagersCanValidate)->first();
            // Si dernier manager existe
            if ($lastManager && $status->tag !== "SUBMITTED") {
                // Si la validation a été faite par le dernier manager du user
                if ($leave->current_validator_level > $lastManager->pivot['level'] || $leave->status_id = 1) {
                    return $adminExist;
                }
            } else {
                return $adminExist;
            }
        } else { // Verification Schema HORIZONTAL
            // Congé validé par un des managers du user donc peut être transmis en paie par l'admin
            return $adminExist;
        }
        // Si le schema est VERTICAL ET que le congé n'a pas encore été validé par le dernier manager du user
        throw new Exception(__('warnings.ErrorNotValidatedByLastManager'));
    }

    /**
     * Envoi le mail et l'alerte
     * @param Leave $leave
     * @param User $userManager
     * @param string $title
     * @param User $userLeave
     * @param int $statusId
     * @param array $keyword
     * @throws Exception
     */
    private function sendMailAndAlert(Leave $leave, User $userManager, string $title, User $userLeave, int $statusId, array $keyword): void
    {
        try {
            $body = "";
            foreach ($keyword as $key => $attributes) {
                $body .= __($key, $attributes, 'fr');
            }

            // Envoi une alerte
            $this->sendAlert($userLeave, $leave, [$userManager->getKey()], $title, $body, $statusId);

            Log::Debug("Email not send ");
            // Envoi un mail

            $user = User::where('email', '=', $userManager->email)->first();
            if (!$user->can_receive_mails) {
                return;
            }

            $userManager->notify(new LeaveInformation([
                'leave' => $leave,
                'title' => $title,
                'greeting' => __('notifications.greeting', ['user_firstname' => $userManager->firstname], $userManager->language),
                'keyword' => $keyword
            ], true));
            Log::Debug('Email send');
        } catch (Exception $exception) {
            Log::Debug("Email not send " . $exception->getMessage());
            throw new Exception($exception->getMessage());
        }
    }

    /**
     * Envoie mail et alerte apres une validation
     * @param $leave
     * @param $userLeave
     * @param $leaveType
     * @throws Exception
     */

    /**
     * Permet la validation d'un congé
     *
     * @param array $datum
     *
     * @param array $dataToReturn
     * @throws Exception
     */
    private function validateLeave(array $datum, &$dataToReturn = []): void
    {
        try {
            $client = Client::find($this->clientId);
            $leave = Leave::find($datum["leave_id"]);
            $categoryId = LeaveTypeCategory::where('slug', 'abs')->pluck('id')->first();
            $isAbsence = $leave->leave_type->leave_type_category_id === $categoryId;

            // Si le congé n'existe pas
            if (!$leave) {
                throw new Exception(__('warnings.ErrorLeaveDontExist'), 404);
            }

            $leaveStatus = Status::find($leave->status_id);
            // Pour valider il faut que le congé soit SUBMITTED
            if ($leaveStatus->tag !== "SUBMITTED" && !$isAbsence) {
                $this->responseMessage = __('warnings.ErrorLeaveStatusDontAllowValidation');
                throw new Exception(__('warnings.ErrorLeaveStatusDontAllowValidation'), 400);
            }

            // Donnée utilisateur du congé
            $userLeave = User::withTrashed()->find($leave->user_id);
            $leaveDate = ApiHelper::convertStartFullDateAndEndFullDate($userLeave, $leave->start_date, $leave->end_date);
            $leaveTypeName = ApiHelper::LeaveTypeName($leave);
            $currentUser = Tools::getCurrentUserWithUuid();
            $profilAdmin = Profile::query()->where('label', 'ADMINISTRATEUR')->first();
            //Si l'utilisateur est admin
            $isAdmin = $profilAdmin->getKey() == $currentUser->profile_id;
            // SI l'utilisateur est son propre manager
            $isOwnManager = $userLeave->managers()->count() == 0 && $currentUser->id == $userLeave->id;
            // Si l'utilisateur qui essaie de valider n'est pas le manager de la personne qui a créé le congé
            $isManager = $userLeave->managers()->where("id", "=", $currentUser->id)->first() || $isOwnManager;

            if (!$isManager && !$isAdmin) {
                $this->responseMessage = __('warnings.NotManagerFromUser');
                throw new Exception(__('warnings.NotManagerFromUser'), 400);
            }

            $userValidating = $this->currentUser;

            $nextManager = null;

            if ($isManager && !$isOwnManager) {
                $nextManager = $this->getNextManager($leave, $userValidating);

                $isAlreadyTreatedByUser = History::query()
                    ->where('user_id', $currentUser->getKey())
                    ->where('leave_id', $leave->getKey())
                    ->where('status_id', 1)
                    ->exists();

                $isCurrentManagerToValidate = $currentUser->getKey() === $userLeave->managers()->where('level', $leave->current_validator_level)->first()->getKey();
                abort_if($isAlreadyTreatedByUser && !$isCurrentManagerToValidate, 403, __('warnings.UserAlreadyTreatedLeave'));
            }

            if ($isAdmin && $this->currentUser->site->client_id !== $userLeave->site->client_id) {
                $this->responseMessage = __('warnings.NotAdminFromUser');
                throw new Exception(__('warnings.NotAdminFromUser'), 400);
            }

            $leave->last_updater_id = $this->currentUser->getKey();

            $statusValidatedId = Status::where("tag", "=", "VALIDATED")->first()->id;

            // Schéma HORIZONTAL
            if ($client->validation_scheme === "HORIZONTAL") {
                $leave->status_id = $statusValidatedId;
                $leave->current_validator_level++;
                //MyLeaves
                $userLeave?->notify(new LeaveAccepted([
                    'leave' => $leave,
                    'title' => __('notifications.title_leaves_period', ['leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date']], $userLeave?->language),
                    'greeting' => __('notifications.greeting', ['user_firstname' => $userLeave?->firstname], $userLeave?->language),
                    'keyword' => [
                        'notifications.leave_accepted' => ['manager_firstname' => $userValidating->firstname, 'manager_lastname' => $userValidating->lastname, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date'], 'leave_type' => $leaveTypeName]
                    ]
                ]));
                //Send push notification
                try {
                    $userLeave?->notify(new UserReceiveNotificationPush([
                        'leave' => $leave,
                        'title' => __('notifications.fcm.title.title_leaves_period', ['leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date']], $userLeave?->language),
                        'message' => __('notifications.fcm.body.leave_accepted', ['manager_firstname' => $userValidating->firstname, 'manager_lastname' => $userValidating->lastname, 'leave_type' => $leaveTypeName], $userLeave?->language),
                        'url' => "/leave/{$leave->getKey()}",
                        'page' => 'myLeaves',
                    ]));
                } catch (Exception $exception) {
                    Log::Debug("Notification Push not send " . $exception->getMessage());
                }
            } // Schéma VERTICAL
            else {
                //Si pas de manager AND qu'il valide ça propre leave (Ce manage lui meme)
                if ($isOwnManager || is_null($nextManager) || $isAdmin) {
                    // le congé est validé
                    $leave->status_id = $statusValidatedId;
                    $leave->current_validator_level = $leave->user->managers()->max('level') + 1;
                    //Send push notification
                    //MyLeaves
                    $userLeave?->notify(new LeaveAccepted([
                        'leave' => $leave,
                        'title' => __('notifications.title_leaves_period', ['leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date']], $userLeave?->language),
                        'greeting' => __('notifications.greeting', ['user_firstname' => $userLeave?->firstname], $userLeave?->language),
                        'keyword' => [
                            'notifications.leave_accepted' => ['manager_firstname' => $userValidating->firstname, 'manager_lastname' => $userValidating->lastname, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date'], 'leave_type' => $leaveTypeName],
                            'notifications.no_more_managers' => [],
                        ]
                    ]));
                    //Send push notification
                    try {
                        $userLeave?->notify(new UserReceiveNotificationPush([
                            'leave' => $leave,
                            'title' => __('notifications.fcm.title.title_leaves_period', ['leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date']], $userLeave?->language),
                            'message' => __('notifications.fcm.body.leave_accepted', ['manager_firstname' => $userValidating->firstname, 'manager_lastname' => $userValidating->lastname, 'leave_type' => $leaveTypeName], $userLeave?->language),
                            'url' => "/leave/{$leave->getKey()}",
                            'page' => 'myLeaves',
                        ]));
                    } catch (Exception $exception) {
                        Log::Debug("Notification Push not send " . $exception->getMessage());
                    }

                    Log::info("Le mail comme quoi le congés de {$userLeave?->firstname} {$userLeave?->lastname} validé par {$userValidating->firstname} {$userValidating->lastname} a bien été envoyé.");
                    $previousManagers = $isAdmin && !$isManager ? $userLeave?->validators()->get() : $userLeave?->managers()->where('level', '<', $userValidating->managed()->where('managed_id', $userLeave?->getKey())->first()?->pivot->level ?? "")->get();
                    $previousManagers->each(function ($previousManager) use ($leave, $userLeave, $userValidating, $leaveTypeName) {
                        $leaveDate = ApiHelper::convertStartFullDateAndEndFullDate($previousManager, $leave->start_date, $leave->end_date);
                        $previousManager->notify(new LeaveInformation([
                            'leave' => $leave,
                            'title' => __('notifications.title_user_leave_accepted', ['user_firstname' => $userLeave->firstname, 'user_lastname' => $userLeave->lastname], $previousManager->language),
                            'greeting' => __('notifications.greeting', ['user_firstname' => $previousManager->firstname], $previousManager->language),
                            'keyword' => [
                                'notifications.leave_validating_by_other_manager' => ['user_firstname' => $userLeave->firstname, 'user_lastname' => $userLeave->lastname, 'manager_firstname' => $userValidating->firstname, 'manager_lastname' => $userValidating->lastname, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date'], 'leave_type' => $leaveTypeName],
                            ]
                        ], true));
                        //Send push notification
                        try {
                            $previousManager?->notify(new UserReceiveNotificationPush([
                                'leave' => $leave,
                                'title' => __('notifications.fcm.title.title_leaves_period', ['leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date']], $previousManager?->language),
                                'message' => __('notifications.leave_validating_by_other_manager', ['user_firstname' => $userLeave->firstname, 'user_lastname' => $userLeave->lastname, 'manager_firstname' => $userValidating->firstname, 'manager_lastname' => $userValidating->lastname, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date'], 'leave_type' => $leaveTypeName], $previousManager?->language),
                                'url' => "/leave/{$leave->getKey()}",
                                'page' => 'myLeaves',
                            ]));
                        } catch (Exception $exception) {
                            Log::Debug("Notification Push not send " . $exception->getMessage());
                        }
                    });
                } // Si il y a un prochain manager
                else {
                    // Envoi le mail et l'alerte au prochain manager
                    $title = __('notifications.title_leave_asking', ['leave_type' => $leaveTypeName], $nextManager->language);
                    $leaveDate = ApiHelper::convertStartFullDateAndEndFullDate($nextManager, $leave->start_date, $leave->end_date);

                    $body = [
                        'notifications.send_validating_leave' => ['user_firstname' => $userLeave->firstname, 'user_lastname' => $userLeave->lastname, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date'], 'leave_type' => $leaveTypeName],
                        'notifications.validate_author' => ['author_firstname' => $userValidating["firstname"] ?? $userLeave["firstname"], 'author_lastname' => $userValidating["lastname"] ?? $userLeave["lastname"]],
                    ];
                    $this->sendMailAndAlert($leave, $nextManager, $title, $userLeave,
                        Status::where("tag", "=", "VALIDATED")->first()->id, $body);

                    $leaveDate = ApiHelper::convertStartFullDateAndEndFullDate($userLeave, $leave->start_date, $leave->end_date);
                    $userLeave?->notify(new LeaveAccepted([
                        'leave' => $leave,
                        'title' => __('notifications.title_follow_asking_leaves', [], $userLeave?->language),
                        'greeting' => __('notifications.greeting', ['user_firstname' => $userLeave?->firstname], $userLeave?->language),
                        'keyword' => [
                            'notifications.leave_accepted' => ['manager_firstname' => $userValidating->firstname, 'manager_lastname' => $userValidating->lastname, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date'], 'leave_type' => $leaveTypeName],
                            'notifications.request_sent_to_next_manager' => ['manager_firstname' => $nextManager->firstname, 'manager_lastname' => $nextManager->lastname],
                        ],
                    ]));
                    //Send push notification
                    try {
                        $nextManager?->notify(new UserReceiveNotificationPush([
                            'leave' => $leave,
                            'title' => __('notifications.fcm.title.title_leave_asking', ['leave_type' => $leaveTypeName], $nextManager?->language),
                            'message' => __('notifications.fcm.body.leave_period', ['user_firstname' => $userLeave->firstname, 'user_lastname' => strtoupper($userLeave->lastname), 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date'], 'leave_type' => $leaveTypeName], $userLeave?->language),
                            'url' => "/leave/{$leave->getKey()}",
                            'page' => 'toValidate',
                        ]));
                    } catch (Exception $exception) {
                        Log::Debug("Notification Push not send " . $exception->getMessage());
                    }
                    $leave->current_validator_level = $leave->current_validator_level + 1;
                }
            }

            // Si le congé ne s'est pas modifié
            if (!$leave->save()) {
                $this->responseMessage = __('warnings.ErrorValidateLeave');
                throw new Exception(__('warnings.ErrorValidateLeave'), 400);
            }

            // Ajoute la validation dans l'historique du congé
            $this->sendToHistory($leave->getKey(), $this->currentUser->getKey(), Status::where("tag", "=", "VALIDATED")->first()->id);
            $this->responseData = $leave->toArray();
            $dataToReturn[] = $this->responseData;
            $this->responseMessage = (!isset($this->responseMessage) || $this->responseMessage == "") ? __('warnings.SuccessValidateLeave') : __('warnings.SuccessMassValidationLeave');


        } catch (Exception $exception) {
            $this->responseMessage = __('warnings.ErrorValidateLeave');
            throw new Exception($exception->getMessage(), $exception->getCode() ?? 500);
        }
    }

    /**
     * Permet le refus d'un congé
     * @param array $datum
     *
     * @param array $dataToReturn
     * @throws Exception
     */
    private function refuseLeave(array $datum, &$dataToReturn = []): void
    {
        $client = Client::find($this->clientId);
        $leave = Leave::find($datum["leave_id"]);
        $leaveTypeName = ApiHelper::LeaveTypeName($leave);
        $reason = $datum["reason"];

        // Si le congé n'existe pas
        if (!$leave) {
            throw new Exception(__('warnings.ErrorLeaveDontExist'));
        }
        // Si la raison n'existe pas
        if (!isset($datum["reason"])) {
            throw new Exception(__('warnings.ReasonDontExist'));
        }
        // Si le congé peux étre refuser
        if (Status::query()->whereIn('tag', ['CANCELED', 'REFUSED'])->where('id', $leave->status_id)->exists()) {
            throw new Exception(__('warnings.LeaveRefused'));
        }

        // Vérification que le user a le droit de refuser le congé
        $arrayRefuser = $this->verifyIfUserCanRefuseLeave($leave);

        if (!$arrayRefuser['manager'] && !$arrayRefuser['admin'] && !$arrayRefuser['adminReplaceManager']) {
            throw new Exception(__('warnings.NoManagerOrAdminForRefuseLeave'));
        }

        $userRefuser = $arrayRefuser['manager'] ?? $arrayRefuser['admin'];
        // Utilisateur à qui appartient le congé
        $userLeave = User::withTrashed()->find($leave->user_id);

        // Si admin remplace manager
        if ($arrayRefuser['adminReplaceManager']) {
            // Schéma VERTICAL - Envoie au manager remplacé
            if ($client->validation_scheme === 'VERTICAL') {
                // Manager remplacé
                $managerReplaced = $userLeave->managers()->firstWhere('level', $leave->current_validator_level);
                if (isset($managerReplaced)) {
                    $leaveDate = ApiHelper::convertStartFullDateAndEndFullDate($managerReplaced, $leave->start_date, $leave->end_date);

                    // Mail + notif -> manager qui s'est fait remplacé par admin
                    $title = "CONGÉS : L'Administrateur a traité une demande de congés à votre place";
                    $body = "Bonjour {$managerReplaced['firstname']} {$managerReplaced['lastname']},
                          \n{$userRefuser->firstname} {strtoupper($userRefuser->lastname)}  a refusé la demande de
                         \n{$leaveTypeName} faite par {$userLeave["firstname"]} {$userLeave['lastname']}
                        \npour la période du {$leaveDate['start_date']} au {$leaveDate['end_date']}.";
                    // Envoi un mail
                    $managerReplaced->notify(new LeaveRefused([
                        'leave' => $leave,
                        'title' => __('notifications.title_another_administrator_refuse_leave', [], $managerReplaced->language),
                        'greeting' => __('notifications.greeting', ['user_firstname' => $managerReplaced->firstname], $managerReplaced->language),
                        'refusal' => $reason,
                        'keyword' => [
                            'notifications.another_administrator_refuse_leave' => ['admin_firstname' => $userRefuser->firstname, 'admin_lastname' => $userRefuser->lastname, 'leave_type' => $leaveTypeName, 'user_firstname' => $userLeave->firstname, 'user_lastname' => $userLeave->lastname, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date']]
                        ]
                    ], false));

                    // Envoi une alerte
                    $this->sendAlert($managerReplaced, $leave, [$managerReplaced->id], $title, $body, $leave->status_id);
                }
            } else { // Schéma HORIZONTAL - Envoie à tous les managers de l'utilisateur
                $managersReplaced = $userLeave->validators()->get();
                foreach ($managersReplaced as $managerReplaced) {
                    $leaveDate = ApiHelper::convertStartFullDateAndEndFullDate($managerReplaced, $leave->start_date, $leave->end_date);

                    // Mail + notif -> manager qui s'est fait remplacé par admin
                    $title = "CONGÉS : L'Administrateur a traité une demande de congés à votre place";
                    $body = "Bonjour $managerReplaced->firstname {strtoupper($managerReplaced->lastname)}, \n$userRefuser->firstname " . strtoupper($userRefuser->lastname) . " a refusé la demande de $leaveTypeName faite par $userLeave->firstname $userLeave->lastname \npour la période du {$leaveDate['start_date']} au {$leaveDate['end_date']}";
                    // Envoi un mail
                    $managerReplaced->notify(new LeaveRefused([
                        'leave' => $leave,
                        'title' => __('notifications.title_another_administrator_refuse_leave', [], $managerReplaced->language),
                        'greeting' => __('notifications.greeting', ['user_firstname' => $managerReplaced->firstname], $managerReplaced->language),
                        'keyword' => [
                            'notifications.another_administrator_refuse_leave' => ['admin_firstname' => $userRefuser->firstname, 'admin_lastname' => $userRefuser->lastname, 'leave_type' => $leaveTypeName, 'user_firstname' => $userLeave->firstname, 'user_lastname' => $userLeave->lastname, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date']]
                        ]
                    ], false));

                    // Envoi une alerte
                    $this->sendAlert($managerReplaced, $leave, [$managerReplaced->id], $title, $body, $leave->status_id);
                }
            }
        }

        //Changement information du congé
        $statusId = Status::query()->firstWhere('tag', 'REFUSED')->getKey();
        $leave->last_updater_id = $this->currentUser->getKey();
        $firstManager = $userLeave->managers()->orderBy('level')->first();
        $leave->status_id = $statusId;

        $previousManagers = $this->getPreviousCurrentValidatorLevelAndManager($leave);
        $leave->current_validator_level = $leave->user->managers()->max('level') + 1;

        // Si le congé ne s'est pas modifié
        if (!$leave->save()) {
            $this->responseMessage = __('warnings.ErrorRefuseLeave');
            throw new Exception(__('warnings.ErrorRefuseLeave'));
        }

        $this->recalcLeavesDistribution($leave->user_id, $leave->leave_type_id);

        $leaveDate = ApiHelper::convertStartFullDateAndEndFullDate($userLeave, $leave->start_date, $leave->end_date);
        // S'il a un manager qui a déjà validé et qu'on est dans le cas d'un schéma VERTICAL, envoie de notifications et de mails aux managers qui ont validé.
        if ($leave->current_validator_level > 1 && $client->validation_scheme === 'VERTICAL') {
            $title = "CONGÉS : Demande de congé refusée";
            $body = "$userRefuser->firstname $userRefuser->lastname a refusé(e) votre demande de $leaveTypeName pour la période du  {$leaveDate['start_date']} au {$leaveDate['end_date']}.";

            if ($userLeave->managers()->where('firstname', $userRefuser->firstname)->where('lastname', $userRefuser->lastname)->exists()) {
                $managersToNotify = collect($previousManagers)
                    ->filter(function ($manager) use ($userRefuser, $userLeave) {
                        return $manager->pivot->level < $userLeave->managers()->where('id', $userRefuser->getKey())->first()->pivot->level;
                    });
            } else {
                $managersToNotify = collect($previousManagers)
                    ->filter(function ($manager) use ($leave) {
                        return $manager->pivot->level < $leave->current_validator_level;
                    });
            }


            foreach ($managersToNotify as $previousManager) {
                // Envoi une alerte
                $this->sendAlert($userLeave, $leave, [$previousManager->id], $title, $body, $statusId);
                //Send Push notifications->manager

                try {
                    $previousManager?->notify(new UserReceiveNotificationPush([
                        'leave' => $leave,
                        'title' => __('notifications.fcm.title.title_leave_cancel_refused_by_other_manager', ['leave_type' => $leaveTypeName], $previousManager->language),
                        'message' => __('notifications.fcm.body.leave_refuse', ['manager_firstname' => $userRefuser->firstname, 'manager_lastname' => $userRefuser->lastname, 'leave_type' => $leaveTypeName], $previousManager?->language),
                        'url' => "/leave/{$leave->getKey()}",
                        'page' => 'myLeaves',
                    ]));
                } catch (Exception $exception) {
                    Log::Debug('Notification Push not send', ['message' => $exception->getMessage()]);
                }
                //Send Mail->manager
                $previousManager?->notify(new LeaveRefused([
                    'leave' => $leave,
                    'title' => __('notifications.fcm.title.title_leave_cancel_refused_by_other_manager', ['leave_type' => $leaveTypeName], $previousManager->language),
                    'greeting' => __('notifications.greeting', ['user_firstname' => $previousManager->firstname], $previousManager->language),
                    'refusal' => $reason,
                    'keyword' => [
                        'notifications.refused_leave' => ['user_firstname' => $userLeave->firstname, 'user_lastname' => $userLeave->lastname, 'manager_firstname' => $userRefuser->firstname, 'manager_lastname' => $userRefuser->lastname, 'leave_type' => $leaveTypeName, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date']]
                    ]
                ]));
            }
            // Envoi un mail
            $userLeave->notify(new LeaveRefused([
                'leave' => $leave,
                'title' => __('notifications.title_leave_refused', [], $userLeave->language),
                'greeting' => __('notifications.greeting', ['user_firstname' => $userLeave->firstname], $userLeave->language),
                'refusal' => $reason,
                'keyword' => [
                    'notifications.manager_refuse_leave' => ['manager_firstname' => $userRefuser->firstname, 'manager_lastname' => $userRefuser->lastname, 'leave_type' => $leaveTypeName, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date']]
                ]
            ]));
            //Send Push notifications
            try {
                $userLeave->notify(new UserReceiveNotificationPush([
                    'leave' => $leave,
                    'title' => __('notifications.fcm.title.title_leave_cancel_refused_by_other_manager', ['leave_type' => $leaveTypeName], $userLeave->language),
                    'message' => __('notifications.fcm.body.leave_refuse', ['manager_firstname' => $userRefuser->firstname, 'manager_lastname' => $userRefuser->lastname, 'leave_type' => $leaveTypeName], $userLeave?->language),
                    'url' => "/leave/{$leave->getKey()}",
                    'page' => 'myLeaves',
                ]));
            } catch (Exception $exception) {
                Log::Debug('Notification Push not send', ['message' => $exception->getMessage()]);
            }
        } else {
            $leave->current_validator_level = $firstManager ? $firstManager->level : 1;
            $title = "CONGÉS : Demande de congé refusée";
            $body = "Bonjour $userLeave->firstname $userLeave->lastname, \n$userRefuser->firstname  $userRefuser->lastname  a refusé(e) votre demande de $leaveTypeName \npour la période du  {$leaveDate['start_date']} au {$leaveDate['end_date']}.";
            $reason = "{$datum["reason"]}";

            if ($userLeave->managers()->where('firstname', $userRefuser->firstname)->where('lastname', $userRefuser->lastname)->exists()) {
                $managersNotified = $userLeave->managers()
                    ->where('level', '<', $userLeave->managers()->where('id', $userRefuser->getKey())->first()->pivot->level)
                    ->get();
            } else {
                $managersNotified = $userLeave->managers()
                    ->where('level', '<=', $leave->current_validator_level)
                    ->get();
            }

            foreach ($managersNotified as $manager) {
                $this->sendAlert($userLeave, $leave, [$manager->id], $title, $body, $statusId);
                try {
                    $manager?->notify(new UserReceiveNotificationPush([
                        'leave' => $leave,
                        'title' => __('notifications.fcm.title.title_leave_cancel_refused_by_other_manager', ['leave_type' => $leaveTypeName], $manager->language),
                        'message' => __('notifications.fcm.body.leave_refuse', ['manager_firstname' => $userRefuser->firstname, 'manager_lastname' => $userRefuser->lastname, 'leave_type' => $leaveTypeName], $manager?->language),
                        'url' => "/leave/{$leave->getKey()}",
                        'page' => 'myLeaves',
                    ]));
                } catch (Exception $exception) {
                    Log::Debug('Notification Push not send', ['message' => $exception->getMessage()]);
                }

                $manager?->notify(new LeaveRefused([
                    'leave' => $leave,
                    'title' => __('notifications.fcm.title.title_leave_cancel_refused_by_other_manager', ['leave_type' => $leaveTypeName], $manager->language),
                    'greeting' => __('notifications.greeting', ['user_firstname' => $manager->firstname], $manager->language),
                    'refusal' => $reason,
                    'keyword' => [
                        'notifications.refused_leave' => ['user_firstname' => $userLeave->firstname, 'user_lastname' => $userLeave->lastname, 'manager_firstname' => $userRefuser->firstname, 'manager_lastname' => $userRefuser->lastname, 'leave_type' => $leaveTypeName, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date']]
                    ]
                ]));
            }
            // Envoi un mail
            $userLeave->notify(new LeaveRefused([
                'leave' => $leave,
                'title' => __('notifications.title_leave_refused', [], $userLeave->language),
                'greeting' => __('notifications.greeting', ['user_firstname' => $userLeave->firstname], $userLeave->language),
                'refusal' => $reason,
                'keyword' => [
                    'notifications.manager_refuse_leave' => ['manager_firstname' => $userRefuser->firstname, 'manager_lastname' => $userRefuser->lastname, 'leave_type' => $leaveTypeName, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date']]
                ]
            ]));
            //Send Push notifications
            try {
                $userLeave->notify(new UserReceiveNotificationPush([
                    'leave' => $leave,
                    'title' => __('notifications.fcm.title.title_leave_cancel_refused_by_other_manager', ['leave_type' => $leaveTypeName], $userLeave->language),
                    'message' => __('notifications.fcm.body.leave_refuse', ['manager_firstname' => $userRefuser->firstname, 'manager_lastname' => $userRefuser->lastname, 'leave_type' => $leaveTypeName], $userLeave?->language),
                    'url' => "/leave/{$leave->getKey()}",
                    'page' => 'myLeaves',
                ]));
            } catch (Exception $exception) {
                Log::Debug('Notification Push not send', ['message' => $exception->getMessage()]);
            }
        }
        $this->sendToHistory($leave->getKey(), $userRefuser->id, $statusId, $datum["reason"]);
        $this->responseData = $leave->toArray();
        $dataToReturn[] = $this->responseData;
        $this->responseMessage = (!isset($this->responseMessage) || $this->responseMessage == "") ? __('messages.SuccessRefuseLeave') : __('messages.SuccessMassValidationLeave');
        return;
        $leave->current_validator_level = $firstManager ? $firstManager->level : 1;

        // Ajoute le refus dans l'historique du congé
        $this->sendToHistory($leave->getKey(), $userRefuser->id, $statusId, $datum["reason"]);

        $this->responseData = $leave->toArray();
        $dataToReturn[] = $this->responseData;
        $this->responseMessage = (!isset($this->responseMessage) || $this->responseMessage == "") ? __('messages.SuccessRefuseLeave') : __('messages.SuccessMassValidationLeave');
    }

    /**
     * Permet de transmettre en paie un congé
     * @param array $datum
     * @param array $dataToReturn
     * @throws Exception
     */
    private function transmittedLeave(array $datum, &$dataToReturn = []): void
    {
        try {
            $leave = Leave::find($datum["leave_id"]);

            // Si le congé n'existe pas
            if (!$leave) {
                throw new Exception(__('warnings.ErrorLeaveDontExist'));
            }

            // Profile du user courant
            $profile = Profile::find($this->currentUser->profile_id);
            // Vérification que son Profile est bien admin
            if ($profile->label !== "ADMINISTRATEUR" && $profile->label !== "ADMINISTRATEURMANAGER") {
                throw new Exception(__('warnings.ErrorActionNotAllowed'));
            }

            // Client du user courant
            $client = Client::find($this->currentUser->site->client_id);

            // Vérification que le user a le droit de transmettre en paie le congé
            $admin = $this->verifyIfUserCanTransmitLeave($client, $leave);
            if (!$admin) {
                throw new Exception(__('warnings.ErrorNoAdminForTransmit'));
            }

            // Si le status du congé est submitted -> admin remplace manager
            if ($leave->status->tag == "SUBMITTED") {
                $leaveTypeName = ApiHelper::LeaveTypeName($leave);
                $userLeave = $leave->user;

                $title = "CONGÉS : L'Administrateur a traité une demande de congés à votre place";

                // Schéma VERTICAL - Envoie au manager remplacé si manager existe
                if ($client->validation_scheme === "VERTICAL" && $userLeave->managers()->count() != 0) {
                    // Récupère le manager qui a été remplacé
                    Log::Debug("Email send VERTICAL manager()->count");
                    $userManager = $userLeave->managers()->where('level', '=', $leave->current_validator_level)->first();
                    if ($userManager == null) {
                        $userManager = $userLeave->managers()->where('level', '>', $leave->current_validator_level)->first();
                        if ($userManager == null) {
                            $userManager = $userLeave->managers()->where('level', '<', $leave->current_validator_level)->orderBy("level", "DESC")->first();
                        }
                    }
                    // Mail + notif -> manager qui s'est fait remplacé par admin

                    $leaveDate = ApiHelper::convertStartFullDateAndEndFullDate($userManager, $leave->start_date, $leave->end_date);

                    $body = "{$this->currentUser["firstname"]} {$this->currentUser["lastname"]} a validé la demande de
                         {$leaveTypeName} faite par {$userLeave->firstname} {$userLeave->lastname}  pour la période du  {$leaveDate['start_date']}  au {$leaveDate['end_date']}.";

                    // Envoi le mail et l'alerte
                    $userManager->notify(new LeaveTransmitted([
                        'leave' => $leave,
                        'title' => __('notifications.title_another_administrator_valid_leave', [], $userManager->language),
                        'greeting' => __('notifications.greeting', ['user_firstname' => $userManager->firstname], $userManager->language),
                        'keyword' => [
                            'notifications.another_administrator_valid_leave' => ['admin_firstname' => $this->currentUser["firstname"], 'admin_lastname' => $this->currentUser["lastname"], 'leave_type' => $leaveTypeName, 'user_firstname' => $userLeave->firstname, 'user_lastname' => $userLeave->lastname, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date']]
                        ]
                    ]));
                    $this->sendAlert($userManager, $leave, [$userManager->id], $title, $body, $leave->statusId);
                    Log::Debug("Email send VERTICAL manager()->count");

                } else { // Schéma HORIZONTAL - Envoie à tous les managers de l'utilisateur
                    $managersReplaced = $userLeave->validators()->get();

                    Log::Debug("Email not send !VERTICAL manager()->count");
                    foreach ($managersReplaced as $managerReplaced) {
                        // Mail + notif -> manager qui s'est fait remplacé par admin

                        $leaveDate = ApiHelper::convertStartFullDateAndEndFullDate($managerReplaced, $leave->start_date, $leave->end_date);

                        $body = " {$this->currentUser["firstname"]}  {$this->currentUser["lastname"]}  a validé la demande de {$leaveTypeName} faite par  {$userLeave->firstname}  {$userLeave->lastname}
                        pour la période du {$leaveDate['start_date']} au {$leaveDate['end_date']}.";

                        // Envoi le mail et l'alerte
                        $managerReplaced->notify(new LeaveTransmitted([
                            'leave' => $leave,
                            'title' => __('notifications.title_another_administrator_valid_leave', [], $managerReplaced->language),
                            'greeting' => __('notifications.greeting', ['user_firstname' => $managerReplaced->firstname], $managerReplaced->language),
                            'keyword' => [
                                'notifications.another_administrator_valid_leave' => ['admin_firstname' => $this->currentUser["firstname"], 'admin_lastname' => $this->currentUser["lastname"], 'leave_type' => $leaveTypeName, 'user_firstname' => $userLeave->firstname, 'user_lastname' => $userLeave->lastname, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date']]
                            ]
                        ]));
                        $this->sendAlert($managerReplaced, $leave, [$managerReplaced->id], $title, $body, $leave->status_id);
                    }
                    Log::Debug("Email send !VERTICAL manager()->count");
                }
            }

            // Récupération de l'ID du statut TRANSMITTED
            $statusId = Status::where("tag", "=", "TRANSMITTED")->first()->id;

            $leave->last_updater_id = $this->currentUser->getKey();
            $leave->current_validator_level++;
            $leave->status_id = $statusId;
            $userLeaveCount = UserLeaveCount::query()->where("leave_type_id", $leave->leave_type_id)->first();
            // Si le congé ne s'est pas modifié
            if (!$leave->save()) {
                $this->responseMessage = __('warnings.ErrorTransmitLeave');
                throw new Exception(__('warnings.ErrorTransmitLeave'));
            }

            if (empty($leave->leave_type_sub_family_id) && !empty($userLeaveCount)) {
                $this->updateUserLeaveCount($leave->leave_type_id, $leave->duration, $leave->user_id, $leave->n1, $leave->n);
            }

            // Pas de mail / notification / alerte en cas de transmission en paie
            // Ajoute la transmission en paie dans l'historique du congé
            $this->sendToHistory($leave->getKey(), $admin->id, $statusId, "");

            $this->responseData = Leave::find($leave->getKey())->toArray();
            $dataToReturn[] = $this->responseData;
            $this->responseMessage = (!isset($this->responseMessage) || $this->responseMessage == "") ? __('messages.SuccessTransmitLeave') : __('messages.SuccessMassValidationLeave');
        } catch (Exception $exception) {
            $this->responseMessage = __('warnings.ErrorTransmitLeave');
            throw new Exception($exception->getMessage());
        }
    }

    private function taskPostStoreAndUpdate(Leave $leave)
    {
        try {
            $leaveTypeName = ApiHelper::LeaveTypeName($leave);
            $user = User::find($leave->user_id);
            $client = Client::find($this->clientId);
            $managers = [];
            $managersExist = $user->validators();
            $title = "CONGÉS : Demande de " . $leaveTypeName;
            $body = $user->firstname . " " . strtoupper($user->lastname) . " a soumis(e) à validation une demande de " . $leaveTypeName . ".";
            $currentUser = Tools::getCurrentUserWithUuid();

            // Envoi à l'historique
            $this->sendToHistory($leave->getKey(), $currentUser->id, $leave->status_id);

            $userLeaveManager = $user->managers()->where("id", "!=", $leave->user_id)->first();
            // Si je suis mon propre manager
            if (!$userLeaveManager && $leave->user_id === $this->currentUser->getKey()) {
                $leave->last_updater_id = $leave->user_id;
                if (!$leave->save()) {
                    throw new Exception(__('warnings.ErrorStoreLeave'));
                }

                $this->validateLeave(["action" => "VALIDATED", "leave_id" => $leave->getKey()]);
            } else { // Sinon
                if ($client->validation_scheme === "VERTICAL") {
                    $manager = $managersExist->where("level", "=", $leave->current_validator_level)->first();
                    if (isset($manager)) {
                        $userManager = User::find($manager->id);

                        if ($manager->id === $this->currentUser->getKey()) {
                            $leave->last_updater_id = $leave->user_id;
                            if (!$leave->save()) {
                                throw new Exception(__('warnings.ErrorStoreLeave'));
                            }
                            $this->validateLeave(["action" => "VALIDATED", "leave_id" => $leave->getKey()]);
                        } else {
                            $leaveDate = ApiHelper::convertStartFullDateAndEndFullDate($userManager, $leave->start_date, $leave->end_date);

                            $userManager->notify(new LeaveInformation([
                                'leave' => $leave,
                                'title' => __('notifications.title_leave_asking', [], $userManager->language),
                                'greeting' => __('notifications.greeting', ['user_firstname' => $userManager->firstname], $userManager->language),
                                'keyword' => [
                                    'notifications.send_validating_leave' => ['user_firstname' => $leave->user->firstname, 'user_lastname' => strtoupper($leave->user->lastname), 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date'], 'leave_type' => $leaveTypeName]
                                ],
                            ], true));

                            Log::Debug("Email send VERTICAL");
                            // Envoi une alerte
                            $this->sendAlert($user, $leave, [$userManager->id], $title, $body);
                            Log::Debug("sendAlert");
                            //Manager
                            //Send push notification
                            try {
                                $userManager?->notify(new UserReceiveNotificationPush([
                                    'leave' => $leave,
                                    'title' => __('notifications.fcm.title.title_leave_asking', ['leave_type' => $leaveTypeName], $userManager?->language),
                                    'message' => __('notifications.fcm.body.leave_period', ['user_firstname' => $leave->user->firstname, 'user_lastname' => strtoupper($leave->user->lastname), 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date'], 'leave_type' => $leaveTypeName], $userManager?->language),
                                    'url' => "/leave/{$leave->getKey()}",
                                    'page' => 'toValidate',
                                ]));
                            } catch (Exception $exception) {
                                Log::Debug("Notification Push not send " . $exception->getMessage());
                            }
                        }
                    }
                } else {
                    foreach ($managersExist->get() as $manager) {
                        $managers[] = $manager["id"];
                        $userManager = User::find($manager["id"]);
                        Log::Debug("Email not send !VERTICAL");
                        $leaveDate = ApiHelper::convertStartFullDateAndEndFullDate($userManager, $leave->start_date, $leave->end_date);

                        $userManager->notify(new LeaveInformation([
                            'leave' => $leave,
                            'title' => __('notifications.title_leave_asking', [], $userManager->language),
                            'greeting' => __('notifications.greeting', ['user_firstname' => $userManager->firstname], $userManager->language),
                            'keyword' => [
                                'notifications.send_validating_leave' => ['user_firstname' => $leave->user->firstname, 'user_lastname' => strtoupper($leave->user->lastname), 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date'], 'leave_type' => $leaveTypeName]
                            ],
                        ], true));
                        Log::Debug("Email send !VERTICAL");
                        //manager
                        try {
                            $userManager?->notify(new UserReceiveNotificationPush([
                                'leave' => $leave,
                                'title' => __('notifications.fcm.title.title_leave_asking', ['leave_type' => $leaveTypeName], $userManager?->language),
                                'message' => __('notifications.fcm.body.leave_period', ['user_firstname' => $leave->user->firstname, 'user_lastname' => strtoupper($leave->user->lastname), 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date'], 'leave_type' => $leaveTypeName], $userManager?->language),
                                'url' => "/leave/{$leave->getKey()}",
                                'page' => 'toValidate',
                            ]));
                        } catch (Exception $exception) {
                            Log::Debug("Notification Push not send " . $exception->getMessage());
                        }
                    }
                    // Envoi une alerte
                    $this->sendAlert($user, $leave, $managers, $title, $body);
                }
            }
        } catch (Exception $exception) {
            throw new Exception($exception->getMessage());
        }
    }

    /**
     * Récupération des jours fériés en fonction du pays de l'utilisateur
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function getPublicHolidays(Request $request)
    {
        try {
            $dataValide = $this->validate($request, [
                "startDate" => "date|required",
                "endDate" => "date|required",
            ]);

            $this->responseData = Holidays::getPublicHolidays($dataValide["startDate"], $dataValide["endDate"], $this->currentUser);
            $this->responseMessage = __('messages.SuccessGetClosedDay');
        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorValidationData');
            $this->responseCode = $exception->status;

        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetClosedDay');
            $this->responseCode = $exception->getCode();
        }
        return $this->formattedResponse();
    }


    /**
     * Récupération des vacances (france uniquement)
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function getHolidays(Request $request)
    {
        try {
            $dataValide = $this->validate($request, [
                "startDate" => "date|required",
                "endDate" => "date|required",
            ]);

            $this->responseData = Holidays::getFrenchHolidays($dataValide["startDate"], $dataValide["endDate"]);
            $this->responseMessage = __('messages.SuccessGetHolidays');
        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorValidationData');
            $this->responseCode = $exception->status;
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetHolidays');
            $this->responseCode = $exception->getCode();
        }
        return $this->formattedResponse();
    }

    /**
     * Récupération d'un congés
     * @param Request $request
     * @param int $id
     *
     * @return JsonResponse
     */
    public function show(Request $request, int $id): JsonResponse
    {
        try {
            // Vérification de l'id
            $this->basicIdVerification($id);
            // Init model
            $leaveModel = Leave::query();
            // Si je veux recupéré le type du congé
            if ($request->query("withLeaveType")) {
                $leaveModel->with("leave_type");
            }
            // Si je veux recupéré le status du congé
            if ($request->query("withStatus")) {
                $leaveModel->with("status");
            }
            // Si je veux recupéré l'historique du congé
            if ($request->query("withHistories")) {
                $leaveModel->with("histories");
            }
            // Si je veux recupéré les l'utilisateur du congé
            if ($request->query("withUser")) {
                $leaveModel->with("user");
            }

            // Récupération du ongés passé en paramètre
            $leave = $leaveModel->find($id);

            // Vérifie si le congé existe
            $this->verifyIfLeaveIfExist($id, $leave);
            // Récupération si le user connecter est manager
            $isManager = $this->currentUserIsManager($leave);
            // Vérifie si le congé appartient à l'utilisateur
            $this->verifyIfLeaveBelongsToUser($leave, $isManager);

            // Format histories
            if ($request->query("withHistories")) {
                $leave = $this->formatHistories($leave);
            }


            $this->responseMessage = __('messages.SuccessGetLeave');
            $this->responseData = $leave->toArray();

            if ($isManager) {
                $this->responseData["is_manager"] = 1;
            }

        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetLeave');
        }

        return $this->formattedResponse();
    }

    public function store(Request $request)
    {
        $errors = [];
        set_time_limit(7200);
        ini_set('memory_limit', '-1');

        try {
            $data = json_decode($request->get("data"), true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Erreur JSON : ' . json_last_error_msg(), 400);
            }
            if (!$data) {
                throw new Exception(__('warnings.ErrorDataArrayDontExist'), 400);
            }

            if ($request->has("allUsers")) {
                if (strtolower(Auth::user()->profile->label) !== 'standard') {
                    $query = User::select('users.id', 'firstname', 'lastname');

                    if (in_array(strtolower(Auth::user()->profile->label), ['administrateur', 'administrateurmanager'])) {
                        $query->join('sites as s', 's.id', 'users.site_id')
                            ->where('s.client_id', Auth::user()->site->client_id);
                    } else {
                        $query->leftJoin('managers as manager_managed_user_no_one_else', function (JoinClause $joinQuery) {
                            $joinQuery->on('manager_managed_user_no_one_else.managed_id', 'users.id')
                                ->where('manager_managed_user_no_one_else.manager_id', Auth::user()->getKey());
                        })
                            ->when(strtolower(Auth::user()->profile->label) === 'director', function ($query) {
                                $query->leftJoin('director_user as du', function (JoinClause $joinQuery) {
                                    $joinQuery->on('du.user_id', 'users.id')->where('du.director_id', Auth::user()->getKey());
                                });
                            })
                            ->where(function ($whereQuery) {
                                $whereQuery->whereNotNull('manager_managed_user_no_one_else.manager_id')
                                    ->when(strtolower(Auth::user()->profile->label) === 'director', function ($query) {
                                        $query->orWhere(function ($orWhereQuery) {
                                            $orWhereQuery->whereNotNull('du.director_id');
                                        });
                                    })
                                    ->orWhere('users.id', Auth::user()->getKey());
                            });
                    }

                    if (!empty($data["usersNotStoreLeave"])) {
                        $query->whereNotIn('users.id', $data["usersNotStoreLeave"]);
                    }

                    $users = $query->get()->pluck('id');
                } else {
                    throw new Exception(__('warnings.BadProfile'), 422);
                }
            } else {
                $users = $data['users'] ?? [$data['user_id']];
            }

            // Vérifications basique d'un congé envoyé en POST
            $this->basicLeaveVerifications($data, $users, $errors);

            // Récupération du type de congé
            $leaveType = LeaveType::find($data["leave_type_id"]);

            if (count($users) > 1 && $leaveType->is_attachment_required) {
                throw new Exception(__('warnings.LeaveTypeNeedFile'), 400);
            } else if (count($users) === 1 && $leaveType->is_attachment_required && $request->file("attachment") === null && !$leaveType->can_justify_later) {
                throw new Exception(__('warnings.FileRequired'), 400);
            } else {
                $file = $request->file("attachment");
            }

            $status = Status::firstWhere("tag", "SUBMITTED")->getKey();

            foreach ($users as $userId) {
                if (isset($errors[$userId])) {
                    continue;
                }

                if ($this->currentUser->getKey() !== $userId && $this->currentUser->profile->label === 'STANDARD') {
                    throw new Exception(__('warnings.ActionNotAllowed'));
                }

                try {
                    $user = User::findOrFail($userId);
                } catch (ModelNotFoundException $e) {
                    if (count($users) > 1) {
                        $errors[$userId]['code'] = 400;
                        $errors[$userId]['message'] = __('warnings.LeaveAlreadyExisteForDate');
                        continue;
                    } else {
                        throw new Exception(__('warnings.UserDontExist'), 404, $e);
                    }
                }

                if ($user->getKey() != $this->currentUser->getKey()) {
                    if ($this->currentUser->profile->label === 'STANDARDMANAGER' && !$this->currentUser->managed()->where('managed_id', $user->getKey())->exists()) {
                        if (count($users) > 1) {
                            $errors[$user->getKey()]['user'] = $user->firstname . ' ' . $user->lastname;
                            $errors[$user->getKey()]['message'] = __('warnings.NotAdminFromUser');
                            continue;
                        } else {
                            throw new Exception(__('warnings.NotAdminFromUser'));
                        }
                    }
                    if ($this->currentUser->profile->label === 'DIRECTOR' && !$this->currentUser->managed()->where('managed_id', $user->getKey())->exists()) {
                        if (count($users) > 1) {
                            $errors[$user->getKey()]['user'] = $user->firstname . ' ' . $user->lastname;
                            $errors[$user->getKey()]['message'] = __('warnings.NotDirectorOrManagerFromUser');
                            continue;
                        } else {
                            throw new Exception(__('warnings.NotDirectorOrManagerFromUser'));
                        }
                    }

                    if ($this->clientId != $user->site->client_id) {
                        if (count($users) > 1) {
                            $errors[$user->getKey()]['user'] = $user->firstname . ' ' . $user->lastname;
                            $errors[$user->getKey()]['message'] = __('warnings.UserBelongToAnotherClient');
                            continue;
                        } else {
                            throw new Exception(__('warnings.UserBelongToAnotherClient'));
                        }
                    }
                }

                $leave = new Leave();
                $leave->user_id = $user->getKey();
                $leave->creator_id = $this->currentUser->getKey();
                $leave->status_id = $status;
                $leave->leave_type_id = $data["leave_type_id"];
                $leave->leave_type_sub_family_id = $data["leave_type_sub_family_id"];
                $leave->start_date = Carbon::create($data["start_date"])->format('Y-m-d H:i:s');
                $leave->end_date = Carbon::create($data["end_date"])->format('Y-m-d H:i:s');
                $leave->comment = $data["comment"];
                $leave->duration = $data["duration"];
                $leave->current_validator_level = 1;
                $leave->leaveDaysDistribution($user->getKey());

                // Check if start_date and end_date is authorized
                if ($leaveType->start_date != null && $leaveType->end_date != null) {
                    if (!($leave->start_date >= $leaveType->start_date && $leave->end_date <= $leaveType->end_date)) {
                        throw new Exception(__('warnings.ErrorDateLeaveTypeNotAllowed'), 400);
                    }
                }

                if ($leaveType->default_leave_value) {
                    if ($leave->duration > $leaveType->default_leave_value) {
                        throw new Exception(__('warnings.DurationTooLong'), 400);
                    }
                }

                // Vérifie que l'utilisateur à suffisamment de jours de congé disponible
                if (!$leaveType->can_exceed && $leaveType->needs_count) {
                    $this->verifIfUserAsEnoughDayAvailable($leave, count($users), $errors);
                }

                if (isset($errors[$userId])) {
                    continue;
                }

                if (isset($file)) {
                    $uploadedFile = $this->fileUpload($file, "leave_attachment/leaveId-" . (Leave::orderBy("id", "DESC")->count() + 1) . "." . $file->clientExtension());
                    $leave->attachment_name = $uploadedFile["name"];
                    $leave->attachment_path = $uploadedFile["path"];
                }

                $hasAlert = false;
                if ($leave->leave_type->needs_count) {
                    //  Retire les conger N-1 et N dans user leave count
                    list($leave->n, $leave->n1, $leave->out_day, $hasAlert) = $this->updateNandN1($leave->start_date, $leave->leave_type_id, $leave->duration, $leave->user_id, $hasAlert, count($users), $errors);
                    if (isset($errors[$userId])) {
                        continue;
                    }
                } else {
                    if (!empty($leave->leave_type_sub_family_id)) {
                        $leave->out_day = 0;
                        $outDay = $leave->leave_type_sub_family->value - $leave->duration;
                        if ($outDay < 0) {
                            $leave->out_day = $outDay;
                        }
                    } elseif (!empty($leave->leave_type->default_leave_value)) {
                        $leave->out_day = 0;
                        $outDay = $leave->leave_type->default_leave_value - $leave->duration;

                        if ($outDay < 0) {
                            $leave->out_day = $outDay;
                        }
                    }
                }

                if (!$leave->save()) {
                    if (count($users) > 1) {
                        $errors[$user->getKey()]['user'] = $user->firstname . ' ' . $user->lastname;
                        $errors[$user->getKey()]['message'] = __('warnings.ErrorStoreLeave');
                        continue;
                    } else {
                        throw new Exception(__('warnings.ErrorStoreLeave'));
                    }
                }

                // Traitement après enregistrement
                $this->taskPostStoreAndUpdate($leave);
                $leave = Leave::find($leave->getKey());

                $this->responseData[] = $leave->toArray();

                if (count($errors) === 0) {
                    $this->responseMessage = [__('messages.SuccessStoreLeave')];

                    if ($hasAlert) {
                        $this->responseMessage = [__('messages.SuccessStoreLeaveByTakingInNCounts')];
                    }
                }
            }
        } catch (Exception $exception) {
            $this->responseMessage = $exception->getMessage();
            $this->responseCode = $exception->getCode();
            $this->responseDetails = $exception->getMessage();
        }

        if (count($users) > 1) {
            if (count($errors) === count($users)) {
                $this->responseMessage = [__('messages.store_leave_with_errors')];
                $this->responseCode = 500;
                $this->responseData['errors'] = $errors;
            } else if (count($errors) >= 1) {
                $this->responseMessage = [__('messages.success_store_leave_with_errors')];
                $this->responseCode = 201;
                $this->responseData['errors'] = $errors;
            }
        }

        return $this->formattedResponse();
    }

    public function updateNandN1($startDate, $leaveTypeId, $duration, $userId, $hasAlert, $users, &$errors = [])
    {
        $userLeaveCount = $this->getUserLeaveCount($leaveTypeId, $userId, $users, $errors);
        if (!empty($userLeaveCount)) {
            $leaves = Leave::query()->whereIn("status_id", Status::whereIn("tag", ["VALIDATED", "SUBMITTED"])->get("id")->toArray())->where("user_id", $userId)->where('leave_type_id', $leaveTypeId)->get();
            $outDays = $leaves->sum('out_day');
            $futureNLeaves = $leaves->sum('n');
            $futureN1Leaves = $leaves->sum('n1');
            $outDay = 0;
            if (empty($userLeaveCount["n-1"])) {
                $outDay = $this->outDay($userLeaveCount, $duration, $outDays, $futureNLeaves);
                $n1 = null;
                $n = $duration;
            } else {
                $june = Carbon::now()->month(6)->startOfMonth();
                if (Carbon::now() >= $june) {
                    $june->addYear();
                }
                if (Carbon::parse($startDate) > $june) {
                    $outDay = $this->outDay($userLeaveCount, $duration, $outDays, $futureNLeaves);
                    $n1 = null;
                    $n = $duration;
                } else {
                    if ($userLeaveCount["n-1"]->balance < ($duration + $futureN1Leaves)) {
                        $n1 = $userLeaveCount["n-1"]->balance - $futureN1Leaves;
                        $n = $duration - $n1;
                        $outDay = $this->outDay($userLeaveCount, 0, $outDays, $n);
                        $hasAlert = true;
                    } else {
                        $n = null;
                        $n1 = $duration;
                    }
                }
            }
        } else {
            $n = null;
            $n1 = null;
            $outDay = null;
            $hasAlert = null;
        }
        return [$n, $n1, $outDay, $hasAlert];
    }

    public function updateUserLeaveCount($leaveTypeId, $duration, $user_id, $n1, $n)
    {
        $userLeaveCount = $this->getUserLeaveCount($leaveTypeId, $user_id);

        if (empty($userLeaveCount["n-1"])) {
            $userLeaveCount["n"]->taken += $duration;
            $userLeaveCount["n"]->balance -= $duration;
        } else {
            $userLeaveCount["n"]->taken += $n;
            $userLeaveCount["n"]->balance -= $n;

            $userLeaveCount["n-1"]->taken += $n1;
            $userLeaveCount["n-1"]->balance -= $n1;

            $userLeaveCount["n-1"]->save();
        }
        $userLeaveCount["n"]->save();
    }

    //Permet de s'avoir les le numbre de jour en surplus
    private function outDay(&$userLeaveCount, $duration, $outDays, $futureLeaves)
    {
        $outDay = 0;
        if ($outDays != 0) {
            $outDay = -$duration;
        } elseif ($userLeaveCount["n"]->balance >= 0) {
            if ($userLeaveCount["n"]->balance - $duration - $futureLeaves < 0) {
                $outDay = $userLeaveCount["n"]->balance - $duration - $futureLeaves;
            }
        } else {
            $outDay = -$duration;
        }
        return $outDay;
    }

    /**
     * Permet d'annulé un congé
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Exception
     * @throws Throwable
     */
    public function cancelLeave(Request $request)
    {
        try {
            $rules = [
                "leave_id" => "required|integer"
            ];
            $data = $this->validate($request, $rules, $this->messages());

            // get Leave and userLeave
            $leave = Leave::find($data["leave_id"]);
            $leaveTypeName = ApiHelper::LeaveTypeName($leave);
            $userLeave = User::withTrashed()->find($leave->user_id);
            $statusCancel = Status::firstWhere("tag", "CANCELED")->getKey();

//             check if user can cancel the leave
            $userCanCancel = $this->verifyIfUserCanCancelLeave($leave);
            if (!$userCanCancel["result"]) { // si l'user peut cancel
                if (!empty($userCanCancel["send_mail"])) { // Il peut recevoir des mails
                    $userManager = $this->getFirstManager($userLeave);
                    if ($userManager && $userManager->id != $leave->user_id) { // Si l'utilisateur n'est pas un manager
                        if ($leave->status->tag === 'SUBMITTED' && $leave->current_validator_level < 2) {
                            $leaveDate = ApiHelper::convertStartFullDateAndEndFullDate($userLeave, $leave->start_date, $leave->end_date);
                            // envoi de mail
                            $userLeave->notify(new LeaveCancelled([
                                'leave' => $leave,
                                'title' => __('notifications.title_leave_cancelled', [], $userLeave->language),
                                'greeting' => __('notifications.greeting', ['user_firstname' => $userLeave->firstname], $userLeave->language),
                                'keyword' => [
                                    'notifications.leave_cancelled_period' => ['leave_type' => $leaveTypeName, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date']]
                                ]
                            ]));

                            $leaveDate = ApiHelper::convertStartFullDateAndEndFullDate($userManager, $leave->start_date, $leave->end_date);
                            $userManager->notify(new LeaveInformation([
                                'leave' => $leave,
                                'title' => __('notifications.title_leave_cancelled_managed', ['user_firstname' => $userLeave->firstname, 'user_lastname' => $userLeave->lastname], $userLeave->language),
                                'greeting' => __('notifications.greeting', ['user_firstname' => $userManager->firstname], $userManager->language),
                                'keyword' => [
                                    'notifications.managed_cancelled_leave' => ['user_firstname' => $userLeave->firstname, 'user_lastname' => $userLeave->lastname, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date'], 'leave_type' => $leaveTypeName]
                                ]
                            ], true, 'planning'));

                            try {
                                $userManager?->notify(new UserReceiveNotificationPush([
                                    'leave' => $leave,
                                    'title' => __('notifications.fcm.title.title_leave_cancelled_managed', [], $userManager?->language),
                                    'message' => __('notifications.fcm.body.managed_cancelled_leave', ['user_firstname' => $userLeave->firstname, 'user_lastname' => $userLeave->lastname, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date'], 'leave_type' => $leaveTypeName], $userManager?->language),
                                    'url' => "/leave/{$leave->getKey()}",
                                    'page' => 'toCancel',
                                ]));
                            } catch (Exception $exception) {
                                Log::Debug("Notification Push not send " . $exception->getMessage());
                            }

                            // cancel du leave
                            $leave->status_id = $statusCancel;
                            $leave->save();
                            $this->recalcLeavesDistribution($leave->user_id, $leave->leave_type_id);

                            $this->responseMessage = __('messages.SuccessCancelLeave');
                            return $this->formattedResponse();
                        } else {
                            // Send Mail + notif
                            $leaveDate = ApiHelper::convertStartFullDateAndEndFullDate($userManager, $leave->start_date, $leave->end_date);
                            $title = "CONGÉS : Demande annulation de  {$leaveTypeName}";

                            $body = "{$userLeave->firstname}  {$userLeave->lastname} vient de soumettre une demande d'annulation de la demande de
                                 {$leaveTypeName} pour la période du  {$leaveDate['start_date']} au  {$leaveDate['end_date']}.";
                            // Envoi l'alerte et du mail
                            $this->sendAlert($userManager, $leave, [$userManager->id], $title, $body, $leave->status_id);
                            $userManager->notify(new LeaveInformation([
                                'leave' => $leave,
                                'title' => __('notifications.title_leave_asking_cancelled_managed', [], $userManager->language),
                                'greeting' => __('notifications.greeting', ['user_firstname' => $userManager->firstname], $userManager->language),
                                'keyword' => [
                                    'notifications.managed_asking_cancelled_leave' => ['user_firstname' => $userLeave->firstname, 'user_lastname' => $userLeave->lastname, 'leave_type' => $leaveTypeName, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date']]
                                ]
                            ], true, 'cancel'));

                            try {
                                $userManager?->notify(new UserReceiveNotificationPush([
                                    'leave' => $leave,
                                    'title' => __('notifications.fcm.title.title_leave_cancel_push', [], $userManager?->language),
                                    'message' => __('notifications.fcm.body.leave_cancel_period', ['user_firstname' => $userLeave?->firstname, 'user_lastname' => $userLeave?->lastname, 'leave_type' => $leaveTypeName], $userManager?->language),
                                    'url' => "/leave/{$leave->getKey()}",
                                    'page' => 'toCancel',
                                ]));
                            } catch (Exception $exception) {
                                Log::Debug("Notification Push not send " . $exception->getMessage());
                            }

                            $leave->status_id = Status::query()->where('tag', 'SUBMITTED_TO_CANCELLATION')->first()?->getKey();
                            $leave->save();

                            $this->sendToHistory($leave->getKey(), $this->currentUser->getKey(), $leave->status_id);
                            // Response
                            $this->responseCode = 200;
                            $this->responseMessage = __('messages.AskForCancelLeave');
                            return $this->formattedResponse();
                        }
                    } else {
                        $leave->status_id = $statusCancel;
                        $leave->save();
                        $this->recalcLeavesDistribution($leave->user_id, $leave->leave_type_id);
                        $this->responseCode = 200;
                        $this->responseMessage = __('messages.SuccessCancelLeave');
                        return $this->formattedResponse();
                    }
                }
                $this->responseCode = 400;
                $this->responseMessage = __('messages.CantCancelLeave');
                $this->responseDetails = $userCanCancel["msg"];
                return $this->formattedResponse();
            }

            $leave->status_id = $statusCancel;
            $leave->save();
            $this->recalcLeavesDistribution($leave->user_id, $leave->leave_type_id);
            $this->responseMessage = __('messages.SuccessTransmittedLeaveCanceled');

            // Ajout dans l'historique du congé
            $this->sendToHistory($leave->getKey(), $this->currentUser->getKey(), $leave->status_id, "");

            // Données pour alerte et mail
            $leaveTypeName = ApiHelper::LeaveTypeName($leave);
            $leaveDate = ApiHelper::convertStartFullDateAndEndFullDate($userLeave, $leave->start_date, $leave->end_date);

            $title = "CONGÉS : Demande de " . $leaveTypeName . " annulée";
            $body = "Votre demande de " . $leaveTypeName . " pour la période du " . $leaveDate['start_date'] . " au " . $leaveDate['end_date'] . " a été annulée par " . $this->currentUser->firstname . " " . strtoupper($this->currentUser->lastname) . ".";

            // Envoi une alerte
            $this->sendAlert($userLeave, $leave, [$this->currentUser->getKey()], $title, $body, $leave->status_id);

            // Envoi un mail
            $userLeave?->notify(new LeaveCancelled([
                'leave' => $leave,
                'title' => __('notifications.title_leave_cancelled', [], $userLeave->language),
                'greeting' => __('notifications.greeting', ['user_firstname' => $userLeave->firstname], $userLeave->language),
                'keyword' => [
                    'notifications.leave_cancelled_period_by_manager' => ['leave_type' => $leaveTypeName, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date'], 'user_firstname' => $this->currentUser->firstname, 'user_lastname' => strtoupper($this->currentUser->lastname)]
                ],
            ]));

            try {
                $userLeave?->notify(new UserReceiveNotificationPush([
                    'leave' => $leave,
                    'title' => __('notifications.fcm.title.title_leave_cancel_refused_manager', [], $userLeave?->language),
                    'message' => __('notifications.fcm.body.leave_cancel_validate', ['manager_firstname' => $this->currentUser->firstname, 'manager_lastname' => $this->currentUser->lastname, 'leave_type' => $leaveTypeName], $userLeave?->language),
                    'url' => "/leave/{$leave->getKey()}",
                    'page' => 'myLeaves',
                ]));
            } catch (Exception $exception) {
                Log::Debug("Notification Push not send " . $exception->getMessage());
            }
        } catch (Exception $exception) {
            $this->responseMessage = __('warnings.ErrorCancelLeave');
            $this->responseDetails = $exception->getMessage();
            $this->responseCode = $exception->getCode();
            return $this->formattedResponse();
        }

        return $this->formattedResponse();
    }

    /**
     * Permet la validation ou le refus d'un/des congé(s)
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function massValidation(Request $request): JsonResponse
    {
        $this->validate($request, $this->configValidation, $this->messages());

        $dataToReturn = [];
        $this->responseMessage = "";

        foreach ($request->all() as $datum) {
            $leave = Leave::query()->firstWhere('id', $datum["leave_id"]);
            $cancelRequest = new Request(['leave_id' => $datum["leave_id"]]);

            if ($leave->status->tag === 'IMPORTED') {
                $dataToReturn[] = [
                    'leave_id' => $datum["leave_id"],
                    'message' => __('warnings.ErrorLeaveStatusDontAllowModification')
                ];
                continue;
            }

            if ($leave->status->tag === 'SUBMITTED_TO_CANCELLATION') {
                switch ($datum["action"]) {
                    case "TRANSMITTED":
                        $this->cancelLeave($cancelRequest);
                        break;
                    case "REFUSED":
                        $this->refuseCancelLeave($request, $leave);
                        break;
                }
            } else {
                switch ($datum["action"]) {
                    case "VALIDATED":
                        $this->validateLeave($datum, $dataToReturn);
                        break;
                    case "REFUSED":
                        $this->refuseLeave($datum, $dataToReturn);
                        break;
                    case "TRANSMITTED":
                        $this->transmittedLeave($datum, $dataToReturn);
                        break;
                }
            }
        }
        $this->responseData = $dataToReturn;

        return $this->formattedResponse();
    }

    public function attachment($id)
    {
        try {
            // Vérification de l'id
            $this->basicIdVerification($id);
            // Récupération du ongés passé en paramètre
            $leave = Leave::find($id);
            if (empty($leave)) {
                throw new Exception(__('warnings.ErrorLeaveDontExist'), 404);
            }

            // Récupération si le user connecter est manager
            $isManager = $this->currentUserIsManager($leave);
            // Vérifie si le type de congés appartient pas à l'utilisateur
            $this->verifyIfLeaveBelongsToUser($leave, $isManager);
            // Vérifie si le type de congés existe
            $this->verifyIfLeaveIfExist($id, $leave);

            // Je récupère le fichier
            $convertedFile = Storage::disk('s3')->get($leave->attachment_path);

            $this->responseMessage = __('messages.SuccessGetLeave');
            $this->responseData = [
                "real_name" => $leave->attachment_name,
                "real_path" => $leave->attachment_path,
                "mime_type" => Storage::disk('s3')->mimeType($leave->attachment_path), //application/pdf
                "file" => base64_encode($convertedFile)
            ];

        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetAttachment');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    public function import(Request &$request)
    {

        if (!$request->file('file')) {
            throw new \Exception('No file uploaded');
        }

        Excel::import($import = new  ImportLeaveAbs(), $request->file('file'));

        return response()->json([
            'success' => (count($import->errors) === 0 ? true : 'PartiallySucces'),
            'message' => (count($import->errors) === 0 ? __('messages.ImportSucces') : __('messages.ImportUserLeavesPartiallySucces', ['successRows' => count($import->rows) - $import->failedRows, 'rows' => count($import->rows)])),
            'errors' => $import->errors
        ]);
    }

    /**
     * Permet l'export des congés
     * @param ExportRequest $request
     * @param \Orion\Http\Requests\Request $orionRequest
     * @return JsonResponse
     * @throws Exception
     */
    public function export(ExportRequest $request, \Orion\Http\Requests\Request $orionRequest)
    {
        set_time_limit(7200);
        ini_set('memory_limit', '-1');

        $filename = $request->validated('filename') ?? Carbon::now()->format('d-m-Y_H-i-s') . '_CEGID';

        // Création du chemin d'enregistrement
        $disk = config('filesystems.default');
        $path = "exports/$this->clientId/";

        // If name already exist
        for ($i = 1; ApiHelper::exportAlreadyExist("$path$filename.csv", $disk); $i++) {
            $filename = "$filename($i)";
        }

        $fileName = "$filename.csv";

        $exportLeave = new LeavesExport(
            $orionRequest,
            $this->clientId,
            ExportLeavesDTO::fromArray($request->validated()),
        );

        $save = Excel::store($exportLeave, "$path$fileName", $disk, \Maatwebsite\Excel\Excel::CSV);

        if (!$save) {
            throw new Exception(__('warnings.CantGenerateFile'), 500);
        }

        if (!$request->validated('file_number')) { // SILAE
            ExportHistory::create(['user_id' => $this->currentUser->getKey(), 'file_name' => $fileName, 'extension' => 'csv', 'type' => 'SILAE']);
        } else { // CEGID
            // Change data in file
            ApiHelper::writeOnFile($exportLeave->getStrFinal(), "$path$fileName", $disk);
            ExportHistory::create(['user_id' => $this->currentUser->getKey(), 'file_name' => $fileName, 'extension' => 'csv', 'type' => 'CEGID']);
        }

        // Delete History and file on minio
        ApiHelper::deleteExportIfTooMuch($this->clientId, $path, $disk);

        // Téléchargement de l'export
        return ApiHelper::downloadExport("$path$fileName", $disk);
    }

    public function exportModel()
    {
        return Excel::download(new LeavesModelImport(), 'LeavesModel_' . now()->isoFormat('YYYY_MM_D_hhmmss') . '.xlsx');
    }

    public function setAttachment(Request $request, Leave $leave)
    {
        $file = $request->file("attachment");

        $uploadedFile = $this->fileUpload($file, "leave_attachment/leaveId-" . (Leave::orderBy("id", "DESC")->count() + 1) . "." . $file->clientExtension());
        $leave->attachment_name = $uploadedFile["name"];
        $leave->attachment_path = $uploadedFile["path"];
        $leave->save();

        $this->responseData = [
            "real_name" => $leave->attachment_name,
            "real_path" => $leave->attachment_path,
            'mime_type' => Storage::disk('s3')->mimeType($leave->attachment_path),
            'file' => base64_encode(Storage::disk('s3')->get($leave->attachment_path))
        ];

        return $this->formattedResponse();
    }

    public function refuseCancelLeave(Request $request, Leave $leave)
    {
        $statutRefused = Status::query()->where("tag", "REFUSED")->first()->getKey();
        $status = Status::find($leave->status_id);
        $profilsAdmin = Profile::whereIn('label', ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->get()->pluck('id')->toArray();
        $isAdmin = in_array($this->currentUser->profile_id, $profilsAdmin);

        if ($isAdmin && $status->tag === "SUBMITTED_TO_CANCELLATION" && $leave->user_id === $this->currentUser->getKey()) {
            $this->getResponse = false;
            $this->responseMessage = __('messages.CantCancelAdminOwnLeave');

            return $this->formattedResponse();
        }

        if ($leave->current_validator_level > $leave->user->validators()->count()) {
            $leave->status_id = Status::query()->where('tag', 'VALIDATED')->first()?->getKey();
            $leave->save();
            $this->responseData = [
                'data' => $leave
            ];
            if ($leave->user->site->client->validation_scheme === 'VERTICAL') {
                $lastManagerValidate = $leave->user->validators()->orderByDesc('managers.level')->first();
            } else {
                $lastManagerValidate = $leave->histories()->where('status_id', $leave->status_id)->first()->user;
            }
            $this->sendToHistory($leave->getKey(), $this->currentUser->getKey(), $statutRefused);
            $this->sendToHistory($leave->getKey(), $lastManagerValidate->getKey(), $leave->status_id);
            $this->responseMessage = __('messages.SuccessValidateLeaveSubToCancelValidated');

        } else {
            $leave->status_id = Status::query()->where('tag', 'SUBMITTED')->first()?->getKey();
            $leave->save();
            $this->responseData = [
                'data' => $leave
            ];
            $this->responseMessage = __('messages.SuccessValidateLeaveSubToCancelSubmitted');
            $this->sendToHistory($leave->getKey(), $this->currentUser->getKey(), $statutRefused);
        }

        $userLeave = User::withTrashed()->find($leave->user_id);
        $leaveDate = ApiHelper::convertStartFullDateAndEndFullDate($userLeave, $leave->start_date, $leave->end_date);
        $leaveTypeName = ApiHelper::LeaveTypeName($leave);

        $title = "CONGÉS : Demande d'annulation de " . $leaveTypeName . " refusé";
        $body = "Votre demande d'annulation de " . $leaveTypeName . " pour la période du " . $leaveDate['start_date'] . " au " . $leaveDate['end_date'] . " a été refusée par " . $this->currentUser->firstname . " " . strtoupper($this->currentUser->lastname) . ".";

        // Envoi une alerte
        $this->sendAlert($userLeave, $leave, [$this->currentUser->getKey()], $title, $body, $leave->status_id);

        $userLeave->notify(new LeaveInformation([
            'leave' => $leave,
            'title' => __('notifications.title_leave_cancel_refused', [], $userLeave->language),
            'greeting' => __('notifications.greeting', ['user_firstname' => $userLeave->firstname], $userLeave->language),
            'keyword' => [
                'notifications.leave_cancelled_refused_by_another_manager' => ['leave_type' => $leaveTypeName, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date'], 'user_firstname' => $this->currentUser->firstname, 'user_lastname' => strtoupper($this->currentUser->lastname)]
            ],
        ], false));

        try {
            $userLeave?->notify(new UserReceiveNotificationPush([
                'leave' => $leave,
                'title' => __('notifications.fcm.title.title_leave_cancel_refused', [], $userLeave?->language),
                'message' => __('notifications.fcm.body.leave_cancelled_refused_by_another_manager', ['leave_type' => $leaveTypeName, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date'], 'user_firstname' => $this->currentUser->firstname, 'user_lastname' => strtoupper($this->currentUser->lastname)], $userLeave?->language),
                'url' => "/leave/{$leave->getKey()}",
                'page' => 'myLeaves',
            ]));
        } catch (Exception $exception) {
            Log::Debug("Notification Push not send " . $exception->getMessage());
        }

        return $this->formattedResponse();
    }

    private function recalcLeavesDistribution($userId, $leaveTypeId)
    {
        $leaves = Leave::query()
            ->select('leaves.*')
            ->join('statuses', 'statuses.id', 'leaves.status_id')
            ->whereIn('statuses.tag', ['SUBMITTED', 'VALIDATED'])
            ->where('user_id', $userId)
            ->where('leave_type_id', $leaveTypeId)
            ->get();

        $nCount = UserLeaveCount::query()
            ->where('user_id', $userId)
            ->where('leave_type_id', $leaveTypeId)
            ->where('is_last_year', false)
            ->first();

        $n1Count = UserLeaveCount::query()
            ->where('user_id', $userId)
            ->where('leave_type_id', $leaveTypeId)
            ->where('is_last_year', true)
            ->first();

        foreach ($leaves as $leave) {
            $futureNLeaves = $leaves->sum('n') - $leave->n;
            $futureN1Leaves = $leaves->sum('n1') - $leave->n1;

            if (!$n1Count) {
                $leave->n1 = 0;
                $leave->n = $leave->duration;
                $leave->out_day = ($nCount->balance - $futureNLeaves < 0) ? abs($nCount->balance - $futureNLeaves) : 0;
            } else {
                $june = Carbon::now()->month(6)->startOfMonth();
                if (Carbon::now() >= $june) {
                    $june->addYear();
                }
                if (Carbon::parse($leave->startDate) > $june) {
                    $leave->n1 = 0;
                    $leave->n = $leave->duration;
                    $leave->out_day = ($nCount->balance - $futureNLeaves < 0) ? abs($nCount->balance - $futureNLeaves) : 0;
                } else {
                    if ($n1Count->balance < ($leave->duration + $futureN1Leaves)) {
                        $leave->n1 = $n1Count->balance - $futureN1Leaves;
                        $leave->n = $leave->duration - $leave->n1;
                        $leave->out_day = ($nCount->balance - $leave->n < 0) ? abs($nCount->balance - $leave->n) : 0;
                    } elseif (Carbon::parse($leave->start_date) < $june && Carbon::parse($leave->end_date) > $june) {
                        $leave->n = collect($leave->leave_days_distribution[$june->year])->filter(fn($value) => $value['month'] >= $june->month)->count();
                        $leave->n1 = collect($leave->leave_days_distribution[$june->year])->filter(fn($value) => $value['month'] < $june->month)->count();
                    } else{
                        $leave->n = 0;
                        $leave->n1 = $leave->duration;
                    }
                }
            }
            $leave->save();
        }
    }

    public function callbackManagerLeave(Leave $leave)
    {
        try {
            $leave->managerToValidate->first()?->notify(new UserReceiveNotificationPush([
                'leave' => $leave,
                'title' => __('notifications.fcm.title.title_leave_asking_validating', [], $leave->managerToValidate->first()?->language),
                'message' => __('notifications.fcm.body.leave_waiting_validation', [], $leave->managerToValidate->first()?->language),
                'url' => "/leave/{$leave->getKey()}",
                'page' => 'toValidate',
            ]));
        } catch (Exception $exception) {
            Log::Debug("Notification Push not send " . $exception->getMessage());
        }

        $this->responseMessage = __('messages.SuccessCallback');

        return $this->formattedResponse();
    }
}

