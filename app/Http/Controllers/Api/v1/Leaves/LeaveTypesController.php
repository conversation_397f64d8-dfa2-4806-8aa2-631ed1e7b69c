<?php

namespace App\Http\Controllers\Api\v1\Leaves;

use App\Http\Controllers\Api\v1\Controller;
use App\Interfaces\Controllers\Api\BaseApiControllerInterface;
use App\Lib\ApiHelper;
use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\LeaveTypeSubFamily;
use App\Models\Api\Profile;
use App\Models\Api\Status;
use App\Models\Api\User;
use App\Models\Api\UserLeaveCount;
use Illuminate\Support\Facades\DB;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class LeaveTypesController extends Controller implements BaseApiControllerInterface
{
    private array $config = [
        "name" => "required|string",
        "default_leave_value" => "nullable|integer|gte:0",
        "is_active" => "boolean",
        "is_monthly" => "required|boolean",
        "is_half_day" => "required|boolean",
        "is_attachment_required" => "required|boolean",
        "is_ignore_by_export" => "required|boolean",
        "can_exceed" => "required|boolean",
        "leave_code" => "required|string",
        "start_date" => "nullable|date",
        "end_date" => "nullable|date",
        "color" => "required|string",
        "leave_type_sub_families" => "nullable|array",
        "leave_type_sub_families.*.leave_type_id" => "nullable|integer|gt:0",
        "leave_type_sub_families.*.name" => "required|string",
        "leave_type_sub_families.*.value" => "required|numeric|gte:0",
        "absence_reason" => "nullable|string",
        "order_appearance" => "required|integer|gte:0",
        "can_justify_later" => "boolean",
        "leave_type_category_id" => "exists:leave_type_categories,id",
        "increment_days_number" => "nullable|numeric",
        "is_auto_increment_active" => "boolean",
        "site_id" => "nullable|integer",
        "is_take_leave" => "required|boolean",
    ];

    #region Private Functions

    /**
     * Vérifie si le type de congés appartient pas à l'utilisateur
     *
     * @param LeaveType $leaveType
     *
     * @throws Exception
     */
    private function checkLeaveTypeBelongsToUser(LeaveType $leaveType = null): void
    {
        if ($leaveType && $leaveType->client_id !== $this->clientId) {
            $this->responseMessage = __('warnings.ErrorDontOwnLeaveType');
            throw new Exception(__('warnings.ErrorDontOwnLeaveType'));
        }
    }

    /**
     * Vérifie si le type de congés existe
     *
     * @param int $id
     * @param LeaveType $leaveType
     *
     * @throws Exception
     */
    private function checkLeaveTypeIfExist(int $id, LeaveType $leaveType = null): void
    {
        if (is_null($leaveType)) {
            throw new Exception(__('warnings.ErrorLeaveTypeNotExist'));
        }
    }

    /**
     * Vérifie si le type de congés existe pour un client
     *
     * @param LeaveType $leaveType
     *
     * @throws Exception
     */
    private function checkLeaveTypeIfExistForClient(LeaveType $leaveType = null): void
    {
        if (!is_null($leaveType)) {
            $this->responseMessage = __('warnings.LeaveTypeAlreadyExist');
            throw new Exception(__('warnings.LeaveTypeAlreadyExist'), 400);
        }
    }

    /**
     * Vérifie si le type de congés peut être supprimé
     *
     * @param LeaveType $leaveType
     *
     * @throws Exception
     */
    private function checkLeaveTypeIfCanBeDeleted(LeaveType $leaveType = null): void
    {
        if (!$leaveType->is_deletable) {
            $this->responseMessage = __('warnings.ErrorLeaveTypeCantBeDeleted');
            throw new Exception(__('warnings.ErrorLeaveTypeCantBeDeleted', ['leave_type_id' => $leaveType->id]));
        }
    }

    /**
     * Check if LeaveTypeSub belongs to client
     * @param $id
     * @throws Exception
     */
    private function checkLeaveTypeSubIfExistForClient($id)
    {
        if (!LeaveTypeSubFamily::find($id)) {
            throw new Exception(__('warnings.ErrorLeaveTypeSubFamilyDontExist'), 400);
        }
        if (LeaveType::withTrashed()->find(LeaveTypeSubFamily::find($id)->leave_type_id)->client_id != $this->clientId) {
            throw new Exception(__('warnings.LeaveSubTypeDontBelongToCustomer'), 400);
        }
    }

    /**
     * Check if only not sensible info are modified
     * @param $id
     * @param $data
     * @return bool
     */
    private function checkSmoothUpdate(LeaveType $leaveType, $data, $request): bool
    {
        // If no change for subfamilies or subfamilies are different
        if ($data["leave_type_sub_families"] != [] || $request->get("leave_type_sub_families") != $leaveType->leave_type_sub_families()->get()->toArray()) {
            return false;
        }
        // Get others data
        $notUpdateArray = $this->config;
        unset($notUpdateArray["leave_type_sub_families"]);
        unset($notUpdateArray["can_exceed"]);
        unset($notUpdateArray["is_half_day"]);
        unset($notUpdateArray["start_date"]);
        unset($notUpdateArray["end_date"]);
        unset($notUpdateArray["is_attachment_required"]);
        unset($notUpdateArray["is_ignore_by_export"]);
        unset($notUpdateArray["color"]);
        unset($notUpdateArray["leave_code"]);
        unset($notUpdateArray["default_leave_value"]);
        unset($notUpdateArray["order_appearance"]);
        $notUpdateArray = array_keys($notUpdateArray);

        // Verify data is not different from actual
        foreach ($notUpdateArray as $notUpdateName) {
            if ($leaveType->$notUpdateName != ($data[$notUpdateName] ?? null)) {
                return false;
            }
        }
        return true;
    }

    private function smoothUpdate($id, $data)
    {
        // Unset data that is not wanted
        $dataToUpdate = ["can_exceed", "is_half_day", "start_date", "end_date", "is_attachment_required", "is_ignore_by_export", "color", "leave_code", "default_leave_value", "order_appearance"];
        $data = collect($data)->filter(function ($value, $key) use ($dataToUpdate){
            return in_array($key, $dataToUpdate);
        })->toArray();

        $data["last_update_id"] = $this->currentUser->id;
        // Update Leavetype with data selected
        $leaveType = LeaveType::find($id);

        if ($leaveType->order_appearance !== $data['order_appearance']) {
            $leaveType->reorderModel(
                updatedModel: $leaveType,
                parentModel: $leaveType->site ?? $leaveType->client,
                relation: 'leave_types',
                oldOrder: $leaveType->order_appearance,
                newOrder: $data['order_appearance']
            );
        }
        $leaveType->update($data);
        if (!$leaveType->save()) {
            throw new Exception(__('warnings.ErrorUpdateLeaveType'));
        }

        DB::commit();
        // Return response
        $this->responseMessage = __('warnings.SuccessUpdateLeaveType');
        $this->responseData = $leaveType->toArray();
        return $this->formattedResponse();
    }

    /**
     * Créer 2 compteurs pour tous les users du client
     * @param $leaveTypeId
     */
    private function createCounters($leaveTypeId)
    {
        $leaveType = LeaveType::find($leaveTypeId);
        $clientId = $this->clientId;
        // Récupération des users
        $users = User::whereHas('site', function ($query) use ($leaveType, $clientId) {
            $query->where('client_id', $clientId);
        })->when($leaveType->site_id !== null, function ($query) use ($leaveType) {
            $query->where('users.site_id', $leaveType->site_id);
        })->get();
        // Création des compteurs
        if ($leaveType->is_pay) {
            foreach ($users as $user) {
                UserLeaveCount::create([
                    "user_id" => $user->id,
                    "leave_type_id" => $leaveTypeId,
                    "acquired" => 0,
                    "taken" => 0,
                    "balance" => 0,
                    "is_last_year" => 0,
                ]);
                UserLeaveCount::create([
                    "user_id" => $user->id,
                    "leave_type_id" => $leaveTypeId,
                    "acquired" => 0,
                    "taken" => 0,
                    "balance" => 0,
                    "is_last_year" => 1,
                ]);
            }
        } else {
            foreach ($users as $user) {
                UserLeaveCount::create([
                    "user_id" => $user->id,
                    "leave_type_id" => $leaveTypeId,
                    "acquired" => 0,
                    "taken" => 0,
                    "balance" => 0,
                    "is_last_year" => 0,
                ]);
            }
        }
    }
    #endregion

    #region Public Functions
    /**
     * Récupération des types de congés liés au Client
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $data = $this->validate($request, array_merge([
                'withLeaveTypeSubFamilies' => 'boolean',
                'is_active' => 'boolean',
                'needs_count' => 'boolean',
                'ordered' => 'boolean',
            ], $this->defaultIndexFilters), $this->messages());

            $leaveTypeModel = LeaveType::query()->where("client_id", "=", $this->clientId);
            // Si on veut les familles en plus
            if (!empty($data["withLeaveTypeSubFamilies"])) {
                $leaveTypeModel->with("leave_type_sub_families");
            }

            // Si on veut les needs count
            if (!empty($data["needs_count"])) {
                $leaveTypeModel->where('needs_count', '=', $data["needs_count"]);
            }

            // Si on veut les needs count
            if (!empty($data["ordered"])) {
                $leaveTypeModel->where('order_appearance', '!=', 0);
            }

            // On affiche pas ceux ayant is_active = 0
            if (isset($data["is_active"])) {
                $leaveTypeModel->where('is_active', '=', $data["is_active"]);
                if ($data["is_active"] == false) { // Si false check s'il est admin
                    $adminProfiles = Profile::whereIn('label', ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->get()->pluck('id')->toArray();
                    // Seulement les admins peuvent les voir
                    if (!in_array($this->currentUser->profile_id, $adminProfiles)) {
                        throw new Exception(__('warnings.NotRightForGetLeaveType'), 403);
                    }
                }
            }

            $lastLeaveType = LeaveType::where("client_id", "=", $this->clientId)
                ->where("last_update_id", "!=", null)->orderBy("updated_at", "desc")->first();

            // Pagination des données
            $leaveTypes = ApiHelper::defaultModelBuilder($leaveTypeModel, $data["orders"] ?? "name,asc",
                $data["limit"] ?? null, $data["paginate"] ?? 0);

            $leaveTypes["last_user_update_name"] = $lastLeaveType ? $lastLeaveType->user->firstname . " " . $lastLeaveType->user->lastname : null;
            $leaveTypes["last_user_update_time"] = $lastLeaveType ? $lastLeaveType->updated_at->format("d-m-Y") : null;

            $this->responseData = $leaveTypes;
            $this->getResponse = true;
            $this->responseMessage = __('warnings.SuccessGetLeaveTypes');
        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetLeaveTypes');
            $this->responseCode = $exception->status;
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetLeaveTypes');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    /**
     * Récupération d'un type de congés
     *
     * @param Request $request
     * @param int $id
     *
     * @return JsonResponse
     */
    public function show(Request $request, int $id): JsonResponse
    {
        try {
            $this->validate($request, ["withLeaveTypeSubFamilies" => "boolean"], $this->messages());
            // Vérification de l'id
            $this->basicIdVerification($id);
            // Init model
            $leaveTypeModel = LeaveType::query();
            // Si on veux les familles en plus
            if ($request->query("withLeaveTypeSubFamilies")) {
                $leaveTypeModel->with("leave_type_sub_families");
            }
            // Récupération du type de congés passé en paramètre
            $leaveType = $leaveTypeModel->find($id);
            // Vérifie si le type de congés existe
            $this->checkLeaveTypeIfExist($id, $leaveType);
            // Vérifie si le type de congés appartient pas à l'utilisateur
            $this->checkLeaveTypeBelongsToUser($leaveType);
            // Vérifie si le congé est actif et s'il l'est que le suser est admin
            $adminProfiles = Profile::whereIn('label', ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER'])->get()->pluck('id')->toArray();
            if ($leaveType->is_active == false && !in_array($this->currentUser->profile_id, $adminProfiles)) {
                throw new Exception(__('warnings.NotRightForGetLeaveType'), 403);
            }

            $this->responseMessage = __('warnings.SuccessGetLeaveType');
            $this->responseData = $leaveType->toArray();
        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetLeaveTypes');
            $this->responseCode = $exception->status;
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetLeaveTypes');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    /**
     * Création d'un type de congés
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            // Validation des données reçu en POST
            $validator = $this->validate($request, $this->config, $this->messages());
            // Récupération du type de congés liés au client
            $leaveTypeExist = LeaveType::firstWhere([["client_id", $this->clientId], ["name", $validator["name"]]]);
            // Vérifie si un leave type existe déjà pour ce client et ce nom
            $this->checkLeaveTypeIfExistForClient($leaveTypeExist);
            // Données POST
            $leaveTypeData = $validator;
            // J'enleve le tableau site_database
            unset($leaveTypeData["leave_type_sub_families"]);
            unset($leaveTypeData["leave_type_bonus_seniorities"]);
            $leaveTypeData["last_update_id"] = $this->currentUser->id;
            $leaveTypeData["client_id"] = $this->clientId;
            $leaveTypeData["is_deletable"] = true;
            $leaveTypeData["needs_count"] = $leaveTypeData["is_monthly"];
            $leaveType = LeaveType::create($leaveTypeData);

            $leaveType->reorderModel(
                updatedModel: $leaveType,
                parentModel: ($leaveType->site_id !== null ? $leaveType->site : $leaveType->client),
                relation: 'leave_types',
                oldOrder: LeaveType::where([['site_id', $leaveType->site_id], ['client_id', $leaveType->client_id]])->max('order_appearance') + 1,
                newOrder: $leaveType->order_appearance
            );

            if ($request->get("leave_type_sub_families")) {
                $leaveTypeSubFamilies = [];
                foreach ($request->get("leave_type_sub_families") as $item) {
                    $leaveTypeSubFamilies[] = new LeaveTypeSubFamily($item);
                }
                // J'enregistre les familles
                $leaveTypeSubFamilies = $leaveType->leave_type_sub_families()->saveMany($leaveTypeSubFamilies);
                $leaveType->leave_type_sub_families = $leaveTypeSubFamilies;
                $leaveType->needs_count = 0;
            }

            // Créer 1 compteur N pour tous les users du client
            if ($leaveType->needs_count == 1) {
                $this->createCounters($leaveType->id);
            }

            $this->responseMessage = __('warnings.SuccessStoreLeaveType');
            $this->responseData = $leaveType->toArray();

        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorStoreLeaveType');
            $this->responseCode = $exception->status;
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorStoreLeaveType');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    /**
     * Modification du type de congés passé en paramètre
     *
     * @param Request $request
     * @param int $id
     *
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            // Validation des données reçu en POST
            $leaveTypeData = $this->validate($request, $this->config, $this->messages());
            // Récupération du type de congés liés au client par id
            $leaveType = LeaveType::find($id);
            // Vérifie si un leave type existe déjà pour cet id
            $this->checkLeaveTypeIfExist($id, $leaveType);
            // Vérifie s'il exist des congés pour ce type
            $leaveExist = Leave::firstWhere("leave_type_id", $id);
            // Vérifie si le type de congés appartient pas à l'utilisateur
            $this->checkLeaveTypeBelongsToUser($leaveType);
            // Récupération du type de congés liés au client et au site par name
            $leaveTypeExist = LeaveType::firstWhere([["client_id", $this->clientId],['site_id', $leaveType->site_id],["name", $request->get("name")]]);
            // On vérifié la condition sur le nom que si on edite un type différent
            if ($leaveTypeExist && $leaveTypeExist->id !== $id) {
                // Vérifie si un leave type existe déjà pour ce client ce site et ce nom
                $this->checkLeaveTypeIfExistForClient($leaveTypeExist);
            }
            //récupération des leaves soumises ou validées
            $leavesOnGoing = Leave::where("leave_type_id", $id)->get();
            //If leave is pay keep it the same way
            if ($leaveType->is_pay) {
                $leaveTypeData["is_pay"] = $leaveType->is_pay;
            }

            $lastValueOrderAppearance = $leaveType ? $leaveType->order_appearance : null;

            DB::beginTransaction();

            if (!$leaveType->is_deletable && $this->checkSmoothUpdate($leaveType, $leaveTypeData, $request)) {
                return $this->smoothUpdate($id, $leaveTypeData);
            }

            if ($leaveExist) {
                // J'enleve le tableau site_database
                unset($leaveTypeData["leave_type_sub_families"]);
                $leaveTypeData["last_update_id"] = $this->currentUser->id;

                // Set client_id
                $leaveTypeData["client_id"] = $this->clientId;
                $leaveTypeData["needs_count"] = $leaveTypeData["is_monthly"];
                $leaveTypeData["is_deletable"] = $leaveType->is_deletable;
                $leaveTypeData['site_id'] = $leaveType->site_id;

                $leaveType = LeaveType::create($leaveTypeData);

                //Set new_leave_type_id in old leaveType
                $oldLeaveType = LeaveType::find($id);
                $oldLeaveType["new_leave_type_id"] = $leaveType->id;
                $oldLeaveType->save();

                // Archivage de l'ancien type de congés
                $oldLeaveType->delete();

                if (LeaveType::find($leaveType->id)->needs_count) {
                    $userLeaveCounts = UserLeaveCount::where('leave_type_id', $id)->get();
                    foreach ($userLeaveCounts as $userLeaveCount) {
                        $userLeaveCount["leave_type_id"] = $leaveType->id;
                        $userLeaveCount->save();
                    }
                }

                if (!empty($leavesOnGoing)) {
                    foreach ($leavesOnGoing as $leave) {
                        $leave->leave_type_id = $leaveType->id;
                        $leave->save();
                    }
                }

                if ($request->get("leave_type_sub_families")) {
                    foreach ($request->get("leave_type_sub_families") as $item) {
                        // Check if LeaveTypeSub belongs to client
                        if (!empty($item["id"])) {
                            $this->checkLeaveTypeSubIfExistForClient($item["id"]);
                            LeaveTypeSubFamily::destroy($item["id"]);
                        }
                    }
                    $leaveTypeSubFamilies = [];
                    foreach ($request->get("leave_type_sub_families") as $item) {
                        unset($item["id"]);
                        $leaveTypeSubFamilies[] = new LeaveTypeSubFamily($item);
                    }
                    // J'enregistre les familles
                    $leaveTypeSubFamilies = $leaveType->leave_type_sub_families()->saveMany($leaveTypeSubFamilies);
                    $leaveType->leave_type_sub_families = $leaveTypeSubFamilies;
                }
            } else {
                $leaveTypeSubFamilies = [];
                if ($request->get("leave_type_sub_families")) {
                    // On modifie les sous familles
                    foreach ($request->get("leave_type_sub_families") as $item) {
                        // Check if LeaveTypeSub belongs to client
                        if (!empty($item["id"])) {
                            $this->checkLeaveTypeSubIfExistForClient($item["id"]);
                            $leaveTypeSubFamily = LeaveTypeSubFamily::find($item["id"]);
                            $leaveTypeSubFamily->name = $item["name"];
                            $leaveTypeSubFamily->value = $item["value"];
                            $leaveTypeSubFamily->absence_reason = $item["absence_reason"];
                            $leaveTypeSubFamilies[] = $leaveTypeSubFamily;
                        } else {
                            $leaveTypeSubFamilies[] = LeaveTypeSubFamily::create([
                                "value" => $item["value"],
                                "name" => $item["name"],
                                "absence_reason" => $item["absence_reason"],
                                "leave_type_id" => $id
                            ]);
                        }
                    }
                    LeaveTypeSubFamily::where("leave_type_id", $leaveType->id)->whereNotIn("id", array_column($leaveTypeSubFamilies, "id"))->delete();
                    $leaveType->needs_count = 0;
                    UserLeaveCount::where('leave_type_id', $leaveType->id)->delete();
                } else {
                    LeaveTypeSubFamily::whereIn('id', $leaveType->leave_type_sub_families->pluck('id'))->delete();
                    $leaveType->needs_count = 1;
                    $userLeaveCount = UserLeaveCount::where('leave_type_id', $leaveType->id)->get()->toArray();
                    if (empty($userLeaveCount)) {
                        $this->createCounters($leaveType->id);
                    }
                }

                // Set client_id
                $leaveType->client_id = $this->clientId;

                unset($leaveTypeData["leave_type_sub_families"]);
                $leaveTypeData["needs_count"] = $leaveTypeData["is_monthly"];
                $leaveType->update($leaveTypeData);

                // Si erreur dans la modification du type de congés
                $leaveType->last_update_id = $this->currentUser->id;
                if (!$leaveType->save()) {
                    throw new Exception(__('warnings.ErrorUpdateLeaveType'));
                }
                $leaveType->leave_type_sub_families = $leaveType->leave_type_sub_families()->saveMany($leaveTypeSubFamilies);
            }

            if ($leaveTypeData["order_appearance"] != $lastValueOrderAppearance) {
                $leaveType->reorderModel(
                    updatedModel: $leaveType,
                    parentModel: $leaveType->site ?? $leaveType->client,
                    relation: 'leave_types',
                    oldOrder: $lastValueOrderAppearance,
                    newOrder: $leaveTypeData['order_appearance']
                );
            }

            if (!$leaveType->is_monthly) {
                $leaveType->user_leave_counts->each(function ($compteur) {
                    $compteur->delete();
                });
            } else {
                if (isset($leaveType->user_leave_counts)) {
                    $leaveType->user_leave_counts->each(function ($compteur) {
                        $compteur->restore();
                    });
                } else {
                    $this->createCounters($leaveType->getKey());
                }
            }

            DB::commit();
            $this->responseMessage = __('warnings.SuccessUpdateLeaveType');
            $this->responseData = $leaveType->toArray();
        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateLeaveType');
            $this->responseCode = $exception->status;
            DB::rollBack();
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateLeaveType');
            $this->responseCode = $exception->getCode();
            DB::rollBack();
        }

        return $this->formattedResponse();
    }

    /**
     * Suppression du type de congés passé en paramètre
     *
     * @param int $id
     *
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            // Vérification de l'id
            $this->basicIdVerification($id);
            // Récupération du type de congés passé en paramètre
            $leaveType = LeaveType::find($id);
            // Vérifie si il n'y a pas de type de congés
            $this->checkLeaveTypeIfExist($id, $leaveType);
            // Vérifie si le type de congés appartient pas à l'utilisateur
            $this->checkLeaveTypeBelongsToUser($leaveType);
            // Vérifie si le type de congés peut être supprimé
            $this->checkLeaveTypeIfCanBeDeleted($leaveType);

            $leaveType = LeaveType::find($id);
            $leaveType->reorderModel(
                updatedModel: $leaveType,
                parentModel: $leaveType->site ?? $leaveType->client,
                relation: 'leave_types',
                oldOrder: $leaveType->order_appearance,
                newOrder: LeaveType::where([['site_id', $leaveType->site_id], ['client_id', $leaveType->client_id]])->max('order_appearance') + 1
            );

            // Suppression du type de congés
            $leaveType->delete();

            $userLeaveCounts = UserLeaveCount::where("leave_type_id", $id)->get();
            foreach ($userLeaveCounts as $userLeaveCount) {
                UserLeaveCount::destroy($userLeaveCount["id"]);
            }
            $this->responseMessage = __('warnings.SuccessDeleteLeaveType');
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorDeleteLeaveType');
        }


        return $this->formattedResponse();
    }
    #endregion
}
