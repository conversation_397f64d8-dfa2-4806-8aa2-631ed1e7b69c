<?php


namespace App\Http\Controllers\Api\v1\Teams;


use App\Http\Controllers\Api\v1\Controller;
use App\Interfaces\Controllers\Api\BaseApiControllerInterface;
use App\Lib\ApiHelper;
use App\Models\Api\Leave;
use App\Models\Api\Manager;
use App\Models\Api\Status;
use App\Models\Api\UserLeaveCount;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class TeamsController extends Controller implements BaseApiControllerInterface
{

    /**
     * Récupération des utilisateurs de l'équipe du manager
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $data = $this->validate($request, [
                "withSites" => "boolean",
                "withTags" => "boolean"
            ], $this->messages());


            // Model manager
            $managerUsers = Manager::query()
                ->join("users", "users.id", "=", "managers.managed_id")->where([["managers.manager_id", "=", $this->currentUser->id], ["users.deleted_at", "=", null]]);
            // Associe user
            $managerUsers->with("user");
            //Association avec des sites
            if (!empty($data['withSites']) && $data['withSites']) {
                $managerUsers->with("user.site");
            }
            //Association avec des tags
            if (!empty($data['withTags']) && $data['withTags']) {
                $managerUsers->with(array("user.tags" => function ($query) {
                    $query->where("tags.user_id", $this->currentUser->id);
                }));
            }
            // Associe compteur des users de l'équipe
            $this->responseData = ApiHelper::defaultModelBuilder($managerUsers->with("user_leave_counts.leave_type"), $request->get("order", "id,asc"),
                $request->get("limit", null), $request->get("paginate", 0));
            $this->getResponse = true;
            $this->responseMessage = __('warnings.SuccessGetUser');

            $bodyResponse = $this->apiHelper
                ->responseFormatter(
                    $this->checkHttpStatus($this->responseCode),
                    $this->responseData,
                    $this->responseMessage,
                    $this->responseDetails,
                    $this->getResponse
                );
            $dataArray = $bodyResponse["data"];
            foreach ($dataArray as $key => $user) {
                // Get user open days
                $userArray = $user->toArray();
                $userArray["futureLeaves"] = $this->getFutureLeaves($user->id);
                $dataArray[$key] = $userArray;
            }
            // Set data from response
            $bodyResponse["data"] = $dataArray;
            return response()->json($bodyResponse);

        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetTeam');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    public function show(Request $request, int $id): JsonResponse
    {
        try {
            throw new Exception(__('warnings.ErrorGetTeamFromApp'));
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetTeam');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    public function store(Request $request): JsonResponse
    {
        try {
            throw new Exception(__('warnings.ErrorStoreTeamFromApp'));
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorStoreTeam');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    public function update(Request $request, int $id): JsonResponse
    {
        try {
            throw new Exception(__('warnings.ErrorUpdateTeamFromApp'));
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateTeam');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    public function destroy(int $id): JsonResponse
    {
        try {
            throw new Exception(__('warnings.ErrorDeleteTeamFromApp'));
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorDeleteTeam');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    private function getFutureLeaves($usersID)
    {
        $leaves = Leave::whereIn("status_id", Status::whereIn("tag", ["VALIDATED", "SUBMITTED"])->get("id")->toArray())
            ->where("user_id", $usersID)->get();
        $userLeaveCounts = UserLeaveCount::query()
            ->join("leave_types", 'leave_types.id', '=', 'user_leave_counts.leave_type_id')
            ->where("user_id", $usersID)
            ->orderBy("leave_type_id")
            ->orderByDesc("is_last_year")
            ->orderBy("order_appearance")->get();
        $rest = 0;
        $temp = true;
        foreach ($userLeaveCounts as $userLeaveCount) {
            foreach ($leaves as $leave) {
                if ($leave->leave_type_id == $userLeaveCount->leave_type_id && $temp == true) {
                    $rest += $leave->duration;
                }
            }

            if ($userLeaveCount->is_last_year == 1) {
                if ($userLeaveCount->balance <= $rest) {
                    $userLeaveCount->futureLeave = $userLeaveCount->balance;
                    $rest -= $userLeaveCount->balance;
                    $temp = false;
                } else {
                    $userLeaveCount->futureLeave = $rest;
                    $rest -= $rest;
                    $temp = false;
                }
            } else {
                $userLeaveCount->futureLeave = $rest;
                $rest -= $rest;
                $temp = true;
            }
        }
        return $userLeaveCounts;
    }
}
