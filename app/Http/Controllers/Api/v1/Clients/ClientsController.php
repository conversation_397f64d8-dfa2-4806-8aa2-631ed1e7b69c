<?php

namespace App\Http\Controllers\Api\v1\Clients;

use App\Http\Controllers\Api\v1\Controller;
use App\Interfaces\Controllers\Api\BaseApiControllerInterface;
use App\Lib\Tools;
use App\Models\Api\Client;
use App\Models\Api\Day;
use App\Models\Api\LeaveType;
use App\Models\Api\LeaveTypeSubFamily;
use Database\Seeders\DatabaseSeeder;
use DateTime;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class ClientsController extends Controller
{
    private array $config = [
        "validation_scheme" => "in:HORIZONTAL,VERTICAL",
        "count_public_holidays" => "boolean",
        "is_pentecost" => "boolean",
        "is_allowed_to_modify_open_days" => "boolean",
    ];

    private array $storeRule = [
        "name" => "string|required|max:255",
        "uuid" => "string|required|max:36"
    ];

    private array $updateRule = [
        "name" => "string|required|max:255"
    ];

    private function setClientDefaultValues($client)
    {
        $defaultLeaveTypes = [
            [
                "client_id" => $client->id,
                "name" => "Congés payés",
                "is_active" => 1,
                "is_monthly" => 1,
                "is_deletable" => 0,
                "is_half_day" => 1,
                "is_attachment_required" => 0,
                "is_ignore_by_export" => 0,
                "color" => "#99cc33",
                "leave_code" => "CPN",
                "can_exceed" => 0,
                "order_appearance" => 1
            ],
            [
                "client_id" => $client->id,
                "name" => "RTT",
                "is_active" => 1,
                "is_monthly" => 1,
                "is_deletable" => 0,
                "is_half_day" => 0,
                "is_attachment_required" => 0,
                "is_ignore_by_export" => 0,
                "color" => "#33cccc",
                "leave_code" => "RTT",
                "can_exceed" => 0,
                "order_appearance" => 2
            ],
            [
                "client_id" => $client->id,
                "name" => "Récupération",
                "is_active" => 1,
                "is_monthly" => 0,
                "is_deletable" => 0,
                "is_half_day" => 0,
                "is_attachment_required" => 0,
                "is_ignore_by_export" => 0,
                "color" => "#009999",
                "leave_code" => "REC",
                "can_exceed" => 0,
                "order_appearance" => 3
            ],
            [
                "client_id" => $client->id,
                "name" => "Absence événement familial",
                "is_active" => 1,
                "is_monthly" => 0,
                "is_deletable" => 0,
                "is_half_day" => 0,
                "is_attachment_required" => 1,
                "is_ignore_by_export" => 0,
                "color" => "#ffcc66",
                "leave_code" => "AEF",
                "can_exceed" => 0,
                "order_appearance" => 0,
                "leave_type_sub_families" => [
                    [
                        "name" => "Enfant malade",
                        "value" => 4
                    ],
                    [
                        "name" => "Mariage du salarié",
                        "value" => 4
                    ],
                    [
                        "name" => "Mariage d'un enfant",
                        "value" => 1
                    ],
                    [
                        "name" => "Naissance ou adoption",
                        "value" => 3
                    ],
                    [
                        "name" => "Décès d'un enfant",
                        "value" => 5
                    ],
                    [
                        "name" => "Décès de son partenaire",
                        "value" => 3
                    ],
                    [
                        "name" => "Décès membre de la famille proche",
                        "value" => 3
                    ],
                ]
            ],
            [
                "client_id" => $client->id,
                "name" => "Congés paternité",
                "default_leave_value" => 11,
                "is_active" => 1,
                "is_monthly" => 0,
                "is_deletable" => 0,
                "is_half_day" => 0,
                "is_attachment_required" => 1,
                "is_ignore_by_export" => 0,
                "color" => "#ffccff",
                "leave_code" => "CPATER",
                "can_exceed" => 0,
                "order_appearance" => 0
            ],
            [
                "client_id" => $client->id,
                "name" => "Congés sans soldes",
                "is_active" => 1,
                "is_monthly" => 0,
                "is_deletable" => 0,
                "is_half_day" => 0,
                "is_attachment_required" => 0,
                "is_ignore_by_export" => 0,
                "color" => "#ffffcc",
                "leave_code" => "CSS",
                "can_exceed" => 0,
                "order_appearance" => 0
            ],
        ];
        foreach ($defaultLeaveTypes as $defaultLeaveType) {
            // Get sub families data
            $subFamilies = null;
            if (!empty($defaultLeaveType['leave_type_sub_families'])) {
                $subFamilies = $defaultLeaveType['leave_type_sub_families'];
                unset($defaultLeaveType['leave_type_sub_families']);
            }
            // Create LeaveType
            $leaveType = LeaveType::create($defaultLeaveType);
            if (!$leaveType->save()) {
                throw new Exception(__('warnings.ErrorStoreLeaveType'), 500);
            }
            // Create LeaveTypeSubFamilies if LeaveType has
            if (!is_null($subFamilies)) {
                foreach ($subFamilies as $subFamily) {
                    $subFamily['leave_type_id'] = $leaveType->id;
                    $leaveTypeSubFamilies = LeaveTypeSubFamily::create($subFamily);
                    if (!$leaveTypeSubFamilies->save()) {
                        throw new Exception(__('warnings.ErrorStoreLeaveTypeSubFamily'),500);
                    }
                }
            }
        }
    }

    #region Private Functions

    /**
     *  Vérifie que l'id soit bien le client ID du user connecté
     *
     * @param int $id
     *
     * @throws Exception
     */
    private function checkClientBelongsToUser(int $id): void
    {
        if ($id !== $this->clientId) {
            throw new Exception(__('warnings.ErrorUpdateOtherCustomer'),  400);
        }
    }
    #endregion

    #region Public Functions

    public function current(Request $request): JsonResponse
    {
        $client = Tools::getCurrentUserWithUuid()->site->client;
        $days = $client->days()->get();
        if (is_null($days)) {
            $openDays = [];
        } else {
            $openDays = $days->pluck('day_name')->toArray();
        }

        foreach ($openDays as $day) {
            $intOpenDays[] = (int)date("N", strtotime($day));
        }

        $client['open_days'] = $intOpenDays;

        return response()->json($client, 200);
    }

    public function show(Request $request, int $id): JsonResponse
    {
        try {
            throw new Exception(__('warnings.CantDisplayCustomerFromThisApp'));
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.CantDisplayCustomerFromThisApp');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    /**
     * Get client by uuid
     * @param $uuid
     * @return JsonResponse
     */
    public function getByUuid($uuid): JsonResponse
    {
        $client = Client::where('uuid', '=', $uuid)->first();
        return response()->json($client, 200);
    }

    /**
     * Function to insert new client
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            // Validate data
            $data = $this->validate($request, $this->storeRule, $this->messages());
            // Check if exist
            if (Client::where('uuid', '=', $data['uuid'])->first()) {
                throw new \Exception(__('warnings.ClientAlreadyExist'),  400);
            }
            // Check soft deleted
            $soft = Client::withTrashed()->where('uuid', '=', $data['uuid'])->first();
            if ($soft) {
                $soft->restore();
                if (!$soft->save()) {
                    throw new Exception(__('warnings.ErrorStoreClient'),  500);
                }
                $this->responseData = $soft->toArray();
            } else {

                $client = Client::create($data);
                if (!$client->save()) {
                    throw new Exception(__('warnings.ErrorStoreClient'),  500);
                }
                $this->setClientDefaultValues($client);
                $this->responseData = $client->toArray();
            }
            $this->responseMessage = __('warnings.SuccessStoreCustomer');
        } catch (ValidationException $exception) {
            $this->responseMessage = __('warnings.ErrorValidationData');
            $this->responseDetails = $exception->validator->errors()->unique();
            $this->responseCode = $exception->status;
        } catch (\Exception $exception) {
            $this->responseMessage = __('warnings.ErrorStoreCustomer');
            $this->responseDetails = $exception->getMessage();
            $this->responseCode = $exception->getCode();
        }
        return $this->formattedResponse();
    }

    /**
     * Modification du client courant
     * Route internal
     *
     * @param Request $request
     * @param string $uuid
     * @return JsonResponse
     */
    public function updateName(Request $request, string $uuid): JsonResponse
    {
        try {
            // Validation des données reçu en POST
            $data = $this->validate($request, $this->updateRule, $this->messages());
            // Récupère le client
            $client = Client::where('uuid', '=', $uuid)->first();
            // Vérifie si le client existe
            if (!$client) {
                throw new Exception(__('warnings.ClientNotExist'),  404);
            }
            // Vérifie que l'id soit bien le client ID du user connecté
            $this->checkClientBelongsToUser($client->id);
            // Mis à jour client
            $client->save($data);


            $this->responseMessage = __('messages.SuccessUpdateCustomer');
            $this->responseData = $client->toArray();
        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateCustomer');
            $this->responseCode = $exception->status;
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateCustomer');
            $this->responseCode = $exception->getCode();
        }
        return $this->formattedResponse();
    }


    public function destroy(string $uuid): JsonResponse
    {
        try {
            // Récupère le client
            $client = Client::where('uuid', '=', $uuid)->first();
            // Vérifie si le client existe
            if (!$client) {
                throw new Exception(__('warnings.ClientNotExist'),404);
            }
            // Vérifie que l'id soit bien le client ID du user connecté
            $this->checkClientBelongsToUser($client->id);
            // Supprime le client
            $client->delete();

            $this->responseMessage = __('messages.SuccessDeleteCustomer');
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorDeleteCustomer');
            $this->responseCode = $exception->getCode();
        }
        return $this->formattedResponse();
    }

    #endregion
    #region PARAMETRAGE

    /**
     * Modification du client courant
     *
     * @param Request $request
     * @param int $id
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            // Vérification de l'id
            $this->basicIdVerification($id);
            // Validation des données reçu en POST
            $data = $this->validate($request, $this->config, $this->messages());
            // Vérifie que l'id soit bien le client ID du user connecté
            $this->checkClientBelongsToUser($id);

            $client = Client::find($id);
            $client->update($data);

            $this->responseMessage = __('messages.SuccessUpdateCustomer');
            $this->responseData = $client->toArray();
        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateCustomer');
            $this->responseCode = $exception->status;
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateCustomer');
            $this->responseCode = $exception->getCode();
        }
        return $this->formattedResponse();
    }

    /**
     * Récupère le schéma de validation
     * @return JsonResponse
     */
    public function getValidationScheme(): JsonResponse
    {
        try {
            $currentClient = $this->getCurrentClient();

            $data = [
                "validation_scheme" => $currentClient->validation_scheme
            ];

            $this->responseData = $data;
            $this->responseMessage = __('messages.SuccessGetParameter');
        } catch (\Exception $exception) {
            $this->responseDetails = $this->responseMessage ?: __('warnings.ErrorGetValidationScheme');
            $this->responseMessage = $exception->getMessage();
            $this->responseCode = $exception->getCode();
        }
        return $this->formattedResponse();
    }

    /**
     * Récupère les jours ouvrés du client
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function getClientOpenDays(): JsonResponse
    {
        try {
            $client = $this->currentUser->client;
            $days = $client->days()->get();
            if (is_null($days)) {
                $openDays = [];
            } else {
                $openDays = $days->pluck('day_name')->toArray();
            }

            foreach ($openDays as $day) {
                $intOpenDays[] = date("N", strtotime($day));
            }

            $this->responseData = $intOpenDays ?? [];
            $this->responseMessage = __('messages.SuccessGetBusinessDay');
        } catch (\Exception $exception) {
            $this->responseDetails = $this->responseMessage ?: __('warnings.ErrorGetBusinessDay');
            $this->responseMessage = $exception->getMessage();
            $this->responseCode = $exception->getCode();
        }
        return $this->formattedResponse();
    }

    /**
     * Mise à jour des jours ouvrés du client
     *
     * @param Request $request
     * @return JsonResponse
     * @throws Exception
     */

    public function updateClientOpenDays(Request $request): JsonResponse
    {
        try {
            // Validate data
            $data = $this->validate($request, [
                "monday" => "boolean",
                "tuesday" => "boolean",
                "wednesday" => "boolean",
                "thursday" => "boolean",
                "friday" => "boolean",
                "saturday" => "boolean",
                "sunday" => "boolean",
            ], $this->messages());
            $client = $this->currentUser->client;

            // Detach all
            $client->days()->detach();
            // Attach days in data
            if (!empty($data)) {
                foreach ($data as $day_name => $bool) {
                    if ($bool) {
                        $day = Day::where('day_name', '=', strtoupper($day_name))->first();
                        $client->days()->attach($day);
                    }
                }
            }

            // Get days open
            $days = $client->days()->get();
            if (is_null($days)) {
                $openDays = [];
            } else {
                $openDays = $days->pluck('day_name')->toArray();
            }

            $this->responseData = $openDays;
            $this->responseMessage = __('messages.SuccessUpdateBusinessDay');
        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateBusinessDay');
            $this->responseCode = $exception->status;
        } catch (\Exception $exception) {
            $this->responseDetails = $this->responseMessage ?: __('warnings.ErrorUpdateBusinessDay');
            $this->responseMessage = $exception->getMessage();
            $this->responseCode = $exception->getCode();
        }
        return $this->formattedResponse();
    }

    public function getClientCountPublicHolidays(Request $request): JsonResponse
    {
        try {

            $currentClient = $this->getCurrentClient();

            $data = [
                "count_public_holidays" => $currentClient->count_public_holidays
            ];

            $this->responseData = $data;
            $this->responseMessage = __('messages.SuccessGetParameter');
        } catch (\Exception $exception) {
            $this->responseDetails = $this->responseMessage ?: __('warnings.ErrorUpdateBusinessDay');
            $this->responseMessage = $exception->getMessage();
            $this->responseCode = $exception->getCode();
        }
        return $this->formattedResponse();
    }

    public function getClientCountPentecost(Request $request): JsonResponse
    {
        try {

            $currentClient = $this->getCurrentClient();

            $data = [
                "is_pentecost" => (bool)$currentClient->is_pentecost
            ];
            $this->responseData = $data;
            $this->responseMessage = __('messages.SuccessGetParameter');
        } catch (\Exception $exception) {
            $this->responseDetails = $this->responseMessage ?: __('warnings.ErrorUpdateBusinessDay');
            $this->responseMessage = $exception->getMessage();
            $this->responseCode = $exception->getCode();
        }
        return $this->formattedResponse();
    }

    public function updateClientCountPublicHolidays(Request $request): JsonResponse
    {
        try {
            // Validate data
            $data = $this->validate($request, [
                "count_public_holidays" => "boolean",
            ], $this->messages());

            $currentClient = $this->getCurrentClient();
            $currentClient->count_public_holidays = $data["count_public_holidays"];
            $currentClient->save();

            $data = [
                "count_public_holidays" => $currentClient->count_public_holidays
            ];

            $this->responseData = $data;
            $this->responseMessage = __('messages.SuccessUpdateParameter');
        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateBusinessDay');
            $this->responseCode = $exception->status;
        } catch (\Exception $exception) {
            $this->responseDetails = $this->responseMessage ?: __('warnings.ErrorUpdateBusinessDay');
            $this->responseMessage = $exception->getMessage();
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    public function updateClientCountPentecost(Request $request)
    {
        try {
            // Validate data
            $data = $this->validate($request, [
                "is_pentecost" => "boolean",
            ], $this->messages());

            $currentClient = $this->getCurrentClient();
            $currentClient->is_pentecost = (bool)$data["is_pentecost"];
            $currentClient->save();
            $data = [
                "is_pentecost" => (bool)$currentClient->is_pentecost
            ];

            $this->responseData = $data;
            $this->responseMessage = __('messages.SuccessUpdateParameter');
        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateBusinessDay');
            $this->responseCode = $exception->status;
        } catch (\Exception $exception) {
            $this->responseDetails = $this->responseMessage ?: __('warnings.ErrorUpdateBusinessDay');
            $this->responseMessage = $exception->getMessage();
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    public function seedDemo()
    {
        $client = Tools::getCurrentUserWithUuid()->site->client;

        if (Client::find($client->getKey())->exists()) {
            app()->make(DatabaseSeeder::class)->run();
            return response('seedingSuccess');
        }

        return response('clientNotFound', 400);
    }

    #endregion
}
