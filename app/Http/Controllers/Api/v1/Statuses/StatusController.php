<?php


namespace App\Http\Controllers\Api\v1\Statuses;


use App\Lib\ApiHelper;
use App\Models\Api\Status;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use \App\Http\Controllers\Api\v1\Controller;
use Illuminate\Validation\ValidationException;

class StatusController extends Controller
{

    /**
     * @inheritDoc
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $this->validate($request, $this->defaultIndexFilters, $this->messages());

            $statusModel = Status::query();

            // Pagination des données
            $this->responseData = ApiHelper::defaultModelBuilder($statusModel, $request->get("orders", "tag,asc"),
                $request->get("limit", null), $request->get("paginate", 0));
            $this->getResponse = true;
            $this->responseMessage = __('warnings.SuccessGetStatuses');
        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.SuccessGetStatuses');
            $this->responseCode = $exception->getCode();
        } catch(Exception $exception){
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.SuccessGetStatuses');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }
}
