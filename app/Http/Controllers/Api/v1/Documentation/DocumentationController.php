<?php


namespace App\Http\Controllers\Api\v1\Documentation;


use App\Http\Controllers\Api\v1\Controller;
use App\Interfaces\Controllers\Api\BaseApiControllerInterface;
use App\Lib\ApiHelper;
use App\Models\Api\Client;
use App\Models\Api\Documentation;
use App\Models\Api\Site;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;

class DocumentationController extends Controller
{
    private array $validationAttachDetach = [
        "site_id" => "required|integer",
        "documentation_id" => "required|integer",
    ];

    /**
     * Récupération des documents
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            if ($request->get("sameClient")) {
                $clientId = $this->currentUser->client->id;
                $documentations = Documentation::whereHas("sites", function ($query) use ($clientId) {
                    $query->whereHas("client", function ($query) use ($clientId) {
                        $query->where("id", $clientId);
                    });
                })->get()->toArray();
            } else {
                $site = Site::find($this->currentUser->site_id);
                $documentations = $site->documentations->toArray();
            }

            foreach ($documentations as $key => $documentation) {
                $documentations[$key]["sites"] = Site::whereHas('documentations', function ($q) use ($documentation) {
                    $q->where('documentation_id', '=', $documentation["id"]);
                })->get()->pluck("id");
            }

            $this->responseData = $documentations;
            $this->responseMessage = __('messages.SuccessGetDocs');
        } catch (\Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetDocs');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    /**
     * Récupération d'un document
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function show(Request $request, int $id)
    {
        try {
            // Vérification de l'id
            $this->basicIdVerification($id);

            $site = Site::find($this->currentUser->site_id);
            $doc = $site->documentations()->wherePivot("documentation_id", $id)->first();
            // Vérification que la documention existe pour le site
            if (is_null($doc)) {
                throw new \Exception(__('warnings.DocDontExistForSite'),  404);
            }
            $docArray = $doc->toArray();

            // Récupération du fichier
            return ApiHelper::downloadExport($docArray["path"]);
        } catch (\Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetDoc');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    /**
     * Ajouter du document passé en paramètre
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->get("data"), true);

            if (!$data) {
                throw new \Exception(__('warnings.ErrorIdArrayDontExist'));
            }
            // Verify Ids
            if (empty($data["ids"])) {
                throw new \Exception(__('warnings.UpdateDocIdError'));
            }

            foreach ($data["ids"] as $site_id) {
                // If no site is found for client
                if (!Site::where('id', '=', $site_id)->where('client_id', '=', $this->clientId)->first()) {
                    throw new \Exception(__('warnings.SiteDontExistForClient'), 404);
                }
            }
            $name = $data["name"] ?? "no-name";
            if ($request->hasFile("documentation")) {
                $file = $request->file("documentation");

                if (!$file) {
                    throw new \Exception(__('warnings.ErrorFileDontExist'));
                }
                $carbon = Carbon::now()->isoFormat("OY-MM-DD_kk:mm:ss");
                $uploadedFile = $this->fileUpload($file, "documentations/" . $this->clientId . "/" . $carbon . "_" . $name . "." . $file->clientExtension());
                $documention = Documentation::create([
                    "name" => $name,
                    "real_name" => $uploadedFile["name"],
                    "path" => $uploadedFile["path"]
                ]);
                if (!$documention->save()) {
                    throw new \Exception(__('warnings.ErrorStoreFile'),  500);
                }
                $documention->sites()->attach($data["ids"]);

                $this->responseMessage = __('messages.SuccessStoreDoc');
            } else {
                throw new \Exception(__('warnings.ErrorLoadFile'));
            }
        } catch (\Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorLoadFile');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    /**
     * Modification du document passé en paramètre
     *
     * @param Request $request
     * @param $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            if (!empty($request->get("data"))) {
                $data = json_decode($request->get("data"), true);
                $name = $data["name"] ?? "no-name";
            } else {
                $name = "no-name";
            }

            if ($request->hasFile("documentation")) {
                $file = $request->file("documentation");
                if (!$file) {
                    throw new \Exception(__('warnings.ErrorFileDontExist'));
                }

                [$doc, $ids] = $this->getDocAndId($data, $id);

                $checkDelete = Storage::disk('s3')->delete([$doc->path]);

                if (!$checkDelete) {
                    throw new \Exception(__('warnings.CantDeleteForUpdateFile'));
                }
                $carbon = Carbon::now()->isoFormat("OY-MM-DD_kk:mm:ss");
                $uploadedFile = $this->fileUpload($file, "documentations/" . $this->clientId . "/" . $carbon . "_" . $name . "." . $file->clientExtension());

                if (!$doc->update([
                    "name" => $name,
                    "real_name" => $uploadedFile["name"],
                    "path" => $uploadedFile["path"]
                ])) {
                    throw new \Exception(__('warnings.ErrorStoreFile'),  500);
                }

                $doc->sites()->detach();
                $doc->sites()->attach($ids);

                $this->responseData = $doc->toArray();
                $this->responseMessage = __('messages.SuccessUpdateDoc');
            } else {

                [$doc, $ids] = $this->getDocAndId($data, $id);

                $nameSplit = explode(".", $doc->name);
                $name = $data["name"] ?? $nameSplit[0];

                if (!$doc->update(["name" => $name])) {
                    throw new \Exception(__('warnings.ErrorStoreFile'), 500);
                }

                $doc->sites()->detach();
                $doc->sites()->attach($ids);

                $this->responseData = $doc->toArray();
                $this->responseMessage = __('messages.SuccessUpdateDoc');
            }
        } catch (\Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorStoreFile');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    private function getDocAndId($data, $id)
    {
        if (empty($data["ids"])) {
            throw new \Exception(__('warnings.UpdateDocIdError'));
        }
        $ids = $data["ids"];

        $clientId = $this->clientId;
        $doc = Documentation::where('id', '=', $id)->whereHas('sites', function ($q) use ($clientId) {
            $q->where('client_id', '=', $clientId);
        })->first();
        if (is_null($doc)) {
            throw new \Exception(__('warnings.DocDontExistForClient'),  404);
        }
        return [$doc, $ids];
    }

    /**
     * Delete un document
     *
     * @param $id
     * @return JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        try {
            // Vérification de l'id
            $this->basicIdVerification($id);

            // Detach
            $clientId = $this->clientId;
            $documentation = Documentation::where('id', '=', $id)->whereHas('sites', function ($q) use ($clientId) {
                $q->where('client_id', '=', $clientId);
            })->first();
            if (!$documentation) throw new \Exception(__('warnings.DocDontExistForClient'),  404);

            $documentation->delete();
            $documentation->sites()->detach();

            $checkDelete = Storage::disk('s3')->delete($documentation->path);
            if (!$checkDelete) {
                throw new \Exception(__('warnings.CantDeleteFile'));

            }

            $this->responseMessage = __('messages.SuccessDeleteFile');
        } catch (\Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorDeleteFile');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }
}
