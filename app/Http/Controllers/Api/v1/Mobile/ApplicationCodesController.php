<?php


namespace App\Http\Controllers\Api\v1\Mobile;


use App\Http\Controllers\Api\v1\Controller;
use App\Models\Api\Application;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Validation\ValidationException;

class ApplicationCodesController extends Controller
{
    public function getCurrentUserQrs(Request $request):JsonResponse
    {
        try {
            $data = $this->validate($request, [
                "country_code" => "string",
                "user_uuid" => "string",
            ], $this->messages());

            // Get uuids
            $app_uuid = Application::where("slug", "=", "api-conges")->first()->uuid;

            // get country id
            $res = Http::withHeaders([
                'InternalKey' => env("INTERNAL_AKT"),
            ])->get(env("QR_API_URL")."/api/countries");
            $countries = $res->json()["data"];
            $countryId = collect($countries)->where("Country", $data["country_code"])->first()["ID"];


            // send request
            $url = env("QR_API_URL")."/api/application_code/".$app_uuid."/".$data["user_uuid"]."?country_id=".$countryId;
            $body = Http::withHeaders([
                'InternalKey' => env("INTERNAL_AKT"),
            ])->get($url);

            $this->responseData = $body->json();
        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag()->unique();
            $this->responseMessage = $this->responseMessage ?? "";
            $this->responseCode = $exception->status;
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?? "";
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }
}
