<?php

namespace App\Http\Controllers\Api\v1\Users;

use App\Excel\Exports\UserLeavesCountsExport;
use App\Excel\Imports\CegidCPLeaveCountersImport;
use App\Excel\Imports\CegidOthersLeaveCountersImport;
use App\Excel\Imports\ExtraComLeaveCountersImport;
use App\Excel\Imports\SilaeLeaveCountersImport;
use App\Http\Controllers\Api\v1\Controller;
use App\Http\Controllers\OrionUsersController;
use App\Lib\ApiHelper;
use App\Models\Api\Client;
use App\Models\Api\ExportHistory;
use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\Status;
use App\Models\Api\User;
use App\Models\Api\UserLeaveCount;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Facades\Excel;

class UserLeaveCountsController extends Controller
{
    private function getLeaveStartDate()
    {
        $date = Client::find($this->clientId)->leave_start_date;
        $year = Carbon::now()->format("Y");
        if ($date == null) {
            return [Carbon::parse($year . "-06-01"), "06", "01"];
        }
        $parseDate = Carbon::parse($year . "-" . Carbon::parse($date)->format("m-d"));
        return [$parseDate, $parseDate->format("m"), $parseDate->format("d")];
    }

    public function getCounts(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            "user_id" => "int",
            "withLeaveType" => "boolean",
            "withFutureLeaves" => "boolean"
        ], $this->messages());

        $currentUser = $this->currentUser;

        //Get n and n-1 counts
        if (!empty($data['user_id'])) {
            $userLeaveCounts = UserLeaveCount::where("user_id", $data['user_id']);
        } else {
            $userLeaveCounts = UserLeaveCount::where("user_id", $currentUser->id);
        }
        //query params
        if (!empty($data['user_id'])) {
            $userLeaveCounts->where("user_id", "=", $data['user_id']);
        }
        //query params
        if (!empty($data['withLeaveType'])) {
            $userLeaveCounts->with("leave_type");
        }

        $this->responseData = ApiHelper::defaultModelBuilder($userLeaveCounts, $request->get("orders", "id,asc"),
            $request->get("limit", null), $request->get("paginate", 0));
        $this->getResponse = false;
        $this->responseMessage = __('messages.SuccessGetUser');

        if (!empty($data["withFutureLeaves"])) {
            // Get body response
            $bodyResponse = $this->apiHelper
                ->responseFormatter(
                    $this->checkHttpStatus($this->responseCode),
                    $this->responseData,
                    $this->responseMessage,
                    $this->responseDetails,
                    $this->getResponse
                );
            $dataArray = $bodyResponse["data"]["data"];
            foreach ($dataArray as $key => $userLeaveCount) {
                // Get user open days
                $userArray = $userLeaveCount;
                $userArray["futureLeaves"] = $this->getFutureLeaves($data["user_id"], $userLeaveCount->leave_type_id, $userLeaveCount->is_last_year);
                $dataArray[$key] = $userArray;
            }
            // Set data from response
            $bodyResponse["data"] = $dataArray;
            return response()->json($bodyResponse);
        }
        $this->responseData = $userLeaveCounts->get()->toArray();
        $this->responseMessage = __('messages.SuccessGetCompteur');
    }

    /**
     * @return JsonResponse
     */
    public function getTeamCounts(): JsonResponse
    {
        try {
            $currentUserId = $this->currentUser->id;
            // Get Users bellow manager (CurrentUser)
            $teamUsers = User::whereHas('manager', function ($query) use ($currentUserId) {
                $query->where('managers.manager_id', '=', $currentUserId)
                    ->where('managers.level', '=', 1);
            })->get();

            $dataTeamUsers = [];

            $timer = Carbon::now();

            // Get Client start date
            [$leaveStartDate, $leaveStartMonth, $leaveStartDay] = $this->getLeaveStartDate();

            //Control if n-1 starting date is 2 years from current date due to different year calendar for leaves
            if ($timer->lessThan($leaveStartDate)) $currentYear = $timer->year - 2;
            else $currentYear = $timer->year - 1;

            //Get n and n-1 counts
            $maximumStartDate = $currentYear . "-" . $leaveStartMonth . "-" . $leaveStartDay;

            foreach ($teamUsers as $teamUser) {

                $userLeaveCounts = UserLeaveCount::where("user_id", '=', $teamUser->id)->get()->toArray();

                $dataUserLeaveCount = [];

                foreach ($userLeaveCounts as $userLeaveCount) {
                    $leaveType = LeaveType::find($userLeaveCount["leave_type_id"])->toArray();

                    if ($leaveType["is_monthly"]) $userLeaveCount["leave_type"] = "monthly";
                    else $userLeaveCount["leave_type"] = "free";
                    array_push($dataUserLeaveCount, $userLeaveCount);
                }
                // Format data
                $arrayUser = $teamUser->toArray();
                $arrayUser['user_leave_counts'] = $dataUserLeaveCount;
                array_push($dataTeamUsers, $arrayUser);
            }
            $this->responseData = $dataTeamUsers;
            $this->responseMessage = __('messages.SuccessGetCompteur');
        } catch (\Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetTeamCompteur');
            $this->responseCode = $exception->getCode();
        }
        return $this->formattedResponse();
    }

    /**
     * Store UserLeaveCount
     * @param Request $request
     * @return JsonResponse
     * @throws \Throwable
     */
    public function store(string $n_1, Request $request): JsonResponse
    {
        try {
            $data = $this->validate($request, [
                "user_id" => "numeric|min:1|required",
                "leave_type_id" => "numeric|min:1|required",
                "acquired" => "numeric|required",
                "taken" => "numeric|required",
                "balance" => "numeric|required",
                "n-1" => "boolean|required"
            ], $this->messages());

            // If User does not exist
            $user = User::find($data["user_id"]);
            throw_if(!$user, new Exception(__('warnings.UserDoesNotExist'), 404));
            // If User belong to my client
            if ($this->clientId != $user->site->client_id) {
                throw new Exception(__('warnings.UserBelongToAnotherClient'),  403);
            }
            // If LeaveType does not exist
            $leaveType = LeaveType::find($data["leave_type_id"]);
            throw_if(!$leaveType, new Exception(__('warnings.LeaveTypeDoesntExist'),404));
            // If LeaveType belong to my client
            if ($this->clientId != $leaveType->client_id) {
                throw new Exception(__('warnings.LeaveTypeBelongToAnotherClient'), 403);
            }

            // If UserLeaveCount does already exist
            if (UserLeaveCount::where("user_id", "=", $user->id)
                ->where("leave_type_id", "=", $leaveType->id)->first()) {
                throw new Exception(__('warnings.UserLeaveCountAlreadyExist'), 400);
            }

            // Create record
            $leaveCount = UserLeaveCount::create($data);
            $leaveCount->save();

            $this->responseData = $leaveCount->toArray();
            $this->responseMessage = __('messages.SuccessStoreCompteur');
        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorStoreCompteur');
            $this->responseCode = $exception->status;
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorStoreCompteur');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    /**
     * Update UserLeaveCount
     * @param Request $request
     * @param $id
     * @return JsonResponse
     * @throws \Throwable
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $data = $this->validate($request, [
                "acquired" => "numeric",
                "taken" => "numeric",
                "balance" => "numeric",
                "is_last_year" => "boolean"
            ], $this->messages());

            // If LeaveCount exist
            $leaveCount = UserLeaveCount::find($id);
            throw_if(!$leaveCount, new Exception(__('warnings.UserLeaveCountDoesntExist'), 404));
            // If LeaveCount User belong to my client
            $user = User::find($leaveCount->user_id);
            if ($this->clientId != $user->site->client_id) {
                throw new Exception(__('warnings.UserLeaveCountBelongToAnotherClient'),  403);
            }

            // Update record
            $data["rh_update"] = date("Y-m-d H:i:s");
            $leaveCount->update($data);
            $leaveCount->save();

            $this->responseData = $leaveCount->toArray();
            $this->responseMessage = __('messages.SuccessUpdateCompteur');
        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateCompteur');
            $this->responseCode = $exception->status;
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateCompteur');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }


    public function export(Request $request, \Orion\Http\Requests\Request $orionRequest)
    {
        try {
            $data = $this->validate($request, [
                "user_ids" => "nullable|array",
                "from_team" => "nullable|boolean",
                "file_name" => "nullable|string"
            ], $this->messages());
            // Création du chemin d'enregistrement
            $path = 'exports/' . $this->clientId . '/';
            $fileName = Carbon::now("Europe/Stockholm")->format('d-m-Y_H-i-s') . '_EXCEL.xls';
            // If file_name has a value
            if (!empty($data["file_name"])) {
                // If name already exist
                if (ApiHelper::exportAlreadyExist($path . $data["file_name"] . ".xls")) {
                    for ($i = 1; ApiHelper::exportAlreadyExist($path . $data["file_name"] . "(" . $i . ").xls"); $i++) ;
                    $data["file_name"] = $data["file_name"] . "(" . $i . ")";
                }
                $fileName = $data["file_name"] . ".xls";
            }
            $disk = 's3';

            $ctrl = new OrionUsersController();
            $userIds = $ctrl->getFetchQuery($orionRequest)->get()->pluck('id')->toArray();

            // Si la requête vient pas de la page "Equipe"
            if (isset($data["from_team"]) && $data["from_team"]) {
                $save = Excel::store(new UserLeavesCountsExport($this->clientId, $userIds), $path . $fileName, $disk, \Maatwebsite\Excel\Excel::XLS);
                if (!$save || !Storage::disk($disk)->exists($path . $fileName)) {
                    throw new Exception(__('warnings.CantGenerateFile'),  500);
                }
                $headers = [
                    'Content-Disposition' => 'attachment; filename=' . $fileName,
                ];
                // Send a file and then delete it from minio with a StreamedResponse
                return response()->stream(function () use ($disk, $path, $fileName) {
                    echo Storage::disk($disk)->get($path . $fileName);
                    Storage::disk($disk)->delete($path . $fileName);
                }, 200, $headers);
            }
            // Création et stockage interne de l'export Excel
            $save = Excel::store(new UserLeavesCountsExport($this->clientId, $userIds), $path . $fileName);
            // Export mis dans l'historique des exports
            if ($save) {
                // Create History
                ExportHistory::create([
                    'user_id' => $this->currentUser->id,
                    'file_name' => $fileName,
                    'extension' => 'xls',
                    'type' => 'EXCEL'
                ]);
                // Delete History and file on minio
                ApiHelper::deleteExportIfTooMuch($this->clientId, $path);
            } else {
                throw new Exception(__('warnings.CantGenerateFile'), 500);
            }

            // Téléchargement de l'export
            return ApiHelper::downloadExport($path . $fileName);
        } catch (Exception $exception) {
            $this->responseMessage = __('warnings.ErrorExportCompteur');
            $this->responseDetails = $exception->getMessage();
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    public function import(Request $request): JsonResponse
    {
        if (!$request->file('file')) {
            throw new \Exception('No file uploaded');
        }

        $disk = config('filesystems.default');
        $file = $request->file('file');
        $importType = $request->input('queueName');

        $import = match ($importType) {
            'cegid-cp' => new CegidCPLeaveCountersImport(Auth::user()),
            'cegid-others' => new CegidOthersLeaveCountersImport(Auth::user()),
             'extra-com' => new ExtraComLeaveCountersImport(Auth::user()),
            default => new SilaeLeaveCountersImport(Auth::user())
        };

        $import->import($file, $disk, \Maatwebsite\Excel\Excel::XLSX);

        return response()->json([
            'success' => (count($import->errors) === 0 ? true : 'PartiallySucces'),
            'message' => (count($import->errors) === 0 ? __('messages.ImportSucces') : __('messages.ImportUserLeaveCountPartiallySucces', ['successRows' => count($import->rows) - $import->failedRows, 'rows' => count($import->rows)])),
            'errors' => $import->errors
        ]);
    }

    private function getFutureLeaves($userId, $leaveTypeId, $is_last_year)
    {
        $leaves = Leave::query()->whereIn("status_id", Status::whereIn("tag", ["VALIDATED", "SUBMITTED"])->get("id")->toArray())
            ->where("user_id", $userId)->get();

        $userLeaveCount = UserLeaveCount::query()->where("user_id", $userId)->where("leave_type_id", $leaveTypeId)->where("is_last_year", $is_last_year)->first();

        foreach ($leaves as $leave) {
            if ($leave->leave_type_id == $userLeaveCount->leave_type_id) {
                if (empty($userLeaveCount->futureLeave)) {
                    $userLeaveCount->futureLeave = 0;
                }
                if ($userLeaveCount->is_last_year == 1) {
                    $userLeaveCount->futureLeave += $leave->n1;
                } else {
                    $userLeaveCount->futureLeave += $leave->n;
                }
            }
        }

        return $userLeaveCount->futureLeave;
    }

    public function usersWithoutLeave()
    {
        return UserLeaveCount::query()
            ->selectRaw('CONCAT(users.firstname, " ", users.lastname) as fullname')
            ->join('users', 'user_leave_counts.user_id', 'users.id')
            ->join('sites', 'users.site_id', 'sites.id')
            ->where('sites.client_id', Auth::user()->site->client_id)
            ->whereNull('user_leave_counts.rh_update')
            ->whereNull('user_leave_counts.deleted_at')
            ->whereNull('users.deleted_at')
            ->where('user_leave_counts.is_last_year', 0)
            ->where('user_leave_counts.acquired', 0)
            ->where('user_leave_counts.taken', 0)
            ->where('user_leave_counts.balance', 0)
            ->where('users.created_at', '<', Carbon::now()->subDays(40))
            ->groupBy(['fullname', 'users.lastname'])
            ->orderBy('users.lastname')
            ->get()
        ;
    }
}
