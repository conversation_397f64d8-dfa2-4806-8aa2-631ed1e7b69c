<?php

namespace App\Http\Controllers\Api\v1\Users;

use App\Http\Controllers\Api\v1\Controller;
use App\Http\Controllers\Api\v1\Leaves\LeavesController;
use App\Interfaces\Controllers\Api\BaseApiControllerInterface;
use App\Lib\ApiHelper;
use App\Models\Api\Client;
use App\Models\Api\Day;
use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\Profile;
use App\Models\Api\Site;
use App\Models\Api\Status;
use App\Models\Api\User;
use App\Models\Api\UserLeaveCount;
use Exception;
use GuzzleHttp\Client as GuzzleClient;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use phpDocumentor\Reflection\Types\Resource_;

class UsersController extends Controller
{
    private array $config = [
        "site_id" => "nullable|integer",
        "matricule" => "nullable|string",
        "enter_date" => "nullable|date_format:Y-m-d H:i:s",
        "can_receive_mails" => "nullable|boolean",
        "can_receive_absentees_reminder_mails" => "nullable|boolean"
    ];
    private string $delimiter = ";";
    // Construction des en-têtes de base
    private array $headers = ["Matricule", "Salarié", "Établissement", "Date d'entrée"];

    private array $excelColumnMapper = [
        0 => "A",
        1 => "B",
        2 => "C",
        3 => "D",
        4 => "E",
        5 => "F",
        6 => "G",
        7 => "H",
        8 => "I",
        9 => "J",
        10 => "K"
    ];

    #region Private Functions

    /**
     * Je vérifie qu'il existe
     * @param Site|null $site
     *
     * @throws Exception
     */
    private function checkIfSiteExist(Site $site = null)
    {
        if (!$site) {
            throw new Exception(__('warnings.ErrorSiteDoesntExist'));
        }
    }

    /**
     * Je vérifie qu'il appartient au client auquel j'appartient
     * @param Site|null $site
     *
     * @throws Exception
     */
    private function checkIfSiteBelongsToMyClient(Site $site = null)
    {
        if ($site && $site->client_id !== $this->clientId) {
            throw new Exception(__('warnings.ErrorDontOwnSite'));
        }
    }

    /**
     * Je vérifie qu'il existe
     * @param User|null $user
     *
     * @throws Exception
     */
    private function checkIfUserExist(User $user = null)
    {
        if (!$user) {
            throw new Exception(__('warnings.ErrorUserDontExist'));
        }
    }

    /**
     * Je vérifie qu'il appartient au client auquel j'appartient
     * @param User|null $user
     *
     * @throws Exception
     */
    private function checkIfUserBelongsToMyClient(User $user = null)
    {
        if ($user && $user->site->client_id !== $this->clientId) {
            throw new Exception(__('warnings.ErrorDontOwnUser'));
        }
    }

    /**
     * @param $request
     * @throws \Throwable
     */
    private function checkForeignKeys($request)
    {
        if (isset($request["client_id"])) {
            $client = Client::find($request["client_id"]);
            throw_if(is_null($client), new \Exception(__('warnings.UserDontExist'),  404));
        }
        if (isset($request["profile_id"])) {
            $profile = Profile::find($request["profile_id"]);
            throw_if(is_null($profile), new \Exception(__('warnings.ProfileDontExist'),  404));
        }
        if (isset($request["site_id"])) {
            $site = Site::find($request["site_id"]);
            throw_if(is_null($site), new \Exception(__('warnings.SiteDontExist'), 404));
        }
    }

    private function getUserOpenDays($user)
    {
        $days = $user->days()->get();
        if (empty($days->toArray())) {
            $client = Client::find($this->clientId);
            $days = $client->days()->get();
            if (is_null($days)) {
                return [];
            }
            return $days->pluck('id')->toArray();
        }
        return $days->pluck('id')->toArray();
    }
    #endregion

    #region Public Functions

    /**
     * Récupération de tous les utilisateurs
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $this->validate($request, array_merge([
                "withClient" => "boolean",
                "withProfile" => "boolean",
                "withSite" => "boolean",
                "withUserLeaveCounts" => "boolean",
                "withTags" => "boolean",
                "withOpenDays" => "boolean",
            ], $this->defaultIndexFilters), $this->messages());
            $userModel = User::query()->whereHas("site", function ($query) {
                $query->where("client_id", "=", $this->clientId);
            });
            // Si on veut le client en plus
            if ($request->query("withClient")) {
                $userModel->with("client");
            }
            // Si on veut les tages des users
            if ($request->query("withTags")) {
                $userModel->with("tags");
            }
            // Si on veut le profil en plus
            if ($request->query("withProfile")) {
                $userModel->with("profile");
            }
            // Si on veut le site en plus
            if ($request->query("withSite")) {
                $userModel->with("site");
            }

            // filter for users
            // {"field": "users", "operator": "like", "value": "%name%"}
            if ($request->input('filters')) {
                $filters = $request->input('filters');
                foreach ($filters as $filter) {
                    if ($filter['field'] === 'users') {
                        $userModel->where('lastname', 'like', $filter['value']);
                    }
                    if ($filter['field'] === 'matricule') {
                        $userModel->where('matricule', 'like', $filter['value']);
                    }
                    if ($filter['field'] === 'sites') {
                        $userModel->whereIn('site_id', $filter['value']);
                    }
                }
            }

            // add sort on users and enter_date
            if ($request->input('sort')) {
                $sort = $request->input('sort');
                foreach ($sort as $sortItem) {
                    if ($sortItem['field'] === 'users') {
                        $userModel->orderBy('lastname', $sortItem['direction']);
                    }
                    if ($sortItem['field'] === 'enter_date') {
                        $userModel->orderBy('enter_date', $sortItem['direction']);
                    }
                }
            }


            // Pagination des données
            $this->responseData = ApiHelper::defaultModelBuilder($userModel->with("user_leave_counts.leave_type"), $request->get("orders", "id,asc"),
                $request->get("limit", null), $request->get("paginate", 0));
            $this->getResponse = true;
            $this->responseMessage = __('messages.SuccessGetUsers');
            // Si on veut les jours ouvrés de l'utilisateur en plus
            if ($request->query("withUserLeaveCounts")) {
                // Get body response
                $bodyResponse = $this->apiHelper
                    ->responseFormatter(
                        $this->checkHttpStatus($this->responseCode),
                        $this->responseData,
                        $this->responseMessage,
                        $this->responseDetails,
                        $this->getResponse
                    );
                $dataArray = $bodyResponse["data"];
                foreach ($dataArray as $key => $user) {
                    // Get user open days
                    $userArray = $user->toArray();
                    $userArray["days"] = $this->getUserOpenDays($user);
                    $userArray["futureLeaves"] = $this->getFutureLeaves($user->id);
                    $dataArray[$key] = $userArray;
                }
                // Set data from response
                $bodyResponse["data"] = $dataArray;
                return response()->json($bodyResponse);
            }
        } catch (ValidationException $exception) {
            $this->responseMessage = __('warnings.ErrorValidationData');
            $this->responseDetails = $exception->validator->errors()->unique();
            $this->responseCode = $exception->status;
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetUserFromApp');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    /**
     * Function for internal com lib
     * @param string $clientUuid
     * @return JsonResponse
     */
    public function show(string $clientUuid): JsonResponse
    {
        try {
            $clientUsers = User::where('client_uuid', $clientUuid)->get();
            $usersFromClient = [];
            foreach ($clientUsers as $user) {
                $usersFromClient[$user['uuid']] = $user;
            }
        } catch (\Exception $exception) {
            return response()->json(['error' => $exception], 400);
        }
        return response()->json($usersFromClient, 200);

    }

    public function profiles()
    {
        try {
            $profiles = Profile::all();
        } catch (\Exception $exception) {
            return response()->json(['error' => $exception], 500);
        }
        return response()->json($profiles, 200);
    }

    public function updateInternal(Request $request, $uuid)
    {
        try {
            $data = $this->validate($request, [
                "profile_id" => "integer|nullable|min:0",
                "site_id" => "integer|nullable|min:0",
                "firstname" => "string|nullable|max:255",
                "lastname" => "string|nullable|max:255",
                "email" => "string|nullable|max:255",
                "uuid" => "string|nullable|max:36",
                "client_uuid" => "string|nullable|max:36",
                "crm_uuid" => "string|nullable|max:255",
                "fcm_token" => "string|nullable|max:255",
                "picture_path" => "string|nullable|max:255",
                "license_path" => "string|nullable|max:255",
                "matricule" => "string|nullable|max:255",
                "enter_date" => "date_format:Y-m-d H:i:s|nullable",
                "planningAccess" => "boolean|nullable"
            ], $this->messages());
            $this->checkForeignKeys($data);

            $userToUpdate = User::withTrashed()->where('uuid', $uuid)->first();

            $userToUpdate->update($data);
            $userToUpdate->refresh();
        } catch (ValidationException $exception) {
            $this->responseMessage = __('warnings.ErrorValidationData');
            $this->responseDetails = $exception->validator->errors()->unique();
            $this->responseCode = $exception->status;
            return $this->formattedResponse();
        } catch (\Exception $exception) {
            $this->responseMessage = __('warnings.InvalideData');
            $this->responseDetails = $exception->getMessage();
            $this->responseCode = $exception->getCode();
            return $this->formattedResponse();
        }
        return response()->json($userToUpdate, 200);
    }

    public function store(Request $request): JsonResponse
    {
        try {
            $data = $this->validate($request, [
                "profile_id" => "integer|required|min:0",
                "site_id" => "integer|nullable|min:0",
                "firstname" => "string|required|max:255",
                "lastname" => "string|required|max:255",
                "email" => "string|required|max:255",
                "uuid" => "string|required|max:36",
                "client_uuid" => "string|nullable|max:36",
                "crm_uuid" => "string|nullable|max:255",
                "fcm_token" => "string|nullable|max:255",
                "picture_path" => "string|nullable|max:255",
                "license_path" => "string|nullable|max:255",
                "matricule" => "string|nullable|max:255",
                "enter_date" => "date_format:Y-m-d H:i:s|nullable",
                "planningAccess" => "boolean|nullable"
            ], $this->messages());
            $this->checkForeignKeys($data);

            $data["planningAccess"] = $data["planningAccess"] ?? 0;

            User::create($data);

        } catch (ValidationException $exception) {
            $this->responseMessage = __('warnings.ErrorValidationData');
            $this->responseDetails = $exception->validator->errors()->unique();
            $this->responseCode = $exception->status;
            return $this->formattedResponse();
        } catch (\Exception $exception) {
            $this->responseMessage = __('warnings.InvalideData');
            $this->responseDetails = $exception->getMessage();
            $this->responseCode = $exception->getCode();
            return $this->formattedResponse();
        }
        return response()->json(['success' => __('warnings.SuccessStoreUser')]);
    }

    /**
     * Modification de l'utilisateur passé en paramètre
     *
     * @param Request $request
     * @param int $id
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            // Vérification de l'id
            $this->basicIdVerification($id);
            // Validation des données reçu en POST
            $data = $this->validate($request, $this->config, $this->messages());
            // Je récupère l'utilisateur
            $user = User::find($id);
            // Je vérifie qu'il existe
            $this->checkIfUserExist($user);
            // Je vérifie qu'il appartient au client auquel j'appartient
            $this->checkIfUserBelongsToMyClient($user);

            $user->update($data);

            $this->responseMessage = __('messages.SuccessUpdateUser');
            $this->responseData = $user->toArray();
        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateUser');
            $this->responseCode = $exception->status;
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateUser');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    public function updateClientOpenDays(Request $request): JsonResponse
    {
        try {
            if ($this->currentUser->site->client->is_allowed_to_modify_open_days && $this->isAdmin()) {
                $responseData = [];
                // Validate data
                $data = $this->validate($request, [
                    "resources" => "array",
                    "resources.*.monday" => "boolean",
                    "resources.*.tuesday" => "boolean",
                    "resources.*.wednesday" => "boolean",
                    "resources.*.thursday" => "boolean",
                    "resources.*.friday" => "boolean",
                    "resources.*.saturday" => "boolean",
                    "resources.*.sunday" => "boolean",
                ], $this->messages());

                foreach ($data['resources'] as $userId => $resource) {
                    $user = User::find($userId);
                    // Detach all
                    $user->days()->detach();
                    // Attach days in data
                    if (!empty($resource)) {
                        foreach ($resource as $day_name => $bool) {
                            if ($bool) {
                                $day = Day::where('day_name', '=', strtoupper($day_name))->first();
                                $user->days()->attach($day);
                            }
                        }
                    }
                    // Get days open
                    $days = $user->days()->get();
                    if (is_null($days)) {
                        $openDays = [];
                    } else {
                        $openDays = $days->pluck('day_name')->toArray();
                    }

                    $responseData[$userId] = $openDays;
                }

                $this->responseData = $responseData;
                $this->responseMessage = __('messages.SuccessUpdateBusinessDay');
            } else {
                $this->responseDetails = 'unauthorizedAction';
                $this->responseMessage = 'This action is unauthorized.';
                $this->responseCode = 403;
            }
        } catch (ValidationException $exception) {
            $this->responseDetails = $exception->validator->getMessageBag();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorUpdateBusinessDay');
            $this->responseCode = $exception->status;
        } catch (\Exception $exception) {
            $this->responseDetails = $this->responseMessage ?: __('warnings.ErrorUpdateBusinessDay');
            $this->responseMessage = $exception->getMessage();
            $this->responseCode = $exception->getCode();
        }
        return $this->formattedResponse();
    }

    public function destroy(string $uuid): JsonResponse
    {
        try {
            User::where('uuid', $uuid)->delete();
        } catch (\Exception $exception) {
            return response()->json($exception, 400);
        }
        return response()->json(['succes' => __('warnings.SuccessDeleteUser')]);
    }

    /**
     * Récupère les information de l'utilisateur courant
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function current(Request $request)
    {
        try {
            $this->validate($request, [
                "withClient" => "boolean",
                "withProfile" => "boolean",
                "withSite" => "boolean",
                "withUserLeaveCounts" => "boolean",
                "withLeaveTypes" => "boolean",
                "withTags" => "boolean",
                "withOpenDays" => "boolean",
                "withFutureLeaves" => "boolean"
            ], $this->messages());
            Log::debug("this->currentUser");
            Log::debug($this->currentUser);

            $user = Auth::user();

            if ($request->query("withManagers")) {
                $user->load(array("managers" => function ($query) {
                    $query->select("users.*", "level");
                }));
            }
            // Si on veut le client en plus
            if ($request->query("withClient")) {
                $user->load("client");
            }
            // Si on veut le profil en plus
            if ($request->query("withProfile")) {
                $user->load("profile");
            }
            // Si on veut les tages des users
            if ($request->query("withTags")) {
                $user->load("tags");
            }
            // Si on veut le site en plus
            if ($request->query("withSite")) {
                $user->load("site");
            }
            // Si on veut les compteur de congés de l'utilisateur en plus
            if ($request->query("withUserLeaveCounts")) {
                $user->load("user_leave_counts.leave_type");
            }
            // Si on veut le type des leaves en plus
            if ($request->query("withLeaveTypes")) {
                $user->load("leaves.leave_type");
            }

            $user['client_days'] = $user->client->days->pluck('id')->toArray();

            $this->responseData = $user->toArray();
            $this->responseMessage = __('messages.SuccessGetCurrentUser');

            if ($request->query("withFutureLeaves")) {
                $this->responseData["futureLeaves"] = $this->getFutureLeaves($user->getKey());
            }
            // Si on veut les jours ouvrés de l'utilisateur en plus
            if ($request->query("withOpenDays")) {
                // Get user open days
                $this->responseData["days"] = $this->getUserOpenDays($user);
            }
        } catch (ValidationException $exception) {
            $this->responseMessage = __('warnings.ErrorValidationData');
            $this->responseDetails = $exception->validator->errors()->unique();
            $this->responseCode = $exception->status;
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetCurrentUser');
        }

        return $this->formattedResponse();
    }

    /**
     * @return JsonResponse
     * @throws GuzzleException
     */
    public function logout()
    {
        $jwt = \Illuminate\Support\Facades\Request::header('Authorization');
        try {
            $portal = \App\Models\Api\Application::where('slug', 'authprovider')->first();
            $client = new GuzzleClient(["base_uri" => "http://" . $portal->docker_name]);
            $data['headers'] = [
                "Authorization" => env("INTERNAL_AKT"),
                'User-Agent' => 'XefiApp Internal Com System/1.0',
                "fromApp" => env("APP_UUID")
            ];
            $data['json'] = [
                'jwt' => $jwt
            ];
            $client->request("POST", "/api/logout-from-app", $data);
        } catch (Exception $ex) {
            return response()->json($ex->getMessage(), 500);
        }
        return response()->json(['message' => __('messages.SuccessLogout')]);
    }
    #endregion

    /**
     * Récupération de tous les utilisateurs
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function indexInternal(Request $request): JsonResponse
    {
        try {
            $data = $this->validate($request, array_merge([
                "withLeave" => "boolean",
                "withStatus" => "boolean",
                "withLeaveType" => "boolean",
                "emails" => ['required'],
                "from" => "nullable|date|required_with:to",
                "to" => "nullable|date"
            ], $this->defaultIndexFilters), $this->messages());

            throw_if(empty($this->clientId), "Aucune JWT à étais envoyer!", "");
            $userModel = User::query()->whereIn("email", $data["emails"])->whereHas("site", function ($query) {
                $query->where("client_id", "=", $this->clientId);
            });
            // Si on veut le client en plus
            if ($data['withLeave']) {
                $userModel->with(['leaves' => function ($query) use ($data) {
                    if (isset($data['from']) && $data['from'] && isset($data['to']) && $data['to']) {
                        $query->where(function ($query) use ($data) {
                            $query->where('start_date', '<=', $data['from'])
                                ->where('end_date', '>=', $data['to']);
                        })
                            ->orWhere(function ($query) use ($data) {
                                $query->where('start_date', '>=', $data['from'])
                                    ->where('start_date', '<=', $data['to']);
                            })
                            ->orWhere(function ($query) use ($data) {
                                $query->where('end_date', '>=', $data['from'])
                                    ->where('end_date', '<=', $data['to']);
                            });
                    } elseif (isset($data['from']) && $data['from']) {
                        $query->where('start_date', '>=', $data['from'])
                            ->orWhere(function ($query) use ($data) {
                                $query->where('start_date', '<=', $data['from'])
                                    ->where('end_date', '<=', $data['from']);
                            });
                    }
                    isset($data['withStatus']) && $query->with('status');
                    isset($data['withLeaveType']) && $query->with('leave_type');
                }]);
            }

            // Pagination des données
            $this->responseData = ApiHelper::defaultModelBuilder($userModel, $request->get("orders", "id,asc"),
                $request->get("limit", null), $request->get("paginate", 0));
            $this->getResponse = true;
            $this->responseMessage = __('messages.SuccessGetUser');
        } catch (ValidationException $exception) {
            $this->responseMessage = __('warnings.ErrorValidationData');
            $this->responseDetails = $exception->validator->errors()->unique();
            $this->responseCode = $exception->status;
        } catch (Exception $exception) {
            $this->responseDetails = $exception->getMessage();
            $this->responseMessage = $this->responseMessage ?: __('warnings.ErrorGetUserFromApp');
            $this->responseCode = $exception->getCode();
        }

        return $this->formattedResponse();
    }

    private function getFutureLeaves($usersID)
    {
        $leaves = Leave::whereIn("status_id", Status::whereIn("tag", ["VALIDATED", "SUBMITTED"])->get("id")->toArray())
            ->where("user_id", $usersID)->get();
//        $transmittedLeaves = Leave::query()->whereIn("status_id", Status::whereIn("tag", ["TRANSMITTED"])->get("id")->toArray())
//            ->where("user_id", $usersID)->get();
        $userLeaveCounts = UserLeaveCount::query()
            ->crossJoin("leave_types", 'user_leave_counts.leave_type_id', '=', 'leave_types.id')
            ->where("user_id", $usersID)
            ->orderBy("leave_type_id")
            ->orderByDesc("is_last_year")
            ->orderBy("order_appearance")->get()
            ->map(function ($userLeaveCount) {
                $userLeaveCount->leave_type = LeaveType::query()->where("id", $userLeaveCount->leave_type_id)->first();
                return $userLeaveCount;
            });
        $rest = 0;
        $count = 0;
        $countLastYear = 0;
        $acquiredLAstYear = 0;
        $totalRest = 0;
        $restBalanceLastYear = 0;
        $temp = true;
        $initDate = "-06-01";
        $endDate = "-05-31";
        $year = Carbon::now()->format("Y");
        foreach ($userLeaveCounts as $userLeaveCount) {
            foreach ($leaves as $leave) {
                if ($leave->leave_type_id == $userLeaveCount->leave_type_id && $temp == true) {
                    // if($leave->start_date >= $year.$initDate){
                    $rest += $leave->duration;
                    // }
                }
            }

            if ($userLeaveCount->is_last_year == 1) {
                $acquiredLAstYear += $userLeaveCount->acquired;
                $restBalanceLastYear += $userLeaveCount->balance; // récupère la balance de n1
                if ($userLeaveCount->balance <= $rest) {
                    $userLeaveCount->futureLeaves = $userLeaveCount->balance;
                    $rest -= $userLeaveCount->balance;
                    $temp = false;
                } else {
                    $userLeaveCount->futureLeaves = $rest;
                    $rest -= $rest;
                    $temp = false;
                }
            } else {
                $userLeaveCount->futureLeaves = $rest;
                $rest -= $rest;
                $temp = true;
            }

            /**
             *
             */

//            if($userLeaveCount->is_last_year == 0){
//                foreach ($transmittedLeaves as $transmittedLeave){
//                    if ($transmittedLeave->leave_type_id == $userLeaveCount->leave_type_id) {
//                        if($transmittedLeave->updated_at->format("Y") <= Carbon::now()->format("Y")){
//                            if($transmittedLeave->updated_at->format("Y") == Carbon::now()->format("Y")){
//                                if($transmittedLeave->updated_at->format("Y-m-d") >= $year.$initDate)
//                                    $count += $transmittedLeave->duration;
//                            }else if($transmittedLeave->updated_at->format("Y") < Carbon::now()->format("Y")){
//                                if($transmittedLeave->updated_at->format("Y-m-d") >= $year-1 .$initDate){
//                                    $count += $transmittedLeave->duration;
//                                }else if($transmittedLeave->updated_at->format("Y-m-d") < $year-1 .$endDate && $transmittedLeave->updated_at->format("Y-m-d") > $year-2 .$initDate){ // 01/06/2019 < now < 31/05/2020
//                                    $countLastYear += $transmittedLeave->duration;
//                                }
//                            }
//                        }
//                    }
//                }
//
//                if($userLeaveCount->is_last_year == 0){
//                    $restLastYear = $acquiredLAstYear - $countLastYear;
//                    $balance = $userLeaveCount->balance + $restBalanceLastYear;
//                    $totalRest += $restLastYear + $userLeaveCount->acquired;
//                    if($totalRest - $count == $balance){
//                        $userLeaveCount->check = 1;
//                    }else{
//                        $userLeaveCount->check = 0;
//                    }
//                    $count = 0;
//                    $countLastYear = 0;
//                    $acquiredLAstYear = 0;
//                    $totalRest = 0;
//                    $restBalanceLastYear = 0;
//                }
//            }
        }
        return $userLeaveCounts;
    }

    public function updateFcmToken(\Orion\Http\Requests\Request $request, User $user = null)
    {
        $userFcm = $user ?? Auth::user();
        $token = $request->input('fcm_token') ?? $request->input('token');

        $userFcm?->update([
            'fcm_token' => $token,
        ]);

        return response()->json(['data' => $userFcm]);
    }
}
