<?php

namespace App\Http\Controllers\Api\v1;

class TestController extends Controller {

	/**
	 * @OA\Get(
	 *     path="/sample/{category}/things",
	 *     operationId="/sample/category/things",
	 *     tags={"yourtag"},
	 *     @OA\Parameter(
	 *         name="category",
	 *         in="path",
	 *         description="The category parameter in path",
	 *         required=true,
	 *         @OA\Schema(type="string")
	 *     ),
	 *     @OA\Parameter(
	 *         name="criteria",
	 *         in="query",
	 *         description="Some optional other parameter",
	 *         required=false,
	 *         @OA\Schema(type="string")
	 *     ),
	 *     @OA\Response(
	 *         response="200",
	 *         description="Returns some sample category things",
	 *         @OA\JsonContent()
	 *     ),
	 *     @OA\Response(
	 *         response="400",
	 *         description="Error: Bad request. When required parameters were not supplied.",
	 *     ),
	 * )
	 */
	public function index(){

	}
}