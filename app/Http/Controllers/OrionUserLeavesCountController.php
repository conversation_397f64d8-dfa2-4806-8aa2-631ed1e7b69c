<?php

namespace App\Http\Controllers;

use App\Models\Api\UserLeaveCount;
use Illuminate\Database\Eloquent\Builder;
use Orion\Concerns\DisableAuthorization;
use Orion\Http\Controllers\Controller;
use Orion\Concerns\DisablePagination;
use Orion\Http\Requests\Request;

class OrionUserLeavesCountController extends Controller
{
    use DisableAuthorization;
    use DisablePagination;

    protected $model = UserLeaveCount::class;


    protected function buildFetchQuery(Request $request, array $requestedRelations): Builder
    {
        $query = parent::buildFetchQuery($request, $requestedRelations);

        $query->select('user_leave_counts.*')
            ->join('leave_types as lt', 'lt.id', 'user_leave_counts.leave_type_id')
            ->where('lt.deleted_at', null)
            ->where('lt.is_active', true)
            ->where('lt.is_take_leave', true)
            ->orderBy('lt.order_appearance');

        return $query;
    }

    public function filterableBy(): array
    {
        return ['user_id', 'user.uuid'];
    }

    public function includes(): array
    {
        return ['leave_type', 'futureLeaves'];
    }
}
