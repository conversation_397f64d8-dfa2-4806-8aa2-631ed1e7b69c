<?php

namespace App\Http\Middleware;

use Illuminate\Http\Request;
use Closure;
use Illuminate\Support\Facades\Log;

class InternalCom
{

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if ($request->header("InternalKey") === env('INTERNAL_AKT')) {
            return $next($request);
        } else {
            Log::alert("Unauthenticated request to the Internal API");
            return response()->json(array("error" => "Forbidden"), 403);
        }
    }
}
