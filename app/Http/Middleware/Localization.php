<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;

class Localization extends Middleware
{
    public function handle($request, Closure $next, ...$guards)
    {
        if (Auth::guard('jwt')->user()) {
            App::setLocale(Auth::guard('jwt')->user()->preferredLocale());
        } elseif (isset($request->input()['language'])) {
            App::setLocale($request->input('language'));
        }

        return $next($request);
    }
}
