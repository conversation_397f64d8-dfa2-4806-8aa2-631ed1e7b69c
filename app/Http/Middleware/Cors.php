<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class Cors
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $origin = $request->headers->get("Origin");
        if ($origin !== null) {
            // Origin host: origin without http,https or port
            $origin_domain = parse_url($origin)['host'];

            // If there is a origin_host contains `.` remove its subdomains
            // meaning that we accept all subdomains of `APP_DOMAIN`
            if (strpos($origin_domain, '.') !== false) {
                preg_match("/(?P<domain>[a-z0-9][a-z0-9\-]{1,63}\.[a-z\.]{2,6})$/i", $origin_domain, $matches);
                $origin_domain = $matches['domain'];
            }

            $domains = explode(";",env('APP_DOMAIN'));
            $haveGoodDomain = true;
            foreach ($domains as $value){
                if($value == $origin_domain){
                    $haveGoodDomain = true;
                }
            }

            if (!$haveGoodDomain) {
                Log::alert("Cors violation. Origin : " . $origin);
                return response()->json("CORS Violation", 403);
            }

            if ($request->getMethod() == 'OPTIONS') {
                $response = response("OK", 200);
            } else {
                $response = $next($request);
            }
            $response->headers->set('Access-Control-Allow-Origin', $origin);
            $response->headers->set('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS,PATCH');
            $response->headers->set('Access-Control-Allow-Headers', '*');
            $response->headers->set('Access-Control-Allow-Credentials', 'true');
            $response->headers->set('Access-Control-Expose-Headers', 'x-fresh-jwt');

            return $response;
        }
        return $next($request);
    }
}
