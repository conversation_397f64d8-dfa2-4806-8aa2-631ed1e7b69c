<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class VerifyWebhookSignature
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure(Request): (Response|RedirectResponse) $next
     * @return Response|RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (
            !$this->validate($request->header('signature'), $request->getContent())
        ) {
            Log::error('This API route is protected by signature');
            abort(403, __('warnings.ApiProctectedBySignature'));
        }

        return $next($request);
    }

    protected function validate(string $signature, string $payload): bool
    {
        if (!$signature) {
            return false;
        }

        $signingSecret = env('INTERNAL_AKT');

        return hash_equals(
            $signature,
            hash_hmac('sha256', $payload, $signingSecret)
        );
    }
}
