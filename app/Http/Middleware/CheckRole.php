<?php

namespace App\Http\Middleware;

use App\Models\Api\Customer;
use Closure;
use Illuminate\Support\Facades\Request;
use Xefi\JWTAuth\Exceptions\JWTParsingException;

class CheckRole
{
    /**
     * Handle an incoming request.
     * @param $request
     * @param Closure $next
     * @param string $role
     * @return \Illuminate\Http\JsonResponse|mixed
     * @throws JWTParsingException
     */
    public function handle($request, Closure $next, string $role)
    {
        if ($role == 'customer') {
            $uuid = Request::header('customerUuid', null);
            $token = Request::header('customerToken', null);

            // Check if headers are not null
            if (empty($uuid) or empty($token)) {
                return response()->json(['message' => __('warnings.HeadersNotFound')], 400);
            }

            // Trying to find contact's customer
            $customer = Customer::where('uuid', '=', $uuid)->where('token', '=', $token)->first();
            if (empty($customer)) {
                return response()->json(['message' => __('warnings.CustomerNotFound')], 404);
            }
        }

        return $next($request);
    }
}
