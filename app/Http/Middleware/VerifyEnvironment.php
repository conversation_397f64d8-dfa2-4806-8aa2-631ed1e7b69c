<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class VerifyEnvironment
{
    /**
     * Handle an incoming request.
     *
     * @param Closure(Request): (Response|RedirectResponse) $next
     */
    public function handle(Request $request, Closure $next, ...$envs)
    {
        if (!in_array(env('APP_ENV'), $envs)) {
            abort(405, __('warnings.env.ActionIsNotAvailableInEnv', ['env' => env('APP_ENV')]));
        }

        return $next($request);
    }
}
