<?php

namespace App\Http\Middleware;

use App\Models\Api\User;
use App\Utils\Jwt\JWT;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Route;
use Xefi\JWTAuth\Exceptions\JWTParsingException;

class CheckProfile {
	/**
	 * Handle an incoming request.
	 *
	 * @param         $request
	 * @param Closure $next
	 * @param string  $profiles
	 *
	 * @return JsonResponse|mixed
	 * @throws JWTParsingException
	 */
	public function handle($request, Closure $next){
		try {
			// Trying to instantiate user"s JWT
			$jwt = new JWT(\request());
		} catch(JWTParsingException $exception){
			return response()->json(["message" => __('warnings.UserDontMatchJwt')], 500);
		}

		// Extract user"s UUID from JWT
		$userUuid = $jwt->userId;
		// Verify if uuid isn"t null
		if(empty($userUuid)){
			return response()->json(["message" => __('warnings.CantFindUuid')], 404);
		}
		// Find user thanks to Uuid and check if exists
		$user = User::where("uuid", "=", $userUuid)->with("profile")->first();
		if(empty($user)){
			return response()->json(["message" => __('warnings.UserDontExist')], 404);
		}

		//Get route name from request
		$routeName = Route::currentRouteName();
		//Check if the user is allowed to access the route
		$allowed = User::where("uuid", $userUuid)
			->join("profiles", "profiles.id", "=", "users.profile_id")
			->leftjoin("route_profile as rp", "rp.profile_id", "=", "profiles.id")
			->leftjoin("routes", "routes.id", "=", "rp.route_id")
			->leftjoin("profile_route_group as prg", "prg.profile_id", "=", "profiles.id")
			->leftjoin("route_groups", "route_groups.id", "=", "prg.route_group_id")
			->leftjoin("route_group_route as rgr", "rgr.route_group_id", "=", "route_groups.id")
			->leftjoin("routes as r", "r.id", "=", "rgr.route_id")
			->where("routes.name", $routeName)
			->count();

		// Checking user found profile
		if(!$allowed){
			return response()->json(["message" => __('warnings.CantDoIt')], 403);
		}

		return $next($request);
	}
}
