<?php

namespace App\Http\Requests\Statistics;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class LeavesPerUsersRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'search' => ['nullable', 'string'],
            'site' => ['nullable', 'uuid'],
            'user_id' => ['nullable', 'uuid'],
            'leave_type_ids' => ['array'],
            'leave_type_ids.*' => ['nullable', 'integer', 'exists:leave_types,id'],
            'period' => ['array'],
            'period.start_date' => ['required_with:period.end_date', 'date'],
            'period.end_date' => ['required_with:period.start_date', 'date'],
            'sorts.*' => ['array'],
            'sorts.*.field' => ['nullable', 'required_with:sorts.*.direction', 'string'],
            'sorts.*.direction' => ['nullable', 'required_with:sorts.*.field', 'string', Rule::in(['asc', 'desc'])],
        ];
    }
}
