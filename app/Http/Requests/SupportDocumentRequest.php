<?php

namespace App\Http\Requests;

use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;
use Orion\Http\Requests\Request;

class SupportDocumentRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function commonRules(): array
    {
        return [
            'name' => ['string', 'required'],
            'creator_id' => ['nullable', 'exists:users,id'],
            'media.*.file' => [
                File::types(['pdf', 'doc', 'docx', 'png', 'jpeg']),
            ],
            'media.*.collection' => [
                Rule::in(['support_document']),
            ],
        ];
    }
}
