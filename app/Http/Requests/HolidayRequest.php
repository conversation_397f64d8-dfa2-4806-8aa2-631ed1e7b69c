<?php

namespace App\Http\Requests;

use App\Models\Api\Holiday;
use App\Models\Api\Leave;
use Illuminate\Support\Facades\DB;
use Orion\Http\Requests\Request;

class HolidayRequest extends Request
{
    public function storeRules(): array
    {
        return [
            'name' => ['required', 'string'],
            'date' => [
                'required',
                'date',
                function (string $attribute, $value, $fail) {
                    if (Leave::query()->where('start_date', '<=', $value)->where('end_date', '>=', $value)->exists()) {
                        $fail(__('validation.custom.leave_already_taken'));
                    }
                    $clientId = $this->input('client_id');
                    $existingHolidayQuery = DB::table('holidays')->where('date', $value);
                    if ($clientId) {
                        $existingHolidayQuery->join('client_holiday', function ($join) use ($clientId) {
                            $join->on('client_holiday.holiday_id', '=', 'holidays.id')
                                ->where('client_holiday.client_id', '=', $clientId);
                        });
                    }
                    if ($existingHolidayQuery->exists()) {
                        $fail(__('validation.custom.holiday_already_exists_for_date_and_client'));
                    }
                }
            ],
            'client_id' => ['required_without:site_id', 'exists:clients,id'],
            'site_id' => ['prohibits:client_id', 'exists:sites,id'],
        ];
    }
}
