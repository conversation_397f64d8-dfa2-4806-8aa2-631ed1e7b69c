<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class LeavesByTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'site' => ['nullable', 'uuid'],
            'user_id' => ['nullable', 'uuid'],
            'leave_type_id' => ['nullable', 'integer', 'exists:leave_types,id'],
            'start_date' => ['nullable', 'date', 'required_with:end_date'],
            'end_date' => ['nullable', 'date', 'required_with:start_date'],
            'period' => [
                'nullable',
                'string',
                'prohibits:start_date,end_date',
                'required_without_all:start_date,end_date',
                Rule::in(['year', 'month', 'week'])
            ],
        ];
    }
}
