<?php

namespace App\Http\Requests;

use Illuminate\Validation\Rule;
use Orion\Http\Requests\Request;

class MediaRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function commonRules(): array
    {
        return [
            'name' => ['string', 'required'],
            'collection_name' => ['required', Rule::in(['support_document'])],
            'model_type' => ['required', Rule::in(['App\Models\SupportDocument'])],
        ];
    }
}
