<?php

namespace App\Models\Api;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @method static find($profile_id)
 * @method static create(string[] $profile)
 */
class Profile extends Model {
	use SoftDeletes;

	protected $guarded = [];

	/**
	 * @return HasMany
	 */
	public function users(){
		return $this->hasMany(User::class);
	}

	/**
	 * @return BelongsToMany
	 */
	public function routes(){
		return $this->belongsToMany(Route::class, "route_profile", "profile_id", "route_id");
	}

	/**
	 * @return BelongsToMany
	 */
	public function route_groups(){
		return $this->belongsToMany(RouteGroup::class, "profile_route_group", "profile_id", "route_group_id");
	}
}
