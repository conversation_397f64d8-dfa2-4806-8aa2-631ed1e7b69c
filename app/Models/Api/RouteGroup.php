<?php


namespace App\Models\Api;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class RouteGroup extends Model {
	use SoftDeletes, HasFactory;

	protected $guarded = [];

	/**
	 * @return BelongsToMany
	 */
	public function profiles(){
		return $this->belongsToMany(Profile::class, "profile_route_group", "route_group_id", "profile_id");
	}

	/**
	 * @return BelongsToMany
	 */
	public function routes(){
		return $this->belongsToMany(Route::class, "route_group_route", "route_group_id", "route_id");
	}

}
