<?php

namespace App\Models\Api;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @method static create(string[] $status)
 * @method static find($status_id)
 * @method static where(string $string, string $string1, string $string2)
 * @method static whereIn(string $string, false|string[] $explode)
 */
class Status extends Model {
	use SoftDeletes;

	protected $guarded = [];

	/**
	 * @return HasMany
	 */
	public function leaves(){
		return $this->hasMany(Leave::class);
	}

	/**
	 * @return HasMany
	 */
	public function histories(){
		return $this->hasMany(History::class);
	}

	/**
	 * @return HasMany
	 */
	public function alerts(){
		return $this->hasMany(Alert::class);
	}
}
