<?php

namespace App\Models\Api;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int    id
 * @property int    client_id
 * @property string name
 * @method static find(int $id)
 * @method static create(array $siteData)
 * @method static where(string $string, string $string1, string $string2)
 */
class Documentation extends Model {
	use SoftDeletes, HasFactory;

	protected $guarded = [];

    /**
     * @return BelongsToMany
     */
	public function sites(): BelongsToMany
    {
		return $this->belongsToMany(Site::class);
	}

}
