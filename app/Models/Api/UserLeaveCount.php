<?php


namespace App\Models\Api;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @method static create(array $user_leave_count)
 * @method static where(string $key, string $sign, string|int $value)
 * @property mixed            user_id
 * @property int|mixed|string leave_type_id
 */
class UserLeaveCount extends Model {
	use SoftDeletes, HasFactory;

	protected $guarded = [];

    protected $casts = [
        'is_last_year' => 'boolean'
    ];


    public function getBalanceAttribute($value)
    {
        return (float)number_format($value, 2);
    }

	/**
	 * @return BelongsTo
	 */
	public function user(){
		return $this->belongsTo(User::class);
	}

    /**
     * @return BelongsTo
     */
    public function leave_type(){
        return $this->belongsTo(LeaveType::class);
    }
}
