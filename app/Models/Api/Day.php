<?php


namespace App\Models\Api;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Day extends Model
{
    public $timestamps = false;
    protected $guarded = [];

    /**
     * @return BelongsToMany
     */
    public function clients(){
        return $this->belongsToMany(Client::class, 'client_days', 'day_id', 'client_id');
    }

    /**
     * @return BelongsToMany
     */
    public function sites(){
        return $this->belongsToMany(Site::class, 'site_days');
    }

    /**
     * @return BelongsToMany
     */
    public function users(){
        return $this->belongsToMany(User::class, 'user_days', 'day_id', 'user_id');
    }
}
