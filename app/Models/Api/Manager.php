<?php

namespace App\Models\Api;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\Pivot;


/**
 * @property int level
 * @property mixed id
 * @property mixed manager_id
 * @method static where(string $string, string $string1, string $string2)
 * @method static create(int[] $manager)
 * @method static find(int $id)
 */
class Manager extends Pivot
{
    use HasFactory;

    protected $table = 'managers';

    protected $guarded = [];

    /**
     * @return BelongsTo
     */
    public function managed(): BelongsTo
    {
        return $this->belongsTo(User::class, 'id');
    }

    /**
     * @return BelongsTo
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    /**
     * @return HasMany
     */
    public function leaves()
    {
        return $this->hasMany(Leave::class, "last_updater_id", "id");
    }

    /**
     * @return HasMany
     */
    public function user_leaves()
    {
        return $this->hasMany(Leave::class, "user_id", "id");
    }

    /**
     * @return HasMany
     */
    public function user_leave_counts()
    {
        return $this->hasMany(UserLeaveCount::class, "user_id", "user_id");
    }

    /**
     * Les tags auxquels un utilisateur à été attaché
     * @return HasManyThrough
     */
    public function tags(): HasManyThrough
    {
        return $this->hasManyThrough(
            Tag::class,
            UserTag::class,
            'user_id',
            'id',
            'user_id',
            'tag_id',
        );
    }
}
