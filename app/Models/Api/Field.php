<?php

namespace App\Models\Api;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Field extends Model {
	use SoftDeletes, HasFactory;

	protected $guarded = [];

    protected $casts = [
        'is_nullable' => 'boolean',
        'is_default' => 'boolean'
    ];

	/**
	 * @return BelongsTo
	 */
	public function section(){
		return $this->belongsTo(Section::class);
	}
}
