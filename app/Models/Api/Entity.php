<?php

namespace App\Models\Api;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Entity extends Model {
	use SoftDeletes, HasFactory;

	protected $guarded = [];

    protected $casts = [
        'hidden' => 'boolean',
        'locked' => 'boolean',
        'is_hidden_annotation' => 'boolean',
    ];

	/**
	 * @return BelongsTo
	 */
	public function client(){
		return $this->belongsTo(Client::class);
	}

	/**
	 * @return HasMany
	 */
	public function sections(){
		return $this->hasMany(Section::class);
	}
}
