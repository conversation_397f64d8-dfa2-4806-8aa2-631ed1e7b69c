<?php

namespace App\Models\Api;

use App\Facades\SynchronizeData;
use App\Lib\Tools;
use App\Utils\DefaultValues\DefaultUserLeaveCounts;
use Illuminate\Contracts\Translation\HasLocalePreference;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Log;
use Znck\Eloquent\Traits\BelongsToThrough;

/**
 * Class User
 * @property int id
 * @property int client_id
 * @package App\Models\Api
 * @method static find(int $id)
 * @method static where(string $string, string $string1, int $clientId)
 * @method static create(array[] $users)
 */
class User extends Authenticatable implements HasLocalePreference
{
    use SoftDeletes, HasFactory, BelongsToThrough, Notifiable;

    protected $guarded = [];

    protected $casts = [
        'can_receive_mails' => 'boolean',
        'can_receive_absentees_reminder_mails' => 'boolean',
    ];

    /**
     * @return \Znck\Eloquent\Relations\BelongsToThrough
     */
    public function client()
    {
        return $this->belongsToThrough(Client::class, Site::class);
    }

    /**
     * @return BelongsTo
     */
    public function profile()
    {
        return $this->belongsTo(Profile::class);
    }

    public function directManager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    /**
     * @return BelongsToMany
     */
    public function managers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'managers', 'managed_id', 'manager_id')
            ->using(Manager::class)
            ->withPivot('level')
            ->withTimestamps();
    }

    /**
     * @return BelongsToMany
     */
    public function validators(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'managers', 'managed_id', 'manager_id')
            ->join('sites', 'sites.id', 'users.site_id')
            ->join('clients', 'clients.id', 'sites.client_id')
            ->using(Manager::class)
            ->withPivot('level')
            ->withTimestamps()
            ->whereColumn('level', '<=', 'clients.number_managers_can_validate');
    }

    /**
     * @return HasMany
     */
    public function directManaged(): HasMany
    {
        return $this->hasMany(User::class, 'manager_id');
    }

    /**
     * @return BelongsToMany
     */
    public function managed(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'managers', 'manager_id', 'managed_id')
            ->using(Manager::class)
            ->withPivot('level')
            ->withTimestamps();
    }

    public function directManagerByLevel($level)
    {
        if ($level === 1) {
            return $this->directManager;
        }

        if ($this->directManager) {
            return $this->directManager->directManagerByLevel($level - 1);
        }

        return null;
    }

    private function findAllManagedUsers(User $user, array &$managedUsers): void
    {
        $directlyManaged = $user->directManaged()->pluck('id')->toArray();

        foreach ($directlyManaged as $managedId) {
            if (!in_array($managedId, $managedUsers)) {
                $managedUsers[] = $managedId;
                $this->findAllManagedUsers(User::find($managedId), $managedUsers);
            }
        }
    }


    /**
     * @return BelongsTo
     */
    public function site()
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * @return HasMany
     */
    public function leaves()
    {
        return $this->hasMany(Leave::class);
    }

    /**
     * @return HasMany
     */
    public function creator()
    {
        return $this->hasMany(Leave::class, 'creator_id');
    }

    /**
     * @return HasMany
     */
    public function histories()
    {
        return $this->hasMany(History::class);
    }

    /**
     * @return HasMany
     */
    public function export_histories()
    {
        return $this->hasMany(ExportHistory::class);
    }

    /**
     * @return HasMany
     */
    public function alerts()
    {
        return $this->hasMany(Alert::class, "user_leave_id", "id");
    }

    /**
     * @return HasMany
     */
    public function user_leave_counts()
    {
        return $this->hasMany(UserLeaveCount::class);
    }

    /**
     * tags assigné à l'utilisateur
     * @return HasMany
     */
    public function creator_tags()
    {
        return $this->hasMany(Tag::class);
    }

    /**
     * tags assigné à l'utilisateur
     * @return BelongsToMany
     */
    public function tags()
    {
        return $this->belongsToMany(Tag::class, 'user_tags', 'user_id', 'tag_id')->withTimestamps();
    }

    /**
     * @return BelongsToMany
     */
    public function days()
    {
        return $this->belongsToMany(Day::class, 'user_days', 'user_id', 'day_id');
    }

    /**
     * Scope a query to include all users managed directly and indirectly by current user.
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeViewDirectManagedUsers(Builder $query): Builder
    {
        $currentUser = Tools::getCurrentUserWithUuid();

        $query->join('managers', function ($join) use ($currentUser) {
            $join->on('managers.managed_id', 'users.id')
                ->where('managers.manager_id', $currentUser->id)
                ->where('managers.level', '<=', $currentUser->site->client->number_managers_can_validate)
                ->when(request()->has('withCurrentUser'), function ($query) use ($currentUser) {
                    $query->orWhere('users.id', $currentUser->id);
                });
        });

        return $query->groupBy('users.id');
    }

    /**
     * Scope a query to include all users managed directly and indirectly by current user.
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeViewUndirectManagedUsers(Builder $query): Builder
    {
        $currentUser = Tools::getCurrentUserWithUuid();

        $query->join('managers', function ($join) use ($currentUser) {
            $join->on('managers.managed_id', 'users.id')
                ->where('managers.manager_id', $currentUser->id)
                ->when(request()->has('withCurrentUser'), function ($query) use ($currentUser) {
                    $query->orWhere('users.id', $currentUser->id);
                });
        });

        return $query->groupBy('users.id');
    }

    public function preferredLocale(): ?string
    {
        return $this->language;
    }

    public function setManager(User $manager = null)
    {
        if ($manager) {
            $this->directManager()->associate($manager);
        } else {
            Log::alert('Manager does not exists on Conges');
            $this->manager_id = null;
        }
    }

    public function directors()
    {
        return $this->belongsToMany(User::class, 'director_user', 'user_id', 'director_id');
    }

    public function usersDirected()
    {
        return $this->belongsToMany(User::class, 'director_user', 'director_id', 'user_id');
    }

    public function canSeeStatistics(): bool
    {
        return $this->profile()->whereIn('label', ['ADMINISTRATEUR', 'ADMINISTRATEURMANAGER', 'DIRECTOR'])->exists() || $this->managed()->exists();
    }

    /**
     * Récupère le token FCM de l'utilisateur.
     *
     * @return string|null
     */
    public function routeNotificationForFcm()
    {
        // Retourner le token FCM stocké pour cet utilisateur
        return $this->fcm_token;
    }
}
