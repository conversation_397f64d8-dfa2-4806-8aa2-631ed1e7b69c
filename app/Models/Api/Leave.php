<?php

namespace App\Models\Api;

use App\Lib\Holidays;
use App\Lib\Tools;
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Prunable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

/**
 * @method static first()
 * @method static orderBy(string $string, string $string1)
 * @method static find($leave_id)
 * @method static create(array $array)
 * @property mixed id
 * @property mixed user_id
 * @property mixed creator_id
 * @property mixed status_id
 * @property mixed leave_type_id
 * @property mixed leave_type_sub_family_id
 * @property mixed|void manager_id
 * @property mixed current_validator_level
 * @property Carbon start_date
 * @property Carbon end_date
 * @property mixed duration
 * @property mixed attachment_name
 * @property mixed attachment_path
 * @property mixed comment
 * @property mixed n
 * @property mixed n1
 */
class Leave extends Model
{
    use SoftDeletes, HasFactory, Prunable;

    protected $guarded = [];

    protected $casts = [
        'is_to_cancel' => 'boolean',
        'leave_days_distribution' => 'collection',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    /**
     * @return BelongsTo
     */
    public function leave_type()
    {
        return $this->belongsTo(LeaveType::class)->withTrashed();
    }

    /**
     * @return BelongsTo
     */
    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    /**
     * @return BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class)->withTrashed();
    }

    /**
     * @return BelongsTo
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id')->withTrashed();
    }

    /**
     * @return belongsTo
     */
    public function leave_type_sub_family()
    {
        return $this->belongsTo(LeaveTypeSubFamily::class);
    }

    /**
     * @return HasMany
     */
    public function histories()
    {
        return $this->hasMany(History::class);
    }

    /**
     * @return HasMany
     */
    public function alerts()
    {
        return $this->hasMany(Alert::class);
    }

    public function lastUpdater()
    {
        return $this->belongsTo(User::class, 'last_updater_id');
    }

    /**
     * Scope a query to only include leaves to validate from users managed.
     *
     * @param Builder $query
     * @param int $userID
     * @return Builder
     */
    public function scopeWaitingForUserValidation(Builder $query, $user): Builder
    {
        $user = Str::isUuid($user) ? User::query()->where('uuid', $user)->first()->getKey() : $user;

        return $query
            ->join('managers as manager_waiting_for_validation', function ($join) use ($user) {
                $join->on('manager_waiting_for_validation.managed_id', 'leaves.user_id')
                    ->where('manager_waiting_for_validation.manager_id', $user)
                    ->whereColumn('manager_waiting_for_validation.level', 'leaves.current_validator_level');
            })
            ->join('statuses as leaves_status', function ($join) {
                $join->on('leaves_status.id', 'leaves.status_id')
                    ->where('leaves_status.tag', 'SUBMITTED');
            });
    }

    /**
     * Scope a query to only include leaves to cancel from users managed.
     *
     * @param Builder $query
     * @param int $userID
     * @return Builder
     */
    public function scopeWaitingForUserCancelation(Builder $query): Builder
    {
        $currentUser = Tools::getCurrentUserWithUuid();

        return $query
            ->join('managers as manager_waiting_for_validation', function ($join) use ($currentUser) {
                $join->on('manager_waiting_for_validation.managed_id', 'leaves.user_id')
                    ->where('manager_waiting_for_validation.manager_id', $currentUser->id)
                    ->where('manager_waiting_for_validation.level', 1);
            })
            ->join('statuses as leaves_status', function ($join) {
                $join->on('leaves_status.id', 'leaves.status_id')
                    ->where('leaves_status.tag', 'SUBMITTED_TO_CANCELLATION');
            });
    }

    /**
     * Scope a query to only include popular users.
     *
     * @param Builder $query
     * @param int $userID
     * @return Builder
     */
    public function scopeWaitingForUserValidationOnDifferentLevel(Builder $query, int $user): Builder
    {
        return $query
            ->join('managers as manager_waiting_for_validation', function ($join) use ($user) {
                $join->on('manager_waiting_for_validation.managed_id', 'leaves.user_id')
                    ->where('manager_waiting_for_validation.manager_id', $user)
                    ->whereColumn('manager_waiting_for_validation.level', '>=', 'leaves.current_validator_level');
            })
            ->join('statuses as leaves_status', function ($join) {
                $join->on('leaves_status.id', 'leaves.status_id')
                    ->where('leaves_status.tag', 'SUBMITTED');
            });
    }

    public function scopeTagsUserLeave(Builder $query): Builder
    {
        $user = Auth::user();
        if (in_array(strtolower($user->profile->label), ['administrateur', 'administrateurmanager'])) {
            return $query
                ->join('user_tags as ut', 'ut.user_id', 'leaves.user_id');
        } else {
            return $query
                ->leftJoin('user_tags as ut', 'ut.user_id', 'leaves.user_id')
                ->leftJoin('tags as t', 't.id', 'ut.tag_id')
                ->where('t.user_id', $user->getKey());
        }
    }

    /**
     * Scope a query to only include popular users.
     *
     * @param Builder $query
     * @param $user
     * @return Builder
     */
    public function scopeAlreadyTreatedByUser(Builder $query, $user): Builder
    {
        $user = Str::isUuid($user) ? User::query()->where('uuid', $user)->first()->getKey() : $user;

        return $query
            ->join('managers as manager_waiting_for_validation', function ($join) use ($user) {
                $join->on('manager_waiting_for_validation.managed_id', 'leaves.user_id')
                    ->where('manager_waiting_for_validation.manager_id', $user)
                    ->whereColumn('manager_waiting_for_validation.level', '<', 'leaves.current_validator_level');
            })
            ->join('statuses as leaves_status', function ($join) {
                $join->on('leaves_status.id', 'leaves.status_id')
                    ->whereIn('leaves_status.tag', ['REFUSED', 'VALIDATED', 'SUBMITTED', 'CANCELED', 'TRANSMITTED', 'SUBMITTED_TO_CANCELLATION']);
            });
    }

    /**
     * Scope a query to only include popular users.
     *
     * @param Builder $query
     * @param $user
     * @return Builder
     */
    public function scopeWeekActivity(Builder $query, $user): Builder
    {
        return $query
            ->join('managers as manager_waiting_for_validation', function ($join) use ($user) {
                $join->on('manager_waiting_for_validation.managed_id', 'leaves.user_id')
                    ->where('manager_waiting_for_validation.manager_id', $user);
            })
            ->join('statuses as leaves_status', function ($join) {
                $join->on('leaves_status.id', 'leaves.status_id')
                    ->whereNotIn('leaves_status.tag', ['REFUSED', 'CANCELED', 'SUBMITTED_TO_CANCELLATION']);
            });
    }

    /**
     * Scope a query to only include leaves to validate from users managed.
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopePlanningMobile(Builder $query): Builder
    {
        if (!in_array(strtolower(Auth::user()->profile->label), ['administrateurmanager', 'standardmanager', 'director'])) {
            return $query;
        }

        return $query
            ->join('managers as manager_waiting_for_validation', function ($join) {
                $join->on('manager_waiting_for_validation.managed_id', 'leaves.user_id')
                    ->where('manager_waiting_for_validation.manager_id', Auth::user()->getKey())
                    ->whereColumn('manager_waiting_for_validation.level', '<=', 'leaves.current_validator_level');
            })
            ->join('statuses as leaves_status', function ($join) {
                $join->on('leaves_status.id', 'leaves.status_id')
                    ->whereIn('leaves_status.tag', ['VALIDATED', 'SUBMITTED','TRANSMITTED']);
            });
    }

    /**
     * Get the prunable model query.
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function prunable()
    {
        return static::whereRelation('user', 'deleted_at', '<', now()->subDays(180));
    }

    public function managerToValidate()
    {
        return $this->user->managers()->wherePivot('level', $this->current_validator_level);
    }

    public function leaveDaysDistribution(int $userId): void
    {
        $leaveOwner = User::find($userId);
        $daysDistribution = [];

        $holidays = Holidays::carbonHolidays($this->start_date, $this->end_date, $leaveOwner->site->country_alpha, $leaveOwner->site->client);

        $openDays = Day::select('days.*')
            ->join('client_days', 'client_days.day_id', 'days.id')
            ->where('client_id', $leaveOwner->site->client_id)
            ->get()
            ->pluck('id')
            ->toArray();

        $carbonPeriod = CarbonPeriod::create(Carbon::createFromFormat('Y-m-d H:i:s', $this->start_date), '1 day', Carbon::createFromFormat('Y-m-d H:i:s', $this->end_date));

        $carbonPeriod->forEach(function (Carbon $date) use (&$daysDistribution, $leaveOwner, $holidays, $openDays) {
            if (in_array($date->dayOfWeekIso, $openDays) && !in_array($date, $holidays)) {
                $daysDistribution[$date->year][] = [
                    'month' => $date->format('m'),
                    'week' => $date->isoWeek,
                    'day' => $date->day,
                    'dayName' => $date->locale($leaveOwner->preferredLocale())->dayName
                ];
            }
        });

        $this->leave_days_distribution = $daysDistribution;
    }

    /**
     * Permet de calculer la durée exacte des congés
     * @param string $startDate
     * @param string $endDate
     * @param int $userId
     * @return float
     * @throws Exception
     */
    public function exactDuration(string $startDate, string $endDate, int $userId): float
    {
        $leaveOwner = User::withTrashed()->find($userId);

        $holidays = Holidays::carbonHolidays($startDate, $endDate, $leaveOwner->site()->withTrashed()->first()->country_alpha, $leaveOwner->site()->withTrashed()->first()->client);

        $openDays = Day::select('days.*')
            ->join('client_days', 'client_days.day_id', 'days.id')
            ->where('client_id', $leaveOwner->site()->withTrashed()->first()->client_id)
            ->get()
            ->pluck('id')
            ->toArray();

        $carbonPeriod = CarbonPeriod::create(Carbon::createFromFormat('Y-m-d H:i:s', $startDate), '1 day', Carbon::createFromFormat('Y-m-d H:i:s', $endDate));

        $duration = 0;
        foreach ($carbonPeriod as $date) {
            if (in_array($date->dayOfWeekIso, $openDays) && !in_array($date, $holidays)) {
                $duration++;
            }
        }

        return $duration;
    }
}
