<?php

namespace App\Models\Api;

use App\Utils\DefaultValues\DefaultLeaveTypes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @method static find(int $id)
 * @property mixed validation_scheme
 */
class Client extends Model
{
    use SoftDeletes, HasFactory;

    protected $guarded = [];
    protected $casts = [
        'count_public_holidays' => 'boolean',
        'is_pentecost' => 'boolean',
        'is_allowed_to_modify_open_days' => 'boolean',
        'client_type_id'
    ];

    /**
     * @return HasMany
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * @return HasMany
     */
    public function sites()
    {
        return $this->hasMany(Site::class);
    }

    /**
     * @return HasMany
     */
    public function entities()
    {
        return $this->hasMany(Entity::class);
    }

    /**
     * @return HasMany
     */
    public function leave_types()
    {
        return $this->hasMany(LeaveType::class);
    }

    /**
     * @return BelongsToMany
     */
    public function days()
    {
        return $this->belongsToMany(Day::class, 'client_days', 'client_id', 'day_id');
    }

    public function holidays(): BelongsToMany
    {
        return $this->belongsToMany(Holiday::class, 'client_holiday');
    }
}
