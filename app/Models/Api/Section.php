<?php

namespace App\Models\Api;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Section extends Model {
	use SoftDeletes, HasFactory;

	protected $guarded = [];

	/**
	 * @return BelongsTo
	 */
	public function entity(){
		return $this->belongsTo(Entity::class);
	}

	/**
	 * @return HasMany
	 */
	public function lookups(){
		return $this->hasMany(LookupField::class);
	}

	/**
	 * @return HasMany
	 */
	public function fields(){
		return $this->hasMany(Field::class);
	}
}
