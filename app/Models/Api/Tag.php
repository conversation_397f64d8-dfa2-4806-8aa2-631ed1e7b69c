<?php

namespace App\Models\Api;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Prunable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Tag extends Model {

	use SoftDeletes, HasFactory, Prunable;

	protected $guarded = [];

	/**
	 * @return BelongsTo
     */
	public function user(): BelongsTo
    {
		return $this->belongsTo(User::class);
	}

    /**
     * @return BelongsToMany
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_tags', 'tag_id', 'user_id');
    }

    public function prunable()
    {
        return static::select('tags.*')->join('users', 'users.id', 'tags.user_id')->where('users.deleted_at', '<=', Carbon::now()->subWeek());
    }

    public function scopeWithUsersOnly(Builder $query)
    {
        return $query->whereHas('users');
    }
}
