<?php

namespace App\Models\Api;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int|mixed    leave_id
 * @property int|mixed    status_id
 * @property int|mixed    user_id
 * @property mixed|string reason
 */
class History extends Model {
	use SoftDeletes;

	protected $guarded = [];

	/**
	 * @return BelongsTo
	 */
	public function leave(){
		return $this->belongsTo(Leave::class);
	}

	/**
	 * @return BelongsTo
	 */
	public function user(){
		return $this->belongsTo(User::class)->withTrashed();
	}

	/**
	 * @return BelongsTo
	 */
	public function status(){
		return $this->belongsTo(Status::class);
	}
}
