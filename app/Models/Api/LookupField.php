<?php

namespace App\Models\Api;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class LookupField extends Model {
	use SoftDeletes;

	protected $guarded = [];

    protected $casts = [
        'is_nullable' => 'boolean'
    ];

	/**
	 * @return BelongsTo
	 */
	public function section(){
		return $this->belongsTo(Section::class);
	}
}
