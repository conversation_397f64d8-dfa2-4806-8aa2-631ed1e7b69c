<?php


namespace App\Models\Api;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @method static create(array $leaveTypeSubFamily)
 * @method static find($id)
 */
class LeaveTypeSubFamily extends Model {
	use SoftDeletes, HasFactory;

	protected $guarded = [];

	/**
	 * @return BelongsTo
     */
	public function leave_type(){
		return $this->belongsTo(LeaveType::class);
	}
}
