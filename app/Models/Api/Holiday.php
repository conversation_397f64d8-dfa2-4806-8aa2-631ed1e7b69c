<?php

namespace App\Models\Api;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Holiday extends Model
{
    use HasFactory;

    protected $fillable = [
        'country_code', 'name', 'year', 'date', 'is_manual'
    ];

    protected $casts = [
        'date' => 'datetime:Y-m-d',
        'is_manual' => 'boolean'
    ];

    public function clients(): BelongsToMany
    {
        return $this->belongsToMany(Client::class, 'client_holiday');
    }

    public function sites(): BelongsToMany
    {
        return $this->belongsToMany(Site::class, 'site_holiday');
    }
}
