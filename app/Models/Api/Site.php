<?php

namespace App\Models\Api;

use App\Facades\SynchronizeData;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int id
 * @property int client_id
 * @property string name
 * @method static find(int $id)
 * @method static create(array $siteData)
 * @method static where(string $field, string $operator, string $value)
 */
class Site extends Model
{
    use SoftDeletes, HasFactory;

    protected $guarded = [];

    /**
     * @return BelongsTo
     */
    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * @return HasMany
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * @return BelongsToMany
     */
    public function documentations(): BelongsToMany
    {
        return $this->belongsToMany(SupportDocument::class)
            ->using(SiteDocument::class);
    }

    /**
     * @return BelongsToMany
     */
    public function days()
    {
        return $this->belongsToMany(Day::class, 'site_days');
    }

    /**
     * @return HasMany
     */
    public function leave_types()
    {
        return $this->hasMany(LeaveType::class);
    }

    public function scopeWithDifferentClientSettings(Builder $query): Builder
    {
        return $query->where(function (Builder $query) {
            $query->whereHas('leave_types')
                ->orWhereHas('days')
                ->orWhereHas('holidays');
        });
    }

    public function holidays(): BelongsToMany
    {
        return $this->belongsToMany(Holiday::class, 'site_holiday');
    }

    public function scopeWithoutDifferentClientSettings(Builder $query): Builder
    {
        return $query->where(function (Builder $query) {
            $query->whereDoesntHave('leave_types')
                ->orWhereDoesntHave('days')
                ->orWhereDoesntHave('holidays');
        });
    }
}
