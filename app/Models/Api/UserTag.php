<?php

namespace App\Models\Api;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Znck\Eloquent\Traits\BelongsToThrough;

class UserTag extends Model {
    use BelongsToThrough;

	protected $guarded = [];

    public function user(): HasOne
    {
        return $this->hasOne(User::class);
    }

    public function Tag(): HasOne
    {
        return $this->hasOne(Tag::class);
    }
}
