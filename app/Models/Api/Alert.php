<?php

namespace App\Models\Api;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int|mixed    user_id
 * @property int|mixed    leave_id
 * @property int|mixed    status_id
 * @property int|mixed    user_leave_id
 * @property mixed|string title
 * @property mixed|string body
 * @property mixed|string status
 */
class Alert extends Model {
	use SoftDeletes, HasFactory;

	protected $guarded = [];

	/**
	 * @return BelongsTo
	 */
	public function leave(){
		return $this->belongsTo(Leave::class);
	}

	/**
	 * @return BelongsTo
	 */
	public function status(){
		return $this->belongsTo(Status::class);
	}

	/**
	 * @return BelongsTo
	 */
	public function user(){
		return $this->belongsTo(User::class);
	}

	/**
	 * @return BelongsTo
	 */
	public function user_leave_id(){
		return $this->belongsTo(User::class, "id", "user_leave_id");
	}
}
