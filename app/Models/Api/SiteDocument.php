<?php

namespace App\Models\Api;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

class SiteDocument extends Pivot
{
    use HasFactory;

    protected $table = 'site_document';

    public function sites(): BelongsTo
    {
        return $this->belongsTo(Site::class);
    }

    public function documents(): BelongsTo
    {
        return $this->belongsTo(SupportDocument::class);
    }
}
