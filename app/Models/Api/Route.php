<?php

namespace App\Models\Api;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Route extends Model {
	use SoftDeletes, HasFactory;

	protected $guarded = [];

	/**
	 * @return BelongsToMany
	 */
	public function profiles(){
		return $this->belongsToMany(Profile::class, "route_profile", "route_id", "profile_id");
	}

	/**
	 * @return BelongsToMany
	 */
	public function routeGroups(){
		return $this->belongsToMany(RouteGroup::class, "route_group_route", "route_id", "route_group_id");
	}
}
