<?php

namespace App\Models\Api;

use App\Utils\Traits\Models\Reorderable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int id
 * @property int client_id
 * @property string name
 * @property bool is_deletable
 * @method static find(int $id)
 * @method static create(array $all)
 * @method static where(string $string, string $string1, int $clientId)
 */
class LeaveType extends Model
{
    use SoftDeletes, HasFactory, Reorderable;

    protected $guarded = [];

    protected $casts = [
        'is_active' => 'boolean',
        'is_monthly' => 'boolean',
        'is_pay' => 'boolean',
        'is_deletable' => 'boolean',
        'is_half_day' => 'boolean',
        'is_attachment_required' => 'boolean',
        'can_exceed' => 'boolean',
        'needs_count' => 'boolean',
        'is_ignore_by_export' => 'boolean',
        'is_pentecost' => 'boolean',
        'can_justify_later' => 'boolean',
        'leave_type_category_id' => 'integer',
        'is_auto_increment_active' => 'boolean',
        'is_only_visible_by_admin' => 'boolean',
        'is_take_leave' => 'boolean',
    ];

    /**
     * @return BelongsTo
     */
    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function leaveTypeCategory()
    {
        return $this->belongsTo(LeaveTypeCategory::class);
    }

    public function site()
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * @return BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, "last_update_id")->withTrashed();
    }

    /**
     * @return HasMany
     */
    public function leaves()
    {
        return $this->hasMany(Leave::class);
    }

    /**
     * @return HasMany
     */
    public function leave_type_sub_families()
    {
        return $this->hasMany(LeaveTypeSubFamily::class);
    }

    /**
     * @return HasMany
     */
    public function user_leave_counts()
    {
        return $this->hasMany(UserLeaveCount::class);
    }

}
