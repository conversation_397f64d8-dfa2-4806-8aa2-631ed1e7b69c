<?php

namespace App\Models\Api;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Prunable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ExportHistory extends Model
{
    use SoftDeletes, HasFactory, Prunable;

    protected $guarded = [];

    /**
     * @return BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function prunable()
    {
        $recentIds = ExportHistory::query()
            ->latest()
            ->take(20)
            ->pluck('id');

        return ExportHistory::withTrashed()
            ->whereNotIn('id', $recentIds);
    }

    protected function pruning()
    {
        $user = $this->user()->withTrashed()->with(['site' => function ($query) {
            $query->withTrashed()->with(['client' => function ($query) {
                $query->withTrashed();
            }]);
        }])->first();

        if ($user && $user->site && $user->site->client) {
            $clientId = $user->site->client->id;
            $path = 'exports/' . $clientId . '/';
            $filePath = $path . $this->file_name;

            if (Storage::disk('minio')->exists($filePath)) {
                $deleted = Storage::disk('minio')->delete($filePath);

                if ($deleted) {
                    Log::info("Fichier supprimé : " . $filePath);
                } else {
                    Log::error("Échec de la suppression du fichier : " . $filePath);
                }
            } else {
                Log::warning("Fichier introuvable : " . $filePath);
            }
            $this->forceDelete();
        }
    }
}
