<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use K<PERSON>it\Firebase\Factory as FirebaseFactory;
use Kreait\Firebase\Messaging\CloudMessage;
use NotificationChannels\Fcm\FcmChannel;

class UserReceiveNotificationPush extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        $channels = [];
        if ($notifiable->fcm_token) {
            $channels[] = FcmChannel::class;
        }

        return $channels;
    }

    /**
     * Get the mail representation of the notification push.
     */
    public function toFcm(object $notifiable)
    {
        $notification = \Kreait\Firebase\Messaging\Notification::create(
            title: $this->data['title'],
            body: $this->data['message'],
        );

        $firebaseCredentials = json_decode(config('firebase.projects.app.credentials'), true);

        $firebase = (new FirebaseFactory)
            ->withServiceAccount($firebaseCredentials)
            ->withProjectId($firebaseCredentials['project_id']);

        $messaging = $firebase->createMessaging();
        $message = CloudMessage::withTarget('token', $notifiable->fcm_token)
            ->withNotification($notification)
            ->withData([
                "user_firstname" => $notifiable->firstname,
                "user_lastname" => $notifiable->lastname,
                "title" =>  $this->data['title'],
                "body"  =>  $this->data['message'],
                "click_action" => "FLUTTER_NOTIFICATION_CLICK",
                "screen" => $this->data['url'],
                "id" => $this->data['leave']->id,
                "navigateTo" => $this->data['page'],
                "app" => "conges-api",
            ]);

        return $messaging->send($message);
    }

    /**
     * Get the mail representation of the mail.
     */
    public function toMail(object $notifiable)
    {
        //
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
