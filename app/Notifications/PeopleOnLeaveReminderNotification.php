<?php

namespace App\Notifications;

use App\Lib\ApiHelper;
use App\Models\Api\Leave;
use App\Models\Api\Status;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PeopleOnLeaveReminderNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $leaves = Leave::select('leaves.leave_type_id', 'leaves.leave_type_sub_family_id', 'leaves.start_date', 'leaves.end_date', 'absentee.firstname', 'absentee.lastname')
            ->join('managers', function ($join) use ($notifiable) {
                $join->on('managers.managed_id', 'leaves.user_id')
                    ->where('managers.manager_id', $notifiable->getKey());
            })
            ->join('users as absentee', 'absentee.id', 'leaves.user_id')
            ->whereIn('leaves.status_id', [Status::where('tag', '=', 'VALIDATED')->sole()->getKey(), Status::where('tag', '=', 'TRANSMITTED')->sole()->getKey()])
            ->where(function ($query){
                $query->whereBetween('leaves.start_date', [Carbon::today()->startOfDay(), Carbon::today()->addWeek()->subSecond()])
                    ->orWhereBetween('leaves.end_date', [Carbon::today()->startOfDay(), Carbon::today()->addWeek()->subSecond()])
                    ->orWhere(function ($query){
                        $query->where('leaves.start_date', '<', Carbon::today()->startOfDay())
                            ->where('leaves.end_date', '>', Carbon::today()->addWeek()->subSecond());
                    });
            })
            ->orderBy('leaves.start_date')
            ->get();

        Carbon::setLocale($notifiable->preferredLocale());
        $multiplelines = [];
        $leaves->each(function($leave) use (&$multiplelines, $notifiable) {
            $leaveDate = ApiHelper::convertStartFullDateAndEndFullDate($notifiable, $leave->start_date, $leave->end_date);
            $leaveTypeName = ApiHelper::LeaveTypeName($leave);
            $multiplelines[] = __('notifications.leave_waiting_to_be_validate_users', ['user_firstname' => $leave->firstname, 'user_lastname' => $leave->lastname, 'leave_start' => $leaveDate['start_date'], 'leave_end' => $leaveDate['end_date'], 'leave_type' => $leaveTypeName]);
        });

        $mailable = (new MailMessage)
            ->subject(__('notifications.title_leaves_waiting_validation_this_week'))
            ->greeting(__('notifications.greeting', ['user_firstname' => $notifiable->firstname]))
            ->line(__('notifications.leave_waiting_to_be_validate_for_managers_this_week'))
            ->salutation(__('notifications.goodbye'));

        foreach ($multiplelines as $keyword ) {
            $mailable->line($keyword);
        }

        $url = env('APP_FRONT_URL').'validation';

        return $mailable
            ->action(__('notifications.app_link'), $url)
            ->markdown('mailing::email')
            ->theme('mailing::components.html.themes.default');
    }
}
