<?php

namespace App\Notifications;

use App\Lib\ApiHelper;
use App\Models\Api\Leave;
use App\Models\Api\Status;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\HtmlString;

class LeaveToValidateReminderAfterStartDateNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $leaves = Leave::select('leaves.leave_type_id', 'leaves.leave_type_sub_family_id', 'leaves.start_date', 'leaves.end_date', 'users.firstname', 'users.lastname')
            ->join('managers', function ($join) use ($notifiable) {
                $join->on('managers.managed_id', 'leaves.user_id')
                    ->where('managers.manager_id', $notifiable->getKey())
                    ->whereColumn('managers.level', 'leaves.current_validator_level');
            })
            ->join('users', 'users.id', 'leaves.user_id')
            ->where('status_id', Status::where('tag', '=', 'SUBMITTED')->sole()->getKey())
            ->whereBetween('start_date', [Carbon::today()->addDays(1)->startOfDay(), Carbon::today()->addDays(2)->endOfDay()])
            ->get();

        Carbon::setLocale($notifiable->preferredLocale());

        $multiplelines = [];
        $leaves->each(function($leave) use ($notifiable, &$multiplelines) {
            $leaveDate = ApiHelper::convertStartFullDateAndEndFullDate($notifiable, $leave->start_date, $leave->end_date);
            $leaveTypeName = ApiHelper::LeaveTypeName($leave);
            $multiplelines[] = __('notifications.leave_waiting_to_be_validate_start_soon', ['user_firstname' => $leave->firstname, 'user_lastname' => $leave->lastname, 'leave_start' => $leaveDate['start_date'], 'leave_type' => $leaveTypeName]);

            if (!is_null($notifiable->fcm_token)) {
                $notifiable->notify(new UserReceiveNotificationPush([
                    'leave' => $leave,
                    'title' => __('notifications.fcm.title.title_leaves_waiting_validation_start_soon', [], $notifiable->language),
                    'message' => __('notifications.fcm.body.leave_waiting_to_be_validate_start_soon', ['user_firstname' => $leave->firstname, 'user_lastname' => $leave->lastname, $leaveDate['start_date'], 'leave_type' => $leaveTypeName], $notifiable->language),
                    'url' => "/leave/{$leave->getKey()}",
                    'page' => 'toValidate',
                ]));
            }
        });

        $mailable = (new MailMessage)
            ->subject(__('notifications.title_leaves_waiting_validation_start_soon'))
            ->greeting(__('notifications.greeting', ['user_firstname' => $notifiable->firstname]))
            ->line(new HtmlString(__('notifications.leaves_waiting_your_validation_in').' <u>'.__('notifications.start_less_48hours').'</u>'))
            ->salutation(__('notifications.goodbye'));

        foreach ($multiplelines as $keyword) {
            $mailable->line($keyword);
        }

        $url = env('APP_FRONT_URL').'validation';

        return $mailable
            ->action(__('notifications.app_link'), $url)
            ->markdown('mailing::email')
            ->theme('mailing::components.html.themes.default');
    }
}
