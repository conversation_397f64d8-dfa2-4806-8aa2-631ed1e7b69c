<?php

namespace App\Notifications;

use App\Models\Api\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class LeaveRefused extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($data, bool $manager = false)
    {
        $this->data = $data;
        $this->manager = $manager;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $mailable = (new MailMessage)
            ->subject($this->data['title'])
            ->greeting($this->data['greeting'])
            ->salutation(__('notifications.goodbye'));

        $mailable->outroLines[] = $this->data['refusal'];

        foreach ($this->data['keyword'] as $keyword => $attributes) {
            if (!empty($attributes)) {
                $mailable->line(__($keyword, $attributes));
            } else {
                $mailable->line(__($keyword));
            }
        }

        $url = ($this->manager === false ? env('APP_FRONT_URL').'leaves' : env('APP_FRONT_URL').'validation');

        return $mailable
            ->action(__('notifications.app_link'), $url)
            ->markdown('mailing::email')
            ->theme('mailing::components.html.themes.default');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
