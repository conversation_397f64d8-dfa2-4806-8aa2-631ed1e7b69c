<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class LeaveInformation extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($data, bool $manager = false, string $tab = '')
    {
        $this->data = $data;
        $this->manager = $manager;
        $this->tab = $tab;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $mailable = (new MailMessage)
            ->subject($this->data['title'])
            ->greeting($this->data['greeting'])
            ->salutation(__('notifications.goodbye'));

        foreach ($this->data['keyword'] as $keyword => $attributes) {
            if (!empty($attributes)) {
                $mailable->line(__($keyword, $attributes));
            } else {
                $mailable->line(__($keyword));
            }
        }

        if ($this->manager === false) {
            $url = env('APP_FRONT_URL').'leaves';
        } else {
            if (isset($this->tab) && $this->tab === 'cancel') {
                $url = env('APP_FRONT_URL').'validation/#validationToCancel';
            } else if (isset($this->tab) && $this->tab === 'planning') {
                $url = env('APP_FRONT_URL').'planning';
            } else {
                $url = env('APP_FRONT_URL').'validation';
            }
        }


        return $mailable
            ->action(__('notifications.app_link'), $url)
            ->markdown('mailing::email')
            ->theme('mailing::components.html.themes.default');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
