<?php

namespace App\Notifications;

use App\Models\Api\LeaveTypeCategory;
use App\Models\Api\Status;
use App\Models\Api\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Spatie\IcalendarGenerator\Components\Calendar;
use Spatie\IcalendarGenerator\Components\Event;
use Spatie\IcalendarGenerator\Enums\ParticipationStatus;
use Spatie\IcalendarGenerator\Properties\TextProperty;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mime\Part\Multipart\AlternativePart;
use Symfony\Component\Mime\Part\Multipart\MixedPart;
use Symfony\Component\Mime\Part\TextPart;

class LeaveAccepted extends Notification implements ShouldQueue
{
    use Queueable;
    public $data;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {

        return ['mail'];

    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = env('APP_FRONT_URL').'leaves';

        $mailable = (new MailMessage)
            ->subject($this->data['title'])
            ->greeting($this->data['greeting'])
            ->salutation(__('notifications.goodbye'));

        foreach ($this->data['keyword'] as $keyword => $attributes) {
            if (!empty($attributes)) {
                $mailable->line(__($keyword, $attributes));
            } else {
                $mailable->line(__($keyword));
            }
        }

        if ($this->data['leave']['status_id'] === Status::firstWhere("tag", "VALIDATED")->getKey()) {
            $leave = $this->data['leave'];

            $mailable->attachData($this->generateLeaveICS($leave), 'invite.ics', [
                'mime' => 'text/calendar; charset=UTF-8; method=REQUEST',
            ]);
        }

        return $mailable->action(__('notifications.app_link'), $url)
            ->markdown('mailing::email')
            ->theme('mailing::components.html.themes.default');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }

    public function generateLeaveICS($leave) {
        $userLeave = User::withTrashed()->firstWhere('id', $leave->user_id);

        $abs = LeaveTypeCategory::where('slug', 'abs')->first();

        $leaveType = $leave->leave_type;

        $name = $leaveType->leave_type_category_id === $abs->getKey() ? 'Absence' : $leaveType->name;

        // création de la réunion à partir du leave
        $ical = Calendar::create($name)
            ->productIdentifier("-//Microsoft Corporation//Outlook 16.0 MIMEDIR//EN")
            ->appendProperty(TextProperty::create('METHOD', 'REQUEST'))
            ->event(Event::create($name)
                ->attendee($userLeave->email, $userLeave->firstname, ParticipationStatus::accepted())
                ->startsAt(new \DateTime($leave->start_date, new \DateTimeZone($userLeave->timezone)))
                ->endsAt(new \DateTime($leave->end_date, new \DateTimeZone($userLeave->timezone)))
                ->organizer('<EMAIL>', env('MAIL_FROM_NAME'))
            )
            ->get();

        return $ical;
    }
}
