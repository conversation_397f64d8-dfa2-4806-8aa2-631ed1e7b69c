<?php

namespace App\Listeners;

use App\Jobs\StructureManagementJob;
use App\Models\Api\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class CalculateHierarchicalManagerListener
{
    /**
     * Handle the event.
     */
    public function handle(User $user): void
    {
        if (!$user->isDirty('is_level_one_manager')) {
            return;
        }

        $recursiveManaged =
            DB::select(
                "
                    with recursive cte (id, manager_id, level) as (
                        select id, manager_id, 0 as level
                        from users
                        where id = :id
                        union
                        select u.id, u.manager_id, level + 1
                        from users u
                        inner join cte on u.manager_id = cte.id
                        where level < 5
                    )
                    select id, level from cte where level > 1;
                ", ['id' => $user->getKey()]
            );

        User::query()->whereIn('id', array_column($recursiveManaged, 'id'))
            ->each(function (User $managed) {
                StructureManagementJob::dispatch($managed);
            }, 10);
    }
}
