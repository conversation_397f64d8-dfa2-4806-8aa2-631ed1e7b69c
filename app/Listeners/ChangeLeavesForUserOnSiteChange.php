<?php

namespace App\Listeners;

use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Queue\InteractsWithQueue;

class ChangeLeavesForUserOnSiteChange
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(User $user): void
    {
        if (!$user->isDirty('site_id') || LeaveType::query()->whereIn('site_id', [$user->getOriginal('site_id'), $user->site_id])->doesntExist()) {
            return;
        }

        $originalSite = $user->getOriginal('site_id');

        $originalLeaveTypes = LeaveType::query()->where('site_id', $originalSite)
            ->orWhere(function (Builder $query) use ($originalSite, $user) {
                $query->where('leave_types.client_id', $user->site->client_id)
                    ->whereNull('leave_types.site_id')
                    ->whereRaw("(SELECT COUNT(*) FROM leave_types WHERE site_id = $originalSite) < 1");
            })
            ->get();

        foreach ($originalLeaveTypes as $originalLeaveType) {
            Leave::query()->where('user_id', $user->getKey())
                ->where('leave_type_id', $originalLeaveType->getKey())
                ->each(function (Leave $leave) use ($user, $originalLeaveType) {
                    $lt = LeaveType::query()
                        ->where(function (Builder $query) use ($user) {
                            $query->where('site_id', $user->site_id)
                                ->orWhere(function ($query) use ($user) {
                                    $query->where('leave_types.client_id', $user->site->client_id)
                                        ->whereNull('leave_types.site_id')
                                        ->whereRaw("(SELECT COUNT(*) FROM leave_types WHERE site_id = $user->site_id) < 1");
                                });
                        })
                        ->where('leave_code', $originalLeaveType->leave_code)
                        ->first();

                    $leave->leave_type_id = $lt->getKey();
                    $leave->save();
                }, 100);
        }
    }
}
