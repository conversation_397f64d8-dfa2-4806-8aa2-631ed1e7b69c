<?php

namespace App\Listeners;

use App\Events\DetachUserDirected;
use App\Lib\Tools;
use App\Models\Api\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Queue\InteractsWithQueue;

class DetachUserDirectedListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {}

    /**
     * Handle the event.
     */
    public function handle(DetachUserDirected $event): void
    {
        $users = User::query()
            ->findMany(
                is_array($event->detachedUsers) ? $event->detachedUsers : $event->detachedUsers->getKey()
            );

        $users->each(function (User $user) use ($event) {
            $user->tags()
                ->when($director = $event->director, function (Builder $whenQuery) use ($director) {
                    $whenQuery->where('tags.user_id', $director->id);
                })
                ->detach();
        });
    }
}
