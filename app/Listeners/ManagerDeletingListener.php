<?php

namespace App\Listeners;

use App\Models\Api\Manager;
use App\Models\Api\Profile;
use App\Models\Api\Tag;
use App\Models\Api\User;

class ManagerDeletingListener
{
    /**
     * Handle the Manager "deleting" event.
     */
    public function handle(Manager $manager): void
    {
        // Vérifier que le manager n'est pas un directeur
        $managerUser = User::find($manager->manager_id);
        $directorProfile = Profile::where('label', 'DIRECTOR')->first();
        
        if ($managerUser->profile_id !== $directorProfile->getKey()) {
            $managedUser = User::find($manager->managed_id);

            // Supprimer l'utilisateur géré des équipes du manager
            Tag::where('user_id', $manager->manager_id)->each(function ($tag) use ($managedUser) {
                $tag->users()->where('user_id', $managedUser->id)->detach();
            });
        }
    }
}
