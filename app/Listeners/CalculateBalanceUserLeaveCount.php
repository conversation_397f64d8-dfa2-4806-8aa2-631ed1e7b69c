<?php

namespace App\Listeners;

use App\Models\Api\UserLeaveCount;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CalculateBalanceUserLeaveCount
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UserLeaveCount $userLeaveCount): void
    {
        $userLeaveCount->balance = $userLeaveCount->acquired - $userLeaveCount->taken;
    }
}
