<?php

namespace App\Listeners;

use App\Events\HolidayCreated;
use App\Models\Api\Client;
use App\Models\Api\Site;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class AttachHoliday
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(HolidayCreated $event): void
    {
        if (isset($event->requestArray['client_id'])) {
            $event->holiday->country_code = Client::find($event->requestArray['client_id'])->sites()->orderBy('created_at')->first()->country_alpha;
            $event->holiday->clients()->attach($event->requestArray['client_id']);
        }

        if (isset($event->requestArray['site_id'])) {
            $event->holiday->country_code = Site::find($event->requestArray['site_id'])->country_alpha;
            $event->holiday->sites()->attach($event->requestArray['site_id']);
        }

        $event->holiday->save();
    }
}
