<?php

namespace App\Excel\Exports;

use App\DTO\ExportLeavesDTO;
use App\Http\Controllers\OrionLeavesController;
use App\Models\Api\Leave;
use App\Models\Api\Tag;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Orion\Http\Requests\Request;

class LeavesExport implements FromQuery, WithMapping, WithHeadings, WithCustomCsvSettings, ShouldAutoSize
{
    use Exportable;

    private array $scopes;
    private bool $withTags;
    private string $strFinal = '';
    private Leave $leaveModel;
    private const string TRANSMITTED_STATUS_TAG = 'TRANSMITTED';

    public function __construct(
        public Request         $orionRequest,
        public int             $clientId,
        public ExportLeavesDTO $exportLeaveDTO,
    )
    {
        $this->leaveModel = new Leave();
        $this->scopes = request()->input('scopes', []);
        $this->withTags = false;

        $this->withTags = (bool)collect($this->scopes)->first(fn(array $scope) => $scope['name'] === 'tagsUserLeave');
    }

    public function query()
    {
        $orionBuilder = (new OrionLeavesController)->getQueryBuilder();

        $query = Leave::query()
            ->select('id', 'user_id', 'status_id', 'leave_type_id', 'leave_type_sub_family_id', 'current_validator_level', 'start_date', 'end_date', 'duration')
            ->with([
                'leave_type' => fn($query) => $query->select('id', 'name', 'leave_code', 'is_ignore_by_export', 'leave_type_category_id', 'absence_reason'),
                'leave_type.leaveTypeCategory' => fn($query) => $query->select('id', 'label'),
                'leave_type_sub_family' => fn($query) => $query->select('id', 'absence_reason'),
                'user' => fn($query) => $query->select('id', 'matricule', 'lastname', 'firstname', 'site_id', 'manager_id'),
                'user.site' => fn($query) => $query->select('id', 'name', 'client_id'),
                'user.directManager' => fn($query) => $query->select('id', 'lastname', 'firstname'),
                'user.managers' => fn($query) => $query->select('id', 'lastname', 'firstname'),
                'user.tags',
            ])
            ->whereHas('user.site', fn($q) => $q->where('client_id', $this->clientId))
            ->whereHas('leave_type', fn($q) => $q->where('is_ignore_by_export', 0))
            ->when(
                $this->exportLeaveDTO->hasValidDate(),
                fn($query) => $query->whereDate('start_date', '<=', $this->exportLeaveDTO->getEndDate())
                    ->whereDate('end_date', '>=', $this->exportLeaveDTO->getStartDate())
            )
            ->orderBy('start_date', 'desc');

        $orionBuilder->applyFiltersToQuery($query, $this->orionRequest);

        // TODO : Enlever avec la refonte
        if ($this->exportLeaveDTO->isTreat) {
            $query->whereHas(
                'status',
                fn($query) => $query->where('tag', self::TRANSMITTED_STATUS_TAG)
            );
        }

        return $query;
    }

    public function headings(): array
    {
        return array_filter([
            __('messages.RegistrationNumber'),
            __('messages.EmployeeName'),
            __('messages.CodeConges'),
            __('messages.Label'),
            __('messages.StartDate'),
            __('messages.EndDate'),
            __('messages.Duration'),
            __('messages.Status'),
            __('messages.Site'),
            __('messages.TypeConges'),
            $this->withTags ? __('messages.Tag') : null,
        ]);
    }

    public function getCsvSettings(): array
    {
        return [
            "use_bom" => "true",
            'delimiter' => ';',
            'enclosure' => '',
        ];
    }

    public function map(mixed $row): array
    {
        /** @var Leave $leave */
        $leave = $row;
        $statusTag = $leave->status->tag ?? null;
        $statusNameValue = $leave->status->name ?? '';

        $currentValidator = $leave->user->managers()->where('level', $leave->current_validator_level)->first();
        $directManager = $leave->user->directManager;

        if ($this->exportLeaveDTO->fileNumber) {
            $rows = $this->mapForCegid($leave);

            foreach ($rows as $row) {
                $this->strFinal .= implode(';', $row) . "\r\n";
            }

            return [];
        }

        $startDate = $leave->start_date;
        $endDate = $leave->end_date;
        $startHour = $startDate->hour;
        $endHour = $endDate->hour;

        if ($statusTag === 'SUBMITTED' && $currentValidator) {
            $managerName = strtoupper($currentValidator->lastname) . " " . ucfirst(strtolower($currentValidator->firstname));
            $statusName = __('messages.waiting_validation_by_manager', ['manager' => $managerName]);
        } elseif ($statusTag === 'SUBMITTED_TO_CANCELLATION') {
            $managerName = strtoupper($directManager->lastname) . " " . ucfirst(strtolower($directManager->firstname));
            $statusName = __('messages.waiting_cancellation_by_manager', ['manager' => $managerName]);
        } else {
            $statusName = $statusNameValue;
        }

        if ($startDate->isSameMonth($endDate)) {
            return $this->formatRow($leave, $startDate, $endDate, $leave->duration, $statusName);
        }

        $rows = [];
        $currentDate = clone $startDate;

        while ($currentDate->startOfMonth()->lte($endDate)) {
            $periodStart = max($currentDate->copy()->startOfMonth(), $startDate)->startOfDay();
            $periodEnd = min($currentDate->copy()->endOfMonth(), $endDate)->endOfDay();

            if (
                $this->exportLeaveDTO->hasValidDate() &&
                !$periodStart->copy()->startOfDay()->isBetween($this->exportLeaveDTO->getStartDate(), $this->exportLeaveDTO->getEndDate()) ||
                !$periodEnd->copy()->startOfDay()->isBetween($this->exportLeaveDTO->getStartDate(), $this->exportLeaveDTO->getEndDate())
            ) {
                $currentDate->addMonth();
                continue;
            }

            $monthDuration = $this->leaveModel->exactDuration($periodStart->format('Y-m-d H:i:s'), $periodEnd->format('Y-m-d H:i:s'), $leave->user_id);

            if ($currentDate->isSameMonth($startDate) && $startHour === 12) {
                $monthDuration -= 0.5;
            }

            if ($currentDate->isSameMonth($endDate) && $endHour === 12) {
                $monthDuration -= 0.5;
            }

            $rows[] = $this->formatRow($leave, $periodStart, $periodEnd, $monthDuration, $statusName);
            $currentDate->addMonth();
        }

        return $rows;
    }

    private function mapForCegid(Leave $leave): array
    {
        $startDate = $leave->start_date;
        $endDate = $leave->end_date;
        $endHour = (int)$endDate->format('H');
        $startHour = (int)$startDate->format('H');
        $absenceReason = $leave->leave_type_sub_family->absence_reason ?? $leave->leave_type->absence_reason ?? 'NC';

        if ($startDate->isSameMonth($endDate)) {
            return [
                $this->createCegidRow($leave, $startDate, $endDate, $endHour, $absenceReason)
            ];
        }

        $rows = [];
        $currentDate = clone $startDate;

        while ($currentDate->startOfMonth()->lte($endDate)) {
            $periodStart = max($currentDate->copy()->startOfMonth(), $startDate);
            $periodEnd = min($currentDate->copy()->endOfMonth(), $endDate);

            if (
                $this->exportLeaveDTO->hasValidDate() &&
                !$periodStart->copy()->startOfDay()->isBetween($this->exportLeaveDTO->getStartDate(), $this->exportLeaveDTO->getEndDate()) ||
                !$periodEnd->copy()->startOfDay()->isBetween($this->exportLeaveDTO->getStartDate(), $this->exportLeaveDTO->getEndDate())
            ) {
                $currentDate->addMonth();
                continue;
            }

            $monthDuration = $this->leaveModel->exactDuration($periodStart->format('Y-m-d H:i:s'), $periodEnd->format('Y-m-d H:i:s'), $leave->user_id);

            if ($periodStart->isSameMonth($startDate) && $startHour === 12) {
                $monthDuration -= 0.5;
            }
            if ($periodEnd->isSameMonth($endDate) && $endHour === 12) {
                $monthDuration -= 0.5;
            }
            $monthDuration = max(0, $monthDuration);

            $rows[] = $this->createCegidRow($leave, $periodStart, $periodEnd, $endHour, $absenceReason, $monthDuration);

            $currentDate->addMonth();
        }

        return $rows;
    }

    private function createCegidRow(Leave $leave, Carbon $startDate, Carbon $endDate, int $endHour, string $absenceReason, ?float $segmentDuration = null): array
    {
        $matricule = $leave->user->matricule;
        $matriculeDisplay = ($matricule !== null && $matricule !== '') ? $matricule : '//';
        $durationToDisplay = $segmentDuration ?: (float)$leave->duration;
        return [
            'MAB',
            $matriculeDisplay,
            $startDate->format('d/m/Y'),
            $endHour !== 0 ? $endDate->format('d/m/Y') : $endDate->copy()->subSecond()->format('d/m/Y'),
            strval($durationToDisplay),
            '',
            $absenceReason,
            'Du ' . $startDate->format('d/m/Y') . ' au ' . ($endHour !== 0 ? $endDate->format('d/m/Y') : $endDate->copy()->subSecond()->format('d/m/Y')),
            '',
            '',
            (int)$startDate->format('H') === 0 ? 'MAT' : 'PAM',
            $endHour === 23 ? 'PAM' : 'MAT',
            '',
            $startDate->format('d/m/Y')
        ];
    }

    private function formatCegidOutput(): string
    {
        $output = "***DEBUT***\r\n";
        $output .= implode(';', [
                '000',
                $this->exportLeaveDTO->fileNumber,
                $this->exportLeaveDTO->getStartDate()->format('d/m/Y'),
                $this->exportLeaveDTO->getEndDate()->format('d/m/Y')
            ]) . "\r\n";
        $output .= $this->strFinal;
        $output .= "***FIN***\r\n";

        return $output;
    }

    public function getStrFinal(): string
    {
        if ($this->exportLeaveDTO->fileNumber) {
            return $this->formatCegidOutput();
        }

        return $this->strFinal;
    }

    private function formatRow(Leave $leave, Carbon $startDate, Carbon $endDate, float $duration, string $statusName): array
    {
        $absenceReason = $leave->leave_type_sub_family->absence_reason ?? $leave->leave_type->absence_reason ?? 'NC';

        /** @var Collection<Tag> $tags */
        $tags = $leave->user->tags;
        $matricule = $leave->user->matricule;
        $matriculeDisplay = ($matricule !== null && $matricule !== '') ? $matricule : '//';

        $formattedDuration = number_format($duration, 1, ',', '');
        $formattedDuration = str_replace(',0', '', $formattedDuration);
        $durationCell = sprintf('="%s"', $formattedDuration);

        $rowData = [
            $matriculeDisplay,
            strtoupper("{$leave->user->lastname} {$leave->user->firstname}"),
            $absenceReason,
            $leave->leave_type->name ?? '',
            $startDate->format('d/m/Y'),
            $endDate->format('d/m/Y'),
            $durationCell,
            $statusName,
            $leave->user->site->name ?? 'NC',
            $leave->leave_type->leaveTypeCategory->label ?? 'NC',
        ];

        if ($this->withTags) {
            $rowData[] = $tags ? $tags->implode('label', ', ') : '';
        }

        return $rowData;
    }
}
