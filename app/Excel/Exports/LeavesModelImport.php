<?php

namespace App\Excel\Exports;

use App\Models\Api\LeaveTypeCategory;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Cell\DataValidation;
use PhpOffice\PhpSpreadsheet\NamedRange;

class LeavesModelImport implements WithEvents, WithHeadings
{
    use Exportable;

    protected array $selects;

    protected int $rowCount;

    protected int $columnCount;

    public function __construct()
    {

        $sites = Auth::user()->site->client->sites()->pluck('name')->toArray();
        $typesAbs = Auth::user()->site->client->leave_types()->where('leave_type_category_id', LeaveTypeCategory::where('slug', 'abs')->first()->getKey())->pluck('name')->unique()->values()->toArray();

        $this->selects = [
            ['columnName' => 'D', 'options' => $sites, 'rangeName' => 'sites'],
            ['columnName' => 'H', 'options' => $typesAbs, 'rangeName' => 'typeAbsCg'],
        ];

        $this->rowCount = 1000;
        $this->columnCount = 10;
    }

    public function headings(): array
    {
        $headings = [
            __('messages.RegistrationNumber'),
            __('messages.EmployeeLastname'),
            __('messages.EmployeeFirstname'),
            __('messages.Site'),
            __('messages.StartDate'),
            __('messages.EndDate'),
            __('messages.Duration'),
            __('messages.TypeAbsence'),
        ];

        return $headings;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();
                $spreadsheet = $sheet->getParent();

                // Add dropdown values to hidden columns and define named ranges
                foreach ($this->selects as $index => $select) {
                    $options = $select['options'];
                    $rangeName = $select['rangeName'];
                    $column = Coordinate::stringFromColumnIndex(50 + $index + 1);
                    $startRow = 1;

                    // Set values in the column vertically
                    foreach ($options as $rowIndex => $option) {
                        $cell = $column . ($startRow + $rowIndex);
                        $sheet->setCellValue($cell, $option);
                    }

                    // Define and set named range
                    $endRow = $startRow + count($options) - 1;
                    $cellRange = "\${$column}\${$startRow}:\${$column}\${$endRow}";
                    $spreadsheet->addNamedRange(new NamedRange(
                        $rangeName,
                        $sheet,
                        $cellRange
                    ));
                }

                // Apply data validation to the target cells
                foreach ($this->selects as $select) {
                    $dropColumn = $select['columnName'];
                    $rangeName = $select['rangeName'];

                    for ($i = 1; $i <= $this->rowCount; $i++) {
                        $cell = "{$dropColumn}{$i}";
                        $validation = $sheet->getCell($cell)->getDataValidation();
                        $validation->setType(DataValidation::TYPE_LIST);
                        $validation->setErrorStyle(DataValidation::STYLE_INFORMATION);
                        $validation->setAllowBlank(false);
                        $validation->setShowInputMessage(true);
                        $validation->setShowErrorMessage(true);
                        $validation->setShowDropDown(true);
                        $validation->setErrorTitle(__('messages.import.error_title'));
                        $validation->setError(__('messages.import.error_value'));
                        $validation->setPromptTitle(__('messages.import.prompt_title'));
                        $validation->setPrompt(__('messages.import.prompt_value'));
                        $validation->setFormula1("={$rangeName}");

                        $sheet->getCell($cell)->setDataValidation($validation);
                    }
                }

                // Hide the columns where dropdown values are stored
                for ($i = 50 + 1; $i <= 50 + count($this->selects); $i++) {
                    $column = Coordinate::stringFromColumnIndex($i);
                    $sheet->getColumnDimension($column)->setVisible(false);
                }

                // Set auto size for visible columns
                for ($i = 1; $i <= $this->columnCount; $i++) {
                    $column = Coordinate::stringFromColumnIndex($i);
                    $sheet->getColumnDimension($column)->setAutoSize(true);
                }
            },
        ];
    }
}
