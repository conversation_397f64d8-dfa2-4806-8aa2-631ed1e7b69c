<?php

namespace App\Excel\Exports;

use App\Models\Api\LeaveType;
use App\Models\Api\Site;
use App\Models\Api\User;
use Illuminate\Contracts\Support\Responsable;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Excel;


class UserLeavesCountsExport implements WithHeadings, WithMapping, FromArray, Responsable
{
    use Exportable;

    /**
     * It's required to define the fileName within
     * the export class when making use of Responsable.
     */
    private $fileName = 'Compteurs.xlsx';

    /**
     * Optional Writer Type
     */
    private $writerType = Excel::XLSX;

    /**
     * Optional headers
     */
    private $headers = [
        'Content-Type' => 'text/csv',
    ];


    private int $clientId;
    private ?array $user_ids;
    private Collection $leaveTypes;
    private Collection $sites;

    public function __construct(int $clientId, array $user_ids = null)
    {
        $this->clientId = $clientId;
        $this->user_ids = $user_ids;
        $this->loadData();
    }

    private function loadData(): void
    {
        $filteredSiteIds = User::query()
            ->whereIn('id', $this->user_ids)
            ->pluck('site_id')
            ->unique();

        if (!empty(request()->input('filters')) && $filteredSiteIds->isNotEmpty()) {

            $this->leaveTypes = LeaveType::where(function ($query) use ($filteredSiteIds) {
                $query->whereIn('site_id', $filteredSiteIds)
                    ->orWhereNull('site_id');
            })
            ->where("client_id", $this->clientId)
            ->where("needs_count", true)
            ->where("is_active", 1)
            ->get();

        } else {

        $this->leaveTypes = LeaveType::where("client_id", $this->clientId)
            ->where("needs_count", true)
            ->where("is_active", 1)
            ->get();
        }

        $this->sites = Site::where('client_id', "=", $this->clientId)
            ->get()
            ->keyBy('id');
    }

    public function array(): array
    {
        $query = User::query()
            ->whereHas('site', function ($query) {
                $query->where('client_id', $this->clientId);
            })
            ->has('user_leave_counts', '>', 0)
            ->with(['site', 'user_leave_counts']);

        if (!is_null($this->user_ids)) {
            $query->whereIn("id", $this->user_ids);
        }

        return [
            [
                "users" => $query->get(),
                "leavesTypes" => $this->leaveTypes
            ]
        ];
    }

    public function headings(): array
    {
        $headers = [
            __('messages.RegistrationNumber'),
            __('messages.Email'),
            __('messages.Employee'),
            __('messages.Establishment'),
            __('messages.EntryDate')
        ];

        $groupedLeaveTypes = $this->leaveTypes->groupBy('leave_code');
        $years = [ 'N-1', 'N'];

        foreach ($groupedLeaveTypes as $leaveCode => $leaveTypeGroup) {
            $isPay = $leaveTypeGroup->contains('is_pay', true);
            $isMonthly = $leaveTypeGroup->contains('is_monthly', true);

            if ($isPay) {
                foreach ($years as $year) {
                    if ($isMonthly) {
                        $headers[] = __('messages.Acquired') . " " . $leaveCode . " " . $year;
                        $headers[] = __('messages.Taken') . " " . $leaveCode . " " . $year;
                    }
                    $headers[] = __('messages.Balance') . " " . $leaveCode . " " . $year;
                }
            } else {
                if ($isMonthly) {
                    $headers[] = __('messages.Acquired') . " " . $leaveCode;
                    $headers[] = __('messages.Taken') . " " . $leaveCode;
                }
                $headers[] = __('messages.Balance') . " " . $leaveCode;
            }
        }

        return $headers;
    }

    public function map($row): array
    {
        $users = $row["users"];
        $leaveTypes = $row["leavesTypes"];
        $groupedLeaveTypes = $leaveTypes->groupBy('leave_code');

        $sortedLeaveTypes = collect();
        foreach ($groupedLeaveTypes as $leaveTypeGroup) {
            $sortedGroup = $leaveTypeGroup->sort(function ($a, $b) {
                return is_null($a->site_id) ? -1 : 1;
            });
            $sortedLeaveTypes = $sortedLeaveTypes->merge($sortedGroup);
        }

        $users = $users->sortBy(function ($user) {
            return strtolower($user->lastname);
        });

        $rows = [];
        foreach ($users as $user) {
            $row = [
                $user->matricule,
                $user->email,
                mb_strtoupper($user->lastname . " " . $user->firstname, 'UTF-8'),
                $this->sites[$user->site_id]->name ?? null,
                $user->enter_date ? date('d/m/Y', strtotime($user->enter_date)) : ""
            ];

            foreach ($sortedLeaveTypes as $leaveType) {
                if (!$this->isValidLeaveTypeForUser($leaveType, $user)) {
                    continue;
                }

                $leaveCountN = $this->getCountsN($user, $leaveType->id);

                if ($leaveType->is_pay) {
                    $leaveCountN1 = $this->getCountsN1($user, $leaveType->id);
                    $row = $this->fillWithValues($leaveType, $row, $leaveCountN1);
                }

                $row = $this->fillWithValues($leaveType, $row, $leaveCountN);
            }

            $rows[] = $row;
        }

        return $rows;
    }

    private function isValidLeaveTypeForUser(LeaveType $leaveType, User $user): bool
    {
        $parentLeaveType = $this->findParentLeaveType($leaveType);

        if ($leaveType->site_id && $parentLeaveType && ($user->site_id !== $leaveType->site_id)) {
            return false;
        }

        $childLeaveType = $this->findChildLeaveType($user, $leaveType);

        return !(!$leaveType->site_id && $childLeaveType);
    }

    private function fillWithValues($leaveType, $row, $user_leave_count): array
    {
        $acquired = '0';
        $taken = '0';
        $balance = '0';

        if (!is_null($user_leave_count)) {
            $acquired = $user_leave_count["acquired"] != 0.0 ? $user_leave_count["acquired"] : $acquired;
            $taken = $user_leave_count["taken"] != 0.0 ? $user_leave_count["taken"] : $taken;
            $balance = $user_leave_count["balance"] != 0.0 ? $user_leave_count["balance"] : $balance;
        }

        if ($leaveType->is_monthly) {
            return array_merge($row, [$acquired, $taken, $balance]);
        }

        return array_merge($row, [$balance]);
    }

    private function findChildLeaveType(User $user, LeaveType $leaveType): ?LeaveType
    {
        return $this->leaveTypes
            ->where('leave_code', $leaveType->leave_code)
            ->where('site_id', $user->site_id)
            ->first();
    }

    private function findParentLeaveType(LeaveType $leaveType): ?LeaveType
    {
        return $this->leaveTypes
            ->where('leave_code', $leaveType->leave_code)
            ->whereNull('site_id')
            ->first();
    }

    private function getUserLeaveCounts(User $user, int $leaveTypeId, bool $isLastYear): ?array
    {
        $counts = $user->user_leave_counts()
            ->where('leave_type_id', $leaveTypeId)
            ->where('is_last_year', $isLastYear)
            ->first();

        return $counts ? $counts->toArray() : null;
    }

    private function getCountsN(User $user, int $leaveTypeId): ?array
    {
        return $this->getUserLeaveCounts($user, $leaveTypeId, false);
    }

    private function getCountsN1(User $user, int $leaveTypeId): ?array
    {
        return $this->getUserLeaveCounts($user, $leaveTypeId, true);
    }
}

