<?php

namespace App\Excel\Imports;

use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\LeaveTypeCategory;
use App\Models\Api\Status;
use App\Models\Api\User;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use PhpOffice\PhpSpreadsheet\Shared\Date;


class ImportLeaveAbs implements ToCollection, WithCustomCsvSettings, WithBatchInserts, WithChunkReading, SkipsEmptyRows
{
    protected $user;
    public $failedRows = 0;

    public $errors = [];
    public $rows = [];

    protected $fieldsColumns = [
        1 => 'HeaderRegistrationNumber',
        2 => 'HeaderLastname',
        3 => 'HeaderFirstname',
        4 => 'HeaderStartDate',
        5 => 'HeaderEndDate',
        6 => 'HeaderDuration',
        7 => 'HeaderLeaveTypeName',
    ];

    public function __construct()
    {
        $this->user = Auth::user();
    }

    protected function addError($message)
    {
        $this->errors[] = $message;
    }

    public function getCsvSettings(): array
    {
        return [
            'input_encoding' => 'guess',
            'delimiter' => ';',
        ];
    }

    public function batchSize(): int
    {
        return 250;
    }

    public function chunkSize(): int
    {
        return 250;
    }

    public function collection(Collection $collection)
    {
        $leaveAbsence = LeaveTypeCategory::firstWhere('slug', 'abs')->getKey();
        foreach ($collection as $index => $row) {
            $this->rows[] = $row;
            $lineNumber = $index + 1;

            $emptyData = $row->filter(function ($value) {
                return is_null($value);
            });

            if ($emptyData->isNotEmpty()) {
                $field = __('messages.'.$this->fieldsColumns[array_key_first($emptyData->toArray())]);
                $this->addError(__('validation.custom.empty_field', ['line' => $lineNumber, 'field' => __('messages.'.$field)]));
                $this->failedRows++;
                continue;
            }

            $matricule = (int) $row[1];
            $lastname = $row[2];
            $firstname = $row[3];
            $startDate =  $row[4];
            $endDate =  $row[5];
            $duration = (float)$row[6];
            $leaveTypeName = $row[7];

            $query = User::select('users.*')
                ->leftJoin('sites', 'users.site_id', 'sites.id')
                ->where('matricule', $matricule)
                ->whereRaw("CONCAT(firstname, ' ', lastname) like ?", "%{$lastname} {$firstname}%")
                ->orWhereRaw("CONCAT(lastname, ' ', firstname) like ?", "%{$lastname} {$firstname}%")
                ->where('sites.client_id', $this->user->site->client_id);

            if (!$query->exists() && $query->withTrashed()->exists()) {
                $this->addError(__('validation.custom.user_deleted', ['line' => $lineNumber, 'matricule' => $matricule]));
                $this->failedRows++;
                continue;
            } else if (!$query->exists() && !$query->withTrashed()->exists()) {
                $this->addError(__('validation.custom.user_not_found', ['line' => $lineNumber, 'matricule' => $matricule]));
                $this->failedRows++;
                continue;
            }

            $user = $query->first();

            if (!$this->validateRow($lineNumber, $matricule, $lastname, $firstname, $startDate, $endDate, $duration, $leaveTypeName)) {
                $this->failedRows++;
                continue;
            }

            $leaveTypes = LeaveType::where('site_id', $user->site->getKey())
                ->orWhere(function ($query) use ($user) {
                    $query->where('client_id', $user->site->client_id)
                        ->whereNull('site_id')
                        ->whereRaw("(SELECT COUNT(*) FROM leave_types WHERE site_id = ".$user->site->getKey()." < 1)");
                })
                ->get();

            $leaveType = $leaveTypes->where('name', $leaveTypeName)->first();
            if (!is_null($leaveType) && $leaveType->leave_type_category_id === $leaveAbsence) {
                if (!($duration > 0)) {
                    $this->addError(__('validation.custom.InvalidDurationValue', ['line' => $lineNumber, 'matricule' => $matricule]));
                    $this->failedRows++;
                    continue;
                }

                $leave = Leave::firstOrCreate([
                    'user_id' => $user->getKey(),
                    'leave_type_id' => $leaveType->getKey(),
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                ], [
                    'creator_id' => Auth::user()->getKey(),
                    'last_updater_id' => Auth::user()->getKey(),
                    'status_id' => Status::whereTag("IMPORTED")->first()->getKey(),
                    'current_validator_level' => $user->managers()->count() + 2,
                    'n' => $duration,
                    'duration' => $duration,
                ]);
                $leave->leaveDaysDistribution($user->getKey());
                $leave->save();
            } else if (!is_null($leaveType) && $leaveType->leave_type_category_id !== $leaveAbsence){
                $this->failedRows++;
            } else {
                $this->failedRows++;
                $this->addError(__('validation.custom.absence_type_not_found', ['line' => $lineNumber, 'leaveTypeCode' => $leaveTypeName]));
            }
        }
    }

    private function validateRow($lineNumber, $matricule, $lastname, $firstname, &$startDate, &$endDate, $duration, $leaveTypeName): bool
    {
        $isValid = true;

        try {
            if (is_float($startDate) && is_numeric($endDate)) {
                $startDate = Date::excelToDateTimeObject($startDate)->format('Y-m-d H:i:s');
                $endDate = Date::excelToDateTimeObject($endDate)->format('Y-m-d H:i:s');
            }
            else if (is_numeric($startDate) && is_numeric($endDate)) {
                $startDate = Carbon::instance(Date::excelToDateTimeObject($startDate))->startOfDay();
                $endDate = Carbon::instance(Date::excelToDateTimeObject($endDate))->endOfDay();
            } else {
                $startDate = Carbon::createFromFormat('d/m/Y', $startDate)->startOfDay();
                $endDate = Carbon::createFromFormat('d/m/Y', $endDate)->endOfDay();
            }
        } catch (\Exception $e) {
            $isValid = false;
            $this->addError(__('validation.custom.InvalidDateFormat', ['line' => $lineNumber]));
        }

        if (!is_numeric($matricule)) {
            $isValid = false;
            $this->addError(__('validation.custom.InvalidMatriculeFormat', ['line' => $lineNumber]));
        }

        if (!is_numeric($duration)) {
            $isValid = false;
            $this->addError(__('validation.custom.InvalidDurationFormat', ['line' => $lineNumber]));
        }

        if (!is_string($leaveTypeName)) {
            $isValid = false;
            $this->addError(__('validation.custom.InvalidLeaveTypeNameFormat', ['line' => $lineNumber]));
        }

        if (!is_string($lastname)) {
            $isValid = false;
            $this->addError(__('validation.custom.InvalidLastNameFormat', ['line' => $lineNumber]));
        }

        if (!is_string($firstname)) {
            $isValid = false;
            $this->addError(__('validation.custom.InvalidFirstNameFormat', ['line' => $lineNumber]));
        }

        return $isValid;
    }
}



