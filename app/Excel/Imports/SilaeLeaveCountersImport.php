<?php

namespace App\Excel\Imports;

use App\Models\Api\LeaveType;
use App\Models\Api\Site;
use App\Models\Api\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class SilaeLeaveCountersImport extends LeaveCountersImport
{
    protected function initialize(Collection $row, int $lineNumber): bool
    {
        $matricule = $row->get(__('messages.HeaderRegistrationNumber'));

        $siteKeys = [__('messages.HeaderCompanyName'), __('messages.HeaderEstablishment'), __('messages.HeaderMainEstablishment')];
        $siteName = $row->first(fn($value, $key) => in_array($key, $siteKeys));

        if (!$matricule) {
            $this->addError(__('validation.custom.matricule_not_specified', ['line' => $lineNumber]));
            $this->failedRows++;
            return false;
        }

        if (!$site = Site::query()->firstWhere('name', $siteName)) {
            $this->addError(__('validation.custom.site_not_existed', ['line' => $lineNumber, 'site' => $siteName]));
            $this->failedRows++;
            return false;
        }

        $query = User::query()
            ->select(['id', 'matricule', 'firstname', 'lastname', 'site_id'])
            ->whereBelongsTo($site)
            ->where('matricule', $matricule)
            ->with('site');

        if ($query->exists()) {
            $this->userModel = $query->first();
            $this->siteModel = $site;
        } else {
            $userInfos = $row->get(__('messages.HeaderEmployee'));

            $queryBadSite = User::query()
                ->select(['id', 'matricule', 'firstname', 'lastname', 'site_id'])
                ->where('matricule', $matricule)
                ->whereRaw(
                    "CONCAT(firstname, ' ', lastname) LIKE ? OR CONCAT(lastname, ' ', firstname) LIKE ?",
                    ["%$userInfos%", "%$userInfos%"]
                )
                ->with('site');

            if ($queryBadSite->exists() && $queryBadSite->first()->site()->isNot($site)) {
                $this->addError(__('validation.custom.user_bad_site', ['line' => $lineNumber, 'site' => $siteName, 'matricule' => $matricule]));
            } elseif ($query->withTrashed()->exists()) {
                $this->addError(__('validation.custom.user_deleted', ['line' => $lineNumber, 'matricule' => $matricule]));
            } else {
                $this->addError(__('validation.custom.user_not_found', ['line' => $lineNumber, 'matricule' => $matricule]));
            }

            $this->failedRows++;
            return false;
        }

        $this->leaveTypes = $this->getLeaveTypes($this->siteModel);

        $entryDateKeys = [__('messages.HeaderEntryDate'), __('messages.HeaderEntryDdate')];
        $this->updateEntryDate($row, $entryDateKeys);

        return true;
    }

    protected function process(Collection $row, string $column, int $lineNumber): void
    {
        $columnUpper = Str::upper($column);

        if (Str::contains($columnUpper, Str::upper(__('messages.HeaderAcquired') . '_'))) {
            $leaveTypeCode = Str::between($columnUpper, Str::upper(__('messages.HeaderAcquired') . '_'), '_N');

            if ($leaveType = $this->leaveTypes->where('leave_code', $leaveTypeCode)->first()) {
                if ($leaveType->needs_count) {
                    $isLastYear = Str::contains($columnUpper, $leaveTypeCode . '_N_1');

                    if (!$leaveType->is_pay && $isLastYear) {
                        return;
                    }

                    $acquiredColumn = $column;
                    $takenColumn = __('messages.HeaderTaken') . '_' . Str::after($column, __('messages.HeaderAcquired') . '_');
                    $balanceColumn = __('messages.HeaderBalance') . '_' . Str::after($column, __('messages.HeaderAcquired') . '_');

                    $this->updateLeaveCount($leaveType, $row, $acquiredColumn, $takenColumn, $balanceColumn, $isLastYear, $lineNumber);
                }
            } else {
                $this->addError(__('validation.custom.leave_type_not_found', ['line' => $lineNumber, 'leaveTypeCode' => $leaveTypeCode]));
            }
        }
    }
}
