<?php

namespace App\Excel\Imports;

use App\Models\Api\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class CegidOthersLeaveCountersImport extends LeaveCountersImport implements WithHeadingRow
{
    protected function initialize(Collection $row, int $lineNumber): bool
    {
        $matricule = $row->get(__('messages.HeaderRegistrationNumberEmployee'));

        if (is_null($matricule)) {
            $this->addError(__(('validation.custom.matricule_not_specified'), ['line' => $lineNumber]));
            $this->failedRows++;
            return false;
        }

        $user = User::query()
            ->select(['users.id', 'matricule', 'firstname', 'lastname', 'users.site_id'])
            ->leftJoin('sites', 'users.site_id', 'sites.id')
            ->where('matricule', $matricule)
            ->where('sites.client_id', $this->user->site->client_id)
            ->with('site')
            ->first();

        if (is_null($user)) {
            $this->addError(__('validation.custom.user_not_found', ['line' => $lineNumber, 'matricule' => $matricule]));
            $this->failedRows++;
            return false;
        }

        $this->userModel = $user;
        $this->siteModel = $this->userModel->site;
        $this->leaveTypes = $this->getLeaveTypes($this->siteModel);

        $entryDateKeys = [__('messages.HeaderEntryDate'), __('messages.HeaderEntryDdate')];
        $this->updateEntryDate($row, $entryDateKeys);

        return true;
    }

    protected function process(Collection $row, string $column, int $lineNumber): void
    {
        $columnUpper = Str::upper($column);

        if (Str::contains($columnUpper, '_' . Str::upper(__('messages.HeaderAcquired')))) {
            $leaveTypeCode = Str::replace('_', ' ', Str::before($columnUpper, '_' . Str::upper(__('messages.HeaderAcquired'))));

            $leaveType = $this->leaveTypes
                ->filter(fn($item) => stripos($item->leave_code, $leaveTypeCode) !== false)
                ->first();

            if (!$leaveType) {
                $this->addError(__('validation.custom.leave_type_not_found', ['line' => $lineNumber, 'leaveTypeCode' => 'CP']));
                return;
            }

            if ($leaveType->needs_count) {
                $acquiredColumn = $column;
                $takenColumn = Str::before($column, '_' . __('messages.HeaderAcquired')) . '_' . __('messages.HeaderTaken');

                $this->updateLeaveCount($leaveType, $row, $acquiredColumn, $takenColumn, lineNumber: $lineNumber);
            }
        }
    }
}
