<?php

namespace App\Excel\Imports;

use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\SkipsUnknownSheets;
use Maatwebsite\Excel\Concerns\Importable;
use Illuminate\Contracts\Auth\Authenticatable;
use App\Models\Api\User;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterImport;

class ExtraComLeaveCountersImport implements WithMultipleSheets, SkipsUnknownSheets, WithEvents
{
    use Importable;

    public array $rows = [];
    public int $failedRows = 0;
    public array $errors = [];

    protected Authenticatable|User $currentUser;
    protected array $sheetImporters = [];

    public function __construct(Authenticatable|User $currentUser)
    {
        $this->currentUser = $currentUser;
        HeadingRowFormatter::default('slug');

    }

    public function sheets(): array
    {
        $this->sheetImporters = [
            0 => new ExtraComSheetImporter($this->currentUser, 1),
            1 => new ExtraComSheetImporter($this->currentUser, 2),
        ];
        return $this->sheetImporters;
    }

    public function onUnknownSheet($sheetName)
    {
        Log::info(__('messages.SheetIgnored', ['sheetName' => $sheetName]));
    }

    public function registerEvents(): array
    {
        return [
            AfterImport::class => function(AfterImport $event) {
                $this->rows = [];
                $this->failedRows = 0;
                $this->errors = [];

                /** @var ExtraComSheetImporter  $importer */
                foreach ($this->sheetImporters as $importer) {
                    $this->rows = array_merge($this->rows, $importer->rows);
                    $this->failedRows += $importer->failedRows;
                    $this->errors = array_merge($this->errors, $importer->errors);

                    if (method_exists($importer, 'failures')) {
                        /** @var \Maatwebsite\Excel\Validators\Failure $failure */
                        foreach ($importer->failures() as $failure) {
                            $attribute = $failure->attribute();
                            $value = $failure->values()[$attribute] ?? __('validation.missing_value_placeholder');
                            $errorMessages = implode(', ', $failure->errors());
                            $this->errors[] = __('validation.cell_error_detail', [
                                'sheet' => $importer->sheetNumber,
                                'row' => $failure->row(),
                                'column' => $attribute,
                                'errors' => $errorMessages,
                                'value' => $value
                            ]);
                        }
                    }
                }
            }
        ];
    }
}
