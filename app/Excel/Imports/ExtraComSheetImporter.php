<?php

namespace App\Excel\Imports;

use App\Models\Api\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Illuminate\Contracts\Auth\Authenticatable;
use Maatwebsite\Excel\Concerns\WithCalculatedFormulas;

class ExtraComSheetImporter extends LeaveCountersImport implements WithStartRow, WithCalculatedFormulas
{
    private const int INDEX_EMPLOYEE = 0;
    private const int INDEX_EMAIL = 1;
    private const int INDEX_BALANCE_RTT_INITIAL = 5;
    private const int INDEX_PRIS_RTT = 6;
    private const int INDEX_ACQUIS_RTT = 7;
    private const int INDEX_BALANCE_RTT_FINAL = 8;
    private const int INDEX_BALANCE_CP = 9;
    public int $sheetNumber;

    public function __construct(Authenticatable|User $user, int $sheetNumber)
    {
        parent::__construct($user);
        $this->sheetNumber = $sheetNumber;
    }

    public function startRow(): int
    {
        return 2;
    }

    public function collection(Collection $collection): void
    {
        $lineNumber = $this->startRow();

        foreach ($collection as $row) {
            $this->rows[] = $row->toArray();
            $currentLine = $lineNumber++;

            $salarieRaw = $row->get(self::INDEX_EMPLOYEE);
            $emailRaw = $row->get(self::INDEX_EMAIL);

            $nomExtraitDuFichier = '';
            if (!empty($salarieRaw)) {
                $salarieTrimmed = trim((string)$salarieRaw);
                $allParts = array_values(array_filter(explode(' ', $salarieTrimmed), 'strlen'));

                if (count($allParts) > 0) {
                    $nomExtraitPart1 = $allParts[0];

                    if (Str::length($nomExtraitPart1) <= 2 && count($allParts) > 1) {
                        $nomExtraitDuFichier = $nomExtraitPart1 . ' ' . $allParts[1];
                    } else {
                        $nomExtraitDuFichier = $nomExtraitPart1;
                    }
                }
            }
            $nomExtraitNormalise = trim(Str::lower($nomExtraitDuFichier));

            if (empty($nomExtraitNormalise)) {
                $this->addError(__('warnings.LastNameMissingFromSalarie', ['line' => $currentLine, 'salarie' => $salarieRaw, 'sheetnbmr' => $this->sheetNumber]));
                $this->failedRows++;
                continue;
            }

            $emailNormalized = $this->normalizeEmail($emailRaw);

            if (empty($emailNormalized)) {
                $this->addError(__('warnings.EmailMissingInvalidNormalized', ['line' => $currentLine, 'original_email' => $emailRaw, 'sheetnbmr' => $this->sheetNumber]));
                $this->failedRows++;
                continue;
            }
            if (!filter_var($emailNormalized, FILTER_VALIDATE_EMAIL)) {
                $this->addError(__('warnings.EmailFormatInvalidNormalized', [
                    'line' => $currentLine,
                    'normalized_email' => $emailNormalized,
                    'original_email' => $emailRaw,
                    'sheetnbmr' => $this->sheetNumber
                ]));
                $this->failedRows++;
                continue;
            }

            $user = User::query()->with('site')->where('email', $emailNormalized)->first();

            if (!$user) {
                $this->addError(__('warnings.UserNotFoundImport', [
                    'line' => $currentLine,
                    'normalized_email' => $emailNormalized,
                    'original_email' => $emailRaw,
                    'sheetnbmr' => $this->sheetNumber
                ]));
                $this->failedRows++;
                continue;
            }

            if (!$user->site) {
                $this->addError(__('warnings.UserMissingSite', ['line' => $currentLine, 'normalized_email' => $emailNormalized, 'sheetnbmr' => $this->sheetNumber]));
                $this->failedRows++;
                continue;
            }
            $this->userModel = $user;
            $this->siteModel = $user->site;

            $this->leaveTypes = $this->getLeaveTypes($this->siteModel);
            $rttType = $this->leaveTypes->firstWhere('leave_code', 'RTT');
            $cpType = $this->leaveTypes->firstWhere('leave_code', 'CP');

            if ($rttType) {
                $soldeRttInitialRaw = $row->get(self::INDEX_BALANCE_RTT_INITIAL);
                $prisRttRaw = $row->get(self::INDEX_PRIS_RTT);
                $acquisRttRaw = $row->get(self::INDEX_ACQUIS_RTT);
                $soldeRttFinalRaw = $row->get(self::INDEX_BALANCE_RTT_FINAL);

                $processRtt = true;

                if (!isset($soldeRttInitialRaw) || !is_numeric($this->convertToFloat($soldeRttInitialRaw))) {
                    $this->addError(__('warnings.SoldeRttInitialInvalidOrMissing', ['line' => $currentLine, 'value' => $soldeRttInitialRaw ?? 'NULL', 'sheetnbmr' => $this->sheetNumber]));
                    $processRtt = false;
                }
                $soldeRttInitial = $this->convertToFloat($soldeRttInitialRaw);

                if (!isset($acquisRttRaw) || !is_numeric($this->convertToFloat($acquisRttRaw))) {
                    $this->addError(__('warnings.AcquisRttInvalidOrMissing', ['line' => $currentLine, 'value' => $acquisRttRaw ?? 'NULL', 'sheetnbmr' => $this->sheetNumber]));
                    $processRtt = false;
                }
                $rttAcquisFichier = $this->convertToFloat($acquisRttRaw);


                if (!isset($prisRttRaw) || !is_numeric($this->convertToFloat($prisRttRaw))) {
                    $this->addError(__('warnings.PrisRttInvalidOrMissing', ['line' => $currentLine, 'value' => $prisRttRaw ?? 'NULL', 'sheetnbmr' => $this->sheetNumber]));
                    $processRtt = false;
                }
                $rttPrisFichier = $this->convertToFloat($prisRttRaw);


                if (!isset($soldeRttFinalRaw) || !is_numeric($this->convertToFloat($soldeRttFinalRaw))) {
                    $this->addError(__('warnings.SoldeRttFinalInvalidOrMissing', ['line' => $currentLine, 'value' => $soldeRttFinalRaw ?? 'NULL', 'sheetnbmr' => $this->sheetNumber]));
                    $processRtt = false;
                }
                $soldeRttFinalFichier = $this->convertToFloat($soldeRttFinalRaw);

                if ($processRtt) {
                    $rttAcquisBdd = $soldeRttInitial + $rttAcquisFichier;
                    $rttSoldeCalcule = $rttAcquisBdd - $rttPrisFichier;

                    if (abs($soldeRttFinalFichier - $rttSoldeCalcule) >= 0.01) {
                        $this->addError(__('warnings.RttBalanceMismatch', [
                            'line' => $currentLine,
                            'email' => $this->userModel->email,
                            'solde_fichier' => $soldeRttFinalFichier,
                            'solde_calcule' => $rttSoldeCalcule,
                            'sheetnbmr' => $this->sheetNumber
                        ]));
                    }

                    $dataRtt = [
                        'rh_update' => Carbon::now(),
                        'acquired' => $rttAcquisBdd,
                        'taken' => $rttPrisFichier,
                        'balance' => $soldeRttFinalFichier,
                    ];

                    $this->updateCurrentYearCountersForRtt($rttType, $dataRtt, $currentLine, $this->sheetNumber);
                }
            } else {
                $this->addError(__('messages.RttTypeNotFound', ['line' => $currentLine, 'sheetnbmr' => $this->sheetNumber]));
            }

            // -- CP --
            if ($cpType) {
                $soldeCpRaw = $row->get(self::INDEX_BALANCE_CP);

                $processCp = true;

                if (!isset($soldeCpRaw) || !is_numeric($this->convertToFloat($soldeCpRaw))) {
                    $this->addError(__('warnings.SoldeCpInvalidOrMissing', ['line' => $currentLine, 'value' => $soldeCpRaw ?? 'NULL', 'sheetnbmr' => $this->sheetNumber]));
                    $processCp = false;
                }
                $soldeCp = $this->convertToFloat($soldeCpRaw);

                if ($processCp) {
                    $this->updateUserCpCounter($cpType, $soldeCp, $currentLine, $this->sheetNumber);
                }
            } else {
                $this->addError(__('messages.CpTypeNotFound', ['line' => $currentLine, 'sheetnbmr' => $this->sheetNumber]));
            }
        }
    }

    protected function initialize(Collection $row, int $lineNumber): bool
    {
        return true;
    }
}
