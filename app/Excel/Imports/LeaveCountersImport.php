<?php

namespace App\Excel\Imports;

use App\Models\Api\LeaveType;
use App\Models\Api\Site;
use App\Models\Api\User;
use App\Models\Api\UserLeaveCount;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use PhpOffice\PhpSpreadsheet\Shared\Date;

abstract class LeaveCountersImport implements ToCollection, WithCustomCsvSettings, SkipsEmptyRows
{
    use Importable;
    protected User $userModel;
    protected Site $siteModel;
    protected Collection $leaveTypes;
    public array $rows = [];
    public int $failedRows = 0;
    public array $errors = [];

    public function __construct(
        public Authenticatable|User $user,
    )
    {
    }

    public function collection(Collection $collection): void
    {
        $collection = $this->preProcessCollection($collection);
        $lineNumber = 1;

        foreach ($collection as $row) {
            if ($this->initialize($row, $lineNumber)) {

                foreach ($row as $column => $value) {
                    $this->process($row, $column, $lineNumber);
                }
            }
            $lineNumber++;
        }
    }

    public function getCsvSettings(): array
    {
        return [
            'input_encoding' => 'guess',
            'delimiter' => ';',
        ];
    }

    protected function preProcessCollection(Collection $collection): Collection
    {
        return $collection;
    }

    protected function getLeaveTypes(Site $site): Collection
    {
        $leaveTypes = LeaveType::query()->whereBelongsTo($site)->get();

        return $leaveTypes->isNotEmpty()
            ? $leaveTypes
            : LeaveType::query()->where(fn($query) => $query->where('client_id', $site->client_id)->whereNull('site_id'))->get();
    }

    abstract protected function initialize(Collection $row, int $lineNumber): bool;

    protected function process(Collection $row, string $column, int $lineNumber): void
    {
    }

    protected function updateCurrentYearCountersForRtt(LeaveType $leaveType, array $leaveData, int $lineNumber,int $sheet): void
    {
        if (!isset($leaveData['acquired'], $leaveData['taken'], $leaveData['balance'], $leaveData['rh_update'])) {
            $this->addError(__('warnings.IncompleteLeaveDataForUpdate', [
                'sheet' => $sheet,
                'line' => $lineNumber,
                'leave_code' => $leaveType->leave_code,
                'expected_fields' => 'acquired, taken, balance, rh_update'
            ]));
            return;
        }

        try {
            UserLeaveCount::query()
                ->updateOrCreate(
                    [
                        'user_id' => $this->userModel->getKey(),
                        'leave_type_id' => $leaveType->getKey(),
                        'is_last_year' => true
                    ],
                    $leaveData
                );
        } catch (\Exception $e) {
            $this->addError(__('warnings.DbErrorLastYearUpdate', [
                'sheet' => $sheet,
                'line' => $lineNumber,
                'leave_code' => $leaveType->leave_code,
                'db_message' => $e->getMessage()
            ]));
        }
    }

    protected function updateUserCpCounter(LeaveType $leaveType, float $valueFromExcel, int $lineNumber, int $sheet): void
    {
        $dataForCounter = [
            'rh_update' => Carbon::now(),
            'acquired' => $valueFromExcel,
            'taken' => 0,
            'balance' => $valueFromExcel,
        ];

        $conditions = [
            'user_id' => $this->userModel->getKey(),
            'leave_type_id' => $leaveType->getKey(),
            'is_last_year' => false
        ];

        try {
            $existingCounter = UserLeaveCount::query()
                ->where('user_id', $this->userModel->getKey())
                ->where('leave_type_id', $leaveType->getKey())
                ->where('is_last_year', false)
                ->first();

            if ($existingCounter) {
                if (abs($existingCounter->acquired - $valueFromExcel) >= 0.01) {
                    $this->addError(__('warnings.CpAcquiredMismatchDetected', [
                        'sheet' => $sheet,
                        'line' => $lineNumber,
                        'user_lastname' => $this->userModel->lastname,
                        'file_acquired_value' => $valueFromExcel,
                        'db_acquired_value' => $existingCounter->acquired
                    ]));
                    $existingCounter->update($dataForCounter);
                    $this->addError(__('warnings.CpAnnualUpdate', [
                        'sheet' => $sheet,
                        'line' => $lineNumber,
                        'user_lastname' => $this->userModel->lastname,
                        'new_acquired_value' => $valueFromExcel
                    ]));
                }
            } else {
                UserLeaveCount::query()->create(array_merge($conditions, $dataForCounter));
                $this->addError(__('warnings.CpNewCounterCreated', [
                    'sheet' => $sheet,
                    'line' => $lineNumber,
                    'user_lastname' => $this->userModel->lastname,
                    'acquired_value' => $valueFromExcel
                ]));
            }
        } catch (\Exception $e) {
            $this->addError(__('warnings.DbErrorCpUpdate', [
                'sheet' => $sheet,
                'line' => $lineNumber,
                'leave_code' => $leaveType->leave_code,
                'db_message' => $e->getMessage()
            ]));
        }
    }

    protected function convertToFloat($value): float
    {
        if (is_numeric($value)) {
            return (float) $value;
        }
        $cleanedValue = preg_replace('/\s+/', '', $value);
        $cleanedValue = str_replace(',', '.', $cleanedValue);
        if (is_numeric($cleanedValue)) {
            return (float) $cleanedValue;
        }
        return 0.0;
    }

    public function addError(string $message): void
    {
        $this->errors[] = $message;
    }

    protected function updateLeaveCount(LeaveType $leaveType, Collection $row, string $acquiredColumn, string $takenColumn, ?string $balanceColumn = null, bool $isLastYear = false, int $lineNumber = 0): void
    {
        $acquired = $this->convertToFloat($row->get($acquiredColumn, 0));
        $taken = $this->convertToFloat($row->get($takenColumn, 0));
        $balance = !empty($balanceColumn) ? $this->convertToFloat($row->get($balanceColumn)) : null;

        $data = ['rh_update' => Carbon::now(), 'acquired' => $acquired, 'taken' => $taken];

        if ($balance !== null && abs($balance - ($acquired - $taken)) >= 0.01) {
            $this->addError(__('validation.custom.balance_invalid', ['line' => $lineNumber]));
            return;
        }
        try {
            UserLeaveCount::query()
                ->updateOrCreate(
                    ['user_id' => $this->userModel->getKey(), 'leave_type_id' => $leaveType->getKey(), 'is_last_year' => $isLastYear],
                    $data
                );
        } catch (\Exception $e) {
            $this->addError("Erreur DB Ligne {$lineNumber} pour {$leaveType->leave_code}: " . $e->getMessage());
        }
    }

    protected function updateEntryDate(Collection $row, array $entryDateKeys): void
    {
        $entryDate = $row->first(fn($value, $key) => in_array($key, $entryDateKeys));
        if ($entryDate) {
            try {
                if (Str::contains($entryDate, '=')) {
                    $entryDate = Str::replace('=', '', $entryDate);
                }
                $formattedDate = is_numeric($entryDate)
                    ? Date::excelToDateTimeObject($entryDate)->format('Y-m-d H:i:s')
                    : Carbon::createFromFormat('d/m/Y', $entryDate)->format('Y-m-d H:i:s');
                $this->userModel->update(['enter_date' => $formattedDate]);
            } catch (\Exception $e) {
                $this->addError("Avertissement: Impossible de lire la date d'entrée '{$entryDate}' pour user {$this->userModel->matricule}.". $e->getMessage());
            }
        }
    }
    protected function normalizeName(string $name): string
    {
        $normalized = strtolower($name);
        $normalized = Str::ascii($normalized);
        $normalized = preg_replace('/[^a-z\s]/', '', $normalized);
        $normalized = preg_replace('/\s+/', ' ', $normalized);

        return trim($normalized);
    }

    protected function normalizeEmail(?string $email): ?string
    {
        if (empty($email)) {
            return null;
        }
        $emailLower = strtolower($email);
        $normalized = Str::ascii($emailLower);
        $normalized = preg_replace('/[^a-z0-9@._-]/', '', $normalized);

        return $normalized ?: null;
    }
}
