<?php

namespace App\Excel\Imports;

use App\Models\Api\LeaveType;
use App\Models\Api\Site;
use App\Models\Api\User;
use App\Models\Api\UserLeaveCount;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;
use PhpOffice\PhpSpreadsheet\Shared\Date;


abstract class LeaveCountersImport implements ToCollection, WithHeadingRow, WithCustomCsvSettings, SkipsEmptyRows
{
    use Importable;

    protected User $userModel;
    protected Site $siteModel;
    protected Collection $leaveTypes;

    public array $rows = [];
    public int $failedRows = 0;
    public array $errors = [];

    public function __construct(
        public Authenticatable|User $user,
    )
    {
        HeadingRowFormatter::default('unique');
    }

    public function collection(Collection $collection): void
    {
        $collection = $this->preProcessCollection($collection);

        $lineNumber = $this->getStartingLineNumber();

        foreach ($collection as $row) {
            $this->rows[] = $row;
            $lineNumber++;

            if ($this->initialize($row, $lineNumber)) {
                foreach ($row as $column => $value) {
                    $this->process($row, $column, $lineNumber);
                }
            }
        }
    }

    public function getCsvSettings(): array
    {
        return [
            'input_encoding' => 'guess',
            'delimiter' => ';',
        ];
    }

    protected function preProcessCollection(Collection $collection): Collection
    {
        return $collection;
    }

    protected function getLeaveTypes(Site $site): Collection
    {
        return LeaveType::query()
            ->whereBelongsTo($site)
            ->orWhere(fn($query) => $query->where('client_id', $site->client_id)->whereNull('site_id'))
            ->get();
    }

    protected function getStartingLineNumber(): int
    {
        return 1;
    }

    abstract protected function initialize(Collection $row, int $lineNumber): bool;

    abstract protected function process(Collection $row, string $column, int $lineNumber): void;

    protected function updateLeaveCount(
        LeaveType  $leaveType,
        Collection $row,
        string     $acquiredColumn,
        string     $takenColumn,
        ?string    $balanceColumn = null,
        bool       $isLastYear = false,
        int        $lineNumber = 0
    ): void
    {
        $acquired = (float)$row->get($acquiredColumn, 0);
        $taken = (float)$row->get($takenColumn, 0);
        $balance = !empty($balanceColumn) ? (float)$row->get($balanceColumn) : null;

        $data = ['rh_update' => Carbon::now(), 'acquired' => $acquired, 'taken' => $taken];

        if ($balance !== null && abs($balance - ($acquired - $taken)) >= 0.01) {
            $this->addError(__('validation.custom.balance_invalid', ['line' => $lineNumber]));
            return;
        }

        UserLeaveCount::query()
            ->updateOrCreate(
                ['user_id' => $this->userModel->getKey(), 'leave_type_id' => $leaveType->getKey(), 'is_last_year' => $isLastYear],
                $data
            );
    }

    /**
     * Update user entry date
     */
    protected function updateEntryDate(Collection $row, array $entryDateKeys): void
    {
        $entryDate = $row->first(fn($value, $key) => in_array($key, $entryDateKeys));

        if ($entryDate) {
            if (Str::contains($entryDate, '=')) {
                $entryDate = Str::replace('=', '', $entryDate);
            }

            $formattedDate = is_numeric($entryDate)
                ? Date::excelToDateTimeObject($entryDate)->format('Y-m-d H:i:s')
                : Carbon::createFromFormat('d/m/Y', $entryDate);

            $this->userModel->update(['enter_date' => $formattedDate]);
        }
    }

    protected function addError($message): void
    {
        $this->errors[] = $message;
    }
}
