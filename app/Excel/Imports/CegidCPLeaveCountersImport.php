<?php

namespace App\Excel\Imports;

use App\Models\Api\LeaveType;
use App\Models\Api\Site;
use App\Models\Api\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class CegidCPLeaveCountersImport extends LeaveCountersImport
{
    protected function preProcessCollection(Collection $collection): Collection
    {
        return $collection->filter(function ($row) {
            return !empty($row['salarie']) && $row['salarie'] !== __('messages.Total') && $row['salarie'] !== __('messages.TotalGeneral');
        });
    }

    protected function getLeaveTypes(Site $site): Collection
    {
        return LeaveType::query()->where('leave_code', 'CP')->where('client_id', $site->client_id)->get();
    }

    protected function initialize(Collection $row, int $lineNumber): bool
    {
        $userInfos = $row->get(__('messages.HeaderEmployee'));

        if (is_null($userInfos)) {
            $this->addError(__('validation.custom.user_not_specified', ['line' => $lineNumber]));
            $this->failedRows++;
            return false;
        }

        $matricule = Str::before($userInfos, ' ');

        if (is_null($matricule)) {
            $this->addError(__(('validation.custom.matricule_not_specified'), ['line' => $lineNumber]));
            $this->failedRows++;
            return false;
        }

        $user = User::query()
            ->select(['users.id', 'matricule', 'firstname', 'lastname', 'users.site_id'])
            ->leftJoin('sites', 'users.site_id', 'sites.id')
            ->where('matricule', $matricule)
            ->where('sites.client_id', $this->user->site->client_id)
            ->with('site')
            ->first();

        if (is_null($user)) {
            $this->addError(__('validation.custom.user_not_found', ['line' => $lineNumber, 'matricule' => $matricule]));
            $this->failedRows++;
            return false;
        }

        $this->userModel = $user;
        $this->siteModel = $this->userModel->site;
        $this->leaveTypes = $this->getLeaveTypes($this->siteModel);

        return true;
    }

    protected function process(Collection $row, string $column, int $lineNumber): void
    {
        $columnUpper = Str::upper($column);

        if ($columnUpper === Str::upper(__('messages.HeaderAcquired')) || $columnUpper === Str::upper(__('messages.HeaderAcquired') . '_2')) {
            $leaveType = $this->leaveTypes->first();

            if (!$leaveType) {
                $this->addError(__('validation.custom.leave_type_not_found', ['line' => $lineNumber, 'leaveTypeCode' => 'CP']));
                return;
            }

            if ($leaveType->needs_count) {
                $isLastYear = !Str::contains($columnUpper, '2');

                if (!$leaveType->is_pay && $isLastYear) {
                    return;
                }

                $acquiredColumn = $column;
                $takenColumn = ($isLastYear === false ? __('messages.HeaderTaken') . '_2' : __('messages.HeaderTaken'));
                $balanceColumn = ($isLastYear === false ? __('messages.HeaderRemaining') . '_2' : __('messages.HeaderRemaining'));

                $this->updateLeaveCount($leaveType, $row, $acquiredColumn, $takenColumn, $balanceColumn, $isLastYear, $lineNumber);
            }
        }
    }
}
