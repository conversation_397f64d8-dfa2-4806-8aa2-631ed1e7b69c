<?php

namespace App\Utils\Statistics;

use App\Models\Api\User;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;

class StatisticsBuilder
{
    public static function applySorts(Builder $query, array $sorts): void
    {
        if (!empty($sorts)) {
            $query->reorder();
        }

        foreach ($sorts as $sort) {
            $query->orderBy($sort['field'], $sort['direction']);
        }
    }

    public static function applyDateRange(Builder $query, string $table, array $dateRange): void
    {
        $startDate = Carbon::parse($dateRange['start_date']);
        $endDate = Carbon::parse($dateRange['end_date']);

        $fields = array_keys($dateRange);

        $query->where(function (Builder $query) use ($table, $fields, $endDate, $startDate) {
            $query->where(function (Builder $query) use ($table, $fields, $startDate) {
                $query->where("$table.$fields[0]", '<=', $startDate)
                    ->where("$table.$fields[1]", '>', $startDate);
            })
                ->orWhere(function (Builder $query) use ($table, $fields, $endDate) {
                    $query->where("$table.$fields[0]", '<=', $endDate)
                        ->where("$table.$fields[1]", '>', $endDate);
                })
                ->orWhere(function (Builder $query) use ($table, $fields, $endDate, $startDate) {
                    $query->where("$table.$fields[0]", '>=', $startDate)
                        ->where("$table.$fields[1]", '<=', $endDate);
                });
        });
    }

    public static function applyManagerQuery(Builder $query, User $currentUser): void
    {
        $query->leftJoin('managers', function (JoinClause $joinQuery) use ($currentUser) {
            $joinQuery->on('managers.managed_id', 'users.id')
                ->where('managers.manager_id', $currentUser->id);
        });

        self::applyDirectorQuery($query, $currentUser);
    }

    public static function applyDirectorQuery(Builder $query, User $user): void
    {
        $query->leftJoin('director_user', 'director_user.user_id', 'users.id')
            ->where(function (Builder $whereQuery) use ($user) {
                $whereQuery->whereNotNull('managers.manager_id')
                    ->when($user->profile->label === 'DIRECTOR', function (Builder $whenQuery) use ($user) {
                        $whenQuery->orWhere(fn(Builder $whereQuery) => $whereQuery->whereNotNull('director_user.director_id'));
                    });
            });
    }
}
