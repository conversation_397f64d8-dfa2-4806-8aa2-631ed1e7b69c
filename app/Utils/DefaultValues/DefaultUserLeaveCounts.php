<?php

namespace App\Utils\DefaultValues;

use App\Models\Api\LeaveType;
use App\Models\Api\User;
use Illuminate\Database\Eloquent\Collection;

class DefaultUserLeaveCounts
{
    public function run(User $user, Collection $leaveTypes)
    {
        $leaveTypes->each(function (LeaveType $leaveType) use ($user) {
            if ($leaveType->needs_count) {
                $user->user_leave_counts()->create([
                    'leave_type_id' => $leaveType->getKey(),
                    'is_last_year' =>  false,
                ]);

                if ($leaveType->is_pay) {
                    $user->user_leave_counts()->create([
                        'leave_type_id' => $leaveType->getKey(),
                        'is_last_year' =>  true,
                    ]);
                }
            }
        });
    }
}
