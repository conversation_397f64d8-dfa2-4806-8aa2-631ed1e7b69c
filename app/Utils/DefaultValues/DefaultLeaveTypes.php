<?php

namespace App\Utils\DefaultValues;

use App\Models\Api\Client;
use App\Models\Api\LeaveType;
use Illuminate\Support\Arr;

class DefaultLeaveTypes
{
    public function run(Client $client)
    {
        $leaveTypesArray = [
            [
                'name' => 'Congés payés',
                'is_pay' => true,
                'is_active' => true,
                'is_monthly' => true,
                'is_deletable' => false,
                'is_half_day' => true,
                'is_attachment_required' => false,
                'can_exceed' => false,
                'leave_code' => 'CP',
                'color' => '#99cc33',
                'order_appearance' => 1,
                'needs_count' => true,
            ],
            [
                'name' => 'RTT',
                'is_active' => false,
                'is_monthly' => true,
                'is_deletable' => false,
                'is_half_day' => false,
                'is_attachment_required' => false,
                'can_exceed' => false,
                'leave_code' => 'RTT',
                'color' => '#33cccc',
                'order_appearance' => 2,
                'needs_count' => true,
            ],
            [
                'name' => 'Récupération',
                'is_active' => false,
                'is_monthly' => true,
                'is_deletable' => false,
                'is_half_day' => false,
                'is_attachment_required' => false,
                'can_exceed' => false,
                'leave_code' => 'REC',
                'color' => '#009999',
                'order_appearance' => 3,
                'needs_count' => true,
            ],
            [
                'name' => 'Absence événement familial',
                'is_active' => true,
                'is_monthly' => false,
                'is_deletable' => false,
                'is_half_day' => false,
                'is_attachment_required' => true,
                'can_exceed' => false,
                'leave_code' => 'AEF',
                'color' => '#ffcc66',
                'order_appearance' => 0,
                'needs_count' => false,
                'leave_type_sub_families' => [
                    [
                        'name' => 'Enfant malade',
                        'value' => 4,
                    ],
                    [
                        'name' => 'Mariage du salarié',
                        'value' => 4,
                    ],
                    [
                        'name' => 'Mariage d\'un enfant',
                        'value' => 1,
                    ],
                    [
                        'name' => 'Naissance ou Adoption',
                        'value' => 3,
                    ],
                    [
                        'name' => 'Décès d\'un enfant',
                        'value' => 5,
                    ],
                    [
                        'name' => 'Décès de son partenaire',
                        'value' => 3,
                    ],
                    [
                        'name' => 'Décès membre de la famille proche',
                        'value' => 3,
                    ],
                ],
            ],
            [
                'name' => 'Congés paternité',
                'is_active' => true,
                'is_monthly' => false,
                'is_deletable' => false,
                'is_half_day' => false,
                'is_attachment_required' => true,
                'can_exceed' => false,
                'default_leave_value' => 11,
                'leave_code' => 'CPATER',
                'color' => '#ffccff',
                'order_appearance' => 0,
                'needs_count' => false,
            ],
            [
                'name' => 'Congés sans soldes',
                'is_active' => false,
                'is_monthly' => false,
                'is_deletable' => false,
                'is_half_day' => false,
                'is_attachment_required' => false,
                'can_exceed' => true,
                'leave_code' => 'CSS',
                'color' => '#ffffcc',
                'order_appearance' => 0,
                'needs_count' => false,
            ],
        ];

        foreach ($leaveTypesArray as $leaveTypeArray) {
            $leaveType = $client->leave_types()->create(
                Arr::except($leaveTypeArray, 'leave_type_sub_families')
            );

            if (isset($leaveTypeArray['leave_type_sub_families'])) {
                $leaveType->leave_type_sub_families()->createMany($leaveTypeArray['leave_type_sub_families']);
            }
        }
    }
}
