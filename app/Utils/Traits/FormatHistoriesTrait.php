<?php

namespace App\Utils\Traits;

use App\Models\Api\History;
use App\Models\Api\Status;
use App\Models\Api\User;

trait FormatHistoriesTrait
{
    private function formatHistories($leave)
    {
        // TODO: Supprimer ce foreach car le chargement des relations 'histories.status'
        // et 'histories.user' doit être géré par le frontend via les 'includes' Orion.
        // Assurez-vous que les requêtes front incluent 'histories.status' et 'histories.user' lorsqu'elles incluent 'histories'.
        foreach ($leave->histories as $key => $history) {
            $leave->histories[$key]->status = Status::find($history->status_id);
            $leave->histories[$key]->user = User::select('firstname', 'lastname')->find($leave->histories[$key]->user_id);
        }

        $userLeave = User::query()->withTrashed()->find($leave->user_id);
        $validationScheme = $userLeave->site->client->validation_scheme;

        switch ($leave->histories->sortBy('created_at')->last()->status->tag) {
            case 'SUBMITTED':
                $status = [
                    'tag' => 'WAITING_VALIDATION',
                    'color' => '0xFFFFBB00',
                    'name' => ($validationScheme === 'VERTICAL' ? __('messages.WaitingValidationManager') : __('messages.WaitingValidationManagers'))
                ];
                $user = ($validationScheme === 'VERTICAL' ? $userLeave->managers()->select('firstname', 'lastname')->firstWhere('level', $leave->current_validator_level) : null);
                break;
            case 'SUBMITTED_TO_CANCELLATION':
                $status = [
                    'tag' => 'WAITING_CANCELLATION',
                    'color' => '0xFFFFBB00',
                    'name' => ($validationScheme === 'VERTICAL' ? __('messages.WaitingCancelationManager') : __('messages.WaitingCancelationManagers'))
                ];
                $user = ($validationScheme === 'VERTICAL' ? $userLeave->managers()->select('firstname', 'lastname')->orderBy('level')->first() : null);
                break;
            default:
                return $leave;
        }

        $newHistory = new History();
        $newHistory->status = new Status($status);
        $newHistory->user = $user;

        $leave->histories->push($newHistory);

        return $leave;
    }
}
