<?php

namespace App\Utils\Traits\Models;

use App\Models\Api\Client;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Pivot;

trait Reorderable
{
    public function reorderModel(Model $updatedModel, Model $parentModel, string $relation, int $oldOrder, int $newOrder): void
    {
        $toIncrement = $newOrder < $oldOrder;

        $parentModel->$relation()
            ->where($updatedModel->getTable().'.order_appearance', $toIncrement ? '>=' : '<=', $newOrder)
            ->where($updatedModel->getTable().'.order_appearance', $toIncrement ? '<' : '>', $oldOrder)
            ->when(get_class($parentModel) === Client::class,
                function (Builder $query) use ($updatedModel){
                    $query->where($updatedModel->getTable().'.site_id', '=', null);
                },
            )
            ->where($updatedModel->getTable().'.id', '!=', $updatedModel->getKey())
            ->each(function (Model $modelOrderToIncrement) use ($toIncrement, $updatedModel) {
                if ($updatedModel instanceof Pivot) {
                    $toIncrement ? $modelOrderToIncrement->pivot->order_appearance++ : $modelOrderToIncrement->pivot->order_appearance--;
                    $modelOrderToIncrement->pivot->saveQuietly();
                } else {
                    $toIncrement ? $modelOrderToIncrement->order_appearance++ : $modelOrderToIncrement->order_appearance--;
                    $modelOrderToIncrement->saveQuietly();
                }
            }, 100);
    }
}
