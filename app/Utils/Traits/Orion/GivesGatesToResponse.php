<?php

namespace App\Utils\Traits\Orion;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Gate;

trait GivesGatesToResponse
{
    public function entityResponse(Model $entity): JsonResource
    {
        $this->addGatesToEntity($entity);

        return parent::entityResponse($entity);
    }

    /**
     * @param  LengthAwarePaginator|Collection  $entities
     */
    public function collectionResponse($entities): ResourceCollection
    {
        foreach ($entities as $entity) {
            $this->addGatesToEntity($entity);
        }

        $this->withMeta('authorizedToCreate', $this->authorizedTo('create', $this->resolveResourceModelClass()));

        return parent::collectionResponse($entities);
    }

    protected function addGatesToEntity(Model $entity)
    {
        // TODO : A voir comment faire
        if (! request()->input('disabledPermissions', false)) {
            $entity->forceFill(['permissions' => [
                'authorizedToView' => $this->authorizedTo('view', $entity),
                'authorizedToUpdate' => $this->authorizedTo('update', $entity),
                'authorizedToDelete' => $this->authorizedTo('delete', $entity),
                'authorizedToForceDelete' => $this->authorizedTo('force_delete', $entity),
                'authorizedToRestore' => $this->authorizedTo('restore', $entity),
            ]]);
        }
    }

    protected function authorizedTo($method, $entity)
    {
        $key = sprintf('%s-%s-%s-%s', is_string($entity) ? $entity : get_class($entity), is_string($entity) ? $entity : $entity->getKey(), $method, Auth::check() ? Auth::user()->getKey() : 'guest');

        return Cache::remember($key, $this->cacheGatesFor(), fn () => Gate::check($method, $entity));
    }

    protected function cacheGatesFor()
    {
        return now()->addSeconds(15);
    }

    protected function responseWithGates($response, $class)
    {
        if ($response instanceof Arrayable) {
            $response = $response->toArray();
        }

        $response['meta']['authorizedToCreate'] = $this->authorizedTo('create', $class);

        $response['data'] = $class::hydrate($response['data'])->map(function ($model) {
            $this->addGatesToEntity($model);

            return $model;
        });

        return $response;
    }
}
