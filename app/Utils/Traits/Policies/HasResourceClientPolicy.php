<?php

namespace App\Utils\Traits\Policies;

use Illuminate\Database\Eloquent\Model;

trait HasResourceClientPolicy
{
    protected function hasSameClient(Model $user, Model $model): bool
    {
        return $model->site->client()->is($user->site->client);
    }

    /**
     * Determine whether the user can update the model if he is the owner.
     */
    protected function updateClient(Model $user, Model $model): bool
    {
        return $this->hasSameClient($user, $model);
    }

    /**
     * Determine whether the user can view the model if he is the owner.
     */
    protected function viewClient(Model $user, Model $model): bool
    {
        return $this->hasSameClient($user, $model);
    }

    /**
     * Determine whether the user can view the model if he is the owner.
     */
    protected function deleteClient(Model $user, Model $model): bool
    {
        return $this->hasSameClient($user, $model);
    }
}
