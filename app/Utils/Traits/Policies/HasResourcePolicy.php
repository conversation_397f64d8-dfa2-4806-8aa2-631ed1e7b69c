<?php

namespace App\Utils\Traits\Policies;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

trait HasResourcePolicy
{
    /**
     * Get the table associated with the policy.
     *
     * @return string
     */
    public function getTable()
    {
        return Str::snake(Str::pluralStudly(Str::replaceLast('Policy', '', class_basename($this))));
    }

    /**
     * Determine whether the user can view any models.
     *
     * @return mixed
     */
    public function viewAny(Model $user)
    {
        return $user->canAny(["view {$this->getTable()}", "view client {$this->getTable()}", "view own {$this->getTable()}"]);
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return mixed
     */
    public function view(Model $user, Model $model)
    {
        if (method_exists($this, 'viewShared') && $this->viewShared($user, $model)) {
            return true;
        }

        if (method_exists($this, 'viewDiffused') && $this->viewDiffused($user, $model)) {
            return true;
        }

        if ($user->can("view {$this->getTable()}")) {
            return true;
        } elseif ($user->can("view client {$this->getTable()}") && method_exists($this, 'viewClient')) {
            return $this->viewClient($user, $model);
        } elseif ($user->can("view own {$this->getTable()}") && method_exists($this, 'viewOwn')) {
            return $this->viewOwn($user, $model);
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     *
     * @return mixed
     */
    public function create(Model $user)
    {
        return $user->can("create {$this->getTable()}");
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return mixed
     */
    public function update(Model $user, Model $model)
    {
        if (method_exists($model, 'updatable') && $model->updatable() !== true) {
            return false;
        }
        if (method_exists($this, 'updateShared') && $this->updateShared($user, $model)) {
            return true;
        }

        if ($user->can("update {$this->getTable()}")) {
            return true;
        } elseif ($user->can("update client {$this->getTable()}") && method_exists($this, 'updateClient')) {
            return $this->updateClient($user, $model);
        } elseif ($user->can("update own {$this->getTable()}") && method_exists($this, 'updateOwn')) {
            return $this->updateOwn($user, $model);
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return mixed
     */
    public function delete(Model $user, Model $model)
    {
        if (method_exists($model, 'deletable') && $model->deletable() !== true) {
            return false;
        }
        if (method_exists($this, 'deleteShared') && $this->deleteShared($user, $model)) {
            return true;
        }

        if ($user->can("delete {$this->getTable()}")) {
            return true;
        } elseif ($user->can("delete client {$this->getTable()}") && method_exists($this, 'deleteClient')) {
            return $this->deleteClient($user, $model);
        } elseif ($user->can("delete own {$this->getTable()}") && method_exists($this, 'deleteOwn')) {
            return $this->deleteOwn($user, $model);
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @return mixed
     */
    public function restore(Model $user, Model $model)
    {
        if (method_exists($this, 'restoreShared') && $this->restoreShared($user, $model)) {
            return true;
        }

        if ($user->can("restore {$this->getTable()}")) {
            return true;
        } elseif ($user->can("restore client {$this->getTable()}") && method_exists($this, 'restoreClient')) {
            return $this->restoreClient($user, $model);
        } elseif ($user->can("restore own {$this->getTable()}") && method_exists($this, 'restoreOwn')) {
            return $this->restoreOwn($user, $model);
        }

        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @return mixed
     */
    public function forceDelete(Model $user, Model $model)
    {
        if (method_exists($this, 'forceDeleteShared') && $this->forceDeleteShared($user, $model)) {
            return true;
        }

        if ($user->can("force_delete {$this->getTable()}")) {
            return true;
        } elseif ($user->can("force_delete client {$this->getTable()}") && method_exists($this, 'forceDeleteClient')) {
            return $this->forceDeleteClient($user, $model);
        } elseif ($user->can("force_delete own {$this->getTable()}") && method_exists($this, 'forceDeleteOwn')) {
            return $this->forceDeleteOwn($user, $model);
        }

        return false;
    }
}
