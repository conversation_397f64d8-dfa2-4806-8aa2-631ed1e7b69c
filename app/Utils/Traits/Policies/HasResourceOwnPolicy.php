<?php

namespace App\Utils\Traits\Policies;

use Illuminate\Database\Eloquent\Model;

trait HasResourceOwnPolicy
{
    protected function isOwner(Model $user, Model $model): bool
    {
        return $model->owner()->is($user);
    }

    /**
     * Determine whether the user can update the model if he is the owner.
     */
    protected function updateOwn(Model $user, Model $model): bool
    {
        return $this->isOwner($user, $model);
    }

    /**
     * Determine whether the user can delete the model if he is the owner.
     */
    protected function deleteOwn(Model $user, Model $model): bool
    {
        return $this->isOwner($user, $model);
    }

    /**
     * Determine whether the user can view the model if he is the owner.
     */
    protected function viewOwn(Model $user, Model $model): bool
    {
        return $this->isOwner($user, $model);
    }

    /**
     * Determine whether the user can view the model if he is the owner.
     */
    protected function restoreOwn(Model $user, Model $model): bool
    {
        return $this->isOwner($user, $model);
    }

    /**
     * Determine whether the user can view the model if he is the owner.
     */
    protected function forceDeleteOwn(Model $user, Model $model): bool
    {
        return $this->isOwner($user, $model);
    }
}
