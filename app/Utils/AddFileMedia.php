<?php

namespace App\Utils;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class AddFileMedia
{
    public static function storeMedia(Request $request, Model $entity)
    {
        if ($request->has('media') && (! is_null($request->file('media')) && count($request->file('media')) > 0)) {
            foreach ($request->file('media') as $key => $media) {
                if (isset($media['file'])) {
                    $entity->addMedia($media['file'])
                        ->toMediaCollection($request->input("media.$key.collection") ?? $entity->getRegisteredMediaCollections()->first()->name);
                }
            }
        }
    }

    public static function batchStoreMedia(Request $request, Model $entity)
    {
        if (
            $request->has('resources.'.$entity->getKey().'.media')
            && (
                ! is_null($request->file('resources.'.$entity->getKey().'.media'))
                && count($request->file('resources.'.$entity->getKey().'.media')) > 0
            )
        ) {
            foreach ($request->file('resources.'.$entity->getKey().'.media') as $key => $media) {
                $entity->addMedia($media['file'])->toMediaCollection($request->input('resources.'.$entity->getKey().".media.$key.collection"));
            }
        }
    }
}
