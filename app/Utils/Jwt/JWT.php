<?php

namespace App\Utils\Jwt;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class JWT
{
    public string $fromApp;

    public string $clientId;

    public ?int $exp = null;

    public int $iat;

    public string $iss;

    public string $jti;

    public int $nbf;

    public array $roles;

    public string $userId = '';

    private string $jwt;

    public function __construct(Request $request)
    {
        $jwt = $request->header('Authorization') ?? $_COOKIE['jwt'] ?? null;
        if ($jwt !== null) {
            $tokenParts = explode('.', $jwt);
            $tokenPayload = base64_decode($tokenParts[1]);
            $jwtPayload = json_decode($tokenPayload);

            $this->fromApp = $jwtPayload->from_app;
            $this->clientId = $jwtPayload->client_id;
            $this->exp = $jwtPayload->exp;
            $this->iat = $jwtPayload->iat;
            $this->iss = $jwtPayload->iss;
            $this->jti = $jwtPayload->jti;
            $this->nbf = $jwtPayload->nbf;
            $this->roles = $jwtPayload->roles;
            $this->userId = $jwtPayload->user_id;
            $this->jwt = $jwt;
        }
    }

    public function isExpired(): bool
    {
        return time() > $this->exp;
    }

    /**
     * Check if AUD is correctly binded
     */
    public function verifyBindingJWT(): bool
    {
        $appUuid = $this->fromApp;
        if ($appUuid !== null && $appUuid == Env('GLOBAL_UUID') || $appUuid == Env('APP_UUID')) {
            return true;
        }

        Log::alert('Bad Binding in Jwt', ['AUD' => $appUuid, 'Jwt' => $this->jwt]);

        return false;
    }

    /**
     * Check if the accessed app uuid pass
     */
    public function keyringCheck(): bool
    {
        $keyring = $this->roles;
        $appUuid = Env('APP_UUID');

        foreach ($keyring as $key) {
            if ($key->App === $appUuid) {
                return true;
            }
        }

        Log::alert('Failed KeyRing Check in Jwt', ['Jwt' => $this->jwt]);

        return false;
    }

    /**
     * Check if the Jwt is valid in redis
     */
    public function checkStored(): bool
    {
        $claimAUD = $this->fromApp;
        $claimUUID = $this->userId;

        if ($this->verifyBindingJWT()) {
            $redisJWTStorage = Redis::connection('jwt');
            $redisResp = $redisJWTStorage->get($claimUUID.'_'.$claimAUD);
            if ($redisResp !== null && $redisResp === $this->jwt) {
                return true;
            }
        }

        return false;
    }
}
