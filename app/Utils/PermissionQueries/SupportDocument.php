<?php

namespace App\Utils\PermissionQueries;

use Illuminate\Database\Eloquent\Builder;

class SupportDocument extends PermissionQuery
{
    public function implementQuery(Builder $query)
    {
        $query->select('support_documents.*');

        if (in_array(strtolower($this->authenticatable->profile->label), ['administrateur', 'administrateurmanager'])) {
            $query->leftJoin('users as u', 'u.id', 'support_documents.creator_id')
                ->leftJoin('sites as s', 's.id', 'u.site_id');
        } else {
            $query
                ->join('site_support_document as ssd', 'ssd.support_document_id', 'support_documents.id')
                ->join('sites as s', 's.id', 'ssd.site_id')
                ->join('users as u', 'u.site_id', 's.id');
        }

        return $query->where('s.client_id', $this->authenticatable->site->client->id)
            ->orWhereNull('support_documents.creator_id')
            ->groupBy('support_documents.id');
    }
}
