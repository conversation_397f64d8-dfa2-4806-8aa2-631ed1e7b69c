<?php

namespace App\Utils\PermissionQueries;

use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use App\Utils\Traits\Makeable;
use Nette\NotImplementedException;

abstract class PermissionQuery
{
    use Makeable;

    protected ?Authenticatable $authenticatable;

    protected bool $isDiffused = false;

    public function __construct()
    {
        $this->authenticatable = Auth::check() ? Auth::user() : null;
    }

    public function forUser(Authenticatable $authenticatable)
    {
        return tap($this, function () use ($authenticatable) {
            $this->authenticatable = $authenticatable;
        });
    }

    public function implementQuery(Builder $query)
    {
        throw new NotImplementedException('Need implementation');
    }

    public function enableDiffused(): void
    {
        $this->isDiffused = true;
    }
}
