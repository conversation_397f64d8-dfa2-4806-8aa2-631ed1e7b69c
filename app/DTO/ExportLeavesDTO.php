<?php

namespace App\DTO;

use Illuminate\Support\Carbon;

class ExportLeavesDTO
{
    public function __construct(
        public ?string $fileNumber,
        public ?string  $startDate,
        public ?string  $endDate,
        public bool    $isTreat
    )
    {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            fileNumber: $data['file_number'] ?? null,
            startDate: $data['start_date'] ?? null,
            endDate: $data['end_date'] ?? null,
            isTreat: $data['is_treat'],
        );
    }

    public function getStartDate(): ?Carbon
    {
        if (is_null($this->startDate)) return null;

        return Carbon::parse($this->startDate);
    }

    public function getEndDate(): ?Carbon
    {
        if (is_null($this->endDate)) return null;

        return Carbon::parse($this->endDate);
    }

    public function hasValidDate(): bool
    {
        return isset($this->startDate, $this->endDate);
    }
}
