<?php

namespace App\Services\SynchronizeData;

use App\Facades\SynchronizeData;
use App\Models\Api\Client;
use App\Models\Api\Site;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Throwable;

class SitesDriver extends SynchronizeDataDriver
{
    /**
     * @param array $data
     * @return Model
     * @throws Throwable
     */
    public function save(array $data): Model
    {
        $site = Site::withTrashed()->firstOrNew([
            'uuid' => $data['id']
        ]);

        $site->fillable(
            array_merge(
                $site->getFillable(),
                ['uuid', 'client_id', 'name', 'country_alpha', 'subdivision', 'subdivision_code']
            )
        );

        $site->fill(
            array_merge(
                Arr::except($data, 'id'),
                ['uuid' => $data['id']]
            )
        );

        $this->beforeSynchronize($site, $data);

        if (is_null($site->deleted_at)) {
            $site->save();

            if ($site->wasRecentlyCreated) {
                $this->afterSynchronize($site);
            }
        } else {
            $site->restore();
        }

        return $site;
    }

    public function delete(array $data)
    {
        if ($site = Site::query()->where('uuid', $data['id'])->first()) {
            $site->delete();
            return $site;
        }

        return null;
    }

    protected function beforeSynchronize($model, array $modelArray = []): void
    {
        $model->country = \Locale::getDisplayRegion("-$model->country_alpha", 'fr');

        Client::where('uuid', $model->client_id)->firstOr(function () use ($modelArray) {
            SynchronizeData::driver('clients')->run('save', $modelArray['client']);
        });

        $model->client_id = Client::query()->where('uuid', $model->client_id)->first()->getKey();
    }
}
