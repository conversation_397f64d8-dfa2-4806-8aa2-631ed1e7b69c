<?php

namespace App\Services\SynchronizeData;

use Exception;
use Illuminate\Database\Eloquent\Model;

class NullDriver extends SynchronizeDataDriver
{
    /**
     * @param array $data
     * @throws Exception
     */
    public function save(array $data): Model
    {
        throw new Exception(__('SynchronizeDataError'));
    }

    public function delete(array $data)
    {
        return null;
    }

    protected function findOrCreateModel(string $field, $value): Model
    {
        throw new Exception(__('SynchronizeDataError'));
    }
}
