<?php

namespace App\Services\SynchronizeData;

use App\Facades\SynchronizeData;
use App\Jobs\StructureManagementJob;
use App\Jobs\StructureSiteUserLeaveCountJob;
use App\Models\Api\LeaveType;
use App\Models\Api\Site;
use App\Models\Api\User;
use App\Utils\DefaultValues\DefaultUserLeaveCounts;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class UsersDriver extends SynchronizeDataDriver
{
    public function save(array $data): Model
    {
        $user = User::withTrashed()->firstOrNew([
            'uuid' => $data['id']
        ]);

        $user->fillable(
            array_merge(
                $user->getFillable(),
                [
                    'uuid', 'profile_id', 'site_id', 'lastname', 'firstname', 'email', 'language', 'manager_id', 'number_managers_can_validate', 'timezone',
                    'is_level_one_manager','enter_date','fcm_token'
                ]
            )
        );

        $user->fill(
            array_merge(
                Arr::except($data, 'id'),
                ['uuid' => $data['id']]
            )
        );

        $this->beforeSynchronize($user, $data);

        if (is_null($user->deleted_at)) {
            if ($user->isDirty('site_id') && $user->exists) {
                StructureSiteUserLeaveCountJob::dispatch($user);
            }

            $user->save();

            StructureManagementJob::dispatch($user);

            if ($user->wasRecentlyCreated) {
                $this->afterSynchronize($user);
            }
        } else {
            $user->restore();
        }

        return $user;
    }

    public function delete(array $data)
    {
        if ($user = User::query()->where('uuid', $data['id'])->first()) {
            $user->delete();
            return $user;
        }

        return null;
    }

    protected function beforeSynchronize($model, array $modelArray = []): void
    {
        Site::query()->where('uuid', $model->site_id)->firstOr(function () use ($modelArray) {
            SynchronizeData::driver('sites')->run('save', $modelArray['site']);
        });

        $site = Site::query()->firstWhere('uuid', $model->site_id);

        $model->site_id = $site->getKey();
        $model->client_uuid = $site->client->uuid;

        $model->setManager(
            User::query()->firstWhere('uuid', $model->manager_id)
        );
    }

    protected function afterSynchronize($model, array $modelArray = []): void
    {
        $leaveTypes = LeaveType::query()
            ->where(function (Builder $query) use ($model) {
                $query->where('site_id', $model->site_id)
                    ->orWhere(function ($query) use ($model) {
                        $query->where('leave_types.client_id', $model->site->client_id)
                            ->whereNull('leave_types.site_id')
                            ->whereRaw("(SELECT COUNT(*) FROM leave_types WHERE site_id = $model->site_id) < 1");
                    });
            })
            ->get();

        app()->make(DefaultUserLeaveCounts::class)->run($model, $leaveTypes);
    }
}
