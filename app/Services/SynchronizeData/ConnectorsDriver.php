<?php

namespace App\Services\SynchronizeData;

use Illuminate\Database\Eloquent\Model;

class ConnectorsDriver extends SynchronizeDataDriver
{
    public function save(array $data): Model
    {
        // TODO: Implement save() method.
    }

    public function delete(array $data)
    {
        // TODO: Implement delete() method.
    }

    protected function findOrCreateModel(string $field, string $value): Model
    {
        // TODO: Implement findOrCreateModel() method.
    }
}
