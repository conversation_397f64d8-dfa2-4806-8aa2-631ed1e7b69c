<?php

namespace App\Services\SynchronizeData;

use App\Models\Api\Client;
use App\Models\Api\ClientType;
use App\Facades\SynchronizeData;
use App\Models\Api\Day;
use App\Utils\DefaultValues\DefaultLeaveTypes;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;
use Throwable;

class ClientsDriver extends SynchronizeDataDriver
{
    /**
     * @param array $data
     * @return Model
     * @throws Throwable
     */
    public function save(array $data): Model
    {
        $client = Client::withTrashed()->firstOrNew([
            'uuid' => $data['id']
        ]);

        $client->fillable(
            array_merge(
                $client->getFillable(),
                ['uuid', 'name', 'client_type_id', 'number_managers_can_validate']
            )
        );

        $client->fill(
            array_merge(
                Arr::except($data, 'id'),
                ['uuid' => $data['id']]
            )
        );

        $this->beforeSynchronize($client);

        if (is_null($client->deleted_at)) {
            $client->save();

            if ($client->wasRecentlyCreated) {
                $this->afterSynchronize($client);
            }
        } else {
            $client->restore();
        }

        return $client;
    }

    /**
     * @throws Exception
     */
    public function delete(array $data)
    {
        if ($client = Client::query()->where('uuid', $data['id'])->first()) {
            $client->delete();
            return $client;
        }

        return null;
    }

    protected function beforeSynchronize($model, array $modelArray = []): void
    {
        Log::info($model);
        ClientType::where('uuid', $model->client_type_id)->firstOr(function () use ($modelArray) {
            SynchronizeData::driver('clients')->run('save', $modelArray['client'],);
        });

        $model->client_type_id = ClientType::whereUuid($model->client_type_id)->first()->getKey();
    }

    protected function afterSynchronize($model, array $modelArray = []): void
    {
        app()->make(DefaultLeaveTypes::class)->run($model);

        $model->days()->sync(
            Day::query()->whereIn('day_name', ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY'])->get()
        );
    }
}
