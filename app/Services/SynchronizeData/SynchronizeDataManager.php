<?php

namespace App\Services\SynchronizeData;

use Illuminate\Support\Manager;

class SynchronizeDataManager extends Manager
{
    public function createClientsDriver(): ClientsDriver
    {
        return new ClientsDriver();
    }

    public function createConnectorsDriver(): ConnectorsDriver
    {
        return new ConnectorsDriver();
    }

    public function createSitesDriver(): SitesDriver
    {
        return new SitesDriver();
    }

    public function createUsersDriver(): UsersDriver
    {
        return new UsersDriver();
    }

    public function getDefaultDriver(): NullDriver
    {
        return new NullDriver();
    }
}
