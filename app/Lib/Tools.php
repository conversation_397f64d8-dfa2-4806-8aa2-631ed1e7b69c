<?php


namespace App\Lib;


use App\Models\Api\User;
use Illuminate\Support\Facades\Auth;
use App\Jobs\StructureManagementJob;
use App\Models\Api\Client;

class Tools
{

    /**
     * Encode an string in base 64 without char like '+/='
     *
     * @param  $data
     * @return string
     */
    public static function base64UrlEncode($data)
    {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    /**
     * Decode a base 64 where char '+/=' was replaced by '-_'
     *
     * @param  $data
     * @return false|string
     */
    public static function base64UrlDecode($data)
    {
        return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
    }

    public static function getCurrentUserWithUuid()
    {
        return Auth::user();
    }

    public static function updateTreeStructure(Client $client)
    {
        User::query()
            ->select('users.*')
            ->join('sites', 'sites.id', 'users.site_id')
            ->join('clients', 'clients.id', 'sites.client_id')
            ->where('clients.uuid', $client->uuid)
            ->chunk(100, function ($users) {
                foreach ($users as $user) {
                    dispatch(new StructureManagementJob($user));
                }
            });
    }
}
