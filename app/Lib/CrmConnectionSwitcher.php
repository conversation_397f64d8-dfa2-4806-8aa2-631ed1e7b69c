<?php

namespace App\Lib;

class CrmConnectionSwitcher
{
    /**
     * Switch CRM - SQL Server Connection
     *
     * @param [string] $host
     * @param [string] $database
     * @return void
     */
    public function SwitchCrmConnection($host, $database)
    {
        config(['database.connections.sqlsrv' => [
            'driver' => 'sqlsrv',
            'host' => $host,
            'database' => $database,
            'username' => env('CRM_USERNAME'),
            'password' => env('CRM_PASSWORD'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => false,
        ]]);
    }
}
