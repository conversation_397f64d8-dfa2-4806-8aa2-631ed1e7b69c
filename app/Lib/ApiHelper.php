<?php


namespace App\Lib;

use App\Models\Api\ExportHistory;
use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\LeaveTypeSubFamily;
use App\Models\Api\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Storage;

class ApiHelper
{
    private $file = null;
    private $data = [];

    public function __construct()
    {
        if (array_key_exists("file", Request::all())) {
            $this->file = Request::all()["file"];
        }
        if (array_key_exists("data", Request::all())) {
            $this->data = json_decode(Request::all()["data"], true);
        }
    }

    /**
     * Verify passed parameters match with expected parameters, and check passed values format
     * @param array $paramsConfig
     * @throws \Exception
     */
    public static function checkQueryParams(array $paramsConfig): void
    {
        $paramsConfig = array_merge($paramsConfig, ['orders' => null, 'paginate' => 'boolean', 'limit' => 'integer', 'page' => 'integer',]);
        // get expected query parameters name
        $names = array_keys($paramsConfig);
        $checkFormat = [];
        $checkAllowedParams = (bool)Request::get('checkAllowedParams', true);

        // Check if passed query parameters are expected
        foreach (Request::all() as $paramName => $paramValue) {
            if (!in_array($paramName, $names) and $checkAllowedParams) {
                throw new \Exception(__('warnings.ParameterNotAllowed', ['param_name' => $paramName]), 400);
            }

            if (array_key_exists($paramName, $paramsConfig)) {
                $checkFormat[$paramName] = $paramValue;
            }
        }

        // Check parameter format
        foreach ($paramsConfig as $name => $format) {
            if (isset($checkFormat[$name])) {
                $value = $checkFormat[$name];

                // If format is an array, it means it's enum values
                if (is_array($format)) {
                    // Check if param value is in enum
                    if ($value === true or !in_array($value, $format)) {
                        throw new \Exception(__('warnings.InvalidValue', [
                            'value' => $value,
                            'allowed_values' => implode(", ", $format["value"])
                        ]), 400);
                    }
                } else {
                    switch ($format) {
                        case 'timestamp':
                        case 'identifier':
                        case 'integer':
                            $ids = explode(',', $value);
                            if (!is_array($value)) {
                                $ids = [$value];
                            }

                            foreach ($ids as $id) {
                                if (!preg_match('/^\d+/', $id)) {
                                    throw new \Exception(__('warnings.UnsignedNumericValue', ['name' => $name]), 400);
                                }
                            }
                            break;

                        case 'boolean':
                            if ((is_string($value) and !in_array($value, ['0', '1'])) or (is_int($value) and !in_array($value, [0, 1]))) {
                                throw new \Exception(__('warnings.RequiredBoolean', ['name' => $name]), 400);
                            }
                            break;

                        case 'date':
                            if (!preg_match('/^\d{4}-(0?[1-9]|1[0-2])-(0?[1-9]|[12][0-9]|3[01])$/', $value)) {
                                throw new \Exception(__('warnings.RequiredDate', ['name' => $name]), 400);
                            }
                            break;

                        case 'datetime':
                            if (!preg_match('/^\d{4}-(0?[1-9]|1[0-2])-(0?[1-9]|[12][0-9]|3[01]) ([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/', $value)) {
                                throw new \Exception(__('warnings.RequiredDateTime', ['name' => $name]), 400);
                            }
                            break;

                        case 'uuid':
                            if (!preg_match('/^[0-9a-fA-F]{8}\-[0-9a-fA-F]{4}\-[0-9a-fA-F]{4}\-[0-9a-fA-F]{4}\-[0-9a-fA-F]{12}$/', $value)) {
                                throw new \Exception(__('warnings.RequiredUuid', ['name' => $name]), 400);
                            }
                            break;
                    }
                }
            }
        }
    }

    /**
     * Verify passed parameters match with expected parameters, and check passed values format
     * @param array $paramsConfig
     * @throws \Exception
     */
    public static function checkJsonData(array $paramsConfig): void
    {
        // get expected query parameters name
        $names = array_keys($paramsConfig);
        $checkFormat = [];
        $checkAllowedParams = (bool)Request::json()->get('checkAllowedParams', true);

        // Check if passed json parameters are expected
        foreach (Request::json()->all() as $paramName => $paramValue) {
            if (!in_array($paramName, $names) and $checkAllowedParams) {
                throw new \Exception(__('warnings.ParameterNotAllowed', ['param_name' => $paramName]), 400);
            }

            if (array_key_exists($paramName, $paramsConfig)) {
                $checkFormat[$paramName] = $paramValue;
            }
        }

        // Check parameter format
        foreach ($paramsConfig as $name => $object) {
            //Check if parameter is in the JSON
            if (array_key_exists($name, $checkFormat)) {
                $value = $checkFormat[$name];

                //Check if value is null
                $isNull = (is_null($value) or ((!is_bool($value) and !is_numeric($value)) and empty($value)));
                //Check if value is not nullable
                if ($object['nullable'] == 0 and $isNull) {
                    throw new \Exception(__('warnings.RequiredValueForParameter', ['name' => $name]), 400);
                }
                //If value is null so we don't throw Exception
                if (!$isNull) {
                    switch ($object['value']) {
                        case 'timestamp':
                        case 'identifier':
                        case 'integer':
                            $ids = explode(',', $value);
                            if (!is_array($value)) {
                                $ids = [$value];
                            }

                            foreach ($ids as $id) {
                                if ($id === true or !preg_match('/^\d+/', $id)) {
                                    throw new \Exception(__('warnings.UnsignedNumericValue', ['name' => $name]), 400);
                                }
                            }
                            break;

                        case 'boolean':
                            if (((is_int($value) and !in_array($value, [0, 1]))) or is_string($value) and !in_array($value, ["0", "1"])) {
                                throw new \Exception(__('warnings.RequiredBoolean', ['name' => $name]), 400);
                            }
                            break;

                        case 'date':
                            if (!preg_match('/^\d{4}-(0?[1-9]|1[0-2])-(0?[1-9]|[12][0-9]|3[01])$/', $value)) {
                                throw new \Exception(__('warnings.RequiredDate', ['name' => $name]), 400);
                            }
                            break;

                        case 'datetime':
                            if (!preg_match('/^\d{4}-(0?[1-9]|1[0-2])-(0?[1-9]|[12][0-9]|3[01]) ([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/', $value)) {
                                throw new \Exception(__('warnings.RequiredDateTime', ['name' => $name]), 400);
                            }
                            break;

                        case 'uuid':
                            if (!preg_match('/^[0-9a-fA-F]{8}\-[0-9a-fA-F]{4}\-[0-9a-fA-F]{4}\-[0-9a-fA-F]{4}\-[0-9a-fA-F]{12}$/', $value)) {
                                throw new \Exception(__('warnings.RequiredUuid', ['name' => $name]), 400);
                            }
                            break;

                        case 'string':
                            if (!is_string($value)) {
                                throw new \Exception(__('warnings.RequiredString', ['name' => $name]), 400);
                            }
                            break;

                        case gettype($object["value"]) == "array":
                            if ($value === 0 or $value === true or !in_array($value, $object["value"])) {
                                throw new \Exception(__('warnings.InvalidValue', [
                                    'value' => $value,
                                    'allowed_values' => implode(", ", $object["value"])
                                ]), 400);
                            }
                            break;
                    }
                }
            }
        }
    }

    public function responseFormatter(int $code = 1, $data = null, $message = null, $details = null, bool $get = false): array
    {
        $response = [];
        // Success response
        if (preg_match('/^2[0-9]{2}$/', $code)) {
            if ($get) {
                $response = array_merge(['message' => $message], $data);
            } else {
                $response = [
                    'message' => $message,
                    'data' => $data
                ];
            }
        } // Error response
        else {
            $response = [
                'message' => $message,
                'data' => $data,
                'details' => is_array($details) ? implode(' ', array_map('ucfirst', $details)) : $details,
            ];
        }
        return $response;
    }

    /**
     * @param object $object
     * @param array $values
     * @param array $setValues
     * @return object
     * Fonction permettant de set les valeurs d'un nouvel object grâce a un tableau de données
     */
    public function setValuesToNewObject(object $object, array $values, array $setValues = []): object
    {
        $filterSetValues = count($setValues) > 0 ? true : false;
        foreach ($values as $key => $value) {
            if (!$filterSetValues or ($filterSetValues and in_array($key, $setValues))) {
                $object->$key = $value;
            }
        }
        return $object;
    }

    /**
     * Add orders, limit and paginate filters to get queries
     * @param Builder $model
     * @param string $ordersBy
     * @param int|null $limit
     * @param int $paginate
     * @return array
     * @throws \Exception
     */
    public static function defaultModelBuilder(Builder $model, string $ordersBy, int $limit = null, int $paginate = 0): array
    {
        $response = null;
        // Adding orders to model
        if (!empty($ordersBy)) {
            $orders = explode(';', $ordersBy);
            foreach ($orders as $infos) {
                // Check value structure
                //throw_if(!preg_match('/^\w+,\w+$/', $infos), new \Exception('Direction is required, and must be specified like \'order,direction\'.', 400));

                [$order, $direction] = explode(',', $infos);
                if (!in_array(strtolower($direction), ['asc', 'desc'])) {
                    throw new \Exception(__('warnings.DirectionAscDesc'), 400);
                }

                $model->orderBy($order, strtolower($direction));
            }
        }

        if ($paginate == 0) {
            $total = $model->count();
            if (!empty($limit)) {
                $model->limit($limit);
                $total = ($total < $limit) ? $total : $limit;
            }

            $response = self::formatGetResponse($model->get(), $total);
        } else {
            $paginate = $model->paginate($limit);
            $response = self::formatGetResponse($paginate->all(), $paginate->total(), $paginate->currentPage(), $paginate->nextPageUrl(), $paginate->lastPage());
        }

        return $response;
    }

    /**
     * Format data from BDD queries before send it to client
     * @param $datas
     * @param int|null $total
     * @param int $currentPage
     * @param string|null $nextPageUrl
     * @param int $lastPage
     * @return array
     */
    public static function formatGetResponse($datas, int $total = null, int $currentPage = 1, string $nextPageUrl = null, int $lastPage = 1): array
    {
        return [
            'data' => $datas,
            'current_page' => $currentPage,
            'next_page_url' => $nextPageUrl,
            'last_page' => $lastPage,
            'total' => $total
        ];
    }

    /**
     * @param string $type
     * @return array
     * @throws \Exception Fonction de check des données avant enregistrement d'un fichier
     */
    public function checkDataWhenUploadFile(string $type = null): array
    {
        $acceptedFileType = ["image/png", "image/jpeg"];
        /*
         * Check si on à bien les données et le fichier dans la requete
         */
        if (($type != "background" && count($this->data) == 0) || (count($this->data) == 0 || !$this->file)) {
            throw new \Exception(__('warnings.LackDataForStore'));
        }

        if ($this->file) {
            $fileMimeType = mime_content_type($this->file->path());

            /*
             * Check si le fichier n'est pas trop volumineux
             */
            if ((filesize($this->file) / 1024) / 1024 > 5 || $fileMimeType == "directory") {
                throw new \Exception(__('warnings.FileWeight'));
            }

            /*
             * Check le type de document envoyé
             */
            if (!in_array($fileMimeType, $acceptedFileType)) {
                throw new \Exception(__('warnings.FileTypeJpgPng'));
            }
        }

        return [$this->data, $this->file];
    }

    /**
     * @param array $ids
     * @throws \Exception
     */
    public static function checkIdFormat($ids = array()): void
    {
        if (!is_array($ids)) {
            $ids = [$ids];
        }

        foreach ($ids as $id) {
            if (!preg_match('/^\d+/', $id)) {
                throw new \Exception(__('warnings.MustFilterById'), 500);
            }
        }
    }

    public static function writeOnFile(string $content, string $filePath, string $disk = 's3')
    {
        if (!Storage::disk($disk)->exists($filePath)) {
            throw new \Exception(__('warnings.ErrorFileDontExist'), 404);
        }
        Storage::disk($disk)->put($filePath, $content);
    }

    public static function downloadExport(string $filePath, string $disk = 's3')
    {
        if (!Storage::disk($disk)->exists($filePath)) {
            throw new \Exception(__('warnings.ErrorFileDontExist'), 404);
        }

        return Storage::disk($disk)->download($filePath);
    }

    public static function exportAlreadyExist(string $filePath, string $disk = 's3')
    {
        return Storage::disk($disk)->exists($filePath);
    }

    public static function deleteExportIfTooMuch(int $clientId, string $path, string $disk = 's3', int $nbMax = 20)
    {
        $exports = ExportHistory::query()
            ->whereHas(
                'user',
                fn($query) => $query->whereHas("site", fn($query) => $query->where('client_id', $clientId))
            )
            ->orderBy('created_at')
            ->get();

        // If too much
        if (count($exports) > $nbMax) {
            // Delete older export to not have more than max
            while (count($exports) > $nbMax) {
                // Delete on disk
                Storage::disk($disk)->delete($path . $exports[0]->file_name);
                // Delete history
                $exports[0]->delete();
                // Get other export
                $exports = ExportHistory::query()
                    ->whereHas(
                        'user',
                        fn($query) => $query->whereHas("site", fn($query) => $query->where('client_id', $clientId))
                    )
                    ->orderBy('created_at')
                    ->get();
            }
        }
    }

    /**
     * Function to make a POST Http request to an url
     * @param string $url
     * @param array $headers
     * @param array $data
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function postHttpRequest(string $url, array $headers, array $data): array
    {
        $guzzleClient = new \GuzzleHttp\Client();

        try {
            // Making call
            $response = $guzzleClient->request('POST', $url, ['json' => $data, 'headers' => $headers]);
            Log::info($response->getBody());
        } catch (\Exception $e) {
            return [
                "message" => $e->getMessage(),
                "http_code" => (!empty($e->getCode()) and preg_match('/^[2-5][0-9]{2}$/', $e->getCode())) ? $e->getCode() : 500,
                "request_error" => true
            ];
        }

        $headers = $response->getHeaders();
        $code = $response->getStatusCode();
        $body = (string)$response->getBody();

        try {
            // Decode response
            $body = json_decode($body, true);
        } catch (\Exception $e) {
            return [
                "message" => $e->getMessage(),
                "http_code" => 500,
                "request_error" => false
            ];
        }

        return [
            "headers" => $headers,
            "http_code" => $code,
            "body" => $body,
            "request_error" => null
        ];
    }

    /**
     * Function to convert string date time
     * Ex: StartDate:   "2021-01-14 00:00:00" ->    "2021-01-14 matin"
     * Ex: EndDate:     "2021-01-21 00:00:00" ->    "2021-01-20 soir"
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    public static function convertStartDateAndEndDate(string $startDate, string $endDate): array
    {
        $carbonStartDate = Carbon::parse($startDate);
        $carbonEndDate = Carbon::parse($endDate);

        //Start Date
        if ($carbonStartDate->hour >= 0 && $carbonStartDate->hour < 12) {
            $returnStartDate = $carbonStartDate->format('d/m/Y') . " " . __('messages.morning');
        } else {
            $returnStartDate = $carbonStartDate->format('d/m/Y') . " " . __('messages.afternoon');
        }

        //End Date
        if ($carbonEndDate->hour >= 0 && $carbonEndDate->hour < 12) {
            $returnEndDate = $carbonEndDate->format('d/m/Y') . " " . __('messages.morning');
        } else {
            $returnEndDate = $carbonEndDate->format('d/m/Y') . " " . __('messages.afternoon');
        }

        return [$returnStartDate, $returnEndDate];
    }

    public static function LeaveTypeName(Leave $leave)
    {
        if ($leave->leave_type_sub_family_id !== NULL) {
            return LeaveTypeSubFamily::withTrashed()->find($leave->leave_type_sub_family_id)->name;
        } else {
            return LeaveType::withTrashed()->find($leave->leave_type_id)->name;
        }
    }

    public static function convertStartFullDateAndEndFullDate(User $user, string $startDate, string $endDate): array
    {
        Carbon::setLocale($user->language);
        $carbonStartDate = Carbon::parse($startDate);
        $carbonEndDate = Carbon::parse($endDate);

        //Start Date
        if ($carbonStartDate->hour >= 0 && $carbonStartDate->hour < 12) {
            $returnDate['start_date'] = $carbonStartDate->translatedFormat('l d F Y') . " " . __('messages.morning', [], $user->language);
        } else {
            $returnDate['start_date'] = $carbonStartDate->translatedFormat('l d F Y') . " " . __('messages.afternoon', [], $user->language);
        }

        //End Date
        if ($carbonEndDate->hour >= 0 && $carbonEndDate->hour < 12) {
            $returnDate['end_date'] = $carbonEndDate->translatedFormat('l d F Y') . " " . __('messages.morning', [], $user->language);
        } else {
            $returnDate['end_date'] = $carbonEndDate->translatedFormat('l d F Y') . " " . __('messages.afternoon', [], $user->language);
        }

        return $returnDate;
    }
}
