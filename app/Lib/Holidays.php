<?php


namespace App\Lib;

use App\Models\Api\Site;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Iteks\Support\Facades\Json;
use Nahid\JsonQ\Jsonq;

class Holidays
{

    public static function getPublicHolidays($startDate, $endDate, $currentUser): array
    {
        $site = Site::find($currentUser->site_id)->first();
        $countryCode = $site->country_alpha;

        return Holidays::getPublicHolidaysByCountryCode($startDate, $endDate, $countryCode);
    }

    public static function getPublicHolidaysByCountryCode($startDate, $endDate, $countryCode, $countrySubCode = null): array
    {
        $startYear = Carbon::parse($startDate)->format("Y");
        $endYear = Carbon::parse($endDate)->format("Y");

        return Holidays::format_data_json($startYear, $endYear, $countryCode, $countrySubCode);
    }

    public static function getFrenchHolidays($startDate, $endDate)
    {
        $holidays = [];

        $response_data = json_decode(Http::get("https://data.education.gouv.fr/api/records/1.0/search/?dataset=fr-en-calendrier-scolaire&rows=1000&q=start_date<{$endDate} AND end_date>={$startDate}&q=zones = 'Zone A' OR zones = 'Zone B' OR zones = 'Zone C'&timezone=Europe/Paris"), true);

        for ($i = 0; $i < count($response_data["records"]); $i++) {
            $tempArray = [];
            $tempArray["name"] = $response_data["records"][$i]["fields"]["description"];
            $tempArray["start_date"] = $response_data["records"][$i]["fields"]["start_date"];
            $tempArray["end_date"] = $response_data["records"][$i]["fields"]["end_date"];
            $tempArray["zones"] = $response_data["records"][$i]["fields"]["zones"];
            if (!in_array($tempArray, $holidays)) {
                $holidays[] = $tempArray;
            }
        }

        return $holidays;
    }

    private static function format_data_json($minYear, $maxYear, $countryCode, $countrySubCode = null)
    {
        $holidays = [];

        $data = Json::toCollection(__DIR__ . '/../../storage/app/holidays.json')
            ->where('country', $countryCode)
            ->first()['data'] ?? [];

        $dataAllYears = collect($data)->where('year', '>=', $minYear)->where('year', '<=', $maxYear)->toArray();

        foreach ($dataAllYears as $dataOneYear) {
            if (is_null($dataOneYear)) {
                continue;
            }

            $dataOneYearArray = collect($dataOneYear['data'])
                ->filter(function ($item) use ($countrySubCode) {
                    return $item['global'] === true || when($countrySubCode !== null, function () use ($item, $countrySubCode) {
                            return $item['global'] === false && in_array($countrySubCode, $item['counties']);
                    });
                })
                ->toArray();

            $holidays = array_merge($holidays, $dataOneYearArray);
        }

        return $holidays;
    }

    public static function carbonHolidays($startDate, $endDate, $countryCode, $client): array
    {
        $holidays = self::getPublicHolidaysByCountryCode($startDate, $endDate, $countryCode, $client);

        return array_map(function ($holiday) use ($holidays) {
            return Carbon::createFromFormat('Y-m-d', $holiday['date'])->hour(0)->minute(0)->second(0);
        }, $holidays);
    }
}
