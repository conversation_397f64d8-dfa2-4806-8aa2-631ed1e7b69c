<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
         cacheDirectory=".phpunit.cache"
>
<coverage>
    <report>
        <html outputDirectory="public/coverage-report"></html>
    </report>
</coverage>

<testsuites>
    <testsuite name="Unit">
        <directory>tests/Unit</directory>
    </testsuite>
    <testsuite name="Feature">
        <directory>tests/Feature</directory>
    </testsuite>
</testsuites>
<source>
    <include>
        <directory>app</directory>
    </include>
    <exclude>
        <directory>app/Providers</directory>
        <directory>app/Console</directory>
        <directory>app/Facades</directory>
        <file>app/Exceptions/Handler.php</file>
    </exclude>
</source>
<php>
    <env name="APP_ENV" value="testing"/>
    <env name="CACHE_DRIVER" value="array"/>
    <env name="QUEUE_CONNECTION" value="sync"/>
    <env name="TELESCOPE_ENABLED" value="false"/>
</php>
</phpunit>

