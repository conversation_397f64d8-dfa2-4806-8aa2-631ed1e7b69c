---
################################################
#                PAS MODIFIABLE                #
################################################
ansible_python_interpreter: "/usr/bin/python3"

docker_name: "local-conges-api"
domain: "xefi-apps.local"
image_version: "php8.3"
docker_port: "40004:40000"
traefik_labels: "{
      'traefik.http.routers.{{ docker_name }}.rule': 'Host(`{{ docker_name }}.{{ domain }}`)',
      'traefik.http.services.{{ docker_name }}.loadbalancer.passhostheader': 'true',
    }"

# identifiants de connexion en root pour la création de la BDD et du user qui sera utilisé par le container
db_connection_user: "root"
db_connection_pass: "{{ sql_root_password }}"
host_volume:
  - "{{ logs_path }}/{{ docker_name }}:/logs"
  - "{{ project_path }}:/var/www"
# Variables d'environnement pour le container Docker
################################################
#                   MODIFIABLE                 #
################################################
build_args:
  AKT_SECRET_KEY: "d54e8bc0b80372176d02a38eefcb6247"
  AKT: "eyJhbGciOiJzaGEyNTYiLCJ0eXAiOiJBS1QifQ.eyJrZXkiOiI0ZHZSN1RtcUVMUU5sTEFEIn0.a1f4947b48bc948f43c1514e53941c531b51f1eff0fa76c7f265da17b998356e"
  APP_DEBUG: "true"
  APP_DOMAIN: "xefi-apps.local"
  APP_ENV: "local"
  APP_KEY: "base64:S8UmXB/IejKViV4mBS6Wba4QcAD8rcmsBrHUxWojdlE="
  APP_NAME: "Conges"
  APP_URL: "http://local-conges-api.xefi-apps.local"
  APP_UUID: "e7102984-9095-5254-845b-8405cd04810b"
  APP_FRONT_URL: "https://conges.dailyapps.fr/"
  BROADCAST_DRIVER: "log"
  CACHE_DRIVER: "file"
  CONNEXION_URL: "http://local-conges.xefi-apps.local/login"
  DB_CONNECTION: "mysql"
  DB_DATABASE: "{{ docker_name }}"
  DB_HOST: "mysql"
  DB_PORT: "3306"
  DB_PASSWORD: "user_{{ docker_name }}_BDD_$$$"
  DB_USERNAME: "{{ docker_name }}"
  FILESYSTEM_CLOUD: "minio"
  GLOBAL_UUID: "78bc7d47-55b2-5755-abd6-5c756a9c62cb"
  INTERNAL_AKT: "le_pouet"
  JWT_SECRET_KEY: "{{ global_jwt_secret_key }}"
  JWT_TTL: "3600"
  LOG_CHANNEL: "stack"
  MAIL_DRIVER: "smtp"
  MAIL_ENCRYPTION: "null"
  MAIL_FROM_ADDRESS: "*******"
  MAIL_FROM_NAME: "DailyApps"
  MAIL_HOST: "mailcatcher"
  MAIL_PASSWORD: "null"
  MAIL_PORT: "1025"
  MAIL_USERNAME: "DailyApps"
  PORTAL_AKT: "eyJhbGciOiJzaGEyNTYiLCJ0eXAiOiJBS1QifQ.eyJrZXkiOiJsc2l5d3hLajBKVERFMUlsIn0.7a4bbf855a3721a05f660f667b37753ddf7cc83945c467d9b48523bd5e1577d8"
  PORTAL_URL: "http://local-app.xefi-apps.local"
  QR_API_URL: "local-qrcode-api"
  QUEUE_CONNECTION: "sync"
  REDIS_HOST: "redis"
  REDIS_PASSWORD: "null"
  REDIS_PORT: "6379"
  REDIS_PREFIX: "XefiApp_local_"
  AWS_BUCKET: "conges"
  AWS_ENDPOINT: "http://minio"
  AWS_ACCESS_KEY_ID: "local-minio"
  AWS_DEFAULT_REGION: "eu-west-1"
  AWS_PORT: "9000"
  AWS_SECRET_ACCESS_KEY: "a2624255bb8d08874a143b06b303c8a0a64fe980366299e40de5ee0d66705928"
  SESSION_DRIVER: "file"
  SESSION_LIFETIME: "120"
  TOKEN_GITLAB: "**************************"
  GITLAB_TOKEN: "**************************"    # token nouveau git
  FIREBASE_CREDENTIALS: '{
    "type": "service_account",
    "project_id": "dailyapps-conges-v2",
    "private_key_id": "516a61908995d9e654d32278edbecefda35d741b",
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    "client_email": "*******",
    "client_id": "104470425403575975205",
    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
    "token_uri": "https://oauth2.googleapis.com/token",
    "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
    "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-8gkgq%40dailyapps-conges-v2.iam.gserviceaccount.com",
    "universe_domain": "googleapis.com"
  }'
