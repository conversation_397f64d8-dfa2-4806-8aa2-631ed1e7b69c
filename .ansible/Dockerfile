FROM harbor.xefi.fr/xefi/dailyapps/php-nginx:8.3-mongodb

ARG TOKEN

WORKDIR /var/www/

COPY . /var/www/

RUN composer config gitlab-token.gitlab.xefi.fr $TOKEN \
    && composer install --no-ansi --no-dev --no-interaction --no-plugins --no-progress --no-scripts --no-suggest --optimize-autoloader \
    && chown -R www-data:www-data .

COPY .supervisord/supervisord.conf /etc/supervisor/supervisord.conf