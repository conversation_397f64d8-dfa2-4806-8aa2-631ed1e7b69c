FROM harbor.xefi.fr/xefi/dailyapps/php-nginx:8.3-mongodb

ARG TOKEN

WORKDIR /var/www/

COPY . /var/www/

RUN echo "Installation de Xdebug..." \
    && pecl install xdebug > /dev/null 2>&1 \
    && docker-php-ext-enable xdebug > /dev/null 2>&1 \
    && export XDEBUG_MODE=coverage


COPY .supervisord/supervisord.conf /etc/supervisor/supervisord.conf

# Second composer install (dev)
RUN composer config gitlab-token.gitlab.xefi.fr $TOKEN \
    && composer install --no-ansi --no-interaction --no-plugins --no-progress --no-scripts --no-suggest --optimize-autoloader \
    && chown -R www-data:www-data .
