APP_NAME="${APP_NAME}"          # string | Nom de l'application | DEV LOCAL | Congé API
APP_ENV=prod                    # string | Nom de l'environnement actuel | (LOCAL-RD-PROD)
APP_KEY=${APP_KEY}              # string | Clé d'encryption aléatoire pour la sécurité | Ne1wGIAHdhOKt4T7nrKRtIZT7XcMDAoU
APP_DEBUG=false                 # bool | Messages d'erreur détaillés | true
APP_URL=${APP_URL}              # string | URL à utilisé quand on lance la commande Php Artisan | http://conge-api.xefiapps.local
APP_DOMAIN=${APP_DOMAIN}        # string | Nom du domaine à utilisé | xefiapps.local | domain only eg : my-domain.tld
CONNEXION_URL=${CONNEXION_URL}  # string | URL de redirection sur Auth | http://app.xefiapps.local/login
QR_API_URL=${QR_API_URL}        # string | Nom du docker de l'api QR Code | qr-code
GLOBAL_UUID=${GLOBAL_UUID}      # string | L'UUID correspondant à l'app Auth | 78bc8d47-54b2-433-abd6-5c756a9c60cb
APP_UUID=${APP_UUID}            # string | L'UUID correspondant à l'app actuel |  78cc8d47-54b2-433-abd6-3c756a9c60cb

LOG_CHANNEL=stack   # string | Le canal par défaut utilisé lors de l'écriture dans les logs | stack

DB_CONNECTION=mysql          # string | Le pilote spécifique utilisé par la bdd | mysql
DB_HOST=${DB_HOST}           # string | Le nom de l'hôte de la bdd | mysql
DB_PORT=3306                 # int | Le numéro de port de la bdd | 3306
DB_DATABASE=${DB_DATABASE}   # string | Le nom de la bdd à utilisé sur l'app | Conge
DB_USERNAME=${DB_USERNAME}   # string | Le nom d'utilisateur à utilisé pour la connection a l'app | root
DB_PASSWORD=${DB_PASSWORD}   # string | Le mot de passe à utilisé pour la connection a l'app | password

# Si utilisation de bdd CRM
#CRM_USERNAME="${CRM_USER_ID}"      # string | Le nom d'utilisateur à utilisé pour la CRM | AuthLaravel
#CRM_PASSWORD="${CRM_USER_PW}"      # string | Le password à utilisé pour la CRM | password

BROADCAST_DRIVER=log    # string | Le driver à utilisé pour le broadcast | log
CACHE_DRIVER=file       # string | Le driver à utilisé pour le cache | file
QUEUE_CONNECTION=sync   # string | L'API de file d'attente de Laravel | sync

REDIS_HOST=${REDIS_HOST}       # string | L'hôte a utilisé par REDIS | redis
REDIS_PASSWORD=null            # string | Mot de passe à utilisé par REDIS | password
REDIS_PORT=6379                # string | Le port à utilisé par REDIS | 6379
REDIS_PREFIX="${REDIS_PREFIX}" # string | Ajout d'un prefixe sur tout les jwt stocké sur redis  | stackdev_

MAIL_DRIVER=smtp                     # string | Le driver à utilisé pour l'envoi de mail | smtp
MAIL_HOST=${MAIL_HOST}               # string | Le nom de l'hôte à utilisé pour le driver de mail | mailcatcher
MAIL_PORT=${MAIL_PORT}               # int | Le port à utilisé pour l'envoi de mail | 1025
MAIL_FROM_ADDRESS=<EMAIL>   # string | L'adresse mail de l'application pour l'envoi des mails | <EMAIL>
MAIL_FROM_NAME=Système               # string | Le nom du mail de l'application pour l'envoi des mails | Système
MAIL_USERNAME=null                   # string | Le nom d'utilisateur pour l'authentifcation stmp | null
MAIL_PASSWORD=null                   # string | Le mot de passe pour l'authentification stmp | password
MAIL_ENCRYPTION=null                 # string | Le protocol d'encryption pour les mails | tls

JWT_SECRET_KEY="${JWT_SECRET_KEY}" # string | La clé secrète du AKT | b739a89f4e42636215ed3d4a99c5d0b7
JWT_TTL=${JWT_TTL} 				   # int 	| Le temps d'expiration du JWT | 3600
PORTAL_AKT=${PORTAL_AKT}           # string | La clé d'accès au portail | b739a89f4e42636215ed3d4a99c5d0b7
INTERNAL_AKT=${INTERNAL_AKT}       # string | La clé d'accès interne AKT | b739a89f4e42636215ed3d4a99c5d0b7
AKT=${AKT}                         # string | La clé d'accès AKT | eyJhbGciOiJzaGEyNTYiLCJ0eXAiOiJBS1QifQ.eyJrZXkiOiJldXQwMGJOUzhqbUlENTVOIn0.964dfd0003e14d66ac6ea2f19b1b1eaef9147e78512730b920f42423c89949ed

# Si utilisation de bdd CRM pour add/update/delete data
#CRM_BRIDGE_URL= # string | L'URL pour l'utilisation de bdd CRM pour add/update/delete data | http://0.0.0.0:0000/api/

# Si utilisation de notifications mobiles
FCM_PUSH_NOTIFICATIONS_URL="${FCM_PUSH_NOTIFICATIONS_URL}" # string | L'URL du Firebase Cloud Messaging (notifications)| https://fcm.googleapis.com/fcm/send
FCM_KEY=${FCM_KEY}                                         # string | La clé pour le Firebase Cloud Messaging | b739a89f4e42636215ed3d4a99c5d0b7

S3_ENDPOINT="${S3_ENDPOINT}"           # string | Le point de terminaison Minio S3 auquel se connecter | http://minio
S3_PORT=${S3_PORT}                     # int    | Le numéro de port de minio | 9000
S3_KEY="${S3_KEY}"                     # string | L'identifiant de la clé d'accès au compte Minio | stackdev
S3_SECRET="${S3_SECRET}"               # string | La clé secrète associée à l'ID de clé d'accès Minio | secret
S3_BUCKET="${S3_BUCKET}"               # string | Nom du Bucket Minio | conges
FILESYSTEM_CLOUD="${FILESYSTEM_CLOUD}" # string | Système de gestion fichier | minio
