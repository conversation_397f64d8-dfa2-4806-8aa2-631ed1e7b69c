<?php

namespace Database\Factories\Api;

use App\Models\Api\LeaveType;
use Illuminate\Database\Eloquent\Factories\Factory;

class LeaveTypeSubFamilyFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $leaveType = LeaveType::inRandomOrder()->first();
        return [
            'leave_type_id' => $leaveType->getKey(),
            'name' => $this->faker->word,
            'value' => random_int(1, 100),
            'absence_reason' => $leaveType->name
        ];
    }
}
