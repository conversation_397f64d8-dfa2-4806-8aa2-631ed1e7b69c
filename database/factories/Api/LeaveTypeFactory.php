<?php

namespace Database\Factories\Api;

use App\Models\Api\Client;
use App\Models\Api\Profile;
use App\Models\Api\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class LeaveTypeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $profilesAdmin = Profile::whereIn('label',['ADMINISTRATEUR','ADMINISTRATEURMANAGER'])->get()->pluck('id')->toArray();
        $client = Client::inRandomOrder()->first();
        $siteId = $client->sites()->get()->pluck('id')->toArray();
        return [
            'name' => $this->faker->randomElement(['Congés payés', 'Congés sans solde', 'Conges maternités/paternités', 'RTT', 'Absences familiales', 'Autres']),
            'last_update_id' => User::whereIn('profile_id', $profilesAdmin)->whereIn('site_id', $siteId)->first(),
            'default_leave_value' => Arr::random([null, $this->faker->randomDigit]),
            'is_active' => $this->faker->boolean,
            'is_monthly' => $this->faker->boolean,
            'is_pay' => $this->faker->boolean,
            'is_deletable' => $this->faker->boolean,
            'is_half_day' => $this->faker->boolean,
            'is_attachment_required' => $this->faker->boolean,
            'can_exceed' => $this->faker->boolean,
            'leave_code' => 'C'.Str::upper($this->faker->randomLetter).Str::upper($this->faker->randomLetter),
            'color' => $this->faker->safeHexColor,
            'order_appearance' => $this->faker->numberBetween(0,4)
        ];
    }
}
