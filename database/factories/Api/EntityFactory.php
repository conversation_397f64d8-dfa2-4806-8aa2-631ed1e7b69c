<?php

namespace Database\Factories\Api;

use App\Models\Api\Client;
use App\Models\Api\Field;
use Illuminate\Database\Eloquent\Factories\Factory;

class EntityFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
                'client_id' => Client::inRandomOrder()
                                ->first()
                                ->getKey(),
                'tag' => $this->faker->word,
                'name' => $this->faker->company,
                'entity' => $this->faker->word,
                'id_field' => '0',
                'id_type' => $this->faker->randomNumber(2, false),
                'commercial_field' => $this->faker->jobTitle,
                'commercial_entity' => $this->faker->word,
                'hidden' => $this->faker->boolean,
                'locked' => $this->faker->boolean,
                'is_hidden_annotation' => $this->faker->boolean


        ];
    }
}
