<?php

namespace Database\Factories\Api;

use App\Models\Api\Client;
use App\Models\Api\Site;

use Illuminate\Database\Eloquent\Factories\Factory;

class SiteFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {

        return [
            'client_id' => Client::inRandomOrder()
                ->first()
                ->getKey(),
            'name' => $this->faker->city,
            'country' => $this->faker->country,
            'country_alpha' => $this->faker->countryCode,
            'subdivision' => null,
            'subdivision_code' => null

        ];
    }
}
