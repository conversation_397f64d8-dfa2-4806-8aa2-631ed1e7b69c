<?php

namespace Database\Factories\Api;


use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\LeaveTypeSubFamily;
use App\Models\Api\Status;
use App\Models\Api\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;
use function GuzzleHttp\Promise\exception_for;

class LeaveFactory extends Factory {

    public function definition(){
        $user = User::inRandomOrder()->first();
        $start_date = $this->faker->dateTimeBetween(now(), '+1 month');
        $start_date->setTime(0,0,0);
        $end_date = Carbon::parse($start_date)->setTime(23,59,59)->addWeeks(random_int(1,4));
        $daysOpen = $user->site->client->days()->get()->pluck('day_name')->toArray();
        $duration = (Carbon::parse($start_date)->diffInHoursFiltered( function(Carbon $date) use($daysOpen){
                return in_array(strtoupper($date->dayName),$daysOpen);
            }, Carbon::parse($end_date)))/24;
        $pdf = $this->faker->word.'.pdf';
        $leaveType = LeaveType::where('client_id', '=', $user->site->client->getKey())->inRandomOrder()->first();
        $leaveTypeSubFamilyId = null;

        if(LeaveTypeSubFamily::where('leave_type_id', '=', $leaveType->getKey())->exists()){
            $leaveTypeSubFamilyId = LeaveTypeSubFamily::where('leave_type_id', '=', $leaveType->getKey())->first()->getKey();
        }



        return [
            "user_id" => $user->getKey(),
            "status_id" => Status::inRandomOrder()->first()->getKey(),
            "leave_type_id" => $leaveType->getKey(),
            "leave_type_sub_family_id" => $leaveTypeSubFamilyId,
            "start_date" => $start_date,
            "end_date" => $end_date,
            "comment" => $this->faker->text || null,
            "attachment_name" => $leaveType->is_attachment_required ? Arr::random([$pdf, null]) : null,
            "attachment_path" => $leaveType->is_attachment_required ? "path" : null,
            'n1' => $duration,
            'duration' => $duration,
        ];
    }
}
