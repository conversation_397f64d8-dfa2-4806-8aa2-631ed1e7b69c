<?php

namespace Database\Factories\Api;

use App\Models\Api\Section;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;

class FieldFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $tag = $this->faker->randomElement(['site_name', 'is_disabled_field', 'email_field', 'first_name', 'last_name']);
        $label = 0;
        $field = 0;
        $type = 0;
        switch ($tag) {
            case "site_name":
                $label = "Nom du site";
                $field = "name";
                $type="nvarchar";
                break;
            case "is_disabled_field":
                $label = "Statut";
                $field = "isdisabled";
                $type="bit";
                break;
            case "email_field":
                $label = "Email";
                $field = "internalemailadress";
                $type="nvarchar";
                break;
            case "first_name":
                $label = "Prenom";
                $field = "firstname";
                $type="nvarchar";
                break;
            case "last_name":
                $label = "Nom";
                $field = "lastname";
                $type="nvarchar";
                break;
        }



        return [
            'section_id' => Section::inRandomOrder()
                ->first()
                ->getKey(),
            'tag' => $tag,
            'label' => $label,
            'field' => $field,
            'type' => $type,
            'is_nullable' => 0,
            'is_default' => 1,
            'index' => 0
        ];
    }

}
