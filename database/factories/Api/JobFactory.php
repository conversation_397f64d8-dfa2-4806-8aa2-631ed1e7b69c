<?php

namespace Database\Factories\Api;

use App\Models\Api\Client;
use Illuminate\Database\Eloquent\Factories\Factory;

class JobFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'queue' => $this->faker->name,
            'file_name' => $this->faker->word.'.xlsx',
            'file_path' => 'worker/imports/'.$this->faker->randomDigit,
            'max_attempts' =>$this->faker->numberBetween(1,15),
            'reserved' => $this->faker->boolean,
            'done' => $this->faker->boolean,
            'avoid_errors' => $this->faker->boolean,
            'client_id' => Client::inRandomOrder()
                            ->first()
                            ->getKey(),
            'log_file_name' => ''
        ];
    }
}
