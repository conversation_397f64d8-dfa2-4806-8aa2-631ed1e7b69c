<?php

namespace Database\Factories\Api;

use App\Models\Api\Entity;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;

class SectionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'entity_id' => Entity::inRandomOrder()->first()->getKey(),
            'label' => Arr::random(['Informations de base', 'Sites', 'Managers'])
        ];
    }
}
