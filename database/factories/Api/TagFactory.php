<?php

namespace Database\Factories\Api;

use App\Models\Api\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class TagFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'label' => $this->faker->jobTitle,
            'user_id' => User::inRandomOrder()->first()->getKey()
        ];
    }
}
