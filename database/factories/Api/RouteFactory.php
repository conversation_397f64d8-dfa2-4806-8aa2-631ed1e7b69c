<?php

namespace Database\Factories\Api;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;

class RouteFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
//        $operation = Arr::random(['leave', 'update', 'destroy', 'store', 'index']);
//        $controllerType = Arr::random(['Clients', 'Documentation', 'Leaves', 'UserLeaveCounts', 'exportHistories', 'site', 'teams']);
//
//        return [
//            'name' => $operation.'.'.$controllerType.'Controller',
//            'type' => $controllerType
//        ];
    }
}
