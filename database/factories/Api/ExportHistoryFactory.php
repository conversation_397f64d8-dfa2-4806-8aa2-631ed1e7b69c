<?php

namespace Database\Factories\Api;

use App\Models\Api\Profile;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ExportHistoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $ext = $this->faker->fileExtension;
        return [
            'user_id' => '',
            'file_name' => $this->faker->word.'.'.$ext,
            'extension' => $ext,
            'type' =>$this->faker->randomElement(['EBP','SAGE','SILAE','EXCEL'])
        ];
    }
}
