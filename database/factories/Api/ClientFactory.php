<?php

namespace Database\Factories\Api;

use App\Models\Api\Client;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class ClientFactory extends Factory
{
    protected $model = Client::class;
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => $this->faker->company,
            'uuid' => Str::uuid(),
            'count_public_holidays' => 1,
            'validation_scheme' => $this->faker->randomElement(['Horizontal', 'Vertical']),
            'is_pentecost' => $this->faker->boolean,
            'number_managers_can_validate' => $this->faker->numberBetween(1, 3),
        ];
    }
}
