<?php

namespace Database\Factories\Api;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class ApplicationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $name = $this->faker->word;
        $slg = Str::slug($name);
        return [
            'name' => $name,
            'docker_name' => $slg.'-api',
            'slug' => $slg,
            'uuid' => Str::uuid()
        ];
    }
}
