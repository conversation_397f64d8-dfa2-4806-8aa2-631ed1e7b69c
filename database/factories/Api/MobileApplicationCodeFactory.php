<?php

namespace Database\Factories\Api;

use Illuminate\Database\Eloquent\Factories\Factory;

class MobileApplicationCodeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'activation_code' => $this->faker->unique()->lexify('????????????'),
            'order_number' => $this->faker->bothify('MX98Y27MJ*'),
            'is_locked' => 1,
            'is_used' => rand(0,1),
            'linked_at' => $this->faker->date()
        ];
    }
}
