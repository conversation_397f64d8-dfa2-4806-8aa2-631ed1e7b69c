<?php

namespace Database\Factories\Api;

use App\Models\Api\Profile;
use App\Models\Api\Site;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $site = Site::inRandomOrder()->first();

        return [
            'profile_id' => Profile::inRandomOrder()
            ->first()
            ->getKey(),
            'site_id' => $site->getKey(),
            'uuid' => Str::uuid(),
            'client_uuid' => $site->client?->uuid,
            'firstname' => $this->faker->firstName,
            'lastname' => $this->faker->lastName,
            'email' => $this->faker->companyEmail,
            'picture_path' => $this->faker->word.'/'.$this->faker->word,
            'license_path' => $this->faker->word.'/'.$this->faker->word,
            'matricule' => $this->faker->randomNumber(5, true),
            'can_receive_mails' => $this->faker->boolean
        ];
    }
}
