<?php

namespace Database\Factories\Api;

use App\Models\Api\LeaveType;
use App\Models\Api\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class UserLeaveCountFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $acquired = $this->faker->numberBetween(5,25);
        $taken = $this->faker->numberBetween(0, $acquired);

        return [
            'leave_type_id' => LeaveType::inRandomOrder()->first()->getKey(),
            'acquired' => $acquired,
            'taken' => $taken,
            'balance' => $acquired - $taken
        ];
    }
}
