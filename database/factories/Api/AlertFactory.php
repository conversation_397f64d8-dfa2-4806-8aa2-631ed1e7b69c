<?php

namespace Database\Factories\Api;

use App\Models\Api\Leave;
use App\Models\Api\Status;
use App\Models\Api\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class AlertFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $leave = Leave::inRandomOrder()->first();
        $userId = $leave->user_id;
        $userSiteId = User::where('id', '=', $userId)->first()->site_id;


       // id d'un manager au hasard responsable de l'alert
        return [
            'status_id' => $leave->status_id,
            'user_id' => $userId,
            'user_leave_id' => User::where('profile_id', '>', 1)->first()->getKey(),
            'title' => 'CONGES : '.$this->faker->word,
            'body' => $this->faker->text

        ];
    }
}
