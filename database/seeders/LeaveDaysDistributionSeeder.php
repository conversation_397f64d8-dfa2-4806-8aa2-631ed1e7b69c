<?php

namespace Database\Seeders;

use App\Models\Api\Leave;
use Illuminate\Database\Seeder;

class LeaveDaysDistributionSeeder extends Seeder {
	/**
	 * Run the database seeds.
	 *
	 * @return void
	 */
	public function run(){
        Leave::query()
            ->select('leaves.*')
            ->join('users', 'users.id', 'leaves.user_id')
            ->join('sites', 'sites.id', 'users.site_id')
            ->join('clients', 'clients.id', 'sites.client_id')
            ->whereNull('users.deleted_at')
            ->each(function (Leave $leave) {
                $leave->leaveDaysDistribution($leave->user_id);
                $leave->save();
            }, 15);
    }
}
