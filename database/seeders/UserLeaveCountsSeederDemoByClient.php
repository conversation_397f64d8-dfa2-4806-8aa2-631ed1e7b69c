<?php

namespace Database\Seeders;

use App\Models\Api\LeaveType;
use App\Models\Api\User;
use App\Models\Api\UserLeaveCount;
use Illuminate\Database\Seeder;

class UserLeaveCountsSeederDemoByClient extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $currentUser = (new DatabaseSeeder())->getUser();
        $client = $currentUser->client;

        $users = User::select('users.*')
            ->join('sites', function ($join) use ($client) {
            $join->on('sites.id', 'users.site_id')
                ->where('sites.client_id', $client->getKey());
        })->get();

        $leaveTypes = LeaveType::query()
            ->where('client_id', $client->getKey())
            ->where('needs_count', true)
            ->get();

        $usersLeavesCount = [];

        foreach ($users as $user) {
            $usersLeavesCount[$user->getKey()] = [];
            foreach ($leaveTypes as $leaveType) {
                //Paramétrage par site
                if (!is_null($leaveType->site_id) && $leaveType->site_id === $user->site_id) {
                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()] = [];
                    switch ($leaveType->leave_code) {
                        case 'CP':
                            switch ($user->firstname.' '.$user->lastname){
                                case 'Jean DUPONT':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 3.25;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 0;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquiredN1'] = 12.5;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['takenN1'] = 0;
                                    break;
                                case 'Clément PETIT':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 2;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 0;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquiredN1'] = 12.5;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['takenN1'] = 2;
                                    break;
                                case 'Nicolas RICHARD':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 6.45;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 0;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquiredN1'] = 16.04;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['takenN1'] = 0;
                                    break;
                                case 'Julien BERNARD':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 7;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 0;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquiredN1'] = 17.5;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['takenN1'] = 0;
                                    break;
                                case 'Alexia MARTIN':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 17;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 0;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquiredN1'] = 33.67;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['takenN1'] = 0;
                                    break;
                            }
                            break;
                        case 'CGM':
                            switch ($user->firstname.' '.$user->lastname){
                                case 'Jean DUPONT':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 6.67;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 1;
                                    break;
                                case 'Clément PETIT':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 17;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 1;
                                    break;
                                case 'Nicolas RICHARD':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 3.85;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 1;
                                    break;
                                case 'Julien BERNARD':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 12;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 9;
                                    break;
                                case 'Alexia MARTIN':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 12;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 12;
                                    break;
                            }
                            break;
                        case 'RCO':
                            switch ($user->firstname.' '.$user->lastname){
                                case 'Jean DUPONT':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 9.18;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 2;
                                    break;
                                case 'Clément PETIT':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 16;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 8;
                                    break;
                                case 'Nicolas RICHARD':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 25;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 0;
                                    break;
                                case 'Julien BERNARD':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 21;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 9;
                                    break;
                                case 'Alexia MARTIN':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 9.12;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 4;
                                    break;
                            }
                            break;
                        case 'RTT':
                            switch ($user->firstname.' '.$user->lastname){
                                case 'Jean DUPONT':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 15;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 1;
                                    break;
                                case 'Clément PETIT':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 22.75;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 21;
                                    break;
                                case 'Nicolas RICHARD':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 7;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 7;
                                    break;
                                case 'Julien BERNARD':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 5.70;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 3;
                                    break;
                                case 'Alexia MARTIN':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 12.5;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 3;
                                    break;
                            }
                            break;
                    }
                }
                else if (is_null($leaveType->site_id) && $user->site->leave_types()->count() === 0) {
                    //Paramétrage par client
                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()] = [];
                    switch ($leaveType->leave_code) {
                        case 'CP':
                            switch ($user->firstname.' '.$user->lastname){
                                case $currentUser->firstname.' '.$currentUser->lastname:
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 8;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 0;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquiredN1'] = 10.04;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['takenN1'] = 7;
                                    break;
                                case 'Simon LEFEBVRE':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 10.18;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 0;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquiredN1'] = 28;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['takenN1'] = 2;
                                    break;
                                case 'Jade PREVOST':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 3;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 0;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquiredN1'] = 19.5;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['takenN1'] = 0;
                                    break;
                                case 'Clara PERRIN':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 2;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 0;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquiredN1'] = 12.83;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['takenN1'] = 0;
                                    break;
                                case 'Arthur DUBOIS':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 0;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 0;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquiredN1'] = 5.7;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['takenN1'] = 0;
                                    break;
                                case 'Hugo LOPEZ':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 6;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 0;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquiredN1'] = 14;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['takenN1'] = 2;
                                    break;
                            }
                            break;
                        case 'CGM':
                            switch ($user->firstname.' '.$user->lastname){
                                case $currentUser->firstname.' '.$currentUser->lastname:
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 12.5;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 0;
                                    break;
                                case 'Simon LEFEBVRE':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 1;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 0;
                                    break;
                                case 'Jade PREVOST':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 24;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 21;
                                    break;
                                case 'Clara PERRIN':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 24.5;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 0;
                                    break;
                                case 'Arthur DUBOIS':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 24.5;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 11.5;
                                    break;
                                case 'Hugo LOPEZ':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 17;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 2;
                                    break;
                            }
                            break;
                        case 'RCO':
                            switch ($user->firstname.' '.$user->lastname){
                                case $currentUser->firstname.' '.$currentUser->lastname:
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 9;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 1;
                                    break;
                                case 'Simon LEFEBVRE':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 6;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 3;
                                    break;
                                case 'Jade PREVOST':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 9;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 5;
                                    break;
                                case 'Clara PERRIN':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 12;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 0;
                                    break;
                                case 'Arthur DUBOIS':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 9.75;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 2;
                                    break;
                                case 'Hugo LOPEZ':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 11;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 10;
                                    break;
                            }
                            break;
                        case 'CFR':
                            switch ($user->firstname.' '.$user->lastname){
                                case $currentUser->firstname.' '.$currentUser->lastname:
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 6.18;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 0;
                                    break;
                                case 'Simon LEFEBVRE':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 5.7;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 0;
                                    break;
                                case 'Jade PREVOST':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 8;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 3;
                                    break;
                                case 'Clara PERRIN':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 12.48;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 3;
                                    break;
                                case 'Arthur DUBOIS':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 10.4;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 4;
                                    break;
                                case 'Hugo LOPEZ':
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['acquired'] = 9.25;
                                    $usersLeavesCount[$user->getKey()][$leaveType->getKey()]['taken'] = 0;
                                    break;
                            }
                            break;
                    }
                }
            }
        }

        foreach ($usersLeavesCount as $userId => $userLeavesCount) {
            foreach ($userLeavesCount as $leaveId => $LeaveCount) {
                $leaveType = LeaveType::find($leaveId);
                UserLeaveCount::create([
                    'user_id' => $userId,
                    'leave_type_id' => $leaveType->getKey(),
                    'acquired' => $LeaveCount['acquired'],
                    'taken' => $LeaveCount['taken'],
                    'balance' => $LeaveCount['acquired'] - $LeaveCount['taken'],
                ]);
                if ($leaveType->is_pay){
                    UserLeaveCount::create([
                        'user_id' => $userId,
                        'leave_type_id' => $leaveType->getKey(),
                        'is_last_year' => true,
                        'acquired' => $LeaveCount['acquiredN1'],
                        'taken' => $LeaveCount['takenN1'],
                        'balance' => $LeaveCount['acquiredN1'] - $LeaveCount['takenN1'],
                    ]);
                }
            }
        }
    }
}
