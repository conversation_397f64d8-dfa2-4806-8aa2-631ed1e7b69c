<?php

namespace Database\Seeders;

use App\Models\Api\Profile;
use App\Models\Api\User;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $this->call(LeaveTypesSeederDemoByClient::class);
        $this->call(DaysSeederDemoByClient::class);
        $this->call(LeavesSeederDemoByClient::class);
        $this->call(UserLeaveCountsSeederDemoByClient::class);
        $this->call(UserTagsSeederDemoByClient::class);
    }

    public function getUser()
    {
        $client_id = request()->header('ClientUuid');

        $currentUser = User::select('users.*')
            ->firstWhere([
                ['client_uuid', $client_id],
                ['users.profile_id', Profile::firstWhere('label', 'ADMINISTRATEURMANAGER')->getKey()],
            ]);

        return $currentUser;
    }
}
