<?php

namespace Database\Seeders;

use App\Models\Api\History;
use App\Models\Api\Leave;
use App\Models\Api\LeaveType;
use App\Models\Api\Status;
use App\Models\Api\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class LeavesSeederDemoByClient extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $currentUser = (new DatabaseSeeder())->getUser();
        $client = $currentUser->client;

        $users = User::select('users.*')
            ->join('sites', function ($join) use ($client) {
                $join->on('sites.id', 'users.site_id')
                    ->where('sites.client_id', $client->getKey());
            })->get();

        $leaveTypes = LeaveType::query()
            ->where('client_id', $client->getKey())
            ->get();

        $userDupont = $users->where('firstname', 'Jean')->where('lastname', 'DUPONT')->first();
        $userPerrin = $users->where('firstname', 'Clara')->where('lastname', 'PERRIN')->first();
        $userBernard = $users->where('firstname', 'Julien')->where('lastname', 'BERNARD')->first();
        $userPrevost = $users->where('firstname', 'Jade')->where('lastname', 'PREVOST')->first();
        $userLefebvre = $users->where('firstname', 'Simon')->where('lastname', 'LEFEBVRE')->first();
        $userPetit = $users->where('firstname', 'Clément')->where('lastname', 'PETIT')->first();
        $userRichard = $users->where('firstname', 'Nicolas')->where('lastname', 'RICHARD')->first();
        $userMartin = $users->where('firstname', 'Alexia')->where('lastname', 'MARTIN')->first();
        $userLopez = $users->where('firstname', 'Hugo')->where('lastname', 'LOPEZ')->first();

        $leaveTypesSiteExpertisLyon = $leaveTypes->whereNull('site_id');
        $leaveTypesSiteExpertisParis = $leaveTypes->whereNotNull('site_id');

        $leaveTypesSiteExpertisLyonCGM = $leaveTypesSiteExpertisLyon->firstWhere('leave_code', 'CGM')?->getKey();
        $leaveTypesSiteExpertisLyonCPATER = $leaveTypesSiteExpertisLyon->firstWhere('leave_code', 'CPATER')?->getKey();
        $leaveTypesSiteExpertisLyonMNP = $leaveTypesSiteExpertisLyon->firstWhere('leave_code', 'MNP')?->getKey();
        $leaveTypesSiteExpertisLyonCP = $leaveTypesSiteExpertisLyon->firstWhere('leave_code', 'CP')?->getKey();
        $leaveTypesSiteExpertisLyonRCO = $leaveTypesSiteExpertisLyon->firstWhere('leave_code', 'RCO')?->getKey();
        $leaveTypesSiteExpertisLyonCFR = $leaveTypesSiteExpertisLyon->firstWhere('leave_code', 'CFR')?->getKey();

        $leaveTypesSiteExpertisParisCP = $leaveTypesSiteExpertisParis->firstWhere('leave_code', 'CP')?->getKey();
        $leaveTypesSiteExpertisParisCGM = $leaveTypesSiteExpertisParis->firstWhere('leave_code', 'CGM')?->getKey();
        $leaveTypesSiteExpertisParisRCO = $leaveTypesSiteExpertisParis->firstWhere('leave_code', 'RCO')?->getKey();
        $leaveTypesSiteExpertisParisRTT = $leaveTypesSiteExpertisParis->firstWhere('leave_code', 'RTT')?->getKey();
        $leaveTypesSiteExpertisParisMNP = $leaveTypesSiteExpertisParis->firstWhere('leave_code', 'MNP')?->getKey();

        $statutSubmit = Status::firstWhere('tag', 'SUBMITTED')->getKey();
        $statutSubmitCancel = Status::firstWhere('tag', 'SUBMITTED_TO_CANCELLATION')->getKey();
        $statutValidate = Status::firstWhere('tag', 'VALIDATED')->getKey();
        $statutCancel = Status::firstWhere('tag', 'CANCELED')->getKey();
        $statutRefused = Status::firstWhere('tag', 'REFUSED')->getKey();
        $statutTransmit = Status::firstWhere('tag', 'TRANSMITTED')->getKey();

        $usersLeaves = [
            $currentUser->getKey() => [
                $leaveTypesSiteExpertisLyonCGM =>
                [
                    [
                        'status_id' => $statutCancel,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->subWeeks(2)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->subWeeks(2)->startOfWeek()->addDays(2)->endOfDay(),
                        'n' => 3,
                        'duration' => 3,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutCancel,
                                'user_id' => $currentUser->getKey(),
                            ]
                        ]
                    ],
                ],
                $leaveTypesSiteExpertisLyonCPATER =>
                [
                    [
                        'status_id' => $statutValidate,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addMonths(2)->startOfMonth()->startOfDay(),
                        'end_date' => Carbon::now()->addMonths(2)->startOfMonth()->addDays(19)->endOfDay(),
                        'duration' => 20,
                        'comment' => 'Naissance',
                        'justificatif' => 'justificatif-cpater',
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                ],
                $leaveTypesSiteExpertisLyonMNP =>
                [
                    [
                        'status_id' => $statutTransmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->subWeek()->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->subWeek()->addDays(2)->endOfDay(),
                        'duration' => 3,
                        'comment' => 'Grippe',
                        'justificatif' => 'justificatif-mnp',
                    ],
                ],
                $leaveTypesSiteExpertisLyonCP =>
                [
                    [
                        'status_id' => $statutTransmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 3,
                        'start_date' => Carbon::now()->subMonths(4)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->subMonths(4)->startOfWeek()->endOfDay(),
                        'n1' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutTransmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutTransmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 3,
                        'start_date' => Carbon::now()->subMonths(4)->addWeek()->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->subMonths(4)->addWeek()->startOfWeek()->endOfDay(),
                        'n1' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutTransmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutTransmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 3,
                        'start_date' => Carbon::now()->subMonths(4)->addWeeks(2)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->subMonths(4)->addWeeks(2)->startOfWeek()->endOfDay(),
                        'n1' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutTransmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutTransmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 3,
                        'start_date' => Carbon::now()->subMonths(4)->addWeeks(3)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->subMonths(4)->addWeeks(3)->startOfWeek()->endOfDay(),
                        'n1' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutTransmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutTransmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 3,
                        'start_date' => Carbon::now()->subMonths(4)->addWeeks(4)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->subMonths(4)->addWeeks(4)->startOfWeek()->endOfDay(),
                        'n1' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutTransmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutTransmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 3,
                        'start_date' => Carbon::now()->subMonths(4)->addWeeks(5)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->subMonths(4)->addWeeks(5)->startOfWeek()->endOfDay(),
                        'n1' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutTransmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutTransmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 3,
                        'start_date' => Carbon::now()->subMonths(4)->addWeeks(6)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->subMonths(4)->addWeeks(6)->startOfWeek()->endOfDay(),
                        'n1' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutTransmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                ],
            ],
            $userDupont->getKey() => [
                $leaveTypesSiteExpertisParisCP =>
                [
                    [
                        'status_id' => $statutValidate,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(2)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(2)->startOfWeek()->endOfDay(),
                        'n1' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userDupont->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                ],
                $leaveTypesSiteExpertisParisRTT =>
                [
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 1,
                        'start_date' => Carbon::now()->addWeeks(4)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(4)->startOfWeek()->addDay()->endOfDay(),
                        'n' => 2,
                        'duration' => 2,
                        'comment' => 'Formation achat',
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userDupont->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 1,
                        'start_date' => Carbon::now()->addWeeks(5)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(5)->startOfWeek()->addDay()->endOfDay(),
                        'n' => 2,
                        'duration' => 2,
                        'comment' => 'Formation achat',
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userDupont->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 1,
                        'start_date' => Carbon::now()->addWeeks(6)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(6)->startOfWeek()->addDay()->endOfDay(),
                        'n' => 2,
                        'duration' => 2,
                        'comment' => 'Formation achat',
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userDupont->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 1,
                        'start_date' => Carbon::now()->addWeeks(7)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(7)->startOfWeek()->addDay()->endOfDay(),
                        'n' => 2,
                        'duration' => 2,
                        'comment' => 'Formation achat',
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userDupont->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 1,
                        'start_date' => Carbon::now()->addWeeks(8)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(8)->startOfWeek()->addDay()->endOfDay(),
                        'n' => 2,
                        'duration' => 2,
                        'comment' => 'Formation achat',
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userDupont->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 1,
                        'start_date' => Carbon::now()->addWeeks(9)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(9)->startOfWeek()->addDay()->endOfDay(),
                        'n' => 2,
                        'duration' => 2,
                        'comment' => 'Formation achat',
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userDupont->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 1,
                        'start_date' => Carbon::now()->addWeeks(10)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(10)->startOfWeek()->addDay()->endOfDay(),
                        'n' => 2,
                        'duration' => 2,
                        'comment' => 'Formation achat',
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userDupont->getKey(),
                            ],
                        ]
                    ],
                ],
            ],
            $userPerrin->getKey() => [
                $leaveTypesSiteExpertisLyonCFR =>
                [
                    [
                        'status_id' => $statutRefused,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(10)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(10)->startOfWeek()->addDay()->endOfDay(),
                        'n' => 2,
                        'duration' => 2,
                        'comment' => 'Manque d\'effectif',
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPerrin->getKey(),
                            ],
                            [
                                'status_id' => $statutRefused,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                ],
                $leaveTypesSiteExpertisLyonCGM =>
                [
                    [
                        'status_id' => $statutSubmitCancel,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeek()->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeek()->startOfWeek()->addDay()->endOfDay(),
                        'n' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPerrin->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutSubmitCancel,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmitCancel,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(2)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(2)->startOfWeek()->addDay()->endOfDay(),
                        'n' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPerrin->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutSubmitCancel,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmitCancel,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(3)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(3)->startOfWeek()->addDay()->endOfDay(),
                        'n' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPerrin->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutSubmitCancel,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmitCancel,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(4)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(4)->startOfWeek()->addDay()->endOfDay(),
                        'n' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPerrin->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutSubmitCancel,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmitCancel,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(5)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(5)->startOfWeek()->addDay()->endOfDay(),
                        'n' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPerrin->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutSubmitCancel,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmitCancel,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(6)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(6)->startOfWeek()->addDay()->endOfDay(),
                        'n' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPerrin->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutSubmitCancel,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmitCancel,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(7)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(7)->startOfWeek()->addDay()->endOfDay(),
                        'n' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPerrin->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutSubmitCancel,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                ],
            ],
            $userBernard->getKey() => [
                $leaveTypesSiteExpertisParisCGM =>
                [
                    [
                        'status_id' => $statutCancel,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(9)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(9)->startOfWeek()->endOfDay(),
                        'n' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userBernard->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutSubmitCancel,
                                'user_id' => $userBernard->getKey(),
                            ],
                            [
                                'status_id' => $statutCancel,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                ],
                $leaveTypesSiteExpertisParisRCO =>
                [
                    [
                        'status_id' => $statutSubmitCancel,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeek()->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeek()->startOfWeek()->endOfDay(),
                        'n' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userBernard->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutSubmitCancel,
                                'user_id' => $userBernard->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmitCancel,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(2)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(2)->startOfWeek()->endOfDay(),
                        'n' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userBernard->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutSubmitCancel,
                                'user_id' => $userBernard->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmitCancel,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(3)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(3)->startOfWeek()->endOfDay(),
                        'n' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userBernard->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutSubmitCancel,
                                'user_id' => $userBernard->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmitCancel,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(4)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(4)->startOfWeek()->endOfDay(),
                        'n' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userBernard->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutSubmitCancel,
                                'user_id' => $userBernard->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmitCancel,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(5)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(5)->startOfWeek()->endOfDay(),
                        'n' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userBernard->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutSubmitCancel,
                                'user_id' => $userBernard->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmitCancel,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(6)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(6)->startOfWeek()->endOfDay(),
                        'n' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userBernard->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutSubmitCancel,
                                'user_id' => $userBernard->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmitCancel,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(7)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(7)->startOfWeek()->endOfDay(),
                        'n' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userBernard->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutSubmitCancel,
                                'user_id' => $userBernard->getKey(),
                            ],
                        ]
                    ],
                ]
            ],
            $userPrevost->getKey() => [
                $leaveTypesSiteExpertisLyonRCO =>
                [
                    [
                        'status_id' => $statutTransmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 4,
                        'start_date' => Carbon::now()->subMonth()->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->subMonth()->startOfWeek()->addDays(2)->endOfDay(),
                        'n' => 3,
                        'duration' => 3,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPrevost->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userLefebvre->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutTransmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                ],
                $leaveTypesSiteExpertisLyonCP =>
                [
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeek()->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeek()->startOfWeek()->addDay()->endOfDay(),
                        'n1' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPrevost->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userLefebvre->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(2)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(2)->startOfWeek()->addDay()->endOfDay(),
                        'n1' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPrevost->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userLefebvre->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(3)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(3)->startOfWeek()->addDay()->endOfDay(),
                        'n1' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPrevost->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userLefebvre->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(4)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(4)->startOfWeek()->addDay()->endOfDay(),
                        'n1' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPrevost->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userLefebvre->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(5)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(5)->startOfWeek()->addDay()->endOfDay(),
                        'n1' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPrevost->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userLefebvre->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(6)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(6)->startOfWeek()->addDay()->endOfDay(),
                        'n1' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPrevost->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userLefebvre->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(7)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(7)->startOfWeek()->addDay()->endOfDay(),
                        'n1' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPrevost->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userLefebvre->getKey(),
                            ],
                        ]
                    ],
                ],
            ],
            $userPetit->getKey() => [
                $leaveTypesSiteExpertisParisCGM =>
                [
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeek()->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeek()->startOfWeek()->endOfDay(),
                        'n' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPetit->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userDupont->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(2)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(2)->startOfWeek()->endOfDay(),
                        'n' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPetit->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userDupont->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(3)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(3)->startOfWeek()->endOfDay(),
                        'n' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPetit->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userDupont->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(4)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(4)->startOfWeek()->endOfDay(),
                        'n' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPetit->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userDupont->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(5)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(5)->startOfWeek()->endOfDay(),
                        'n' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPetit->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userDupont->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(6)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(6)->startOfWeek()->endOfDay(),
                        'n' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPetit->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userDupont->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(7)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(7)->startOfWeek()->endOfDay(),
                        'n' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPetit->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userDupont->getKey(),
                            ],
                        ]
                    ],
                ],
                $leaveTypesSiteExpertisParisRTT =>
                [
                    [
                        'status_id' => $statutTransmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 4,
                        'start_date' => Carbon::now()->subWeeks(10)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->subWeeks(10)->startOfWeek()->addDays(2)->endOfDay(),
                        'n' => 3,
                        'duration' => 3,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPetit->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userDupont->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutTransmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutTransmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 4,
                        'start_date' => Carbon::now()->subWeeks(9)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->subWeeks(9)->startOfWeek()->addDays(2)->endOfDay(),
                        'n' => 3,
                        'duration' => 3,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPetit->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userDupont->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutTransmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutTransmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 4,
                        'start_date' => Carbon::now()->subWeeks(8)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->subWeeks(8)->startOfWeek()->addDays(2)->endOfDay(),
                        'n' => 3,
                        'duration' => 3,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPetit->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userDupont->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutTransmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutTransmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 4,
                        'start_date' => Carbon::now()->subWeeks(7)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->subWeeks(7)->startOfWeek()->addDays(2)->endOfDay(),
                        'n' => 3,
                        'duration' => 3,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPetit->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userDupont->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutTransmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutTransmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 4,
                        'start_date' => Carbon::now()->subWeeks(6)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->subWeeks(6)->startOfWeek()->addDays(2)->endOfDay(),
                        'n' => 3,
                        'duration' => 3,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPetit->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userDupont->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutTransmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutTransmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 4,
                        'start_date' => Carbon::now()->subWeeks(5)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->subWeeks(5)->startOfWeek()->addDays(2)->endOfDay(),
                        'n' => 3,
                        'duration' => 3,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPetit->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userDupont->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutTransmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutTransmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 4,
                        'start_date' => Carbon::now()->subWeeks(4)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->subWeeks(4)->startOfWeek()->addDays(2)->endOfDay(),
                        'n' => 3,
                        'duration' => 3,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userPetit->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userDupont->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                            [
                                'status_id' => $statutTransmit,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                ],
            ],
            $userRichard->getKey() => [
                $leaveTypesSiteExpertisParisCP =>
                [
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 1,
                        'start_date' => Carbon::now()->addWeeks(2)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(2)->startOfWeek()->addDay()->endOfDay(),
                        'n1' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userRichard->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 1,
                        'start_date' => Carbon::now()->addWeeks(3)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(3)->startOfWeek()->addDay()->endOfDay(),
                        'n1' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userRichard->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 1,
                        'start_date' => Carbon::now()->addWeeks(4)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(4)->startOfWeek()->addDay()->endOfDay(),
                        'n1' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userRichard->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 1,
                        'start_date' => Carbon::now()->addWeeks(5)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(5)->startOfWeek()->addDay()->endOfDay(),
                        'n1' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userRichard->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 1,
                        'start_date' => Carbon::now()->addWeeks(6)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(6)->startOfWeek()->addDay()->endOfDay(),
                        'n1' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userRichard->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 1,
                        'start_date' => Carbon::now()->addWeeks(7)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(7)->startOfWeek()->addDay()->endOfDay(),
                        'n1' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userRichard->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 1,
                        'start_date' => Carbon::now()->addWeeks(8)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(8)->startOfWeek()->addDay()->endOfDay(),
                        'n1' => 2,
                        'duration' => 2,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userRichard->getKey(),
                            ],
                        ]
                    ],
                ],
                $leaveTypesSiteExpertisParisRCO =>
                [
                    [
                        'status_id' => $statutValidate,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(10)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(10)->startOfWeek()->addDays(2)->endOfDay(),
                        'n' => 3,
                        'duration' => 3,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userRichard->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutValidate,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(11)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(11)->startOfWeek()->addDays(2)->endOfDay(),
                        'n' => 3,
                        'duration' => 3,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userRichard->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutValidate,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(12)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(12)->startOfWeek()->addDays(2)->endOfDay(),
                        'n' => 3,
                        'duration' => 3,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userRichard->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutValidate,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(13)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(13)->startOfWeek()->addDays(2)->endOfDay(),
                        'n' => 3,
                        'duration' => 3,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userRichard->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutValidate,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(14)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(14)->startOfWeek()->addDays(2)->endOfDay(),
                        'n' => 3,
                        'duration' => 3,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userRichard->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutValidate,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(15)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(15)->startOfWeek()->addDays(2)->endOfDay(),
                        'n' => 3,
                        'duration' => 3,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userRichard->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutValidate,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(16)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(16)->startOfWeek()->addDays(2)->endOfDay(),
                        'n' => 3,
                        'duration' => 3,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userRichard->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $currentUser->getKey(),
                            ],
                        ]
                    ],
                ],
                $leaveTypesSiteExpertisParisMNP =>
                [
                    [
                        'status_id' => $statutTransmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->subMonth()->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->subMonth()->startOfWeek()->addDay()->endOfDay(),
                        'n' => 2,
                        'duration' => 2,
                        'justificatif' => 'justificatif-mnp',
                    ],
                ],
            ],
            $userMartin->getKey() => [
                $leaveTypesSiteExpertisParisCP =>
                [
                    [
                        'status_id' => $statutValidate,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(2)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(2)->startOfWeek()->addDays(2)->endOfDay(),
                        'n1' => 3,
                        'duration' => 3,
                        'comment' => 'Séjour en famille',
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userMartin->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userRichard->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutValidate,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(3)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(3)->startOfWeek()->addDays(2)->endOfDay(),
                        'n1' => 3,
                        'duration' => 3,
                        'comment' => 'Séjour en famille',
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userMartin->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userRichard->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutValidate,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(4)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(4)->startOfWeek()->addDays(2)->endOfDay(),
                        'n1' => 3,
                        'duration' => 3,
                        'comment' => 'Séjour en famille',
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userMartin->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userRichard->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutValidate,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(5)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(5)->startOfWeek()->addDays(2)->endOfDay(),
                        'n1' => 3,
                        'duration' => 3,
                        'comment' => 'Séjour en famille',
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userMartin->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userRichard->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutValidate,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(6)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(6)->startOfWeek()->addDays(2)->endOfDay(),
                        'n1' => 3,
                        'duration' => 3,
                        'comment' => 'Séjour en famille',
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userMartin->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userRichard->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutValidate,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(7)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(7)->startOfWeek()->addDays(2)->endOfDay(),
                        'n1' => 3,
                        'duration' => 3,
                        'comment' => 'Séjour en famille',
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userMartin->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userRichard->getKey(),
                            ],
                        ]
                    ],
                    [
                        'status_id' => $statutValidate,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 2,
                        'start_date' => Carbon::now()->addWeeks(8)->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeeks(8)->startOfWeek()->addDays(2)->endOfDay(),
                        'n1' => 3,
                        'duration' => 3,
                        'comment' => 'Séjour en famille',
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userMartin->getKey(),
                            ],
                            [
                                'status_id' => $statutValidate,
                                'user_id' => $userRichard->getKey(),
                            ],
                        ]
                    ],
                ],
            ],
            $userLopez->getKey() => [
                $leaveTypesSiteExpertisLyonCFR =>
                [
                    [
                        'status_id' => $statutSubmit,
                        'last_updater_id' => $currentUser->getKey(),
                        'current_validator_level' => 1,
                        'start_date' => Carbon::now()->addWeek()->startOfWeek()->startOfDay(),
                        'end_date' => Carbon::now()->addWeek()->startOfWeek()->endOfDay(),
                        'n' => 1,
                        'duration' => 1,
                        'histories' => [
                            [
                                'status_id' => $statutSubmit,
                                'user_id' => $userLopez->getKey(),
                            ],
                        ]
                    ],
                ],
            ],
        ];

        foreach ($usersLeaves as $userId => $userLeaves) {
            foreach ($userLeaves as $leaveId => $userLeave) {
                foreach ($userLeave as $leave) {
                    $leaveCreated = new Leave([
                        'user_id' => $userId,
                        'creator_id' => $userId,
                        'status_id' => $leave['status_id'],
                        'leave_type_id' => $leaveId,
                        'last_updater_id' => $userId,
                        'current_validator_level' => $leave['current_validator_level'],
                        'start_date' => $leave['start_date'],
                        'end_date' => $leave['end_date'],
                        'n' => (isset($leave['n']) ? $leave['n'] : null),
                        'n1' => (isset($leave['n1']) ? $leave['n1'] : null),
                        'duration' => $leave['duration'],
                        'comment' => (isset($leave['comment']) ? $leave['comment'] : null),
                    ]);
                    $leaveCreated->leaveDaysDistribution($userId);
                    $leaveCreated->saveQuietly();

                    if (isset($leave["justificatif"])) {
                        if (!Storage::exists("clients/{$client->getKey()}/images/demo/justificatif-".Str::lower(LeaveType::find($leaveId)->leave_code).".jpg")) {
                            Storage::put("clients/{$client->getKey()}/images/demo/justificatif-".Str::lower(LeaveType::find($leaveId)->leave_code).".jpg", Storage::disk('public_images')->get("demo/justificatif-".Str::lower(LeaveType::find($leaveId)->leave_code).".jpg"));
                        }
                        $leaveCreated->attachment_name = "justificatif-".Str::lower(LeaveType::find($leaveId)->leave_code).".jpg";
                        $leaveCreated->attachment_path = "clients/{$client->getKey()}/images/demo/justificatif-".Str::lower(LeaveType::find($leaveId)->leave_code).".jpg";
                        $leaveCreated->saveQuietly();
                    }
                    if (isset($leave['histories'])) {
                        foreach ($leave['histories'] as $history) {
                            History::createQuietly([
                                'leave_id' => $leaveCreated->getKey(),
                                'status_id' => $history['status_id'],
                                'user_id' => $history['user_id'],
                                'reason' => (isset($leave['reason']) ? $leave['reason'] : ''),
                            ]);
                        }
                    }
                }
            }
        }
    }
}
