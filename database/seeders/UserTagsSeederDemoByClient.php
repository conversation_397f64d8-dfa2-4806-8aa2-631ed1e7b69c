<?php

namespace Database\Seeders;

use App\Models\Api\Tag;
use App\Models\Api\User;
use Illuminate\Database\Seeder;

class UserTagsSeederDemoByClient extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $currentUser = (new DatabaseSeeder())->getUser();
        $client = $currentUser->client;

        $users = User::select('users.*')
            ->join('sites', function ($join) use ($client) {
            $join->on('sites.id', 'users.site_id')
                ->where('sites.client_id', $client->getKey());
        })->get();

        $tagsName = ["Alpha", "Omega", "Delta", "Gamma", "Epsilon"];

        foreach ($tagsName as $tagName) {
            $tags[] = Tag::create([
                'label' => $tagName,
                'user_id' => $currentUser->getKey()
            ]);
        }

        $tags = collect($tags);

        foreach ($users as $user) {
            switch ($user->firstname.' '.$user->lastname) {
                case 'Jade PREVOST':
                    $user->tags()->attach($tags->firstWhere('label', 'Alpha'));
                    break;
                case 'Simon LEFEBVRE':
                case 'Clara PERRIN':
                    $user->tags()->attach($tags->firstWhere('label', 'Omega'));
                    break;
                case 'Jean DUPONT':
                case 'Clément PETIT':
                case 'Nicolas RICHARD':
                    $user->tags()->attach($tags->firstWhere('label', 'Delta'));
                    break;
                case 'Julien BERNARD':
                    $user->tags()->attach($tags->firstWhere('label', 'Epsilon'));
                    break;
            }
        }
    }
}
