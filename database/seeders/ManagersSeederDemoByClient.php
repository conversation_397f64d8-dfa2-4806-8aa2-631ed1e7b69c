<?php

namespace Database\Seeders;

use App\Lib\Tools;
use App\Models\Api\Client;
use App\Models\Api\Manager;
use App\Models\Api\User;
use Database\Factories\Api\ManagerFactory;
use Illuminate\Database\Seeder;

class ManagersSeederDemoByClient extends Seeder {
	/**
	 * Run the database seeds.
	 *
	 * @return void
	 */
	public function run(){
        $currentUser = Tools::getCurrentUserWithUuid();
        $client = $currentUser->site->client;

		$users = User::select('users.*')
            ->join('sites', 'sites.id', 'users.site_id')
            ->where('sites.client_id', $client->getKey())
            ->get();

        foreach($users as $user){
            $managers = [];
            switch ($user->lastname){
                case 'Prevost':
                    $managers = User::whereIn('lastname', ['Lefebvre'])->where('site_id', $user->site_id)->get();
                    break;
                case 'Dubois':
                case 'Lopez':
                case 'Perrin':
                    $managers = User::whereIn('lastname', ['Prevost', 'Lefebvre'])->where('site_id', $user->site_id)->get();
                    break;
            }

            $i = 1;
            foreach ($managers as $manager) {
                if ($i === 1){
                    $user->manager_id = $manager->getKey();
                }
                Manager::factory()->create(['managed_id' => $user->getKey(), 'manager_id' => $manager->getKey(), 'level' => $i]);
                $i++;
            }
        }
    }
}
