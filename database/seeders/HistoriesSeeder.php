<?php

namespace Database\Seeders;

use App\Models\Api\History;
use App\Models\Api\Leave;
use App\Models\Api\Status;
use App\Models\Api\User;
use Exception;
use Illuminate\Database\Seeder;

class HistoriesSeeder extends Seeder {
    private $secondIncrement;
    private function submittedHistories($leave){
        // Reset Increment
        $this->secondIncrement = 0;
        //Create History
        History::create([
            "leave_id" => $leave->id,
            "status_id" => Status::where('tag', '=', 'SUBMITTED')->first()->id,
            "user_id" => $leave->user_id,
            "reason" => "",
        ]);
        $currentLevel = $leave->current_validator_level;
        $firstManager = $leave->user->managers()->orderBy('level')->first();
        // If user has manager
        if (!is_null($firstManager)) {
            // If level is higher than first manager
            if ($currentLevel > $firstManager->level){
                //Create History for all Managers of user except the first
                for ($i = 1; $i < $currentLevel; $i++) {
                    //Get Manager level $i of User
                    $manager = User::find($leave->user_id)->managers()
                        ->where('level', '=', $i)->first();
                    if (!is_null($manager)) {
                        // Increment
                        $this->secondIncrement++;
                        //Create History
                        $history = History::create([
                            "leave_id" => $leave->id,
                            "status_id" => Status::where('tag', '=', 'VALIDATED')->first()->id,
                            "user_id" => $manager->manager_id,
                            "reason" => "",
                        ]);
                        $created_at = $history->created_at;
                        $history->created_at = $created_at->addSeconds($this->secondIncrement);
                        $history->updated_at = $history->created_at;
                        $history->save();
                    }
                }
            }
        }
    }

    private function validatedHistories($leave){
        // Increment
        $this->secondIncrement++;
        //Get last Manager of User
        $lastManager = (User::find($leave->user_id)->managers()
                ->orderBy('level', 'desc')->first()) ?? $leave->user;
        //Create History
        $history = History::create([
            "leave_id" => $leave->id,
            "status_id" => Status::where('tag', '=', 'VALIDATED')->first()->id,
            "user_id" => $lastManager->id,
            "reason" => "",
        ]);
        $created_at = $history->created_at;
        $history->created_at = $created_at->addSeconds($this->secondIncrement);
        $history->updated_at = $history->created_at;
        $history->save();
    }
    /**
     * Run the database seeds.
     *
     * @return void
     * @throws Exception
     */
	public function run()
    {
        $leaves = Leave::all();

        foreach($leaves as $leave){
            switch (Status::find($leave->status_id)->tag){
                case 'TRANSMITTED':
                    $this->submittedHistories($leave);
                    $this->validatedHistories($leave);
                    // Increment
                    $this->secondIncrement++;
                    //Get Admin ID of User
                    $admin_id = (User::where('site_id', '=', User::find($leave->user_id)->site_id)
                        ->where('profile_id', '=', 3)->first()->id) ?? $leave->user_id;
                    //Create History
                    $history = History::create([
                        "leave_id" => $leave->id,
                        "status_id" => $leave->status_id,
                        "user_id" => $admin_id,
                        "reason" => "",
                    ]);
                    $created_at = $history->created_at;
                    $history->created_at = $created_at->addSeconds($this->secondIncrement);
                    $history->updated_at = $history->created_at;
                    $history->save();
                    break;
                case 'VALIDATED':
                    $this->submittedHistories($leave);
                    $this->validatedHistories($leave);
                    break;
                case 'SUBMITTED':
                    $this->submittedHistories($leave);
                    break;
                case 'CANCELED':
                    $this->secondIncrement++;
                    $manager_id = (User::find($leave->user_id)->managers()
                        ->orderBy('level', 'asc')->first()->id) ?? $leave->user_id;
                    $history = History::create([
                        "leave_id" => $leave->id,
                        "status_id" => $leave->status_id,
                        "user_id" => $manager_id,
                        "reason" => "Demande annulée.",
                    ]);
                    $created_at = $history->created_at;
                    $history->created_at = $created_at->addSeconds($this->secondIncrement);
                    $history->updated_at = $history->created_at;
                    $history->save();
                    break;
                case 'REFUSED':
                    $this->submittedHistories($leave);
                    // Increment
                    $this->secondIncrement++;
                    //Get Manager ID of User
                    $manager_id = (User::find($leave->user_id)->managers()
                            ->where('level', '<=', rand(1,3))
                            ->orderBy('level', 'asc')->first()->id) ?? $leave->user_id;
                    //Create History
                    $history = History::create([
                        "leave_id" => $leave->id,
                        "status_id" => $leave->status_id,
                        "user_id" => $manager_id,
                        "reason" => "Demande non autorisée.",
                    ]);
                    $created_at = $history->created_at;
                    $history->created_at = $created_at->addSeconds($this->secondIncrement);
                    $history->updated_at = $history->created_at;
                    $history->save();
                    break;
                case 'SUBMITTED_TO_CANCELLATION':
                    $this->submittedHistories($leave);
                    $this->validatedHistories($leave);
                    $this->secondIncrement++;
                    $manager_id = (User::find($leave->user_id)->managers()
                        ->orderBy('level', 'asc')->first()->id) ?? $leave->user_id;
                    $history = History::create([
                        "leave_id" => $leave->id,
                        "status_id" => $leave->status_id,
                        "user_id" => $manager_id,
                        "reason" => "En demande d'annulation.",
                    ]);
                    $created_at = $history->created_at;
                    $history->created_at = $created_at->addSeconds($this->secondIncrement);
                    $history->updated_at = $history->created_at;
                    $history->save();
                    break;
                default:
                    throw new Exception('Unexpected value');
            }
        }

    }
}
