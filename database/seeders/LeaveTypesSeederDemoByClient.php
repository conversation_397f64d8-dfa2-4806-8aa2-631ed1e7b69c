<?php

namespace Database\Seeders;

use App\Lib\Tools;
use App\Models\Api\LeaveType;
use App\Models\Api\LeaveTypeSubFamily;
use Illuminate\Database\Seeder;

class LeaveTypesSeederDemoByClient extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $currentUser = (new DatabaseSeeder())->getUser();
        $client = $currentUser->client;
        $siteExpertisParis = $client->sites()->firstWhere('name', 'Expertis Paris');

        if ($client->leave_types->count() > 0) {
            $client->leave_types->each(function ($leaveType) {
                $leaveType->user_leave_counts()->forceDelete();
            });

            $client->leave_types()->forceDelete();
        }

        //Types de congés par clients
        LeaveType::create([
            'client_id' => $client->getKey(),
            'name' => 'Congés payés',
            'default_leave_value' => null,
            'is_active' => 1,
            'is_monthly' => 1,
            'is_pay' => 1,
            'is_deletable' => 0,
            'is_half_day' => 0,
            'is_attachment_required' => 0,
            'can_exceed' => 1,
            'leave_code' => 'CP',
            'color' => '#ff0000',
            'needs_count' => 1,
            'order_appearance' => 1,
            'can_justify_later' => 0,
            'site_id' => null,
            'leave_type_category_id' => 1,
        ]);

        LeaveType::create([
            'client_id' => $client->getKey(),
            'name' => 'Congés de mobilité',
            'default_leave_value' => 3,
            'is_active' => 1,
            'is_monthly' => 1,
            'is_pay' => 0,
            'is_deletable' => 0,
            'is_half_day' => 1,
            'is_attachment_required' => 0,
            'can_exceed' => 1,
            'leave_code' => 'CGM',
            'color' => '#00d103',
            'needs_count' => 1,
            'order_appearance' => 1,
            'can_justify_later' => 0,
            'is_auto_increment_active' => 1,
            'site_id' => null,
            'leave_type_category_id' => 1,
        ]);

        $congesCFR = LeaveType::create([
            'client_id' => $client->getKey(),
            'name' => 'Congés formation rémunérée',
            'default_leave_value' => null,
            'is_active' => 1,
            'is_monthly' => 0,
            'is_pay' => 0,
            'is_deletable' => 0,
            'is_half_day' => 1,
            'is_attachment_required' => 0,
            'can_exceed' => 0,
            'leave_code' => 'CFR',
            'color' => '#df3dff',
            'needs_count' => 1,
            'order_appearance' => 3,
            'can_justify_later' => 1,
            'site_id' => null,
            'leave_type_category_id' => 1,
        ]);

        LeaveType::create([
            'client_id' => $client->getKey(),
            'name' => 'Repos compensatoire obligatoire',
            'default_leave_value' => 3,
            'is_active' => 1,
            'is_monthly' => 0,
            'is_pay' => 0,
            'is_deletable' => 0,
            'is_half_day' => 0,
            'is_attachment_required' => 0,
            'can_exceed' => 1,
            'leave_code' => 'RCO',
            'color' => '#0028f0',
            'needs_count' => 1,
            'order_appearance' => 4,
            'can_justify_later' => 0,
            'site_id' => null,
            'leave_type_category_id' => 1,
        ]);

        LeaveType::create([
            'client_id' => $client->getKey(),
            'name' => 'Congés parternités',
            'default_leave_value' => 25,
            'is_active' => 1,
            'is_monthly' => 0,
            'is_pay' => 0,
            'is_deletable' => 0,
            'is_half_day' => 0,
            'is_attachment_required' => 1,
            'can_exceed' => 0,
            'leave_code' => 'CPATER',
            'color' => '#1b0de3',
            'needs_count' => 0,
            'order_appearance' => 5,
            'can_justify_later' => 0,
            'site_id' => null,
            'leave_type_category_id' => 1,
        ]);

        LeaveType::create([
            'client_id' => $client->getKey(),
            'name' => 'Maladie non professionnelle',
            'default_leave_value' => null,
            'is_active' => 1,
            'is_monthly' => 0,
            'is_pay' => 0,
            'is_deletable' => 0,
            'is_half_day' => 0,
            'is_attachment_required' => 0,
            'can_exceed' => 0,
            'leave_code' => 'MNP',
            'color' => '#ebd700',
            'needs_count' => 0,
            'order_appearance' => 1,
            'can_justify_later' => 0,
            'site_id' => null,
            'leave_type_category_id' => 2,
        ]);

        LeaveType::create([
            'client_id' => $client->getKey(),
            'name' => 'Accident du travail',
            'default_leave_value' => null,
            'is_active' => 1,
            'is_monthly' => 0,
            'is_pay' => 0,
            'is_deletable' => 0,
            'is_half_day' => 0,
            'is_attachment_required' => 0,
            'can_exceed' => 0,
            'leave_code' => '120',
            'color' => '#ff6666',
            'needs_count' => 0,
            'order_appearance' => 2,
            'can_justify_later' => 0,
            'site_id' => null,
            'leave_type_category_id' => 2,
        ]);

        //Types de congés par sites
        LeaveType::create([
            'client_id' => $client->getKey(),
            'name' => 'Congés payés',
            'default_leave_value' => null,
            'is_active' => 1,
            'is_monthly' => 1,
            'is_pay' => 1,
            'is_deletable' => 0,
            'is_half_day' => 0,
            'is_attachment_required' => 0,
            'can_exceed' => 1,
            'leave_code' => 'CP',
            'color' => '#ff0000',
            'needs_count' => 1,
            'order_appearance' => 1,
            'can_justify_later' => 0,
            'site_id' => $siteExpertisParis->getKey(),
            'leave_type_category_id' => 1,
        ]);

        LeaveType::create([
            'client_id' => $client->getKey(),
            'name' => 'Congés de mobilité',
            'default_leave_value' => 3,
            'is_active' => 1,
            'is_monthly' => 1,
            'is_pay' => 0,
            'is_deletable' => 0,
            'is_half_day' => 1,
            'is_attachment_required' => 0,
            'can_exceed' => 1,
            'leave_code' => 'CGM',
            'color' => '#00d103',
            'needs_count' => 1,
            'order_appearance' => 1,
            'can_justify_later' => 0,
            'is_auto_increment_active' => 1,
            'site_id' => $siteExpertisParis->getKey(),
            'leave_type_category_id' => 1,
        ]);

        LeaveType::create([
            'client_id' => $client->getKey(),
            'name' => 'RTT',
            'default_leave_value' => null,
            'is_active' => 1,
            'is_monthly' => 0,
            'is_pay' => 0,
            'is_deletable' => 0,
            'is_half_day' => 0,
            'is_attachment_required' => 0,
            'can_exceed' => 0,
            'leave_code' => 'RTT',
            'color' => '#FF5B7A5A',
            'needs_count' => 1,
            'order_appearance' => 3,
            'can_justify_later' => 0,
            'site_id' => $siteExpertisParis->getKey(),
            'leave_type_category_id' => 1,
        ]);

        LeaveType::create([
            'client_id' => $client->getKey(),
            'name' => 'Repos compensatoire obligatoire',
            'default_leave_value' => 3,
            'is_active' => 1,
            'is_monthly' => 0,
            'is_pay' => 0,
            'is_deletable' => 0,
            'is_half_day' => 0,
            'is_attachment_required' => 0,
            'can_exceed' => 1,
            'leave_code' => 'RCO',
            'color' => '#0028f0',
            'needs_count' => 1,
            'order_appearance' => 4,
            'can_justify_later' => 0,
            'site_id' => $siteExpertisParis->getKey(),
            'leave_type_category_id' => 1,
        ]);

        LeaveType::create([
            'client_id' => $client->getKey(),
            'name' => 'Congés parternités',
            'default_leave_value' => 25,
            'is_active' => 1,
            'is_monthly' => 0,
            'is_pay' => 0,
            'is_deletable' => 0,
            'is_half_day' => 0,
            'is_attachment_required' => 1,
            'can_exceed' => 0,
            'leave_code' => 'CPATER',
            'color' => '#FF5B7A5A',
            'needs_count' => 0,
            'order_appearance' => 5,
            'can_justify_later' => 0,
            'site_id' => $siteExpertisParis->getKey(),
            'leave_type_category_id' => 1,
        ]);

        LeaveType::create([
            'client_id' => $client->getKey(),
            'name' => 'Maladie non professionnelle',
            'default_leave_value' => null,
            'is_active' => 1,
            'is_monthly' => 0,
            'is_pay' => 0,
            'is_deletable' => 0,
            'is_half_day' => 0,
            'is_attachment_required' => 0,
            'can_exceed' => 0,
            'leave_code' => 'MNP',
            'color' => '#ebd700',
            'needs_count' => 0,
            'order_appearance' => 1,
            'can_justify_later' => 0,
            'site_id' => $siteExpertisParis->getKey(),
            'leave_type_category_id' => 2,
        ]);

        LeaveType::create([
            'client_id' => $client->getKey(),
            'name' => 'Accident du travail',
            'default_leave_value' => null,
            'is_active' => 1,
            'is_monthly' => 0,
            'is_pay' => 0,
            'is_deletable' => 0,
            'is_half_day' => 0,
            'is_attachment_required' => 0,
            'can_exceed' => 0,
            'leave_code' => '120',
            'color' => '#ff6666',
            'needs_count' => 0,
            'order_appearance' => 2,
            'can_justify_later' => 0,
            'site_id' => $siteExpertisParis->getKey(),
            'leave_type_category_id' => 2,
        ]);

        LeaveType::create([
            'client_id' => $client->getKey(),
            'name' => 'Mise à pied conservatoire',
            'default_leave_value' => null,
            'is_active' => 1,
            'is_monthly' => 0,
            'is_pay' => 0,
            'is_deletable' => 0,
            'is_half_day' => 0,
            'is_attachment_required' => 0,
            'can_exceed' => 0,
            'leave_code' => '641',
            'color' => '#d3a4f9',
            'needs_count' => 0,
            'order_appearance' => 3,
            'can_justify_later' => 0,
            'site_id' => $siteExpertisParis->getKey(),
            'leave_type_category_id' => 2,
        ]);

        LeaveTypeSubFamily::create([
            'name' => 'Ecole',
            'leave_type_id' => $congesCFR->getKey(),
            'value' => 4,
            'absence_reason' => 'CRFE'
        ]);

        LeaveTypeSubFamily::create([
            'name' => 'Formation',
            'leave_type_id' => $congesCFR->getKey(),
            'value' => 3,
            'absence_reason' => 'CFRF'
        ]);
    }
}
