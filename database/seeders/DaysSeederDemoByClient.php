<?php

namespace Database\Seeders;

use App\Models\Api\Day;
use App\Models\Api\Holiday;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class DaysSeederDemoByClient extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $currentUser = (new DatabaseSeeder())->getUser();
        $client = $currentUser->site->client;

        $client->days->each(function ($clientDay) use($client){
            $client->days()->detach($clientDay);
        });

        $siteExpertisParis = $client->sites()->firstWhere('name', 'Expertis Paris');

        $days = ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"];

        foreach ($days as $day_name){
            $day = Day::firstWhere('day_name', $day_name);

            $client->days()->attach($day);
            $siteExpertisParis->days()->attach($day);
        }

        $holidays = Holiday::where('year', '>=', Carbon::now()->subYear()->year)->get();
        foreach ($holidays as $holiday) {
            $client->holidays()->attach($holiday);
            $siteExpertisParis->holidays()->attach($holiday);
        }
    }
}
