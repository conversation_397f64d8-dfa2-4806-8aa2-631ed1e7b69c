<?php

namespace Database\Seeders;

use App\Models\Api\Profile;
use Illuminate\Database\Seeder;

class ProfilesSeeder extends Seeder {
	/**
	 * Run the database seeds.
	 *
	 * @return void
	 */
	public function run(){
		$profiles = [
			[
				"label" => "STANDARD"
			],
            [
                "label" => "STANDARDMANAGER"
            ],
			[
				"label" => "ADMINISTRATEUR"
			],
            [
            "label" => "ADMINISTRATEURMANAGER"
            ],
		];

		foreach($profiles as $profile){
			Profile::create($profile);
		}
	}
}
