<?php

namespace Database\Seeders;

use App\Models\Api\Profile;
use App\Models\Api\Route;
use App\Models\Api\RouteGroup;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RouteProfilesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $routesStandardManager = [
            "validation.LeavesController",
            "index.TeamsController",
            "team-count.UserLeaveCountsController",
            "export.UserLeaveCountsController",
        ];

        $routesAdministrateur = [
            "store.LeaveTypesController",
            "update.LeaveTypesController",
            "destroy.LeaveTypesController",

            "update.SitesController",

            "leave-date.ClientsController",
            "update-open-days.ClientsController",
            "update-count-public-holidays.ClientsController",
            "update.ClientsController",

            "index.TeamsController",

            "validation.LeavesController",
            "import.LeavesController",
            "export.LeavesController",
            "exportModel.LeavesController",

            "index.ExportHistoriesController",
            "downloadExport.ExportHistoriesController",
            "destroy.ExportHistoriesController",

            "export.UserLeaveCountsController",
            "import.UserLeaveCountsController",
            "update.UserLeaveCountsController",

            "store.DocumentationController",
            "update.DocumentationController",
            "destroy.DocumentationController",
        ];

        $routesAdministrateurManager = array_unique(array_merge($routesAdministrateur, $routesStandardManager));

        $routes = [];

        $profiles = Profile::all();
        foreach ($profiles as $profile) {
            switch ($profile->label) {
                case 'STANDARDMANAGER':
                    $routes = $routesStandardManager;
                    break;
                case 'ADMINISTRATEUR':
                    $routes = $routesAdministrateur;
                    break;
                case 'ADMINISTRATEURMANAGER':
                    $routes = $routesAdministrateurManager;
                    break;
            }

            // Attach the routes
            foreach ($routes as $routeName) {
                $route = Route::where('name', $routeName)->first();
                throw_if(empty($route), \Exception::class, 'Route ' . $routeName . ' not found.');
                $profile->routes()->attach($route);
            }
        }
    // associations de profile de route à un groupe
    $routes = Route::all();
    foreach($routes as $route){
            DB::table('route_group_route')->insert([
               'route_id' => $route->id,
                'route_group_id' => RouteGroup::inRandomOrder()->first()->getKey()
            ]);
    }
    }
}
