<?php


namespace Database\Seeders;


use App\Models\Api\Route;
use App\Models\Api\RouteGroup;
use Faker\Factory;
use Illuminate\Database\Seeder;

class RoutesSeeder extends Seeder {
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(){


        $routes = [
            ["name" => "store.LeaveTypesController", "type" => "leaveTypes"],
            ["name" => "update.LeaveTypesController", "type" => "leaveTypes"],
            ["name" => "destroy.LeaveTypesController", "type" => "leaveTypes"],

            ["name" => "update.SitesController", "type" => "sites"],

            ["name" => "leave-date.ClientsController", "type" => "clients"],
            ["name" => "update-open-days.ClientsController", "type" => "clients"],
            ["name" => "update-count-public-holidays.ClientsController", "type" => "clients"],
            ["name" => "update.ClientsController", "type" => "clients"],

            ["name" => "validation.LeavesController", "type" => "leaves"],
            ["name" => "import.LeavesController", "type" => "leaves"],
            ["name" => "export.LeavesController", "type" => "leaves"],
            ["name" => "exportModel.LeavesController", "type" => "leaves"],

            ["name" => "index.TeamsController", "type" => "teams"],

            ["name" => "count.UserLeaveCountsController", "type" => "userLeaveCounts"],
            ["name" => "update.UserLeaveCountsController", "type" => "userLeaveCounts"],
            ["name" => "team-count.UserLeaveCountsController", "type" => "userLeaveCounts"],
            ["name" => "export.UserLeaveCountsController", "type" => "userLeaveCounts"],
            ["name" => "import.UserLeaveCountsController", "type" => "userLeaveCounts"],

            ["name" => "index.ExportHistoriesController", "type" => "exportHistories"],
            ["name" => "downloadExport.ExportHistoriesController", "type" => "exportHistories"],
            ["name" => "destroy.ExportHistoriesController", "type" => "exportHistories"],
        ];

        foreach($routes as $route) {
            Route::create($route);
        }
        RouteGroup::factory()
            ->count(rand(3,10))
            ->create();
    }
}
