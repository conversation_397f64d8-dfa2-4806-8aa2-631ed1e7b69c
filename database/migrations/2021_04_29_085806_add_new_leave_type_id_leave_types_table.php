<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNewLeaveTypeIdLeaveTypesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('leave_types', function (Blueprint $table) {
            $table->unsignedBigInteger("new_leave_type_id")->nullable()
                ->comment("Est renseigné lors de l'archivage du leave_type, il fait le lien vers le leave_type qui le remplace.");

            $table->foreign("new_leave_type_id")->references("id")->on("leave_types");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('leave_types', function (Blueprint $table) {
            $table->removeColumn('new_leave_type_id');
        });
    }
}
