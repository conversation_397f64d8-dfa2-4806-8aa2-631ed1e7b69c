<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLeaveTypeSubFamiliesTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("leave_type_sub_families", function(Blueprint $table){
			$table->bigIncrements("id")->comment("Identifiant du sous-type de congé");
			$table->unsignedBigInteger("leave_type_id")->comment("Identifiant du type de congé");
			$table->text("name")->comment("Nom du sous-type de congé");
			$table->float("value")->comment("Durée du congé ayant ce sous-type");
			$table->softDeletes();
			$table->timestamps();

			$table->foreign("leave_type_id")->references("id")->on("leave_types")->onDelete("cascade");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("leave_type_sub_families");
	}
}
