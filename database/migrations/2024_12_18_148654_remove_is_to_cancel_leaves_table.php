<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Api\Leave;
use App\Models\Api\Status;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Leave::query()->where('is_to_cancel', 1)->where('status_id', '!=', Status::query()->where('tag', 'CANCELED')->first()?->getKey())->each(function (Leave $leave) {
            $leave->status_id = Status::query()->where('tag', 'SUBMITTED_TO_CANCELLATION')->first()?->getKey();
            $leave->saveQuietly();
        }, 50);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Leave::query()->where('is_to_cancel', 1)->where('status_id', '!=', Status::query()->where('tag', 'CANCELED')->first()?->getKey())->each(function (Leave $leave) {
            $leave->is_to_cancel = Status::query()->where('tag', 'SUBMITTED_TO_CANCELLATION')->first()?->getKey();
            $leave->saveQuietly();
        }, 50);
    }
};
