<?php

use App\Models\Api\Client;
use App\Models\Api\Holiday;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Client::query()
            ->whereNull('deleted_at')
            ->each(function (Client $client) {
                $site = $client->sites()->orderBy('created_at')->whereNull('deleted_at')->first();

                if ($site){
                    $relatedHolidays = Holiday::query()->where('country_code', $site->country_alpha)->get();

                    if ($relatedHolidays->isEmpty()) {
                        $relatedHolidays = Holiday::query()->where('country_code', 'FR')->get();
                    }

                    $client->holidays()->sync($relatedHolidays);
                }
            }, 10);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
