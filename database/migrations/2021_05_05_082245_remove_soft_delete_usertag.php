<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RemoveSoftDeleteUsertag extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('user_tags', 'deleted_at')) {
            \App\Models\Api\UserTag::where('deleted_at','!=',null)->forceDelete();
            Schema::table('user_tags', function (Blueprint $table) {
                $table->dropColumn('deleted_at');
            });
        };
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (!Schema::hasColumn('user_tags', 'deleted_at')) {
            Schema::table('user_tags', function (Blueprint $table) {
                $table->softDeletes();
            });
        };
    }
}
