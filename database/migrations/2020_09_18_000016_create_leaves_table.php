<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLeavesTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("leaves", function(Blueprint $table){
			$table->bigIncrements("id")->comment("Identifiant de la demande de congé");
			$table->unsignedBigInteger("user_id")->comment("Identifiant de l'utilisateur à qui appartient la demande");
			$table->unsignedBigInteger("status_id")->comment("Identifiant du status de la demande");
			$table->unsignedBigInteger("leave_type_id")->comment("Identifiant du type de congé");
			$table->unsignedBigInteger("leave_type_sub_family_id")->nullable()->comment("Identifiant du sous-type de congé s'il y en a un");
			$table->unsignedBigInteger("last_updater_id")->nullable()->comment("Identifiant du dernier utilisateur à avoir mis à jour la demande");
			$table->integer("current_validator_level")->default(1)->comment("Nombre indiquant le niveau de validation courant de la demande. Il est utilisé seulement dans un schéma de validation VERTICAL");
			$table->dateTime("start_date")->comment("Date indiquant le début du congé");
			$table->dateTime("end_date")->comment("Date indiquant la fin du congé");
            $table->double("n")->nullable()->comment("Nombre décrémenté au compteur de l'année courante");
            $table->double("n1")->nullable()->comment("Nombre décrémenté au compteur de l'année précédente. On décrémente ce compteur en priorité");
			$table->double("duration")->default(0)->comment("Durée du congé");
			$table->string("attachment_name")->comment("Nom du fichier rattaché à la demande si nécessaire");
			$table->string("attachment_path")->comment("Chemin du fichier rattaché à la demande si nécessaire");
			$table->text("comment")->nullable()->comment("Commentaire de la demande");
			$table->softDeletes();
			$table->timestamps();

			$table->foreign("user_id")->references("id")->on("users")->onDelete("cascade");
			$table->foreign("status_id")->references("id")->on("statuses")->onDelete("cascade");
			$table->foreign("leave_type_id")->references("id")->on("leave_types")->onDelete("cascade");
			$table->foreign("leave_type_sub_family_id")->references("id")->on("leave_type_sub_families")->onDelete("cascade");
			$table->foreign("last_updater_id")->references("id")->on("users")->onDelete("cascade");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("leaves");
	}
}
