<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRouteProfileTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("route_profile", function(Blueprint $table){
			$table->unsignedBigInteger("route_id");
			$table->unsignedInteger("profile_id");
			$table->softDeletes();
			$table->timestamps();

			$table->foreign("route_id")->references("id")->on("routes")->onDelete("cascade");
			$table->foreign("profile_id")->references("id")->on("profiles")->onDelete("cascade");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("route_profile");
	}
}
