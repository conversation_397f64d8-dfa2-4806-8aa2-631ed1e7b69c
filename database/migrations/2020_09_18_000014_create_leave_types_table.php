<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLeaveTypesTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		// seniority_parameters --> json
		// { sign: ">", years: 5, value: 1 }
		// { sign: ">", years: 10, value: 2 }

		Schema::create("leave_types", function(Blueprint $table){
			$table->bigIncrements("id")->comment("Identifiant du type de congé");
			$table->unsignedBigInteger("client_id")->comment("Identifiant du client");
            $table->unsignedBigInteger("last_update_id")->nullable()->comment("Identifiant du dernier utilisateur à avoir mis à jour le type de congé");
			$table->string("name")->comment("Nom du type de congé");
			$table->integer("default_leave_value")->nullable()->default(null)->comment("Si renseigné, ce nombre indique la valeur de la durée d'un congé ayant ce type");
			$table->boolean("is_active")->default(1)->comment("Booléen indiquant si les collaborateurs du client peuvent poser des demandes de congé avec ce type");
			$table->boolean("is_monthly")->comment("Booléen indiquant si les compteurs de ce type ont les champs acquired, taken et balance de remplis pour true sinon seulement le champ acquired sera rempli");
            $table->boolean("is_pay")->default(0)->comment("Booléen indiquant si le type de congé renseigne les congés payés. Ainsi, seulement un type de congé par client aura ce booléen à true");
			$table->boolean("is_deletable")->default(1)->comment("Booléen indiquant si le type de congé peut être supprimé par le client. Seulement les types de congé créés par le client peuvent l'être");
			$table->boolean("is_half_day")->default(0)->comment("Booléen indiquant si ce type de congé autorise les durées équivalentes à des demi-journées telles que 0.5, 2.5, etc...");
			$table->boolean("is_attachment_required")->default(0)->comment("Booléen indiquant si ce type de congé oblige l'attachement de justificatif (ATTACHMENT_NAME et ATTACHMENT_PATH) pour les demandes");
			$table->boolean("can_exceed")->default(0)->comment("Booléen indiquant si le type de congé autorise les utilisateurs à faire des demandes de congés d'une durée dépassant leur nombre acquis dans leurs compteurs");
			$table->string("leave_code")->comment("Champ renseignant le nom de code du type de congé, utilisé dans le logiciel de paie du client");
            $table->dateTime("start_date")->nullable()->comment("Si renseigné, la période d'une demande d'un congé ne pourra pas se dérouler avant cette date");
            $table->dateTime("end_date")->nullable()->comment("Si renseigné, la période d'une demande d'un congé ne pourra pas se dérouler après cette date");
			$table->string("color")->comment("Couleur au format héxadécimal affichée sur le front");
			$table->boolean("needs_count")->default(true)->comment("Booléen indiquant si le type de congé a besoin de compteurs pour les utilisateurs. Les types de congé créés par le client auront toujours ce champ à true");
			$table->unsignedInteger('order_appearance')->comment("Nombre indiquant la position à afficher sur le front, si égal à 0 alors ne pas afficher");
			$table->softDeletes();
			$table->timestamps();

            $table->foreign("last_update_id")->references("id")->on("users")->onDelete("cascade");
			$table->foreign("client_id")->references("id")->on("clients")->onDelete("cascade");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("leave_types");
	}
}
