<?php

use App\Models\Api\ClientType;
use Illuminate\Support\Str;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('client_types', function (Blueprint $table) {
            $table->id();
            $table->string('uuid');
            $table->string('label')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::table('clients', function (Blueprint $table) {
            $table->foreignIdFor(ClientType::class, 'client_type_id')
                ->after('name')
                ->nullable()
                ->constrained('client_types');
        });

        ClientType::create(['uuid' => '0416789f-f841-47c2-bc30-313a1100c73b', 'label' => 'agence_en_propre']);
        ClientType::create(['uuid' => 'a578ee00-c7c3-4e80-9a3e-329350cd9188', 'label' => 'franchise']);
        ClientType::create(['uuid' => '1e949ff8-5b17-4c28-8023-aea94aeb02ee', 'label' => 'client_final']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('client_types');
    }
};
