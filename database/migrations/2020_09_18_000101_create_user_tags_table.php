<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserTagsTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("user_tags", function(Blueprint $table){
			$table->unsignedBigInteger("tag_id");
			$table->unsignedBigInteger("user_id");
			$table->softDeletes();
			$table->timestamps();

            $table->foreign("tag_id")->references("id")->on("tags")->onDelete("cascade");
            $table->foreign("user_id")->references("id")->on("users")->onDelete("cascade");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("user_tags");
	}
}
