<?php


use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDocumentationSiteTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create("documentation_site", function(Blueprint $table){
            $table->unsignedBigInteger("documentation_id");
            $table->unsignedBigInteger("site_id");

            $table->foreign("documentation_id")->references("id")->on("documentations")->onDelete("cascade");
            $table->foreign("site_id")->references("id")->on("sites")->onDelete("cascade");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists("documentation_sites");
    }

}
