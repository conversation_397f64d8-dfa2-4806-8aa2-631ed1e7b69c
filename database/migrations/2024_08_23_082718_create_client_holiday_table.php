<?php

use App\Models\Api\Client;
use App\Models\Api\Holiday;
use App\Models\Api\Site;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('client_holiday', function (Blueprint $table) {
            $table->foreignIdFor(Client::class);
            $table->foreignIdFor(Holiday::class);

            $table->primary(['client_id', 'holiday_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('client_holiday');
    }
};
