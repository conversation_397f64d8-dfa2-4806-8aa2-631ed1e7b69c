<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddNameAndModifyEnumToConnectionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("ALTER TABLE connections MODIFY type ENUM('AD', 'CRM', 'SAGE', 'EBP', 'IDYLIS', 'Baptigest') DEFAULT 'AD'");

        Schema::table('connections', function (Blueprint $table) {
            $table->string("name");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement("ALTER TABLE connections MODIFY type ENUM('AD', 'CRM')");
        Schema::table('connections', function (Blueprint $table) {
            $table->dropColumn("name");
        });
    }
}
