<?php

use App\Models\Api\Holiday;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Http;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $availableCountryCodes = ['FR', 'GB', 'DE', 'ES', 'IT', 'NL'];

        foreach ($availableCountryCodes as $countryCode) {
            $period = CarbonPeriod::create('2021-01-01', '1 year', '2030-01-01');

            $period->forEach(function (Carbon $date) use ($countryCode) {
                $publicHolidays = Http::publicHolidays()->get("/PublicHolidays/$date->year/$countryCode")->json();

                foreach ($publicHolidays as $publicHoliday) {
                    Holiday::query()
                        ->create([
                            'year' => $date->year,
                            'name' => $publicHoliday['localName'],
                            'country_code' => $publicHoliday['countryCode'],
                            'date' => $publicHoliday['date'],
                        ]);
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
