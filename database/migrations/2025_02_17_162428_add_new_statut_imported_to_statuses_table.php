<?php

use Illuminate\Database\Migrations\Migration;
use App\Models\Api\Status;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('statuses', function (Blueprint $table) {
            $table->enum('tag', ['VALIDATED', 'SUBMITTED', 'REFUSED', 'CANCELED', 'TRANSMITTED', 'SUBMITTED_TO_CANCELLATION', 'IMPORTED'])->change();
        });

        Status::create([
            'tag' => "IMPORTED",
            'name' => "Importé",
            'color' => "0xFF997326"
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('statuses', function (Blueprint $table) {
            $table->enum('tag', ['VALIDATED', 'SUBMITTED', 'REFUSED', 'CANCELED', 'TRANSMITTED', 'SUBMITTED_TO_CANCELLATION', 'IMPORTED'])->change();
        });

        Status::firstWhere('tag', 'IMPORTED')->delete();
    }
};
