<?php

use App\Lib\Tools;
use App\Models\Api\Client;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateManagerManagedUserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('manager_managed_user', function (Blueprint $table) {
            $table->primary(['manager_id', 'managed_id']);

            $table->unsignedBigInteger("manager_id");
            $table->unsignedBigInteger("managed_id");

            $table->foreign("manager_id")->references("id")->on("users");
            $table->foreign("managed_id")->references("id")->on("users");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('manager_managed_user');
    }
}
