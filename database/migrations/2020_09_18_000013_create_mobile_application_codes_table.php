<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMobileApplicationCodesTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("mobile_application_codes", function(Blueprint $table){
			$table->bigIncrements("id");
			$table->string("activation_code");
			$table->string("order_number");
			$table->string("user_uuid");
			$table->boolean("is_locked");
			$table->boolean("is_used");
			$table->dateTime("linked_at");
			$table->softDeletes();
			$table->timestamps();

		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("mobile_application_codes");
	}
}
