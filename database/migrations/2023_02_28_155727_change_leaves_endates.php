<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeLeavesEndates extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $leavesBeforeMEP = \App\Models\Api\Leave::where(function ($query){
                $query->where('start_date', '>=', \Carbon\Carbon::create(2022))
                    ->orWhere('end_date', '>=', \Carbon\Carbon::create(2022));
            })
            ->where('end_date', 'like', '%00:00:00')
            ->where('created_at', '<', \Carbon\Carbon::create(2023,02,24,17,40))
            ->get();

        $leavesAfterMEP = \App\Models\Api\Leave::where('end_date', 'like', '%00:00:00')
            ->where('created_at', '>', \Carbon\Carbon::create(2023,02,24,17,40))
            ->get();

        $leavesBeforeMEP->each(function ($leaveBeforeMEP){
           $leaveBeforeMEP->end_date = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $leaveBeforeMEP->end_date)->subMinute();
           $leaveBeforeMEP->save();
        });

        $leavesAfterMEP->each(function ($leaveAfterMEP){
            $leaveAfterMEP->end_date = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $leaveAfterMEP->end_date)->addHours(23)->addMinutes(59)->addSeconds(59);
            $leaveAfterMEP->save();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        $leavesBeforeMEP = \App\Models\Api\Leave::where(function ($query){
            $query->where('start_date', '>=', \Carbon\Carbon::create(2022))
                ->orWhere('end_date', '>=', \Carbon\Carbon::create(2022));
        })
            ->where('end_date', 'like', '%00:00:00')
            ->where('created_at', '<', \Carbon\Carbon::create(2023,02,24,17,40))
            ->get();

        $leavesAfterMEP = \App\Models\Api\Leave::where('end_date', 'like', '%00:00:00')
            ->where('created_at', '>', \Carbon\Carbon::create(2023,02,24,17,40))
            ->get();

        $leavesBeforeMEP->each(function ($leaveBeforeMEP){
            $leaveBeforeMEP->end_date = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $leaveBeforeMEP->end_date)->addMinute();
            $leaveBeforeMEP->save();
        });

        $leavesAfterMEP->each(function ($leaveAfterMEP){
            $leaveAfterMEP->end_date = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $leaveAfterMEP->end_date)->subHours(23)->subMinutes(59)->subSeconds(59);
            $leaveAfterMEP->save();
        });
    }
}
