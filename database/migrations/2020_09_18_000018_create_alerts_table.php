<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAlertsTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("alerts", function(Blueprint $table){
			$table->bigIncrements("id");
			$table->unsignedBigInteger("leave_id");
			$table->unsignedBigInteger("status_id");
			$table->unsignedBigInteger("user_id");
			$table->unsignedBigInteger("user_leave_id");
			$table->string("title");
			$table->text("body");
			$table->enum("status", ["READ", "UNREAD", "ARCHIVED"]);
			$table->softDeletes();
			$table->timestamps();

			$table->foreign("leave_id")->references("id")->on("leaves")->onDelete("cascade");
			$table->foreign("status_id")->references("id")->on("statuses")->onDelete("cascade");
			$table->foreign("user_id")->references("id")->on("users")->onDelete("cascade");
			$table->foreign("user_leave_id")->references("id")->on("users")->onDelete("cascade");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("alerts");
	}
}
