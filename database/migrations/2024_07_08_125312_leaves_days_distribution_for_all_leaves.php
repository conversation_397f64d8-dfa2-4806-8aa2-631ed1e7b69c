<?php

use App\Models\Api\Leave;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        ini_set("memory_limit", "-1");
        set_time_limit(0);

        Leave::query()
            ->select('leaves.*')
            ->join('users', 'users.id', 'leaves.user_id')
            ->join('sites', 'sites.id', 'users.site_id')
            ->join('clients', 'clients.id', 'sites.client_id')
            ->whereNull('users.deleted_at')
            ->each(function (Leave $leave) {
                $leave->leaveDaysDistribution($leave->user_id);
                $leave->save();
            }, 15);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
