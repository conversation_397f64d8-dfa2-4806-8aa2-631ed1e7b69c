<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserLeaveCountsTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("user_leave_counts", function(Blueprint $table){
			$table->bigIncrements("id")->comment("Identifiant du compteur");
			$table->unsignedBigInteger("user_id")->comment("Identifiant de l'utilisateur à qui appartient le compteur");
			$table->unsignedBigInteger("leave_type_id")->comment("Identifiant du type de congé");
			$table->float("acquired")->default(0)->comment("Nombre renseignant le nombre de jour acquis pour ce type de congé");
			$table->float("taken")->default(0)->comment("Nombre renseignant le nombre de jour pris pour ce type de congé");
			$table->float("balance")->default(0)->comment("Nombre renseignant le nombre de jour restant pour ce type de congé");
			$table->boolean("is_last_year")->default(0)->comment("True = N-1 / False = N");
			$table->softDeletes();
			$table->timestamps();

			$table->foreign("user_id")->references("id")->on("users")->onDelete("cascade");
			$table->foreign("leave_type_id")->references("id")->on("leave_types")->onDelete("cascade");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("user_leave_counts");
	}
}
