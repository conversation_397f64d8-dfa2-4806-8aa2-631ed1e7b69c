<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateClientDaysTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('client_days', function (Blueprint $table) {
            $table->unsignedBigInteger("client_id");
            $table->unsignedBigInteger("day_id");

            $table->foreign("client_id")->references("id")->on("clients")->onDelete("cascade");
            $table->foreign("day_id")->references("id")->on("days")->onDelete("cascade");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('client_days');
    }
}
