<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLookupFieldsTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("lookup_fields", function(Blueprint $table){
			$table->bigIncrements("id");
			$table->unsignedBigInteger("section_id");
			$table->string("foreign_entity");
			$table->string("tag");
			$table->string("label");
			$table->string("id_field");
			$table->string("display_field");
			$table->integer("index");
			$table->boolean("is_nullable");
			$table->softDeletes();
			$table->timestamps();

			$table->foreign("section_id")->references("id")->on("sections")->onDelete("cascade");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("lookup_fields");
	}
}
