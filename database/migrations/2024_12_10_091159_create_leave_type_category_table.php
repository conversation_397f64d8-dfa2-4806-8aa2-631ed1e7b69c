<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('leave_type_categories', function (Blueprint $table) {
            $table->id();
            $table->string('label');
            $table->string('slug');
            $table->timestamps();
        });
        DB::table('leave_type_categories')->insert([
            ['id' => 1, 'label' => 'Congés', 'slug' => 'cg', 'created_at' => now(), 'updated_at' => now()],
            ['id' => 2, 'label' => 'Absence', 'slug' => 'abs', 'created_at' => now(), 'updated_at' => now()]
        ]);

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('leave_type_categories');
    }
};
