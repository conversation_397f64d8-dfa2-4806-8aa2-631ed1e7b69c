<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTagsTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("tags", function(Blueprint $table){
			$table->bigIncrements("id");
			$table->string("label");
			$table->unsignedBigInteger("user_id");
			$table->softDeletes();
			$table->timestamps();

            $table->foreign("user_id")->references("id")->on("users")->onDelete("cascade");
		});
	}

    /**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("tags");
	}
}
