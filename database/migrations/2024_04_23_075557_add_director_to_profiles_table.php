<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('profiles', function (Blueprint $table) {
            $table->enum('label', ['STANDARD', 'ADMINISTRATEUR', 'STANDARDMANAGER', 'ADMINISTRATEURMANAGER', 'DIRECTOR'])->change();
        });

        \App\Models\Api\Profile::create(['label' => 'DIRECTOR']);

        Schema::create('director_user', function (Blueprint $table) {
            $table->foreignId('director_id')->constrained('users');
            $table->foreignId('user_id')->constrained('users');

            $table->primary(['director_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        \App\Models\Api\Profile::where('label', 'DIRECTOR')->first()->delete();
        Schema::dropIfExists('director_user');
    }
};
