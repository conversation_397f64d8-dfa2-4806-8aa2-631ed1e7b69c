<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProfileRouteGroupTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("profile_route_group", function(Blueprint $table){
			$table->unsignedInteger("profile_id");
			$table->unsignedBigInteger("route_group_id");
			$table->softDeletes();
			$table->timestamps();

			$table->foreign("profile_id")->references("id")->on("profiles")->onDelete("cascade");
			$table->foreign("route_group_id")->references("id")->on("route_groups")->onDelete("cascade");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("profile_route_group");
	}
}
