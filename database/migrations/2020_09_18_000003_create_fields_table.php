<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFieldsTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("fields", function(Blueprint $table){
			$table->bigIncrements("id");
			$table->unsignedBigInteger("section_id");
			$table->string("tag");
			$table->string("label");
			$table->string("field");
			$table->string("type");
			$table->integer("index");
			$table->boolean("is_nullable");
			$table->boolean("is_default");
			$table->softDeletes();
			$table->timestamps();

			$table->foreign("section_id")->references("id")->on("sections")->onDelete("cascade");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("fields");
	}
}
