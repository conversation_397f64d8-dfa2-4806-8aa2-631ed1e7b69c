<?php

use App\Models\Api\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ReworkManagersForNewPortal extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->foreignIdFor(\App\Models\Api\User::class, 'manager_id')->nullable()->after('site_id');
        });

        Schema::table('managers', function (Blueprint $table) {
            $table->renameColumn('user_id', 'managed_id');
            $table->unsignedInteger('level')->change();
        });

//        (new CreateManagerManagedUserTable())->down();

        Schema::table('clients', function (Blueprint $table) {
            $table->unsignedInteger('number_managers_can_validate')->default(0);
        });

        User::all()->each(function ($user) {
            $userDirectManager = $user->managers()->where('level', 1)->first();
            $user->manager_id = $userDirectManager ? $userDirectManager->getKey() : null;
            $user->save();
        });

        User::all()->each(function ($user) {
            $managerList = [];
            $this->cleanDirectManagers($user, $managerList);
        });

        User::all()->each(function ($user) {
            \App\Jobs\StructureManagementJob::dispatch($user);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('manager_id');
        });

        Schema::table('managers', function (Blueprint $table) {
            $table->renameColumn('managed_id', 'user_id');
            $table->integer('level')->change();
        });

//        (new CreateManagerManagedUserTable())->up();

        Schema::table('clients', function (Blueprint $table) {
            $table->dropColumn('number_managers_can_validate');
        });
    }

    private function cleanDirectManagers(User $user, array &$managerList)
    {
        if ($user->directManager) {
            if (User::find($user->getKey())->manager_id) {
                $managerList[] = $user->getkey();
            }

            if(in_array($user->manager_id, $managerList)) {
                $user->manager_id = null;
                $user->save();
                $user->managers()->detach($user->manager_id);
                $managerList = [];
                return;
            }

            $this->cleanDirectManagers($user->directManager, $managerList);
        }
    }
}
