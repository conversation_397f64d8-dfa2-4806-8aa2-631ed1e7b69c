<?php

use Illuminate\Database\Migrations\Migration;

class AddSubjectToCancellationToStatusesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        \App\Models\Api\Status::create([
            'tag' => 'SUBMITTED_TO_CANCELLATION',
            'name' => 'Soumis à annulation',
            'color' => '0xFFFF8A00',
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        \App\Models\Api\Status::firstWhere('tag', 'SUBMITTED_TO_CANCELLATION')->delete();
    }
}
