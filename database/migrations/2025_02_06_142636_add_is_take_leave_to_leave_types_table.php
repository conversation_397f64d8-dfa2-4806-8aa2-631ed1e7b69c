<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leave_types', function (Blueprint $table) {
            $table->boolean('is_take_leave')->default(true)->comment('Détermine si la pose de congé/absence est possible pour ce leave_type.');
        });

        \App\Models\Api\LeaveType::all()->each(function ($leaveType) {
           if (!$leaveType->is_active) {
               $leaveType->is_take_leave = false;
               $leaveType->save();
           }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leave_types', function (Blueprint $table) {
            $table->dropColumn('is_take_leave');
        });
    }
};
