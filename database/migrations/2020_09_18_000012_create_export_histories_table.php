<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateExportHistoriesTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("export_histories", function(Blueprint $table){
			$table->bigIncrements("id");
			$table->unsignedBigInteger("user_id");
			$table->string("file_name");
			$table->string("extension");
			$table->enum('type', ["EXCEL", "EBP", "SILAE", "SAGE"]);
			$table->softDeletes();
			$table->timestamps();

			$table->foreign("user_id")->references("id")->on("users")->onDelete("cascade");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("export_histories");
	}
}
