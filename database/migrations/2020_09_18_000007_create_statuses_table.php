<?php

use App\Models\Api\Status;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStatusesTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("statuses", function(Blueprint $table){
			$table->bigIncrements("id");
			$table->enum("tag", ["DRAFT", "VALIDATED", "SUBMITTED", "CORRECTED", "REFUSED", "CANCELED", "TRANSMITTED"]);
			$table->string("name")->nullable();
			$table->string("color")->nullable();
			$table->string("class")->nullable();
			$table->softDeletes();
			$table->timestamps();
		});

        $statuses = [
            [
                "tag" => "VALIDATED",
                "name" => "Validé",
                "color" => "0xFF9DD400"
            ],
            [
                "tag" => "SUBMITTED",
                "name" => "Soumis à validation",
                "color" => "0xFFFFBE0B"
            ],
            [
                "tag" => "REFUSED",
                "name" => "Refusée",
                "color" => "0xFFCD0309"
            ],
            [
                "tag" => "CANCELED",
                "name" => "Annulé",
                "color" => "0xFF868793"
            ],
            [
                "tag" => "TRANSMITTED",
                "name" => "Transmis en paie",
                "color" => "0xFF5FAEDB"
            ]
        ];

        foreach ($statuses as $status) {
            Status::create($status);
        }
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("statuses");
	}
}
