<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateJobsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('jobs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('queue')->index();
            $table->text('file_name');
            $table->text('file_path');
            $table->integer('attempts')->default(0);
            $table->integer('max_attempts')->default(1);
            $table->bigInteger('reserved')->nullable();
            $table->boolean('done')->default(false);
            $table->boolean('avoid_errors')->default(false);
            $table->unsignedBigInteger('client_id');
            $table->string('log_file_name')->nullable();

            $table->foreign("client_id")->references("id")->on("clients")->onDelete("cascade");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('jobs');
    }
}
