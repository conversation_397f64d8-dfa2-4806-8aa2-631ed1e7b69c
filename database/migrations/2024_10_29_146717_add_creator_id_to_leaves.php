<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leaves', function (Blueprint $table) {
            $table->foreignId('creator_id')->nullable()->after('user_id')->constrained('users');
        });

        \App\Models\Api\Leave::query()->each(function (\App\Models\Api\Leave $leave) {
            $leave->creator_id = $leave->user_id;
            $leave->saveQuietly();
        }, 50);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leaves', function (Blueprint $table) {
            $table->dropForeign('leaves_creator_id_foreign');
            $table->dropColumn('creator_id');
        });

        \App\Models\Api\Leave::query()->each(function (\App\Models\Api\Leave $leave) {
            $leave->creator_id = null;
            $leave->saveQuietly();
        }, 50);
    }
};
