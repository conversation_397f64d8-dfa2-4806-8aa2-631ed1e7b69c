<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEntitiesTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("entities", function(Blueprint $table){
			$table->bigIncrements("id");
			$table->unsignedBigInteger("client_id");
			$table->string("tag");
			$table->string("name");
			$table->string("entity");
			$table->string("id_field");
			$table->string("id_type");
			$table->string("commercial_field");
			$table->string("commercial_entity");
			$table->boolean("hidden");
			$table->boolean("locked");
			$table->boolean("is_hidden_annotation");
			$table->softDeletes();
			$table->timestamps();

			$table->foreign("client_id")->references("id")->on("clients")->onDelete("cascade");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("entities");
	}
}
