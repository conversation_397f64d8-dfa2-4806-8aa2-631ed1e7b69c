<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRouteGroupRouteTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("route_group_route", function(Blueprint $table){
			$table->unsignedBigInteger("route_id");
			$table->unsignedBigInteger("route_group_id");
			$table->softDeletes();
			$table->timestamps();

			$table->foreign("route_id")->references("id")->on("routes")->onDelete("cascade");
			$table->foreign("route_group_id")->references("id")->on("route_groups")->onDelete("cascade");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("route_group_route");
	}
}
