<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateManagersTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("managers", function(Blueprint $table){
			$table->unsignedBigInteger("user_id");
			$table->unsignedBigInteger("manager_id");
			$table->integer("level");
			$table->timestamps();

			$table->foreign("user_id")->references("id")->on("users")->onDelete("cascade");
			$table->foreign("manager_id")->references("id")->on("users")->onDelete("cascade");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("managers");
	}
}
