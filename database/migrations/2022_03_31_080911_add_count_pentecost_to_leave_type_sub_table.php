<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCountPentecostToLeaveTypeSubTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // création de la colonne count_pentecost dans la table leave_type_sub
        Schema::table('clients', function (Blueprint $table) {
            $table->boolean('is_pentecost')->default(false);
        });

        // récupération des profil admin et admin_manager
        $admin = \App\Models\Api\Profile::where('label', 'ADMINISTRATEUR')->first();
        $adminManager = \App\Models\Api\Profile::where('label', 'ADMINISTRATEURMANAGER')->first();

        // création de la route
        $route = \App\Models\Api\Route::query()->create([
            'name' => 'update-count-pentecost.ClientsController',
            'type' => 'clients'
        ]);
        if ($admin instanceof \App\Models\Api\Profile) {
            $admin->routes()->attach($route);
        }
        if ($adminManager instanceof \App\Models\Api\Profile) {
            $adminManager->routes()->attach($route);
        }
        $route->save();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('clients', function (Blueprint $table) {
            $table->dropColumn('is_pentecost');
        });
        \App\Models\Api\Route::where('name', 'update-count-pentecost.ClientsController')->delete();
    }
}
