<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSitesTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("sites", function(Blueprint $table){
			$table->bigIncrements("id");
			$table->unsignedBigInteger("client_id");
			$table->unsignedBigInteger("site_database_id")->nullable();
			$table->string("name");
			$table->string("country");
			$table->string("country_alpha", 2);
			$table->string("subdivision")->nullable();
			$table->string("subdivision_code")->nullable();
			$table->softDeletes();
			$table->timestamps();

            $table->foreign("client_id")->references("id")->on("clients")->onDelete("cascade");
            $table->foreign("site_database_id")->references("id")->on("site_databases")->onDelete("cascade");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("sites");
	}
}
