<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSectionsTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("sections", function(Blueprint $table){
			$table->bigIncrements("id");
			$table->unsignedBigInteger("entity_id");
			$table->string("label");
			$table->softDeletes();
			$table->timestamps();

			$table->foreign("entity_id")->references("id")->on("entities")->onDelete("cascade");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("sections");
	}
}
