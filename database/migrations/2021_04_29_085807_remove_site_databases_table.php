<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RemoveSiteDataBasesTable extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(){
        if (Schema::hasColumn('sites', 'site_database_id')) {
            Schema::table('sites', function (Blueprint $table) {
                $table->dropForeign(['site_database_id']);
                $table->dropColumn('site_database_id');
            });
        };
        Schema::drop("site_databases");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(){
        if (Schema::hasColumn('sites', 'site_database_id')) {
            Schema::table('sites', function (Blueprint $table) {
                $table->dropColumn('site_database_id');
            });
        };
        Schema::drop("site_databases");
    }
}
