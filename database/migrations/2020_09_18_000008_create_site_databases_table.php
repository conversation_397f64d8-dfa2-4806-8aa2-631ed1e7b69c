<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSiteDatabasesTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("site_databases", function(Blueprint $table){
			$table->bigIncrements("id");
			$table->unsignedBigInteger("client_id");
			$table->string("host");
			$table->string("username");
			$table->string("password");
			$table->string("database");
			$table->enum('type', ["EBP", "SILAE", "SAGE"]);
			$table->boolean("is_locked")->default(0);
			$table->softDeletes();
			$table->timestamps();

			$table->foreign("client_id")->references("id")->on("clients")->onDelete("cascade");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("site_databases");
	}
}
