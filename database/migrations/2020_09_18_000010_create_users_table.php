<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUsersTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("users", function(Blueprint $table){
			$table->bigIncrements("id");
			$table->unsignedInteger("profile_id");
			$table->unsignedBigInteger("site_id");
			$table->uuid("uuid");
			$table->string("crm_uuid")->nullable();
			$table->uuid("client_uuid")->nullable();
			$table->string("firstname");
			$table->string("lastname");
			$table->string("email");
			$table->string("fcm_token")->nullable();
			$table->string("picture_path")->nullable();
			$table->string("license_path")->nullable();
			$table->string("matricule")->nullable();
			$table->dateTime("enter_date")->nullable();
			$table->boolean("can_receive_mails")->default(true);
			$table->softDeletes();
			$table->timestamps();

			$table->foreign("profile_id")->references("id")->on("profiles")->onDelete("cascade");
			$table->foreign("site_id")->references("id")->on("sites")->onDelete("cascade");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("users");
	}
}
