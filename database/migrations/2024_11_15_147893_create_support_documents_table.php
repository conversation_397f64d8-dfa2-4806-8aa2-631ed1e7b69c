<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('support_documents', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->foreignid('creator_id')->nullable()->constrained('users');
            $table->timestamps();
        });

        Schema::create('site_support_document', function (Blueprint $table) {
            $table->foreignid('site_id')->constrained('sites');
            $table->foreignUuid('support_document_id')->constrained('support_documents');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('support_documents');
        Schema::dropIfExists('site_support_document');
    }
};
