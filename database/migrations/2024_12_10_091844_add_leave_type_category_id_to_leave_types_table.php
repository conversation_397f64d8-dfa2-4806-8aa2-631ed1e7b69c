<?php

use App\Models\Api\LeaveTypeCategory;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Api\LeaveType;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('leave_types', function (Blueprint $table) {
            $table->foreignId('leave_type_category_id')->default(1)->constrained('leave_type_categories');
        });

        LeaveType::query()->each(function (LeaveType $leaveType) {
            $leaveType->leave_type_category_id = ($leaveType->is_only_visible_by_admin === 1 ? LeaveTypeCategory::where('slug', 'abs')->first()->getKey() : LeaveTypeCategory::where('slug', 'cg')->first()->getKey());
            $leaveType->saveQuietly();
        }, 50);
    }

    public function down(): void
    {
        LeaveType::query()->each(function (LeaveType $leaveType) {
            $leaveType->is_only_visible_by_admin = ($leaveType->is_only_visible_by_admin === LeaveTypeCategory::where('slug', 'abs')->first()->getKey() ? 1 : 0);
            $leaveType->saveQuietly();
        }, 50);

        Schema::table('leave_types', function (Blueprint $table) {
            $table->dropForeign(['leave_type_category_id']);
            $table->dropColumn('leave_type_category_id');
        });
    }
};
