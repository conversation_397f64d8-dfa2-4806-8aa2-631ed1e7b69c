<?php

use App\Models\Api\Tag;
use App\Models\Api\UserTag;
use Illuminate\Database\Migrations\Migration;

class DeleteUsersFromTagsDeleted extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Tag::onlyTrashed()->each(function ($tag) {
            UserTag::query()->where('tag_id', $tag->getKey())->delete();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

    }
}
