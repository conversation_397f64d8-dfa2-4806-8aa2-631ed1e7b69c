<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateClientsTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up(){
		Schema::create("clients", function(Blueprint $table){
			$table->bigIncrements("id")->comment("Identifiant du client");
			$table->string("name")->comment("Nom du client");
			$table->uuid("uuid")->comment("Identifiant personnel du client identique sur toutes les applications");
			$table->boolean("count_public_holidays")->default(true)->comment("Paramètre indiquant si le client souhaite que les compteurs de ses collaborateurs soient décrémentés s'ils posent un congé durant un jour férié");
			$table->enum("validation_scheme", ["HORIZONTAL", "VERTICAL"])->default("VERTICAL")->comment("Paramètre indiquant le type de schéma de validation : HORIZONTAL, VERTICAL");
			$table->softDeletes();
			$table->timestamps();
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down(){
		Schema::dropIfExists("clients");
	}
}
