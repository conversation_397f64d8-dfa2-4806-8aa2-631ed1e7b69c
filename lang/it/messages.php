<?php

return [
    'morning' => ' (<PERSON><PERSON>)',
    'afternoon' => ' (<PERSON><PERSON><PERSON><PERSON>)',
    'CreateSettingsSuccess' => 'Creazione delle impostazioni riuscita',
    'RemoveSettingsSuccess' => 'Le impostazioni sono state rimosse con successo.',
    'UpdateSuccess' => 'Aggiornamento riuscito',
    'WaitingPay' => 'In attesa di pagamento',
    'Waiting' => 'In attesa',
    'InProcess' => 'In corso',
    'SuccessStoreLeave' => 'Congedo creato',
    'SuccessCancelLeave' => 'Congedo annullato',
    'AskForCancelLeave' => 'Richiesta di annullamento congedo inviata',
    'SuccessCallback' => 'Richiamo riuscito',
    'CantCancelLeaveNotValidated' => 'Impossibile annullare il congedo perché non è stato convalidato',
    'CantCancelLeaveTransmitted' => 'Impossibile annullare il congedo perché è stato trasmesso',
    'CantCancelLeaveRefused' => 'Impossibile annullare il congedo perché è stato rifiutato',
    'LeaveCanceled' => 'Congedo annullato',
    'DontOwnLeaveOrManagerUser' => 'Non hai il permesso di modificare questo congedo o non sei il manager di questo utente',
    'CantCancelLeaveValidated' => 'Impossibile annullare il congedo perché è stato convalidato',
    'NotAdminFromUser' => 'Non sei amministratore di questo utente',
    'ErrorTransmisLeaveWrongStatus' => 'Errore, il congedo non può essere trasmesso perché ha uno stato errato',
    'SuccessRefuseLeave' => 'Congedo rifiutato',
    'SuccessMassValidationLeave' => 'Congedi trasmessi',
    'SuccessTransmitLeave' => 'Congedo trasmesso',
    'SuccessGetClosedDay' => 'Festività recuperate',
    'SuccessGetHolidays' => 'Vacanze recuperate',
    'SuccessGetLeave' => 'Congedo recuperato',
    'SuccessGetUser' => 'Utente recuperato',
    'SuccessGetCompteur' => 'Contatori recuperati',
    'SuccessStoreCompteur' => 'Contatore creato',
    'SuccessUpdateCompteur' => 'Contatore aggiornato',
    'SuccessUpdateUser' => 'Utente aggiornato',
    'SuccessUpdateBusinessDay' => 'Giornata lavorativa aggiornata',
    'SuccessGetCurrentUser' => 'Utente corrente recuperato',
    'SuccessLogout' => 'Logout riuscito',
    'SuccessUpdateCustomer' => 'Cliente aggiornato',
    'SuccessDeleteCustomer' => 'Cliente eliminato',
    'SuccessGetBusinessDay' => 'Giornata lavorativa recuperata',
    'SuccessGetDocs' => 'Documenti recuperati',
    'SuccessUpdateDoc' => 'Documento aggiornato',
    'SuccessDeleteFile' => 'File eliminato',
    'SuccessGetHistory' => 'Storico recuperato',
    'SuccessDeleteHistory' => 'Storico eliminato',
    'CantCancelLeave' => 'Impossibile annullare il congedo',
    'OtherAdminValidated' => 'Un altro amministratore ha già convalidato questo congedo',
    'UserNotInClient' => 'L\'utente non appartiene al tuo cliente',
    'SuccessAttachUsersToDirector' => 'Utenti assegnati al direttore',
    'SuccessDetachUsersToDirector' => 'Utenti staccati dal direttore',
    'SuccessStoreLeaveByTakingInNCounts' => 'Congedo creato utilizzando il contatore N',
    'SuccessStoreHoliday'=>'I giorni festivi sono stati creati con successo',
    'UserWithNoTags' => 'Collaboratori senza team',
    'UserNotManaged' => 'Collaboratori non gestiti',
    'success_store_leave_with_errors' => 'Congedi creati con successo, ma si sono verificati errori.',
    'AnyTags' => 'Nessun team',
    'ImportSucces' => 'Importazione riuscita',
    'ErrorImport' => 'Errore durante l\'importazione',
    'acquis_cp_n_1' => 'acquisito_cp_n_1',
    'pris_cp_n_1' => 'preso_cp_n_1',
    'solde_cp_n_1' => 'saldo_cp_n_1',
    'acquis_cp_n' => 'acquisito_cp_n',
    'pris_cp_n' => 'preso_cp_n',
    'solde_cp_n' => 'saldo_cp_n',
    'acquis_type_n_1' => 'acquisito_:tipo_n_1',
    'pris_type_n_1' => 'preso_:tipo_n_1',
    'solde_type_n_1' => 'saldo_:tipo_n_1',
    'acquis_type_n' => 'acquisito_:tipo_n',
    'pris_type_n' => 'preso_:tipo_n',
    'solde_type_n' => 'saldo_:tipo_n',
    'ImportUserLeaveCountPartiallySucces' => 'Si sono verificati errori durante l\'importazione dei contatori (:successRows / :rows righe importate).',
    'ImportUserLeavesPartiallySucces' => 'Si sono verificati errori durante l\'importazione delle assenze (:successRows / :rows righe importate).',
    'RegistrationNumber' => 'Numero di matricola',
    'RegistrationNumberEmployee' => 'Numero di matricola dipendente',
    'CompanyName' => 'Ragione sociale',
    'MainEstablishment' => 'Stabilimento principale',
    'Email' => 'Email',
    'Employee' => 'Dipendente',
    'EmployeeName' => 'Nome e cognome',
    'EmployeeLastname' => 'Cognome',
    'EmployeeFirstname' => 'Nome',
    'Establishment' => 'Stabilimento',
    'EntryDate' => 'Data di ingresso',
    'Acquired' => 'Acquisito',
    'Taken' => 'Preso',
    'Balance' => 'Saldo',
    'Label' => 'Etichetta',
    'CodeConges' => 'Codice congedo',
    'CodeAbsence' => 'Codice assenza',
    'StartDate' => 'Data di inizio',
    'EndDate' => 'Data di fine',
    'Duration' => 'Numero di giorni',
    'Status' => 'Stato',
    'Site' => 'Sito',
    'TypeCongesAbsence' => 'Tipo di assenza/ferie',
    'TypeAbsence' => 'Tipo di assenza',
    'TypeConges' => 'Tipo',
    'Tag' => 'Squadra',
    'HeaderRegistrationNumber' => 'matricola',
    'HeaderRegistrationNumberEmployee' => 'matricola_dipendente',
    'HeaderCompanyName' => 'ragione_sociale',
    'HeaderEstablishment' => 'stabilimento',
    'HeaderMainEstablishment' => 'stabilimento_principale',
    'HeaderEmail' => 'email',
    'HeaderEmployee' => 'dipendente',
    'HeaderLastname' => 'cognome',
    'HeaderFirstname' => 'nome',
    'HeaderEntryDate' => 'data_ingresso',
    'HeaderEntryDdate' => 'data_ingresso',
    'HeaderStartDate' => 'data_inizio',
    'HeaderEndDate' => 'data_fine',
    'HeaderDuration' => 'durata',
    'HeaderAcquired' => 'acquisito',
    'HeaderTaken' => 'preso',
    'HeaderBalance' => 'paga',
    'HeaderRemaining' => 'rimanente',
    'HeaderLeaveTypeName' => 'tipo_congedo',
    'Total' => 'Totale',
    'TotalGeneral' => 'Totale generale',
    'store_leave_with_errors' => 'Si è verificato un errore durante la creazione dei congedi...',
    'waiting_validation_by_manager' => 'In attesa di convalida da parte di :manager',
    'waiting_cancellation_by_manager' => 'In attesa di annullamento da parte di :manager',
    'VALIDATED' => 'Convalidato',
    'SUBMITTED' => 'Inviato per convalida',
    'REFUSED' => 'Rifiutato',
    'CANCELED' => 'Annullato',
    'TRANSMITTED' => 'Trasmissione alla busta paga',
    'SUBMITTED_TO_CANCELLATION' => 'Inviato per annullamento',
    'ImportPartiallySucces' => 'Importazione parzialmente riuscita',
    'UnsupportedFileType' => 'Tipo di file non supportato. Si prega di usare un file CSV, XLSX, o XLS.',
    'import' => [
        'error_title' => 'Errore di input',
        'error_value' => 'Il valore non è nella lista.',
        'prompt_title' => 'Scegli dalla lista',
        'prompt_value' => 'Si prega di scegliere un valore dall\'elenco a discesa.',
    ],
    'AlreadyTreatedLeavesCount' => 'Conteggio dei permessi già trattati',
    'WaitingValidationLeavesCount' => 'Conteggio dei permessi in attesa di convalida',
    'WaitingCancelationLeavesCount' => 'Conteggio dei permessi in attesa di cancellazione',
    'SuccessCancelLeaveSubToCancel'=> 'La richiesta di annullamento è stata rifiutata. Il congedo è ora annullato.',
    'SuccessValidateLeaveSubToCancel'=> 'La richiesta di annullamento è stata convalidata con successo, quindi il congedo è annullato.',
    'SuccessValidateLeaveSubToCancelValidated' => 'La richiesta di annullamento è stata rifiutata. Il congedo è tornato al suo stato \'Convalidato\'.',
    'SuccessValidateLeaveSubToCancelSubmitted' => 'La richiesta di annullamento è stata rifiutata. Il congedo è tornato al suo stato \'Presentato\'.',
    'SuccessMassValidationLeaveSubToCancel'=> 'Le richieste di annullamento sono state convalidate con successo, quindi i congedi sono annullati.',
    'CantCancelAdminOwnLeave'=>'Un amministratore non può annullare le proprie ferie',
    'SuccessTransmittedLeaveCanceled'=> 'La richiesta di annullamento è stata convalidata con successo, quindi il congedo è \'Annullato\'.',
    'SheetIgnored' => "Import ExtraCom: Foglio ':sheetName' ignorato.",
    'CpTypeNotFound' => 'Foglio :sheetnbmr Riga :line: Tipo di congedo CP non trovato per il sito.',
    'RttTypeNotFound' => 'Foglio :sheetnbmr Riga :line: Tipo di congedo RTT non trovato per il sito.',

];
