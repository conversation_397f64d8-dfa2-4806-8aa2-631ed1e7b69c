<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Linee di linguaggio di validazione
    |--------------------------------------------------------------------------
    |
    | Le seguenti linee di linguaggio contengono i messaggi di errore predefiniti
    | utilizzati dalla classe di validazione. Alcune di queste regole hanno più versioni, come
    | le regole di dimensione. Sentiti libero di modificare ciascuno di questi messaggi qui.
    |
    */

    'accepted' => 'Il campo :attribute deve essere accettato.',
    'accepted_if' => 'Il campo :attribute deve essere accettato quando :other è :value.',
    'active_url' => 'Il campo :attribute non è un URL valido.',
    'after' => 'Il campo :attribute deve essere una data successiva a :date.',
    'after_or_equal' => 'Il campo :attribute deve essere una data successiva o uguale a :date.',
    'alpha' => 'Il campo :attribute può contenere solo lettere.',
    'alpha_dash' => 'Il campo :attribute può contenere solo lettere, numeri e trattini.',
    'alpha_num' => 'Il campo :attribute può contenere solo numeri e lettere.',
    'array' => 'Il campo :attribute deve essere un array.',
    'ascii' => 'Il campo :attribute può contenere solo caratteri alfanumerici a un byte e simboli.',
    'before' => 'Il campo :attribute deve essere una data precedente a :date.',
    'before_or_equal' => 'Il campo :attribute deve essere una data precedente o uguale a :date.',
    'between' => [
        'numeric' => 'Il campo :attribute deve essere tra :min e :max.',
        'file' => 'Il campo :attribute deve essere tra :min e :max kilobyte.',
        'string' => 'Il campo :attribute deve essere tra :min e :max caratteri.',
        'array' => 'Il campo :attribute deve avere tra :min e :max elementi.',
    ],
    'boolean' => 'Il campo :attribute deve essere vero o falso.',
    'confirmed' => 'Il campo di conferma :attribute non corrisponde.',
    'current_password' => 'La password è errata.',
    'date' => 'Il campo :attribute non è una data valida.',
    'date_equals' => 'Il campo :attribute deve essere una data uguale a :date.',
    'date_format' => 'Il campo :attribute non corrisponde al formato :format.',
    'decimal' => 'Il campo :attribute deve avere :decimal decimali.',
    'declined' => 'Il campo :attribute deve essere rifiutato.',
    'declined_if' => 'Il campo :attribute deve essere rifiutato quando :other è :value.',
    'different' => 'I campi :attribute e :other devono essere diversi.',
    'digits' => 'Il campo :attribute deve essere composto da :digits cifre.',
    'digits_between' => 'Il campo :attribute deve essere composto da almeno :min e massimo :max cifre.',
    'dimensions' => 'Il campo :attribute ha dimensioni di immagine non valide.',
    'distinct' => 'Il campo contiene un valore duplicato.',
    'doesnt_end_with' => 'Il campo :attribute non può terminare con uno dei seguenti elementi: :values.',
    'doesnt_start_with' => 'Il campo :attribute non può iniziare con uno dei seguenti elementi: :values.',
    'email' => 'Il campo :attribute deve essere un indirizzo email valido.',
    'ends_with' => 'Il campo :attribute deve terminare con uno dei seguenti: :values.',
    'enum' => 'Il campo :attribute selezionato è invalido.',
    'exists' => 'Il campo :attribute selezionato è invalido.',
    'file' => 'Il campo :attribute deve essere un file.',
    'filled' => 'Il campo :attribute è obbligatorio.',
    'gt' => [
        'array' => 'Il campo :attribute deve avere più di :value elementi.',
        'file' => 'Il campo :attribute deve essere maggiore di :value kilobyte.',
        'numeric' => 'Il campo :attribute deve essere maggiore di :value.',
        'string' => 'Il campo :attribute deve essere maggiore di :value caratteri.',
    ],
    'gte' => [
        'array' => 'Il campo :attribute deve avere :value elementi o più.',
        'file' => 'Il campo :attribute deve essere maggiore o uguale a :value kilobyte.',
        'numeric' => 'Il campo :attribute deve essere maggiore o uguale a :value.',
        'string' => 'Il campo :attribute deve essere maggiore o uguale a :value caratteri.',
    ],
    'image' => 'Il campo :attribute deve essere un\'immagine.',
    'in' => 'Il campo :attribute è invalido.',
    'in_array' => 'Il campo :attribute non esiste in :other.',
    'integer' => 'Il campo :attribute deve essere un numero intero.',
    'ip' => 'Il campo :attribute deve essere un indirizzo IP valido.',
    'ipv4' => 'Il campo :attribute deve essere un indirizzo IPv4 valido.',
    'ipv6' => 'Il campo :attribute deve essere un indirizzo IPv6 valido.',
    'json' => 'Il campo :attribute deve essere una stringa JSON valida.',
    'lowercase' => 'Il campo :attribute deve essere in minuscolo.',
    'lt' => [
        'array' => 'Il campo :attribute deve avere meno di :value elementi.',
        'file' => 'Il campo :attribute deve essere inferiore a :value kilobyte.',
        'numeric' => 'Il campo :attribute deve essere inferiore a :value.',
        'string' => 'Il campo :attribute deve essere inferiore a :value caratteri.',
    ],
    'lte' => [
        'array' => 'Il campo :attribute non deve avere più di :value elementi.',
        'file' => 'Il campo :attribute deve essere inferiore o uguale a :value kilobyte.',
        'numeric' => 'Il campo :attribute deve essere inferiore o uguale a :value.',
        'string' => 'Il campo :attribute deve essere inferiore o uguale a :value caratteri.',
    ],
    'mac_address' => 'Il campo :attribute deve essere un indirizzo MAC valido.',
    'max' => [
        'array' => 'Il campo :attribute non può avere più di :max elementi.',
        'file' => 'Il campo :attribute non può essere più grande di :max kilobyte.',
        'numeric' => 'Il campo :attribute non può essere maggiore di :max.',
        'string' => 'Il campo :attribute non può essere più lungo di :max caratteri.',
    ],
    'max_digits' => 'Il campo :attribute non deve avere più di :max cifre.',
    'mimes' => 'Il campo :attribute deve essere un file di tipo: :values.',
    'mimetypes' => 'Il campo :attribute deve essere un file di tipo: :values.',
    'min' => [
        'array' => 'Il campo :attribute deve avere almeno :min elementi.',
        'file' => 'Il campo :attribute deve essere almeno di :min kilobyte.',
        'numeric' => 'Il campo :attribute deve essere almeno :min.',
        'string' => 'Il campo :attribute deve essere di almeno :min caratteri.',
    ],
    'min_digits' => 'Il campo :attribute deve avere almeno :min cifre.',
    'missing' => 'Il campo :attribute deve essere mancante.',
    'missing_if' => 'Il campo :attribute deve essere mancante quando :other è :value.',
    'missing_unless' => 'Il campo :attribute deve essere mancante a meno che :other sia :value.',
    'missing_with' => 'Il campo :attribute deve essere mancante quando :value è presente.',
    'missing_with_all' => 'Il campo :attribute deve essere mancante quando :values sono presenti.',
    'multiple_of' => 'Il campo :attribute deve essere un multiplo di :value.',
    'not_in' => 'Il campo :attribute selezionato è invalido.',
    'not_regex' => 'Il formato del campo :attribute è invalido.',
    'numeric' => 'Il campo :attribute deve essere un numero.',
    'password' => [
        'letters' => 'Il campo :attribute deve contenere almeno una lettera.',
        'mixed' => 'Il campo :attribute deve contenere almeno una lettera maiuscola e una minuscola.',
        'numbers' => 'Il campo :attribute deve contenere almeno un numero.',
        'symbols' => 'Il campo :attribute deve contenere almeno un simbolo.',
        'uncompromised' => 'Il dato :attribute fornito è stato compromesso. Si prega di scegliere un altro :attribute.',
    ],
    'present' => 'Il campo :attribute deve essere presente.',
    'prohibited' => 'Il campo :attribute è vietato.',
    'prohibited_if' => 'Il campo :attribute è vietato quando :other è :value.',
    'prohibited_unless' => 'Il campo :attribute è vietato a meno che :other non sia in :values.',
    'prohibits' => 'Il campo :attribute proibisce la presenza di :other.',
    'regex' => 'Il formato del campo :attribute è invalido.',
    'required' => 'Il campo :attribute è obbligatorio.',
    'required_array_keys' => 'Il campo :attribute deve contenere voci per: :values.',
    'required_if' => 'Il campo :attribute è obbligatorio quando :other è :value.',
    'required_if_accepted' => 'Il campo :attribute è obbligatorio quando :other è accettato.',
    'required_unless' => 'Il campo :attribute è obbligatorio a meno che :other sia in :values.',
    'required_with' => 'Il campo :attribute è obbligatorio quando :values è presente.',
    'required_with_all' => 'Il campo :attribute è obbligatorio quando :values sono presenti.',
    'required_without' => 'Il campo :attribute è obbligatorio quando :values non è presente.',
    'required_without_all' => 'Il campo :attribute è obbligatorio quando nessuno dei :values è presente.',
    'same' => 'I campi :attribute e :other devono corrispondere.',
    'size' => [
        'numeric' => 'Il campo :attribute deve essere :size.',
        'file' => 'Il campo :attribute deve essere :size kilobyte.',
        'string' => 'Il campo :attribute deve essere :size caratteri.',
        'array' => 'Il campo :attribute deve avere :size elementi.',
    ],
    'starts_with' => 'Il campo :attribute deve iniziare con uno dei seguenti: :values.',
    'string' => 'Il campo :attribute deve essere una stringa.',
    'timezone' => 'Il campo :attribute deve essere un fuso orario valido.',
    'unique' => 'Il campo :attribute è già stato preso.',
    'unique_entry' => 'Ogni voce deve essere associata a un indirizzo email e un nome univoci.',
    'uploaded' => 'Il campo :attribute non è riuscito a caricare.',
    'uppercase' => 'Il campo :attribute deve essere maiuscolo.',
    'url' => 'Il campo :attribute deve essere un URL valido.',
    'ulid' => 'Il campo :attribute deve essere un ULID valido.',
    'uuid' => 'Il campo :attribute deve essere un UUID valido.',
    'emptyValue' => 'Il campo :attribute è vuoto.',

    /*
    |--------------------------------------------------------------------------
    | Messaggi di validazione personalizzati
    |--------------------------------------------------------------------------
    |
    | Qui puoi specificare messaggi di validazione personalizzati per gli attributi utilizzando la convenzione "attribute.rule" per nominare le righe.
    | Questo ci permette di specificare rapidamente una riga di lingua personalizzata per una determinata regola di attributo.
    |
    */

    'custom' => [
        'leave_already_taken' => 'Impossibile registrare. Le ferie sono già state prese in questa data.',
        'empty_field' => 'Riga :line : Il campo :field non è stato compilato.',
        'matricule_not_specified' => 'Riga :line : Il matricola non è stato specificato.',
        'site_not_specified' => 'Riga :line : Il sito non è stato specificato.',
        'site_not_existed' => 'Riga :line : Il sito non esiste - :site.',
        'absence_type_not_found' => 'Riga :line : Tipo di assenza non trovato - Codice :leaveTypeCode',
        'leave_type_not_found' => 'Riga :line : Tipo di congedo non trovato - Codice :leaveTypeCode',
        'user_bad_site' => 'Riga :line : L\'utente si trova nel sito sbagliato (:site) - Matricola :matricule',
        'user_not_found' => 'Riga :line : Utente non trovato - Matricola :matricule',
        'user_not_specified' => 'Riga :line : L\'utente non è stato specificato.',
        'user_deleted' => 'Riga :line : L\'utente è stato eliminato - Matricola :matricule',
        'balance_invalid' => 'Riga :line : Il saldo non corrisponde al valore atteso.',
        'user_not_on_your_clients' => 'Riga :line : L\'utente non fa parte dei tuoi clienti - Matricola :matricule',
        'InvalidLeaveTypeNameFormat' => 'Riga :line : Il tipo di assenza deve essere una stringa.',
        'InvalidDurationFormat' => 'Riga :line : La durata dell\'assenza deve essere numerica.',
        'InvalidDurationValue' => 'Riga :line: La durata dell\'assenza deve essere maggiore di 0',
        'InvalidMatriculeFormat' => 'Riga :line : Il matricola deve essere numerica.',
        'InvalidLastNameFormat' => 'Riga :line : Il cognome deve essere una stringa.',
        'InvalidFirstNameFormat' => 'Riga :line : Il nome deve essere una stringa.',
        'InvalidDateFormat' => 'Riga :line : Formato data non valido.',
    ],

    /*
    |--------------------------------------------------------------------------
    | Attributi di validazione personalizzati
    |--------------------------------------------------------------------------
    |
    | Le righe di lingua seguenti vengono utilizzate per sostituire il segnaposto dell'attributo con qualcosa di più amichevole per il lettore, come "Indirizzo email"
    | invece di "email". Ciò ci aiuta semplicemente a rendere il nostro messaggio più espressivo.
    |
    */

    'attributes' => [
        'activity' => 'attività',
        'activities' => 'attività',
        'address' => 'indirizzo',
        'addresses' => 'indirizzi',
        'age' => 'età',
        'ages' => 'età',
        'amount' => 'importo',
        'amounts' => 'importi',
        'answer' => 'risposta',
        'answers' => 'risposte',
        'available' => 'disponibile',
        'availables' => 'disponibili',
        'barcode' => 'codice a barre',
        'barcodes' => 'codici a barre',
        'birth_date' => 'data di nascita',
        'brand' => 'marca',
        'brands' => 'marche',
        'brand_name' => 'nome della marca',
        'buying_price' => 'prezzo d\'acquisto',
        'category' => 'categoria',
        'categories' => 'categorie',
        'city' => 'città',
        'cities' => 'città',
        'civility' => 'civiltà',
        'civilities' => 'civilità',
        'comment' => 'commento',
        'comments' => 'commenti',
        'company' => 'azienda',
        'companies' => 'aziende',
        'confirmed' => 'confermato',
        'confirmed_at' => 'confermato il',
        'content' => 'contenuto',
        'contents' => 'contenuti',
        'country' => 'paese',
        'countries' => 'paesi',
        'customer' => 'cliente',
        'customers' => 'clienti',
        'day' => 'giorno',
        'days' => 'giorni',
        'date_end' => 'data di fine',
        'date_start' => 'data di inizio',
        'directory' => 'cartella',
        'directory_name' => 'nome della cartella',
        'directories' => 'cartelle',
        'directories_name' => 'nome delle cartelle',
        'directories_names' => 'nomi delle cartelle',
        'email_banned' => 'email bannato',
        'email_confirmed' => 'email confermata',
        'email_validated' => 'email validata',
        'email_prohibited' => 'email proibita',
        'emails_banned' => 'email bannate',
        'emails_confirmed' => 'email confermate',
        'emails_validated' => 'email validate',
        'emails_prohibited' => 'email proibite',
        'file' => 'file',
        'files' => 'file',
        'first_name' => 'nome',
        'first_names' => 'nomi',
        'gender' => 'genere',
        'genders' => 'generi',
        'hour' => 'ora',
        'hours' => 'ore',
        'is_active' => 'è attivo?',
        'is_banned' => 'è bannato?',
        'job' => 'lavoro',
        'jobs' => 'lavori',
        'last_name' => 'cognome',
        'last_names' => 'cognomi',
        'link' => 'link',
        'links' => 'link',
        'month' => 'mese',
        'name' => 'nome',
        'names' => 'nomi',
        'office' => 'ufficio',
        'offices' => 'uffici',
        'other' => 'altro',
        'others' => 'altri',
        'paid_at' => 'pagato il',
        'password' => 'password',
        'password_confirmation' => 'conferma della password',
        'password_current' => 'password attuale',
        'passwords' => 'passwords',
        'phone' => 'telefono',
        'phones' => 'telefoni',
        'postal_code' => 'CAP',
        'price' => 'prezzo',
        'published_at' => 'pubblicato il',
        'quantity' => 'quantità',
        'quantities' => 'quantità',
        'rate' => 'tariffa',
        'rates' => 'tariffe',
        'response' => 'risposta',
        'responses' => 'risposte',
        'role' => 'ruolo',
        'roles' => 'ruoli',
        'second' => 'secondo',
        'seconds' => 'secondi',
        'siren_number' => 'numero di siren',
        'siret_number' => 'numero di siret',
        'size' => 'taglia',
        'sizes' => 'taglie',
        'status' => 'stato',
        'statuses' => 'stati',
        'street' => 'via',
        'subfolder' => 'sottocartella',
        'subfolders' => 'sottocartelle',
        'subdirectory' => 'sottocartella',
        'subdirectories' => 'sottocartelle',
        'subject' => 'oggetto',
        'subjects' => 'oggetti',
        'summary' => 'riepilogo',
        'summarys' => 'riepiloghi',
        'supplier' => 'fornitore',
        'suppliers' => 'fornitori',
        'tax' => 'tassa',
        'time' => 'ora',
        'title' => 'titolo',
        'titles' => 'titoli',
        'user' => 'utente',
        'users' => 'utenti',
        'username' => 'nome utente',
        'usernames' => 'nomi utente',
        'value' => 'valore',
        'values' => 'valori',
        'vat' => 'IVA',
        'vat_rate' => 'aliquota IVA',
        'website' => 'sito web',
        'websites' => 'siti web',
        'year' => 'anno',
        'years' => 'anni',
        'default_footer_text' => 'testo predefinito del piè di pagina',
        'media_footer' => 'media del piè di pagina',
        'survey_id' => 'id del sondaggio',
        'firstname' => 'nome',
        'lastname' => 'cognome',
        'email' => 'email',
        'label' => 'etichetta',
        'description' => 'descrizione',
        'started_at' => 'data di inizio',
        'ended_at' => 'data di fine',
        'accompanying_person_limit' => 'limite di accompagnatori',
        'event_status_id' => 'id dello stato dell\'evento',
        'guest_ids' => 'id degli ospiti',
        'event_id' => 'id dell\'evento',
        'phone_number' => 'numero di telefono',
        'phone_code' => 'codice telefono',
        'app_from' => 'Creato da',
        'sticker' => 'etichetta',
        'is_vip' => 'è un VIP',
        'user_related_id' => 'id dell\'accompagnatore',
        'is_notified_user_related' => 'notificare l\'accompagnatore via email',
        'is_owner' => 'è un organizzatore',
        'is_ticket_generated' => 'invito inviato',
        'collection_name' => 'nome della collezione',
        'model_type' => 'tipo di modello',
        'media' => 'media',
        'header_text' => 'testo dell\'intestazione',
        'guests' => 'partecipanti',
        'entry.survey_id' => 'id del sondaggio della risposta',
        'entry.firstname' => 'nome dell\'entrata',
        'entry.lastname' => 'cognome dell\'entrata',
        'entry.answers' => 'risposte dell\'entrata',
        'entry_id' => 'id dell\'entrata',
        'question_id' => 'id della domanda',
        'hint' => 'suggerimento',
        'order' => 'ordine',
        'question_type_id' => 'id del tipo di domanda',
        'questionable_type' => 'entità associata alla domanda',
        'questionable_id' => 'id dell\'entità associata alla domanda',
        'is_required' => 'è obbligatorio',
        'has_escape_answer' => 'domanda altra',
        'location_id' => 'id della posizione',
        'capacity' => 'capacità',
        'overflow' => 'superamento consentito',
        'reminder_days_before_session' => 'numero di giorni per il promemoria prima della sessione',
        'thanks_days_after_session' => 'numero di giorni per il ringraziamento dopo la sessione',
        'feedback_survey_id' => 'id del sondaggio di feedback',
        'is_survey_related' => 'sondaggio correlato',
        'is_signable' => 'firma manuale',
        'details' => 'dettagli',
        'creator_id' => 'id del creatore',
        'survey_status_id' => 'id dello stato del sondaggio',
        'is_template' => 'modello',
        'max_entries' => 'massimo di risposte',
        'updated_by_id' => 'aggiornato dall\'utente',
        'expires_at' => 'scade il',
        'footer_text' => 'testo del piè di pagina',
        'title_alignment' => 'allineamento del titolo',
        'holiday_already_exists_for_date_and_client' => 'Esiste già un giorno festivo per questa data e questo cliente.'
    ],
];
