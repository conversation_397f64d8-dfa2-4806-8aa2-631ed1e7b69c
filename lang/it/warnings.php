<?php

return [

    'env' => [
        'ActionIsNotAvailableInEnv' => 'Questa azione non è disponibile nell\'ambiente :env',
    ],
    'ClientTableDosntExist' => 'La tabella dei clienti non esiste',
    'UnauthorizedAction' => 'Azione non autorizzata',
    'SettingsAlreadyExists' => 'Le impostazioni esistono già',
    'ErrorBatchUpdateTags' => 'Errore durante l\'aggiornamento in batch dei tag',
    'TagAlreadyExists' => 'Il tag esiste già',
    'BadProfile' => 'Profilo non valido',
    'ProfileNotFound' => 'Profilo non trovato',
    'InternalError' => 'Errore interno',
    'DellUsersSuccess' => 'Utenti eliminati con successo',
    'AddUsersSuccess' => 'Utenti aggiunti con successo',
    'LogoutSuccess' => 'Disconnessione riuscita',
    'UserAlreadyTreatedLeave' => 'Hai già convalidato questo permesso',
    'NoLeaveCounter' => 'Nessun contatore di permessi trovato',
    'NotEnoughLeaveDay' => 'Non ci sono abbastanza giorni di permesso',
    'UserDontMatchJwt' => 'L\'utente non corrisponde al token JWT',
    'LeaveAlreadyExisteForDate' => 'La data richiesta è già occupata da un\'altra richiesta',
    'DurationRequired' => 'Durata richiesta',
    'DurationMustBeANumber' => 'La durata deve essere un numero',
    'DurationMustBePositive' => 'La durata deve essere positiva',
    'ErrorLeaveDuration' => 'Errore nella durata del permesso',
    'ErrorLeaveDurationHalfDayNotPermit' => 'Errore nella durata del permesso, le mezze giornate non sono permesse',
    'GetLeaveType' => 'Recupero del tipo di permesso',
    'ErrorLeaveTypeDontExist' => 'Errore, il tipo di permesso non esiste',
    'ErrorLeaveTypeDontBelongToClient' => 'Errore, il tipo di permesso non appartiene al cliente',
    'ErrorGetStatuses' => 'Errore durante il recupero degli stati',
    'ErrorStatusDontExist' => 'Stato inesistente',
    'ErrorLeaveTypeSubFamilyDontExist' => 'Errore, la sottofamiglia del tipo di permesso non esiste',
    'ErrorLeaveTypeSubFamilyDontBelongToLeave' => 'Errore, la sottofamiglia del tipo di permesso non appartiene al tipo di permesso',
    'ErrorStartDateRequired' => 'Data di inizio richiesta',
    'ErrorEndDateRequired' => 'Data di fine richiesta',
    'ErrorStartDateAboveEndDate' => 'La data di inizio è successiva alla data di fine',
    'ErrorSameDate' => 'Stessa data di inizio e fine',
    'DontOwnLeave' => 'Non hai il diritto di convalidare questo permesso',
    'LeaveDontExist' => 'Permesso inesistente',
    'NotAdminFromUser' => 'Non sei amministratore di questo utente',
    'NotAdminOrManagerFromUser' => 'Non sei né amministratore né manager di questo utente',
    'NotDirectorOrManagerFromUser' => 'Non sei né direttore né manager di questo utente',
    'CantRefuseLeaveWrongStatus' => 'Impossibile rifiutare questo permesso perché ha uno stato errato',
    'ErrorNotValidatedByLastManager' => 'Errore, il permesso non è stato convalidato dall\'ultimo manager',
    'ErrorLeaveStatusDontAllowValidation' => 'Errore, lo stato del permesso non consente la convalida',
    'NotManagerFromUser' => 'Non sei manager di questo utente',
    'SuccessValidateLeave' => 'Permesso convalidato con successo',
    'SuccessMassValidationLeave' => 'Permessi convalidati con successo',
    'ErrorValidateLeave' => 'Errore nella convalida del permesso',
    'ErrorLeaveDontExist' => 'Permesso inesistente',
    'NoManagerOrAdminForRefuseLeave' => 'Non c\'è un manager o un amministratore per rifiutare questo permesso',
    'ErrorRefuseLeave' => 'Errore nel rifiuto del permesso',
    'ErrorActionNotAllowed' => 'Azione non consentita',
    'ErrorNoAdminForTransmit' => 'Errore, nessun amministratore per trasmettere questo permesso',
    'ErrorTransmitLeave' => 'Errore nella trasmissione del permesso',
    'ErrorStoreLeave' => 'Errore nel salvataggio del permesso',
    'SuccessGetClosedDay' => 'Giorni festivi recuperati con successo',
    'ErrorValidationData' => 'Errore nella convalida dei dati',
    'ErrorGetClosedDay' => 'Errore nel recupero dei giorni festivi',
    'SuccessGetHolidays' => 'Vacanze recuperate con successo',
    'ErrorGetHolidays' => 'Errore nel recupero delle vacanze',
    'SuccessGetLeave' => 'Permesso recuperato con successo',
    'ErrorGetLeave' => 'Errore nel recupero del permesso',
    'ErrorDataArrayDontExist' => 'Array di dati inesistente',
    'FileRequired' => 'File richiesto',
    'ActionNotAllowed' => 'Azione non consentita',
    'UserDontExist' => 'Utente inesistente',
    'UserBelongToAnotherClient' => 'Utente appartenente a un altro cliente',
    'ErrorDateLeaveTypeNotAllowed' => 'Errore, la data non è consentita per questo tipo di permesso',
    'DurationTooLong' => 'Durata troppo lunga',
    'ErrorValidateRefuseLeave' => 'Errore nella convalida/rifiuto del permesso',
    'ErrorGetAttachment' => 'Errore nel recupero dell\'allegato',
    'CantGenerateFile' => 'Impossibile generare il file',
    'ErrorExportLeave' => 'Errore nell\'esportazione dei permessi',
    'ErrorDontOwnLeaveType'=>'Non hai il diritto di modificare questo tipo di permesso',
    'ErrorLeaveTypeNotExist'=>'Il tipo di permesso non esiste',
    'LeaveTypeAlreadyExist'=>'Questo tipo di permesso esiste già',
    'LeaveSubTypeDontBelongToCustomer'=>'La sottofamiglia del tipo di permesso non appartiene al cliente',
    'ErrorUpdateLeaveType'=>'Errore nell\'aggiornamento del tipo di permesso',
    'SuccessUpdateLeaveType'=>'Tipo di permesso aggiornato con successo',
    'NotRightForGetLeaveType'=>'Non hai il diritto di recuperare questo tipo di permesso',
    'ErrorGetLeaveTypes'=>'Errore nel recupero dei tipi di permesso',
    'SuccessGetLeaveType'=>'Tipo di permesso recuperato con successo',
    'SuccessStoreLeaveType'=>'Tipo di permesso salvato con successo',
    'ErrorStoreLeaveType'=>'Errore nel salvataggio del tipo di permesso',
    'SuccessDeleteLeaveType'=>'Tipo di permesso eliminato con successo',
    'ErrorDeleteLeaveType'=>'Errore nell\'eliminazione del tipo di permesso',
    'ErrorLeaveTypeCantBeDeleted'=>'Questo tipo di permesso non può essere eliminato :leave_type_id',
    'ErrorGetManager'=>'Errore nel recupero del manager',
    'ErrorStoreManager'=>'Errore nel salvataggio del manager',
    'ErrorUpdateManager'=>'Errore nell\'aggiornamento del manager',
    'ErrorDeleteManager'=>'Errore nell\'eliminazione del manager',
    'SuccessStoreToken'=>'Token salvato con successo',
    'ErrorStoreToken'=>'Errore nel salvataggio del token',
    'SuccessClearToken'=>'Token eliminato con successo',
    'ErrorDontOwnSite'=>'Non hai il diritto di modificare questo sito',
    'ErrorGetSite'=>'Errore nel recupero del sito',
    'ValidationErrors'=>'Errori di convalida',
    'SuccessGetSites'=>'Siti recuperati con successo',
    'ErrorUpdateSite'=>'Errore nell\'aggiornamento del sito',
    'SuccessGetStatuses'=>'Stati recuperati con successo',
    'NotAccessToTag'=>'Non hai accesso a questo tag',
    'SuccessGetTags'=>'Tag recuperati con successo',
    'ErrorGetTags'=>'Errore nel recupero dei tag',
    'SuccessGetUser'=>'Utente recuperato con successo',
    'ErrorGetUsers'=>'Errore nel recupero degli utenti',
    'SuccessUpdateTag'=>'Tag aggiornato con successo',
    'ErrorUpdateTag'=>'Errore nell\'aggiornamento del tag',
    'SuccessStoreTag'=>'Tag salvato con successo',
    'ErrorStoreTag'=>'Errore nel salvataggio del tag',
    'ErrorDeleteTag'=>'Errore nell\'eliminazione del tag',
    'SuccessDeleteTag'=>'Tag eliminato con successo',
    'RelationAlreadyExist'=>'Relazione già esistente',
    'SuccessAddUserToTag'=>'Utente aggiunto al tag con successo',
    'ErrorAddUserToTag'=>'Errore nell\'aggiunta dell\'utente al tag',
    'RelationDontExist'=>'Relazione inesistente',
    'SuccessRemoveUserFromTag'=>'Utente rimosso dal tag con successo',
    'ErrorRemoveUserFromTag'=>'Errore nella rimozione dell\'utente dal tag',
    'SuccessUpdateUserTag'=>'Tag dell\'utente aggiornati con successo',
    'ErrorUpdateUserTag'=>'Errore nell\'aggiornamento dei tag dell\'utente',
    'ErrorGetTeam'=>'Errore nel recupero del team',
    'ErrorStoreTeam'=>'Errore nel salvataggio del team',
    'ErrorUpdateTeam'=>'Errore nell\'aggiornamento del team',
    'ErrorDeleteTeam'=>'Errore nell\'eliminazione del team',
    'SuccessImportFile'=>'File importato con successo',
    'ErrorImportFile'=>'Errore nell\'importazione del file',
    'ErrorGetTeamCompteur'=>'Errore nel recupero del contatore del team',
    'UserDoesNotExist'=>'Utente inesistente',
    'LeaveTypeDoesntExist'=>'Tipo di permesso inesistente',
    'LeaveTypeBelongToAnotherClient'=>'Tipo di permesso appartenente a un altro cliente',
    'UserLeaveCountAlreadyExist'=>'Il contatore di permessi dell\'utente esiste già',
    'ErrorStoreCompteur'=>'Errore nel salvataggio del contatore',
    'UserLeaveCountDoesntExist'=>'Il contatore di permessi dell\'utente non esiste',
    'UserLeaveCountBelongToAnotherClient'=>'Il contatore di permessi dell\'utente appartiene a un altro cliente',
    'ErrorUpdateCompteur'=>'Errore nell\'aggiornamento del contatore',
    'ErrorExportCompteur'=>'Errore nell\'esportazione del contatore',
    'ErrorImportLeave'=>'Errore nell\'importazione dei permessi',
    'ErrorSaveFileInMinio'=>'Errore nel salvataggio del file in MinIO',
    'ErrorSiteDoesntExist'=>'Sito inesistente',
    'ErrorUserDontExist'=>'Utente inesistente',
    'ErrorDontOwnUser'=>'Non hai il diritto di modificare questo utente',
    'ProfileDontExist'=>'Profilo inesistente',
    'SiteDontExist'=>'Sito inesistente',
    'ErrorUpdateUser'=>'Errore nell\'aggiornamento dell\'utente',
    'ErrorUpdateBusinessDay'=>'Errore nell\'aggiornamento del giorno lavorativo',
    'SuccessDeleteUser' =>'Utente eliminato con successo',
    'ErrorGetCurrentUser'=>'Errore nel recupero dell\'utente corrente',
    'InvalidDateFormat'=>'Formato data non valido',
    'NullDateFormat'=>'La data di inizio o di fine è nulla',
    'LeaveTypeNotFound'=>'Tipo di congedo non trovato',
    'LeaveTypeClientMismatch'=>'Il tipo di congedo non appartiene al cliente',
    'SubFamilyMismatch'=>'La sottofamiglia del tipo di congedo non appartiene al cliente',
    'ErrorGetUserFromApp'=>'Errore nel recupero dell\'utente dall\'applicazione',
    'ErrorStoreLeaveTypeSubFamily'=>'Errore nel salvataggio della sottofamiglia del tipo di permesso',
    'ErrorUpdateOtherCustomer'=>'Errore nell\'aggiornamento di un altro cliente',
    'CantDisplayCustomerFromThisApp'=>'Non puoi visualizzare questo cliente da questa applicazione',
    'ErrorStoreClient'=>'Errore nel salvataggio del cliente',
    'SuccessStoreCustomer'=>'Cliente creato con successo',
    'ErrorStoreCustomer'=>'Errore nella creazione del cliente',
    'ClientNotExist'=>'Cliente inesistente',
    'ErrorUpdateCustomer'=>'Errore nell\'aggiornamento del cliente',
    'ErrorDeleteCustomer'=>'Errore nell\'eliminazione del cliente',
    'SuccessUpdateLeaveDate'=>'Data del permesso aggiornata con successo',
    'ErrorGetBusinessDay'=>'Errore nel recupero del giorno lavorativo',
    'ErrorGetValidationScheme'=>'Errore nel recupero dello schema di convalida',
    'ErrorGetDocs'=>'Errore nel recupero dei documenti',
    'DocDontExistForSite'=>'Documento inesistente per questo sito',
    'ErrorGetDoc'=>'Errore nel recupero del documento',
    'ErrorIdArrayDontExist'=>'Array di ID inesistente',
    'SiteDontExistForClient'=>'Sito inesistente per questo cliente',
    'ErrorFileDontExist'=>'File inesistente',
    'ErrorStoreFile'=>'Errore nel salvataggio del file',
    'SuccessStoreDoc'=>'Documento creato con successo',
    'ErrorLoadFile'=>'Errore nel caricamento del file',
    'DocDontExistForClient'=>'Documento inesistente per questo cliente',
    'CantDeleteForUpdateFile'=>'Impossibile eliminare questo documento perché viene utilizzato per aggiornare un file',
    'CantDeleteFile'=>'Impossibile eliminare il file',
    'ErrorDeleteFile'=>'Errore nell\'eliminazione del file',
    'ErrorGetExportHistory'=>'Errore nel recupero della cronologia delle esportazioni',
    'ErrorNoHistoryForClient'=>'Nessuna cronologia per questo cliente',
    'ErrorDeleteExportHistory'=>'Errore nell\'eliminazione della cronologia delle esportazioni',
    'ErrorCancelLeave'=>'Errore nell\'annullamento del permesso',
    'ErrorValidateCancelLeave'=>'Errore nella convalida dell\'annullamento del permesso',
    'SuccessGetLeaveTypes'=>'Tipi di permesso recuperati con successo',
    'SuccessGetSite'=>'Sito recuperato con successo',
    'SuccessUpdateSite'=>'Sito aggiornato con successo',
    'SuccessGetTag'=>'Tag recuperato con successo',
    'ErrorGetTag'=>'Errore nel recupero del tag',
    'TagDontExist'=>'Tag inesistente',
    'ErrorGetTeamFromApp'=>'Errore nel recupero del team dall\'applicazione',
    'ErrorStoreTeamFromApp'=>'Errore nel salvataggio del team dall\'applicazione',
    'ErrorUpdateTeamFromApp'=>'Errore nell\'aggiornamento del team dall\'applicazione',
    'ErrorDeleteTeamFromApp'=>'Errore nell\'eliminazione del team dall\'applicazione',
    'InvalideData'=> 'Formato data non valido per la data: :date',
    'SuccessStoreUser' => 'Utente registrato con successo',
    'MustBeInteger'=>'Deve essere un intero',
    'IsRequired'=> 'È richiesto',
    'MustBeEnum'=>'Deve essere un\'enumerazione',
    'MustBeString'=>'Deve essere una stringa',
    'MustBeBool'=>'Deve essere un booleano',
    'MustBeNotEmpty'=> 'Non deve essere vuoto',
    'MustBeNumber'=>'Deve essere un numero',
    'MustBeEmail'=>'Deve essere un\'email',
    'MustBeUUID'=> 'Deve essere un UUID',
    'MustBeDate'=> 'Deve essere una data',
    'MustBeDateAfter'=>'Deve essere una data successiva',
    'MustBeDateBefore'=>'Deve essere una data precedente',
    'MustRespectFormat'=>'Deve rispettare il formato',
    'UserNotFound'=>'Utente non trovato',
    'ErrorIdAbove'=>'L\'ID deve essere maggiore di 0',
    'ErrorIdNumber'=>'L\'ID deve essere un numero',
    'ErrorFileType'=>'Il tipo di file non è valido',
    'ErrorFileSize'=>'La dimensione del file è troppo grande',
    'ErrorSendAlert'=>'Errore nell\'invio dell\'allerta',
    'ErrorSaveHistoric'=>'Errore nel salvataggio della cronologia',
    'ClientAlreadyExist'=>'Cliente già esistente',
    'ExportNotFound'=>'L\'esportazione non è stata trovata',
    'ReasonDontExist'=>'Motivo non trovato',
    'LeaveRefused'=>'Permesso rifiutato',
    'UpdateDocIdError'=>'Errore nell\'aggiornamento dell\'ID del documento',
    'CantFindUuid'=>'Impossibile trovare l\'uuid',
    'CantDoIt'=>'Non hai i diritti per eseguire questa azione',
    'HeadersNotFound'=>'Intestazioni non trovate',
    'CustomerNotFound'=>'Cliente non trovato',
    'ApiProctectedBySignature'=>'API protetta da firma',
    'ParameterNotAllowed' => "Il parametro ':param_name' non è consentito su questa rotta.",
    'RequiredValueForParameter'=>'Il valore per il parametro :name è obbligatorio.',
    'UnsignedNumericValue'=>'Il formato previsto per :name è un valore numerico non firmato.',
    'RequiredBoolean'=>'Il formato previsto per :name è un valore booleano 0/1.',
    'RequiredDate'=>'Il formato previsto per :name è un valore di data',
    'RequiredDateTime'=>'Il formato previsto per :name è un valore di data e ora',
    'RequiredUuid'=>'Il formato previsto per :name è un valore uuid',
    'RequiredString'=>'Il formato previsto per :name è un valore di stringa.',
    'InvalidValue' => "Il valore ':value' non è consentito. Valori consentiti: :allowed_values.",
    'DirectionAscDesc'=>'La direzione deve essere ascendente (asc) o discendente (desc).',
    'LackDataForStore'=>'Mancano dati per salvare.',
    'FileWeight'=>'Il file è troppo grande: massimo 5 MB.',
    'FileTypeJpgPng'=>'Il tipo di file non è supportato, solo JPG o PNG.',
    'MustFilterById'=>'Devi filtrare per id',
    'ClientTableNotFound'=>'La tabella dei clienti non è stata trovata',
    'LeaveTypeNeedFile'=>'Questo tipo di congedo necessita di un file',
    'UserNotExist' => 'L\'utente con matricola :matricule non esiste',
    'UserDeleted' => 'L\'utente con matricola :matricule è stato eliminato',
    'SiteNotExist' => 'Il sito dell\'utente :matricule non esiste',
    'LeaveNotCreated' => 'Il congedo per l\'utente :matricule non può essere creato',
    'CurrentUser' => 'L\'utente corrente non ha il profilo corretto',
    'ManagedByCurrentUser'=>'Non sei gestito da questo utente',
    'ErrorLeaveStatusDontAllowModification' => 'Errore, lo stato del congedo non consente la modifica',
];

