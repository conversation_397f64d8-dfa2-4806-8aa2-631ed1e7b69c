<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Authentication Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used during authentication for various
    | messages that we need to display to the user. You are free to modify
    | these language lines according to your application's requirements.
    |
    */
    'env' => [
        'ActionIsNotAvailableInEnv' => 'Cette action n\'est pas disponible sur l\'environnement :env',
    ],
    'ClientTableDosntExist' => 'La table des clients n\'existe pas ',
    'UnauthorizedAction' => 'Action non autorisée',
    'SettingsAlreadyExists' => 'Les paramètres existent déjà',
    'ErrorBatchUpdateTags' => 'Erreur lors de la mise à jour des tags en batch',
    'TagAlreadyExists' => 'Le tag existe déjà',
    'BadProfile' => 'Profil invalide',
    'ProfileNotFound' => 'Profil introuvable',
    'InternalError' => 'Erreur interne',
    'DellUsersSuccess' => ' Utilisateurs supprimés avec succès',
    'AddUsersSuccess' => 'Utilisateurs ajoutés avec succès',
    'LogoutSuccess' => 'Déconnexion réussie',
    'UserAlreadyTreatedLeave' => 'Vous avez déjà validé ce congé',
    'NoLeaveCounter' => 'Aucun compteur de congé trouvé',
    'NotEnoughLeaveDay' => 'Pas assez de jours de congé',
    'UserDontMatchJwt' => 'L\'utilisateur ne correspond pas au jeton JWT',
    'LeaveAlreadyExisteForDate' => 'La date demandée est déjà occupée par une autre demande',
    'DurationRequired' => 'Durée requise',
    'DurationMustBeANumber' => 'La durée doit être un nombre',
    'DurationMustBePositive' => 'La durée doit être positive',
    'ErrorLeaveDuration' => 'Erreur de durée de congé',
    'ErrorLeaveDurationHalfDayNotPermit' => 'Erreur de durée de congé, les demi-journées ne sont pas permises',
    'GetLeaveType' => 'Récupération du type de congé',
    'ErrorLeaveTypeDontExist' => 'Erreur, le type de congé n\'existe pas',
    'ErrorLeaveTypeDontBelongToClient' => 'Erreur, le type de congé ne fait pas partie du client',
    'ErrorGetStatuses' => 'Erreur lors de la récupération des statuts',
    'ErrorStatusDontExist' => 'Statut inexistant',
    'ErrorLeaveTypeSubFamilyDontExist' => 'Erreur, la sous-famille de type de congé n\'existe pas',
    'ErrorLeaveTypeSubFamilyDontBelongToLeave' => 'Erreur, la sous-famille de type de congé ne fait pas partie du type de congé',
    'ErrorStartDateFormat' => 'Format de date de début incorrect',
    'ErrorEndDateFormat' => 'Format de date de fin incorrect',
    'ErrorStartDateRequired' => 'Date de début requise',
    'ErrorEndDateRequired' => 'Date de fin requise',
    'ErrorStartDateAboveEndDate' => 'La date de début est supérieure à la date de fin',
    'ErrorSameDate' => 'Même date de début et de fin',
    'DontOwnLeave' => 'Vous n\'avez pas le droit de valider ce congé',
    'LeaveDontExist' => 'Congé inexistant',
    'NotAdminFromUser' => 'Vous n\'êtes pas administrateur de cet utilisateur',
    'NotAdminOrManagerFromUser' => 'Vous n\'êtes ni administrateur ni manager de cet utilisateur',
    'NotDirectorOrManagerFromUser' => 'Vous n\'êtes ni directeur ni manager de cet utilisateur',
    'CantRefuseLeaveWrongStatus' => 'Impossible de refuser ce congé car il a un mauvais statut',
    'ErrorNotValidatedByLastManager' => 'Erreur, le congé n\'a pas été validé par le dernier manager',
    'NotManagerFromUser' => 'Vous n\'êtes pas manager de cet utilisateur',
    'SuccessValidateLeave' => 'Congé validé',
    'SuccessMassValidationLeave' => 'Congés validés',
    'ErrorValidateLeave' => 'Erreur de validation de congé',
    'ErrorLeaveDontExist' => 'Congé inexistant',
    'NoManagerOrAdminForRefuseLeave' => 'Il n\'y a pas de manager ou d\'administrateur pour refuser ce congé',
    'ErrorRefuseLeave' => 'Erreur de refus de congé',
    'ErrorActionNotAllowed' => 'Action non autorisée',
    'ErrorNoAdminForTransmit' => 'Erreur, aucun administrateur pour transmettre ce congé',
    'ErrorTransmitLeave' => 'Erreur de transmission de congé',
    'ErrorStoreLeave' => 'Erreur de stockage de congé',
    'SuccessGetClosedDay' => 'Jours fériés récupérés',
    'ErrorValidationData' => 'Erreur de validation des données',
    'ErrorGetClosedDay' => 'Erreur lors de la récupération des jours fériés',
    'SuccessGetHolidays' => 'Vacances récupérées',
    'ErrorGetHolidays' => 'Erreur lors de la récupération des vacances',
    'SuccessGetLeave' => 'Congé récupéré',
    'ErrorGetLeave' => 'Erreur lors de la récupération du congé',
    'ErrorDataArrayDontExist' => 'Données du tableau inexistantes',
    'FileRequired' => 'Fichier requis',
    'ActionNotAllowed' => 'Action non autorisée',
    'UserDontExist' => 'Utilisateur inexistant',
    'UserBelongToAnotherClient' => 'Utilisateur appartenant à un autre client',
    'ErrorDateLeaveTypeNotAllowed' => 'Erreur, la date n\'est pas autorisée pour ce type de congé',
    'DurationTooLong' => 'Durée trop longue',
    'ErrorValidateRefuseLeave' => 'Erreur de validation/refus de congé',
    'ErrorGetAttachment' => 'Erreur lors de la récupération du fichier joint',
    'CantGenerateFile' => 'Impossible de générer le fichier',
    'ErrorExportLeave' => 'Erreur lors de l\'exportation des congés',
    'ErrorDontOwnLeaveType'=>'Vous n\'avez pas le droit de modifier ce type de congé',
    'ErrorLeaveTypeNotExist'=>'Le type de congé n\'existe pas',
    'LeaveTypeAlreadyExist'=>'Ce type de congé existe déjà',
    'LeaveSubTypeDontBelongToCustomer'=>'La sous famille de type de congé ne fait pas partie du client',
    'ErrorUpdateLeaveType'=>'Erreur lors de la modification du type de congé',
    'SuccessUpdateLeaveType'=>'Modification du type de congé effectuée avec succès',
    'NotRightForGetLeaveType'=>'Vous n\'avez pas le droit de récupérer ce type de congé',
    'ErrorGetLeaveTypes'=>'Erreur lors de la récupération des types de congé',
    'SuccessGetLeaveType'=>'Récupération du type de congé effectuée avec succès',
    'SuccessStoreLeaveType'=>'Stockage du type de congé effectué avec succès',
    'ErrorStoreLeaveType'=>'Erreur lors du stockage du type de congé',
    'SuccessDeleteLeaveType'=>'Suppression du type de congé effectuée avec succès',
    'ErrorDeleteLeaveType'=>'Erreur lors de la suppression du type de congé',
    'ErrorLeaveTypeCantBeDeleted'=>'Ce type de congé ne peut pas être supprimé :leave_type_id',
    'ErrorGetManager'=>'Erreur lors de la récupération du manager',
    'ErrorStoreManager'=>'Erreur lors du stockage du manager',
    'ErrorUpdateManager'=>'Erreur lors de la modification du manager',
    'ErrorDeleteManager'=>'Erreur lors de la suppression du manager',
    'SuccessStoreToken'=>'Stockage du token effectué avec succès',
    'ErrorStoreToken'=>'Erreur lors du stockage du token',
    'SuccessClearToken'=>'Token effacé avec succès',
    'ErrorDontOwnSite'=>'Vous n\'avez pas le droit de modifier ce site',
    'ErrorGetSite'=>'Erreur lors de la récupération du site',
    'ValidationErrors'=>'Erreurs de validation',
    'SuccessGetSites'=>'Sites récupérés avec succès',
    'ErrorUpdateSite'=>'Erreur lors de la modification du site',
    'SuccessGetStatuses'=>'Statuts récupérés avec succès',
    'NotAccessToTag'=>'Vous n\'avez pas accès à ce tag',
    'SuccessGetTags'=>'Tags récupérés avec succès',
    'ErrorGetTags'=>'Erreur lors de la récupération des tags',
    'SuccessGetUser'=>'Utilisateur récupéré avec succès',
    'ErrorGetUsers'=>'Erreur lors de la récupération des utilisateurs',
    'SuccessUpdateTag'=>'Modification du tag effectuée avec succès',
    'ErrorUpdateTag'=>'Erreur lors de la modification du tag',
    'SuccessStoreTag'=>'Stockage du tag effectué avec succès',
    'ErrorStoreTag'=>'Erreur lors du stockage du tag',
    'ErrorDeleteTag'=>'Erreur lors de la suppression du tag',
    'SuccessDeleteTag'=>'Suppression du tag effectuée avec succès',
    'RelationAlreadyExist'=>'Relation déjà existante',
    'SuccessAddUserToTag'=>'Ajout de l\'utilisateur au tag effectué avec succès',
    'ErrorAddUserToTag'=>'Erreur lors de l\'ajout de l\'utilisateur au tag',
    'RelationDontExist'=>'Relation inexistante',
    'SuccessRemoveUserFromTag'=>'Retrait de l\'utilisateur du tag effectué avec succès',
    'ErrorRemoveUserFromTag'=>'Erreur lors du retrait de l\'utilisateur du tag',
    'SuccessUpdateUserTag'=>'Modification des tags de l\'utilisateur effectuée avec succès',
    'ErrorUpdateUserTag'=>'Erreur lors de la modification des tags de l\'utilisateur',
    'ErrorGetTeam'=>'Erreur lors de la récupération de l\'équipe',
    'ErrorStoreTeam'=>'Erreur lors du stockage de l\'équipe',
    'ErrorUpdateTeam'=>'Erreur lors de la modification de l\'équipe',
    'ErrorDeleteTeam'=>'Erreur lors de la suppression de l\'équipe',
    'SuccessImportFile'=>'Fichier importé avec succès',
    'ErrorImportFile'=>'Erreur lors de l\'importation du fichier',
    'ErrorGetTeamCompteur'=>'Erreur lors de la récupération du compteurs de l\'équipe',
    'UserDoesNotExist'=>'Utilisateur inexistant',
    'LeaveTypeDoesntExist'=>'Type de congé inexistant',
    'LeaveTypeBelongToAnotherClient'=>'Type de congé appartenant à un autre client',
    'UserLeaveCountAlreadyExist'=>'Le compteur de congé de l\'utilisateur existe déjà',
    'ErrorStoreCompteur'=>'Erreur lors du stockage du compteur',
    'UserLeaveCountDoesntExist'=>'Compteur de congé de l\'utilisateur inexistant',
    'UserLeaveCountBelongToAnotherClient'=>'Compteur de congé de l\'utilisateur appartenant à un autre client',
    'ErrorUpdateCompteur'=>'Erreur lors de la modification du compteur',
    'ErrorExportCompteur'=>'Erreur lors de l\'exportation du compteur',
    'ErrorImportLeave'=>'Erreur lors de l\'importation des congés',
    'ErrorSaveFileInMinio'=>'Erreur lors de l\'enregistrement du fichier dans MinIO',
    'ErrorSiteDoesntExist'=>'Site inexistant',
    'ErrorUserDontExist'=>'Utilisateur inexistant',
    'ErrorDontOwnUser'=>'Vous n\'avez pas le droit de modifier cet utilisateur',
    'ProfileDontExist'=>'Profil inexistant',
    'SiteDontExist'=>'Site inexistant',
    'ErrorUpdateUser'=>'Erreur lors de la modification de l\'utilisateur',
    'ErrorUpdateBusinessDay'=>'Erreur lors de la modification du jour commercial',
    'SuccessDeleteUser' =>'Utilisateur supprimé avec succès',
    'InvalidDateFormat'=>'Format de date invalide pour la date : :date',
    'NullDateFormat'=>'Date de début ou de fin est null',
    'LeaveTypeNotFound'=>'Type de congé non trouvé',
    'LeaveTypeClientMismatch'=>'Type de congé ne fait pas partie du client',
    'SubFamilyMismatch'=>'Sous famille de type de congé ne fait pas partie du client',
    'ErrorGetCurrentUser'=>'Erreur lors de la récupération de l\'utilisateur courant',
    'CurrentUser' => 'Utilisateur courant n\'a pas le bon profil',
    'ManagedByCurrentUser'=>'Vous n\'êtes pas managé par cet utilisateur',
    'ErrorGetUserFromApp'=>'Erreur lors de la récupération de l\'utilisateur depuis l\'application',
    'ErrorStoreLeaveTypeSubFamily'=>'Erreur lors du stockage de la sous famille de type de congé',
    'ErrorUpdateOtherCustomer'=>'Erreur lors de la modification d\'un autre client',
    'CantDisplayCustomerFromThisApp'=>'Vous ne pouvez pas afficher ce client depuis cette application',
    'ErrorStoreClient'=>'Erreur lors du stockage du client',
    'SuccessStoreCustomer'=>'Client créé avec succès',
    'ErrorStoreCustomer'=>'Erreur lors de la création du client',
    'ClientNotExist'=>'Client inexistant',
    'ErrorUpdateCustomer'=>'Erreur lors de la modification du client',
    'ErrorDeleteCustomer'=>'Erreur lors de la suppression du client',
    'SuccessUpdateLeaveDate'=>'Modification de la date de congé effectuée avec succès',
    'ErrorGetBusinessDay'=>'Erreur lors de la récupération du jour commercial',
    'ErrorGetValidationScheme'=>'Erreur lors de la récupération du schéma de validation',
    'ErrorGetDocs'=>'Erreur lors de la récupération des documents',
    'DocDontExistForSite'=>'Document inexistant pour ce site',
    'ErrorGetDoc'=>'Erreur lors de la récupération du document',
    'ErrorIdArrayDontExist'=>'Identifiant du tableau inexistant',
    'SiteDontExistForClient'=>'Site inexistant pour ce client',
    'ErrorFileDontExist'=>'Fichier inexistant',
    'ErrorStoreFile'=>'Erreur lors du stockage du fichier',
    'SuccessStoreDoc'=>'Document créé avec succès',
    'ErrorLoadFile'=>'Erreur lors du chargement du fichier',
    'DocDontExistForClient'=>'Document inexistant pour ce client',
    'CantDeleteForUpdateFile'=>'Impossible de supprimer ce document car il est utilisé pour mettre à jour un fichier',
    'CantDeleteFile'=>'Impossible de supprimer ce fichier',
    'ErrorDeleteFile'=>'Erreur lors de la suppression du fichier',
    'ErrorGetExportHistory'=>'Erreur lors de la récupération de l\'historique des exports',
    'ErrorNoHistoryForClient'=>'Aucune historique pour ce client',
    'ErrorDeleteExportHistory'=>'Erreur lors de la suppression de l\'historique des exports',
    'ErrorCancelLeave'=>'Erreur lors de l\'annulation du congé',
    'ErrorValidateCancelLeave'=>'Erreur lors de la validation de l\'annulation du congé',
    'SuccessGetLeaveTypes'=>'Types de congé récupérés avec succès',
    'SuccessGetSite'=>'Site récupéré avec succès',
    'SuccessUpdateSite'=>'Modification du site effectuée avec succès',
    'SuccessGetTag'=>'Tag récupéré avec succès',
    'ErrorGetTag'=>'Erreur lors de la récupération du tag',
    'TagDontExist'=>'Tag inexistant',
    'ErrorGetTeamFromApp'=>'Erreur lors de la récupération de l\'équipe depuis l\'application',
    'ErrorStoreTeamFromApp'=>'Erreur lors du stockage de l\'équipe depuis l\'application',
    'ErrorUpdateTeamFromApp'=>'Erreur lors de la modification de l\'équipe depuis l\'application',
    'ErrorDeleteTeamFromApp'=>'Erreur lors de la suppression de l\'équipe depuis l\'application',
    'InvalideData'=> 'Données invalides',
    'SuccessStoreUser' => 'Utilisateur enregistrer',
    'MustBeInteger'=>'Doit être un entier',
    'IsRequired'=> 'Est requis',
    'MustBeEnum'=>'Doit être un énumération',
    'MustBeString'=>'Doit etre une chaine de charactère',
    'MustBeBool'=>'Doit etre un booléen',
    'MustBeNotEmpty'=> 'Doit etre non vide',
    'MustBeNumber'=>'Doit etre un nombre',
    'MustBeEmail'=>'Doit etre un email',
    'MustBeUUID'=> 'Doit etre un UUID',
    'MustBeDate'=> 'Doit etre une date',
    'MustBeDateAfter'=>'Doit etre une date après',
    'MustBeDateBefore'=>'Doit etre une date avant',
    'MustRespectFormat'=>'Doit respecter le format',
    'UserNotFound'=>'Utilisateur non trouvé',
    'ErrorIdAbove'=>'L\'identifiant doit être supérieur à 0',
    'ErrorIdNumber'=>'L\'identifiant doit être un nombre',
    'ErrorFileType'=>'Le type de fichier est invalide',
    'ErrorFileSize'=>'La taille du fichier est trop grande',
    'ErrorSendAlert'=>'Erreur lors de l\'envoi de l\'alerte',
    'ErrorSaveHistoric'=>'Erreur lors de la sauvegarde de l\'historique',
    'ClientAlreadyExist'=>'Client déjà existant',
    'ExportNotFound'=>'L\'export n\'a pas été trouvé',
    'ReasonDontExist'=>'Raison non trouvée',
    'LeaveRefused'=>'Congé refusé',
    'UpdateDocIdError'=>'Erreur lors de la mise à jour de l\'identifiant du document',
    'CantFindUuid'=>'Impossible de trouver l\'uuid',
    'CantDoIt'=>'Vous n\'avez pas les droits pour effectuer cette action',
    'HeadersNotFound'=>'En-têtes non trouvés',
    'CustomerNotFound'=>'Client non trouvé',
    'ApiProctectedBySignature'=>'API protégée par signature',
    'ParameterNotAllowed' => "Le paramètre ':param_name' n'est pas autorisé sur cette route.",
    'RequiredValueForParameter'=>'La valeur pour le paramètre :name est obligatoire.',
    'UnsignedNumericValue'=>'Le format attendu pour :name est une valeur numérique non signée.',
    'RequiredBoolean'=>'Le format attendu pour :name est une valeur boolean 0/1.',
    'RequiredDate'=>'Le format attendu pour :name est une valeur date',
    'RequiredDateTime'=>'Le format attendu pour :name est une valeur datetime',
    'RequiredUuid'=>'Le format attendu pour :name est une valeur uuid',
    'RequiredString'=>'Le format attendu pour :name est une valeur string.',
    'InvalidValue' => "La valeur ':value' n'est pas autorisée. Valeurs autorisées : :allowed_values.",
    'DirectionAscDesc'=>'La direction doit être soit ascendant (asc) soit descendant (desc).',
    'LackDataForStore'=>'Il vous manque des données pour sauvegarder.',
    'FileWeight'=>'Le fichier est trop volumineux: 5mo maximum.',
    'FileTypeJpgPng'=>'Le type de fichier n\'est pas supporté, JPG ou PNG seulement.',
    'MustFilterById'=>'Vous devez filtrer par id',
    'ClientTableNotFound'=>'La table des clients n\'existe pas',
    'LeaveTypeNeedFile'=>'Ce type de congé a besoin d\'un fichier',
    'UserNotExist' => 'L\'utilisateur avec le matricule :matricule n\'existe pas',
    'UserDeleted' => 'L\'utilisateur avec le matricule :matricule a été supprimé',
    'SiteNotExist' => 'Le site de l\'utilisateur :matricule n\'existe pas',
    'LeaveNotCreated' => 'Le congés de l\'utilisateur :matricule n\'a pas pu être créé',
    'LastNameMissingFromSalarie'=> 'Feuille :sheetnbmr Ligne :line : Le nom de famille est manquant dans la colonne Salarié (valeur: ":salarie").',
    'EmailMissingInvalidNormalized' => 'Feuille :sheetnbmr Ligne :line : L\'email est manquant ou invalide après normalisation (email original : ":original_email").',
    'EmailFormatInvalidNormalized' => 'Feuille :sheetnbmr Ligne :line : Format d\'email invalide après normalisation (email normalisé: ":normalized_email", email original : ":original_email").',
    'UserNotFoundImport' => 'Feuille :sheetnbmr Ligne :line : Utilisateur non trouvé avec l\'email normalisé ":normalized_email" (email original : ":original_email").',
    'UserLastNameMismatch' => 'Feuille :sheetnbmr Ligne :line : Le nom pour l\'utilisateur (email : :email) ne correspond pas. Nom attendu (BDD) : ":expected_name", nom trouvé (fichier) : ":name_from_file".',
    'UserMissingSite' => 'Feuille :sheetnbmr Ligne :line : Le site est manquant pour l\'utilisateur avec l\'email normalisé ":normalized_email".',
    'SoldeRttInitialInvalidOrMissing' => 'Feuille :sheetnbmr Ligne :line : Le solde RTT initial est invalide ou manquant (valeur: ":value").',
    'AcquisRttInvalidOrMissing' => 'Feuille :sheetnbmr Ligne :line : La valeur \'Acquis RTT\' est invalide ou manquante (valeur: ":value").',
    'PrisRttInvalidOrMissing' => 'Feuille :sheetnbmr Ligne :line : La valeur \'Pris RTT\' est invalide ou manquante (valeur: ":value").',
    'SoldeRttFinalInvalidOrMissing' => 'Feuille :sheetnbmr Ligne :line : Le solde RTT final est invalide ou manquant (valeur: ":value").',
    'RttBalanceMismatch' => 'Feuille :sheetnbmr Ligne :line pour :email : Incohérence du solde RTT. Solde fichier: :solde_fichier, Solde calculé: :solde_calcule.',
    'SoldeCpInvalidOrMissing'=> 'Feuille :sheetnbmr Ligne :line : Solde CP invalide ou manquant (valeur: :value).',

    'CpAcquiredMismatchDetected' => '[X] - Feuille :sheet, Ligne :line : Compteur CP pour :user_lastname erroné : CP acquis (:file_acquired_value) en provenance du fichier ne correspond pas à la valeur du nombre de CP acquis actuel (:db_acquired_value).',
    'CpAnnualUpdate' => 'Feuille :sheet, Ligne :line : Compteur CP pour :user_lastname mis à jour. Nouveaux CP acquis : :new_acquired_value.',
    'CpNewCounterCreated' => 'Feuille :sheet, Ligne :line : Compteur CP pour :user_lastname créé avec :acquired_value CP acquis.',
    'DbErrorCpUpdate' => 'Feuille :sheet, Ligne :line : Erreur base de données lors de la mise à jour du compteur CP (:leave_type_code). Détails : :db_message',

    'ErrorLeaveStatusDontAllowValidation' => 'Erreur, le statut du congé ne permet pas sa validation',
    'ErrorLeaveStatusDontAllowModification' => 'Erreur, le statut du congé ne permet pas sa modification',
];
