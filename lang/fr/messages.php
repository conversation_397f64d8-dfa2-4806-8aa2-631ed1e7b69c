<?php

return [
    'morning' => ' (<PERSON><PERSON>)',
    'afternoon' => ' (<PERSON><PERSON>-mid<PERSON>)',
    'CreateSettingsSuccess' => 'Création des paramètres réussie',
    'RemoveSettingsSuccess' => 'Les paramètres ont été supprimés avec succès.',
    'UpdateSuccess' => 'Mise à jour réussie',
    'WaitingPay' => ' En attente du service paie',
    'WaitingValidationManager' => 'En attente de validation par : :user_firstname :user_lastname',
    'WaitingValidationManagers' => 'En attente de validation par l\'un de vos managers',
    'WaitingCancelationManager' => 'En attente d\'annulation par : :user_firstname :user_lastname',
    'WaitingCancelationManagers' => 'En attente d\'annulation par l\'un de vos managers',
    'Waiting' => 'En attente',
    'InProcess' => 'En cours',
    'SuccessStoreLeave'=>'Votre demande a bien été créée',
    'SuccessCancelLeave'=>'Congé annulé',
    'AskForCancelLeave'=>'Demande d\'annulation de congé envoyée',
    'SuccessCallback'=>'Rappel réussi',
    'CantCancelLeaveNotValidated'=>'Impossible d\'annuler ce congé car il n\'a pas été validé',
    'CantCancelLeaveTransmitted'=>'Impossible d\'annuler ce congé car il a été transmis',
    'CantCancelLeaveRefused'=>'Impossible d\'annuler ce congé car il a été refusé',
    'LeaveCanceled'=>'Congé annulé',
    'DontOwnLeaveOrManagerUser'=>'Vous n\'avez pas le droit de modifier ce congé ou vous n\'êtes pas manager de cet utilisateur',
    'CantCancelLeaveValidated'=> 'Impossible d\'annuler ce congé car il a été validé',
    'NotAdminFromUser'=>'Vous n\'êtes pas administrateur de cet utilisateur',
    'ErrorTransmisLeaveWrongStatus'=>'Erreur, le congé ne peut pas être transmis car il a une mauvaise statut',
    'SuccessRefuseLeave'=>'Congé refusé',
    'SuccessMassValidationLeave'=>'Congés transmis',
    'SuccessTransmitLeave'=>'Congé transmis',
    'SuccessGetClosedDay'=>'Jours fériés récupérés',
    'SuccessGetHolidays'=>'Vacances récupérées',
    'SuccessGetLeave'=>'Congé récupéré',
    'SuccessGetUser'=>'Utilisateur récupéré',
    'SuccessGetCompteur'=>'Compteurs récupérés',
    'SuccessStoreCompteur'=>'Compteur créé',
    'SuccessUpdateCompteur'=>'Compteur mis à jour',
    'SuccessUpdateUser'=>'Utilisateur mis à jour',
    'SuccessUpdateBusinessDay'=>'Jour de travail mis à jour',
    'SuccessGetCurrentUser'=>'Utilisateur courant récupéré',
    'SuccessLogout'=>'Déconnexion réussie',
    'SuccessUpdateCustomer'=>'Client mis à jour',
    'SuccessDeleteCustomer'=>'Client supprimé',
    'SuccessGetBusinessDay' => 'Jour de travail récupéré',
    'SuccessGetDocs' => 'Documents récupérés',
    'SuccessUpdateDoc' => 'Document mis à jour',
    'SuccessDeleteFile' => 'Fichier supprimé',
    'SuccessGetHistory' => 'Historique récupéré',
    'SuccessDeleteHistory'=>'Historique supprimé',
    'CantCancelLeave'=>'Impossible d\'annuler ce congé',
    'OtherAdminValidated'=> 'Un autre admin a déjà validé ce congé',
    'UserNotInClient'=>'L\'utilisateur n\'est pas dans votre client',
    'SuccessAttachUsersToDirector'=>'Utilisateurs attachés au directeur',
    'SuccessDetachUsersToDirector'=>'Utilisateurs détachés du directeur',
    'SuccessStoreLeaveByTakingInNCounts'=>'Congé créé en prenant le compteur N',
    'SuccessStoreHoliday'=>'Les jours fériés ont bien été créés',
    'UserWithNoTags' => 'Collaborateurs sans équipes',
    'UserNotManaged'=>'Collaborateurs non managés',
    'success_store_leave_with_errors' => 'Les congés pour les collaborateurs ont été créés avec succès, toutefois, des erreurs sont survenues.',
    'AnyTags' => 'Aucune équipe',
    'ImportSucces' => 'Import réussi',
    'ErrorImport' =>'Erreur lors de l\'import',
    'acquis_cp_n_1' => 'acquis_cp_n_1',
    'pris_cp_n_1' => 'pris_cp_n_1',
    'solde_cp_n_1' => 'solde_cp_n_1',
    'acquis_cp_n' => 'acquis_cp_n',
    'pris_cp_n' => 'pris_cp_n',
    'solde_cp_n' => 'solde_cp_n',
    'acquis_type_n_1' => 'acquis_:type_n_1',
    'pris_type_n_1' => 'pris_:type_n_1',
    'solde_type_n_1' => 'solde_:type_n_1',
    'acquis_type_n' => 'acquis_:type_n',
    'pris_type_n' => 'pris_:type_n',
    'solde_type_n' => 'solde_:type_n',
    'ImportUserLeaveCountPartiallySucces' => 'Des erreurs sont survenues durant l\'import des compteurs (:successRows / :rows lignes importées).',
    'ImportUserLeavesPartiallySucces' => 'Des erreurs sont survenues durant l\'import des absences (:successRows / :rows lignes importées).',
    'RegistrationNumber' => 'Matricule',
    'RegistrationNumberEmployee' => 'Matricule Salarié',
    'CompanyName' => 'Raison Sociale',
    'MainEstablishment' => 'Etablissement principal',
    'Email' => 'Email',
    'Employee' => 'Salarié',
    'EmployeeName' => 'Nom Prénom',
    'EmployeeLastname' => 'Nom',
    'EmployeeFirstname' => 'Prénom',
    'Establishment' => 'Établissement',
    'EntryDate' => 'Date entrée',
    'Acquired' => 'Acquis',
    'Taken' => 'Pris',
    'Balance' => 'Solde',
    'Label' => 'Libellé',
    'CodeConges' => 'Code congé',
    'CodeAbsence' => 'Code absence',
    'StartDate' => 'Date de début',
    'EndDate' => 'Date de fin',
    'Duration' => 'Nbr jour(s)',
    'Status' => 'Statut',
    'Site' => 'Site',
    'TypeConges' => 'Type',
    'TypeCongesAbsence' => 'Type d\'absence/congés',
    'TypeAbsence' => 'Type d\'absence',
    'Tag' => 'Équipe',
    'HeaderRegistrationNumber' => 'matricule',
    'HeaderRegistrationNumberEmployee' => 'matricule_salarie',
    'HeaderCompanyName' => 'raison_sociale',
    'HeaderEstablishment' => 'etablissement',
    'HeaderMainEstablishment' => 'etablissement_principal',
    'HeaderEmail' => 'email',
    'HeaderEmployee' => 'salarie',
    'HeaderLastname' => 'nom',
    'HeaderFirstname' => 'prénom',
    'HeaderEntryDate' => 'date_entree',
    'HeaderEntryDdate' => 'date_dentree',
    'HeaderStartDate' => 'date_debut',
    'HeaderEndDate' => 'date_fin',
    'HeaderDuration' => 'duration',
    'HeaderAcquired' => 'acquis',
    'HeaderTaken' => 'pris',
    'HeaderBalance' => 'solde',
    'HeaderRemaining' => 'restants',
    'HeaderLeaveTypeName' => 'type conges',
    'Total' => 'Total',
    'TotalGeneral' => 'Total général',
    'store_leave_with_errors' => 'Une erreur semble être subvenue lors de la pose des congés...',
    'waiting_validation_by_manager' => 'En attente de validation par :manager',
    'waiting_cancellation_by_manager' => 'En attente d\'annulation par :manager',
    'VALIDATED' => 'Validé',
    'SUBMITTED' => 'Soumis à validation',
    'REFUSED' => 'Refusée',
    'CANCELED' => 'Annulé',
    'TRANSMITTED' => 'Transmis en paie',
    'SUBMITTED_TO_CANCELLATION' => 'Soumis à annulation',
    'ImportPartiallySucces'=>'L\'import a réussi partiellement',
    'UnsupportedFileType' => 'Type de fichier non supporté. Veuillez utiliser un fichier CSV, XLSX, ou XLS.',
    'import' => [
        'error_title' => 'Erreur de saisie',
        'error_value' => 'La valeur ne se trouve pas dans la liste.',
        'prompt_title' => 'Choisissez dans la liste',
        'prompt_value' => 'Veuillez choisir une valeur dans la liste déroulante.',
    ],
    'AlreadyTreatedLeavesCount' => 'Nombre de congés déjà traités',
    'WaitingValidationLeavesCount' => 'Nombre de congés en attente de validation',
    'WaitingCancelationLeavesCount' => 'Nombre de congés en attente d\'annulation',
    'SuccessCancelLeaveSubToCancel'=> 'La demande d\'annulation a bien été validée, le congé est donc annulé.',
    'SuccessValidateLeaveSubToCancel'=> 'La demande d\'annulation a bien été validée, le congé est donc annulé',
    'SuccessValidateLeaveSubToCancelValidated' => 'La demande d\'annulation a été refusée. Le congé est revenu à son statut \'Validé\'.',
    'SuccessValidateLeaveSubToCancelSubmitted' => 'La demande d\'annulation a été refusée. Le congé est revenu à son statut \'Soumis\'.',
    'SuccessMassValidationLeaveSubToCancel'=> 'Les demandes d\'annulation ont bien été validées, les congés sont donc annulés',
    'CantCancelAdminOwnLeave'=>'Un administrateur ne peut pas annuler ses propres congés',
    'SuccessTransmittedLeaveCanceled'=> 'La demande d\'annulation a bien été validée, le congé est donc \'Annulé\'.'
];
