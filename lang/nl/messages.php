<?php

return [
    'morning' => 'Ochtend',
    'afternoon' => 'Middag',
    'CreateSettingsSuccess' => 'Instellingen succesvol aangemaakt',
    'RemoveSettingsSuccess' => 'Instellingen succesvol verwijderd',
    'UpdateSuccess' => 'Succesvolle update',
    'WaitingPay' => 'Wachten op betaling',
    'Waiting' => 'In afwachting',
    'InProcess' => 'In behandeling',
    'SuccessStoreLeave' => 'Verlof succesvol aangemaakt',
    'SuccessCancelLeave' => 'Verlof succesvol geannuleerd',
    'AskForCancelLeave' => 'Verlofannulering aangevraagd',
    'SuccessCallback' => 'Terugbelverzoek succesvol',
    'CantCancelLeaveNotValidated' => 'Kan dit verlof niet annuleren omdat het niet gevalideerd is',
    'CantCancelLeaveTransmitted' => 'Kan dit verlof niet annuleren omdat het al verzonden is',
    'CantCancelLeaveRefused' => 'Kan dit verlof niet annuleren omdat het geweigerd is',
    'LeaveCanceled' => 'Verlof geannuleerd',
    'DontOwnLeaveOrManagerUser' => 'U heeft geen recht om dit verlof te wijzigen of u bent geen manager van deze gebruiker',
    'CantCancelLeaveValidated' => 'Kan dit verlof niet annuleren omdat het gevalideerd is',
    'NotAdminFromUser' => 'U bent geen beheerder van deze gebruiker',
    'ErrorTransmisLeaveWrongStatus' => 'Fout, verlof kan niet worden verzonden omdat het de verkeerde status heeft',
    'SuccessRefuseLeave' => 'Verlof geweigerd',
    'SuccessMassValidationLeave' => 'Verlof succesvol verzonden',
    'SuccessTransmitLeave' => 'Verlof succesvol verzonden',
    'SuccessGetClosedDay' => 'Feestdagen succesvol opgehaald',
    'SuccessGetHolidays' => 'Vakanties succesvol opgehaald',
    'SuccessGetLeave' => 'Verlof succesvol opgehaald',
    'SuccessGetUser' => 'Gebruiker succesvol opgehaald',
    'SuccessGetCompteur' => 'Tellers succesvol opgehaald',
    'SuccessStoreCompteur' => 'Teller succesvol aangemaakt',
    'SuccessUpdateCompteur' => 'Teller succesvol geüpdatet',
    'SuccessUpdateUser' => 'Gebruiker succesvol geüpdatet',
    'SuccessUpdateBusinessDay' => 'Werkdag succesvol geüpdatet',
    'SuccessGetCurrentUser' => 'Huidige gebruiker succesvol opgehaald',
    'SuccessLogout' => 'Succesvol uitgelogd',
    'SuccessUpdateCustomer' => 'Klant succesvol geüpdatet',
    'SuccessDeleteCustomer' => 'Klant succesvol verwijderd',
    'SuccessGetBusinessDay' => 'Werkdag succesvol opgehaald',
    'SuccessGetDocs' => 'Documenten succesvol opgehaald',
    'SuccessUpdateDoc' => 'Document succesvol geüpdatet',
    'SuccessDeleteFile' => 'Bestand succesvol verwijderd',
    'SuccessGetHistory' => 'Geschiedenis succesvol opgehaald',
    'SuccessDeleteHistory' => 'Geschiedenis succesvol verwijderd',
    'CantCancelLeave' => 'Kan dit verlof niet annuleren',
    'OtherAdminValidated' => 'Een andere beheerder heeft dit verlof al gevalideerd',
    'UserNotInClient' => 'De gebruiker is niet in uw klant',
    'SuccessAttachUsersToDirector' => 'Gebruikers succesvol aan directeur gekoppeld',
    'SuccessDetachUsersToDirector' => 'Gebruikers succesvol van directeur losgekoppeld',
    'SuccessStoreLeaveByTakingInNCounts' => 'Verlof succesvol aangemaakt met teller N',
    'SuccessStoreHoliday'=>'De feestdagen zijn succesvol aangemaakt',
    'UserWithNoTags' => 'Gebruikers zonder teams',
    'UserNotManaged' => 'Niet beheerde gebruikers',
    'success_store_leave_with_errors' => 'Verlof succesvol aangemaakt voor gebruikers, echter, er zijn fouten opgetreden.',
    'AnyTags' => 'Geen team',
    'ImportSucces' => 'Import succesvol',
    'ErrorImport' => 'Fout bij importeren',
    'acquis_cp_n_1' => 'acquis_cp_n_1',
    'pris_cp_n_1' => 'pris_cp_n_1',
    'solde_cp_n_1' => 'solde_cp_n_1',
    'acquis_cp_n' => 'acquis_cp_n',
    'pris_cp_n' => 'pris_cp_n',
    'solde_cp_n' => 'solde_cp_n',
    'acquis_type_n_1' => 'acquis_:type_n_1',
    'pris_type_n_1' => 'pris_:type_n_1',
    'solde_type_n_1' => 'solde_:type_n_1',
    'acquis_type_n' => 'acquis_:type_n',
    'pris_type_n' => 'pris_:type_n',
    'solde_type_n' => 'solde_:type_n',
    'ImportUserLeaveCountPartiallySucces' => 'Er zijn fouten opgetreden tijdens het importeren van de tellers (:successRows / :rows regels geïmporteerd).',
    'ImportUserLeavesPartiallySucces' => 'Er zijn fouten opgetreden tijdens het importeren van de afwezigheden (:successRows / :rows regels geïmporteerd).',
    'RegistrationNumber' => 'Registratienummer',
    'RegistrationNumberEmployee' => 'Medewerkernummer',
    'CompanyName' => 'Bedrijfsnaam',
    'MainEstablishment' => 'Hoofvestiging',
    'Email' => 'E-mail',
    'Employee' => 'Medewerker',
    'EmployeeName' => 'Naam Voornaam',
    'EmployeeLastname' => 'Achternaam',
    'EmployeeFirstname' => 'Voornaam',
    'Establishment' => 'Vestiging',
    'EntryDate' => 'Ingangsdatum',
    'Acquired' => 'Verkregen',
    'Taken' => 'Genomen',
    'Balance' => 'Saldo',
    'Label' => 'Label',
    'CodeConges' => 'Verlofcode',
    'CodeAbsence' => 'Afwezigheidscode',
    'StartDate' => 'Startdatum',
    'EndDate' => 'Einddatum',
    'Duration' => 'Aantal dagen',
    'Status' => 'Status',
    'Site' => 'Locatie',
    'TypeCongesAbsence' => 'Type afwezigheid/verlof',
    'TypeAbsence' => 'Type afwezigheid',
    'TypeConges' => 'Type',
    'Tag' => 'Ploeg',
    'HeaderRegistrationNumber' => 'registratienummer',
    'HeaderRegistrationNumberEmployee' => 'medewerkernummer',
    'HeaderCompanyName' => 'bedrijfsnaam',
    'HeaderEstablishment' => 'vestiging',
    'HeaderMainEstablishment' => 'hoofdvestiging',
    'HeaderEmail' => 'e-mail',
    'HeaderEmployee' => 'medewerker',
    'HeaderLastname' => 'achternaam',
    'HeaderFirstname' => 'voornaam',
    'HeaderEntryDate' => 'ingangsdatum',
    'HeaderEntryDdate' => 'datum_ingang',
    'HeaderStartDate' => 'startdatum',
    'HeaderEndDate' => 'einddatum',
    'HeaderDuration' => 'duur',
    'HeaderAcquired' => 'verkregen',
    'HeaderTaken' => 'genomen',
    'HeaderBalance' => 'betalen',
    'HeaderRemaining' => 'overig',
    'HeaderLeaveTypeName' => 'type_verlof',
    'Total' => 'Totaal',
    'TotalGeneral' => 'Algemeen totaal',
    'store_leave_with_errors' => 'Er lijkt een fout te zijn opgetreden bij het aanvragen van verlof...',
    'waiting_validation_by_manager' => 'Wachten op goedkeuring door :manager',
    'waiting_cancellation_by_manager' => 'Wachten op annulering door :manager',
    'VALIDATED' => 'Goedgekeurd',
    'SUBMITTED' => 'Ingediend ter goedkeuring',
    'REFUSED' => 'Weigeren',
    'CANCELED' => 'Geannuleerd',
    'TRANSMITTED' => 'Verzonden naar loonadministratie',
    'SUBMITTED_TO_CANCELLATION' => 'Ingediend voor annulering',
    'ImportPartiallySucces' => 'Import gedeeltelijk succesvol',
    'UnsupportedFileType' => 'Bestandstype wordt niet ondersteund. Gebruik een CSV-, XLSX- of XLS-bestand.',
    'import' => [
        'error_title' => 'Invoerfout',
        'error_value' => 'De waarde staat niet in de lijst.',
        'prompt_title' => 'Kies uit de lijst',
        'prompt_value' => 'Kies een waarde uit de vervolgkeuzelijst.',
    ],
    'AlreadyTreatedLeavesCount' => 'Aantal reeds behandelde verlofdagen',
    'WaitingValidationLeavesCount' => 'Aantal verlofdagen in afwachting van validatie',
    'WaitingCancelationLeavesCount' => 'Aantal verlofdagen in afwachting van annulering',
    'SuccessCancelLeaveSubToCancel'=> 'Het verzoek tot annulering is afgewezen. Het verlof is nu geannuleerd.',
    'SuccessValidateLeaveSubToCancel'=> 'Het annuleringsverzoek is succesvol gevalideerd. Het verlof is nu geannuleerd.',
    'SuccessValidateLeaveSubToCancelValidated' => 'Het verzoek om annulering is geweigerd. Het verlof is teruggekeerd naar zijn status \'Gevalideerd\'.',
    'SuccessValidateLeaveSubToCancelSubmitted' => 'Het verzoek om annulering is geweigerd. Het verlof is teruggekeerd naar zijn status \'Ingediend\'.',
    'SuccessMassValidationLeaveSubToCancel'=> 'De annuleringsverzoeken zijn succesvol gevalideerd, de verloven zijn daarom geannuleerd.',
    'CantCancelAdminOwnLeave'=>'Een beheerder kan zijn eigen verlof niet annuleren',
    'SuccessTransmittedLeaveCanceled'=> 'Het annuleringsverzoek is succesvol gevalideerd, de verlof is daarom \'Geannuleerd\'.',
    'SheetIgnored' => "ExtraCom Import: Blad ':sheetName' genegeerd.",
    'CpTypeNotFound' => 'Blad :sheetnbmr Regel :line: CP-verloftype niet gevonden voor de site.',
    'RttTypeNotFound' => 'Blad :sheetnbmr Regel :line: RTT-verloftype niet gevonden voor de site.',

];
