<?php
return [
    'env' => [
        'ActionIsNotAvailableInEnv' => 'Deze actie is niet beschik<PERSON>ar in de omgeving :env',
    ],
    'ClientTableDosntExist' => 'De klantentabel bestaat niet',
    'UnauthorizedAction' => 'Niet-geautoriseerde actie',
    'SettingsAlreadyExists' => 'Instellingen bestaan al',
    'ErrorBatchUpdateTags' => 'Fout tijdens batch-update van tags',
    'TagAlreadyExists' => 'De tag bestaat al',
    'BadProfile' => 'Ongeldig profiel',
    'ProfileNotFound' => 'Profiel niet gevonden',
    'InternalError' => 'Interne fout',
    'DellUsersSuccess' => 'Gebruikers succesvol verwijderd',
    'AddUsersSuccess' => 'Gebruikers succesvol toegevoegd',
    'LogoutSuccess' => 'Uitloggen succesvol',
    'UserAlreadyTreatedLeave' => 'Je hebt dit verlof al gevalideerd',
    'NoLeaveCounter' => 'Geen verlofteller gevonden',
    'NotEnoughLeaveDay' => 'Niet genoeg verlofdagen',
    'UserDontMatchJwt' => 'De gebruiker komt niet overeen met het JWT-token',
    'LeaveAlreadyExisteForDate' => 'De gevraagde datum is al bezet door een ander verzoek',
    'DurationRequired' => 'Duur vereist',
    'DurationMustBeANumber' => 'De duur moet een getal zijn',
    'DurationMustBePositive' => 'De duur moet positief zijn',
    'ErrorLeaveDuration' => 'Fout in verlofduur',
    'ErrorLeaveDurationHalfDayNotPermit' => 'Fout in verlofduur, halve dagen zijn niet toegestaan',
    'GetLeaveType' => 'Verloftype ophalen',
    'ErrorLeaveTypeDontExist' => 'Fout, het verloftype bestaat niet',
    'ErrorLeaveTypeDontBelongToClient' => 'Fout, het verloftype behoort niet tot de klant',
    'ErrorGetStatuses' => 'Fout bij het ophalen van statussen',
    'ErrorStatusDontExist' => 'Status bestaat niet',
    'ErrorLeaveTypeSubFamilyDontExist' => 'Fout, de subfamilie van het verloftype bestaat niet',
    'ErrorLeaveTypeSubFamilyDontBelongToLeave' => 'Fout, de subfamilie van het verloftype behoort niet tot het verloftype',
    'ErrorStartDateFormat' => 'Onjuist startdatumformaat',
    'ErrorEndDateFormat' => 'Onjuist einddatumformaat',
    'ErrorStartDateRequired' => 'Startdatum vereist',
    'ErrorEndDateRequired' => 'Einddatum vereist',
    'ErrorStartDateAboveEndDate' => 'De startdatum is groter dan de einddatum',
    'ErrorSameDate' => 'Dezelfde start- en einddatum',
    'DontOwnLeave' => 'Je hebt niet het recht om dit verlof te valideren',
    'LeaveDontExist' => 'Verlof bestaat niet',
    'NotAdminFromUser' => 'Je bent geen beheerder van deze gebruiker',
    'NotAdminOrManagerFromUser' => 'Je bent noch beheerder noch manager van deze gebruiker',
    'NotDirectorOrManagerFromUser' => 'U bent noch directeur noch beheerder van deze gebruiker',
    'CantRefuseLeaveWrongStatus' => 'Kan dit verlof niet weigeren omdat het een verkeerde status heeft',
    'ErrorNotValidatedByLastManager' => 'Fout, het verlof is niet gevalideerd door de laatste manager',
    'ErrorLeaveStatusDontAllowValidation' => 'Fout, de verlofstatus staat geen validatie toe',
    'NotManagerFromUser' => 'Je bent geen manager van deze gebruiker',
    'SuccessValidateLeave' => 'Verlof gevalideerd',
    'SuccessMassValidationLeave' => 'Verloven gevalideerd',
    'ErrorValidateLeave' => 'Fout bij verlofvalidatie',
    'ErrorLeaveDontExist' => 'Verlof bestaat niet',
    'NoManagerOrAdminForRefuseLeave' => 'Er is geen manager of beheerder om dit verlof te weigeren',
    'ErrorRefuseLeave' => 'Fout bij het weigeren van verlof',
    'ErrorActionNotAllowed' => 'Actie niet toegestaan',
    'ErrorNoAdminForTransmit' => 'Fout, geen beheerder om dit verlof te verzenden',
    'ErrorTransmitLeave' => 'Fout bij het verzenden van verlof',
    'ErrorStoreLeave' => 'Fout bij het opslaan van verlof',
    'SuccessGetClosedDay' => 'Feestdagen opgehaald',
    'ErrorValidationData' => 'Fout bij gegevensvalidatie',
    'ErrorGetClosedDay' => 'Fout bij het ophalen van feestdagen',
    'SuccessGetHolidays' => 'Vakanties opgehaald',
    'ErrorGetHolidays' => 'Fout bij het ophalen van vakanties',
    'SuccessGetLeave' => 'Verlof opgehaald',
    'ErrorGetLeave' => 'Fout bij het ophalen van verlof',
    'ErrorDataArrayDontExist' => 'Gegevensarray bestaat niet',
    'FileRequired' => 'Bestand vereist',
    'ActionNotAllowed' => 'Actie niet toegestaan',
    'UserDontExist' => 'Gebruiker bestaat niet',
    'UserBelongToAnotherClient' => 'Gebruiker behoort tot een andere klant',
    'ErrorDateLeaveTypeNotAllowed' => 'Fout, de datum is niet toegestaan voor dit verloftype',
    'DurationTooLong' => 'Duur te lang',
    'ErrorValidateRefuseLeave' => 'Fout bij validatie/weigering van verlof',
    'ErrorGetAttachment' => 'Fout bij het ophalen van bijlage',
    'CantGenerateFile' => 'Kan bestand niet genereren',
    'ErrorExportLeave' => 'Fout bij het exporteren van verloven',
    'ErrorDontOwnLeaveType' => 'Je hebt niet het recht om dit verloftype te wijzigen',
    'ErrorLeaveTypeNotExist' => 'Het verloftype bestaat niet',
    'LeaveTypeAlreadyExist' => 'Dit verloftype bestaat al',
    'LeaveSubTypeDontBelongToCustomer' => 'De subfamilie van het verloftype behoort niet tot de klant',
    'ErrorUpdateLeaveType' => 'Fout bij het bijwerken van het verloftype',
    'SuccessUpdateLeaveType' => 'Verloftype succesvol bijgewerkt',
    'NotRightForGetLeaveType' => 'Je hebt niet het recht om dit verloftype op te halen',
    'ErrorGetLeaveTypes' => 'Fout bij het ophalen van verloftypen',
    'SuccessGetLeaveType' => 'Verloftype succesvol opgehaald',
    'SuccessStoreLeaveType' => 'Verloftype succesvol opgeslagen',
    'ErrorStoreLeaveType' => 'Fout bij het opslaan van het verloftype',
    'SuccessDeleteLeaveType' => 'Verloftype succesvol verwijderd',
    'ErrorDeleteLeaveType' => 'Fout bij het verwijderen van het verloftype',
    'ErrorLeaveTypeCantBeDeleted' => 'Dit verloftype kan niet worden verwijderd :leave_type_id',
    'ErrorGetManager' => 'Fout bij het ophalen van manager',
    'ErrorStoreManager' => 'Fout bij het opslaan van manager',
    'ErrorUpdateManager' => 'Fout bij het bijwerken van manager',
    'ErrorDeleteManager' => 'Fout bij het verwijderen van manager',
    'SuccessStoreToken' => 'Token succesvol opgeslagen',
    'ErrorStoreToken' => 'Fout bij het opslaan van token',
    'SuccessClearToken' => 'Token succesvol gewist',
    'ErrorDontOwnSite' => 'Je hebt niet het recht om deze site te wijzigen',
    'ErrorGetSite' => 'Fout bij het ophalen van site',
    'ValidationErrors' => 'Validatiefouten',
    'SuccessGetSites' => 'Sites succesvol opgehaald',
    'ErrorUpdateSite' => 'Fout bij het bijwerken van site',
    'SuccessGetStatuses' => 'Statussen succesvol opgehaald',
    'NotAccessToTag' => 'Je hebt geen toegang tot deze tag',
    'SuccessGetTags' => 'Tags succesvol opgehaald',
    'ErrorGetTags' => 'Fout bij het ophalen van tags',
    'SuccessGetUser' => 'Gebruiker succesvol opgehaald',
    'ErrorGetUsers' => 'Fout bij het ophalen van gebruikers',
    'TagDontExist' => 'Tag bestaat niet',
    'SuccessUpdateTag' => 'Tag succesvol bijgewerkt',
    'ErrorUpdateTag' => 'Fout bij het bijwerken van tag',
    'SuccessStoreTag' => 'Tag succesvol opgeslagen',
    'ErrorStoreTag' => 'Fout bij het opslaan van tag',
    'ErrorDeleteTag' => 'Fout bij het verwijderen van tag',
    'SuccessDeleteTag' => 'Tag succesvol verwijderd',
    'RelationAlreadyExist' => 'Relatie bestaat al',
    'SuccessAddUserToTag' => 'Gebruiker succesvol toegevoegd aan tag',
    'ErrorAddUserToTag' => 'Fout bij het toevoegen van gebruiker aan tag',
    'RelationDontExist' => 'Relatie bestaat niet',
    'SuccessRemoveUserFromTag' => 'Gebruiker succesvol verwijderd van tag',
    'ErrorRemoveUserFromTag' => 'Fout bij het verwijderen van gebruiker van tag',
    'SuccessUpdateUserTag' => 'Gebruikerstags succesvol bijgewerkt',
    'ErrorUpdateUserTag' => 'Fout bij het bijwerken van gebruikerstags',
    'ErrorGetTeam' => 'Fout bij het ophalen van team',
    'ErrorStoreTeam' => 'Fout bij het opslaan van team',
    'ErrorUpdateTeam' => 'Fout bij het bijwerken van team',
    'ErrorDeleteTeam' => 'Fout bij het verwijderen van team',
    'SuccessImportFile' => 'Bestand succesvol geïmporteerd',
    'ErrorImportFile' => 'Fout bij het importeren van bestand',
    'ErrorGetTeamCompteur' => 'Fout bij het ophalen van teamtellers',
    'UserDoesNotExist' => 'Gebruiker bestaat niet',
    'LeaveTypeDoesntExist' => 'Verloftype bestaat niet',
    'LeaveTypeBelongToAnotherClient' => 'Verloftype behoort tot een andere klant',
    'UserLeaveCountAlreadyExist' => 'Gebruikersverlofteller bestaat al',
    'ErrorStoreCompteur' => 'Fout bij het opslaan van teller',
    'UserLeaveCountDoesntExist' => 'Gebruikersverlofteller bestaat niet',
    'UserLeaveCountBelongToAnotherClient' => 'Gebruikersverlofteller behoort tot een andere klant',
    'ErrorUpdateCompteur' => 'Fout bij het bijwerken van teller',
    'ErrorExportCompteur' => 'Fout bij het exporteren van teller',
    'ErrorImportLeave' => 'Fout bij het importeren van verloven',
    'ErrorSaveFileInMinio' => 'Fout bij het opslaan van bestand in MinIO',
    'ErrorSiteDoesntExist' => 'Site bestaat niet',
    'ErrorUserDontExist' => 'Gebruiker bestaat niet',
    'ErrorDontOwnUser' => 'Je hebt niet het recht om deze gebruiker te wijzigen',
    'ProfileDontExist' => 'Profiel bestaat niet',
    'SiteDontExist' => 'Site bestaat niet',
    'ErrorUpdateUser' => 'Fout bij het bijwerken van gebruiker',
    'ErrorUpdateBusinessDay' => 'Fout bij het bijwerken van werkdag',
    'SuccessDeleteUser' => 'Gebruiker succesvol verwijderd',
    'ErrorGetCurrentUser' => 'Fout bij het ophalen van huidige gebruiker',
    'InvalidDateFormat'=>'Ongeldig datumformaat voor de datum: :date',
    'NullDateFormat'=>'Start- of einddatum is null',
    'LeaveTypeNotFound'=>'Verloftype niet gevonden',
    'LeaveTypeClientMismatch'=>'Verloftype behoort niet tot de klant',
    'SubFamilyMismatch'=>'Onderfamilie van verloftype behoort niet tot de klant',
    'ErrorGetUserFromApp' => 'Fout bij het ophalen van gebruiker uit app',
    'ErrorStoreLeaveTypeSubFamily' => 'Fout bij het opslaan van verloftype subfamilie',
    'ErrorUpdateOtherCustomer' => 'Fout bij het bijwerken van andere klant',
    'CantDisplayCustomerFromThisApp' => 'Je kunt deze klant niet weergeven vanuit deze app',
    'ErrorStoreClient' => 'Fout bij het opslaan van klant',
    'SuccessStoreCustomer' => 'Klant succesvol aangemaakt',
    'ErrorStoreCustomer' => 'Fout bij het aanmaken van klant',
    'ClientNotExist' => 'Klant bestaat niet',
    'ErrorUpdateCustomer' => 'Fout bij het bijwerken van klant',
    'ErrorDeleteCustomer' => 'Fout bij het verwijderen van klant',
    'SuccessUpdateLeaveDate' => 'Verlofdatum succesvol bijgewerkt',
    'ErrorGetBusinessDay' => 'Fout bij het ophalen van werkdag',
    'ErrorGetValidationScheme' => 'Fout bij het ophalen van validatieschema',
    'ErrorGetDocs' => 'Fout bij het ophalen van documenten',
    'DocDontExistForSite' => 'Document bestaat niet voor deze site',
    'ErrorGetDoc' => 'Fout bij het ophalen van document',
    'ErrorIdArrayDontExist' => 'Array-ID bestaat niet',
    'SiteDontExistForClient' => 'Site bestaat niet voor deze klant',
    'ErrorFileDontExist' => 'Bestand bestaat niet',
    'ErrorStoreFile' => 'Fout bij het opslaan van bestand',
    'SuccessStoreDoc' => 'Document succesvol aangemaakt',
    'ErrorLoadFile' => 'Fout bij het laden van bestand',
    'DocDontExistForClient' => 'Document bestaat niet voor deze klant',
    'CantDeleteForUpdateFile' => 'Kan dit document niet verwijderen omdat het wordt gebruikt om een bestand bij te werken',
    'CantDeleteFile' => 'Kan bestand niet verwijderen',
    'ErrorDeleteFile' => 'Fout bij het verwijderen van bestand',
    'ErrorGetExportHistory' => 'Fout bij het ophalen van exportgeschiedenis',
    'ErrorNoHistoryForClient' => 'Geen geschiedenis voor deze klant',
    'ErrorDeleteExportHistory' => 'Fout bij het verwijderen van exportgeschiedenis',
    'ErrorCancelLeave' => 'Fout bij het annuleren van verlof',
    'ErrorValidateCancelLeave' => 'Fout bij het valideren van verlofannulering',
    'SuccessGetLeaveTypes' => 'Verloftypen succesvol opgehaald',
    'SuccessGetSite' => 'Site succesvol opgehaald',
    'SuccessUpdateSite' => 'Site succesvol bijgewerkt',
    'SuccessGetTag' => 'Tag succesvol opgehaald',
    'ErrorGetTag' => 'Fout bij het ophalen van tag',
    'ErrorGetTeamFromApp' => 'Fout bij het ophalen van team uit app',
    'ErrorStoreTeamFromApp' => 'Fout bij het opslaan van team uit app',
    'ErrorUpdateTeamFromApp' => 'Fout bij het bijwerken van team uit app',
    'ErrorDeleteTeamFromApp' => 'Fout bij het verwijderen van team uit app',
    'InvalideData' => 'Ongeldige gegevens',
    'SuccessStoreUser' => 'Gebruiker geregistreerd',
    'MustBeInteger' => 'Moet een geheel getal zijn',
    'IsRequired' => 'Is vereist',
    'MustBeEnum' => 'Moet een enumeratie zijn',
    'MustBeString' => 'Moet een string zijn',
    'MustBeBool' => 'Moet een boolean zijn',
    'MustBeNotEmpty' => 'Mag niet leeg zijn',
    'MustBeNumber' => 'Moet een getal zijn',
    'MustBeEmail' => 'Moet een e-mail zijn',
    'MustBeUUID' => 'Moet een UUID zijn',
    'MustBeDate' => 'Moet een datum zijn',
    'MustBeDateAfter' => 'Moet een datum na zijn',
    'MustBeDateBefore' => 'Moet een datum voor zijn',
    'MustRespectFormat' => 'Moet het formaat respecteren',
    'UserNotFound' => 'Gebruiker niet gevonden',
    'ErrorIdAbove' => 'ID moet groter zijn dan 0',
    'ErrorIdNumber' => 'ID moet een getal zijn',
    'ErrorFileType' => 'Ongeldig bestandstype',
    'ErrorFileSize' => 'Bestandsgrootte is te groot',
    'ErrorSendAlert' => 'Fout bij het verzenden van waarschuwing',
    'ErrorSaveHistoric' => 'Fout bij het opslaan van geschiedenis',
    'ClientAlreadyExist' => 'Klant bestaat al',
    'ExportNotFound' => 'Export niet gevonden',
    'ReasonDontExist' => 'Reden niet gevonden',
    'LeaveRefused' => 'Verlof geweigerd',
    'UpdateDocIdError' => 'Fout bij het bijwerken van document-ID',
    'CantFindUuid' => 'Kan UUID niet vinden',
    'CantDoIt' => 'Je hebt niet de rechten om deze actie uit te voeren',
    'HeadersNotFound' => 'Headers niet gevonden',
    'CustomerNotFound' => 'Klant niet gevonden',
    'ApiProctectedBySignature' => 'API beschermd door handtekening',
    'ParameterNotAllowed' => "De parameter ':param_name' is niet toegestaan op deze route.",
    'RequiredValueForParameter' => 'De waarde voor de parameter :name is vereist.',
    'UnsignedNumericValue' => 'Het verwachte formaat voor :name is een niet-ondertekende numerieke waarde.',
    'RequiredBoolean' => 'Het verwachte formaat voor :name is een booleaanse waarde 0/1.',
    'RequiredDate' => 'Het verwachte formaat voor :name is een datumwaarde',
    'RequiredDateTime' => 'Het verwachte formaat voor :name is een datetime-waarde',
    'RequiredUuid' => 'Het verwachte formaat voor :name is een UUID-waarde',
    'RequiredString' => 'Het verwachte formaat voor :name is een stringwaarde.',
    'InvalidValue' => "De waarde ':value' is niet toegestaan. Toegestane waarden: :allowed_values.",
    'DirectionAscDesc' => 'De richting moet oplopend (asc) of aflopend (desc) zijn.',
    'LackDataForStore' => 'Je mist gegevens om op te slaan.',
    'FileWeight' => 'Het bestand is te groot: maximaal 5MB.',
    'FileTypeJpgPng' => 'Het bestandstype wordt niet ondersteund, alleen JPG of PNG.',
    'MustFilterById' => 'Je moet filteren op id',
    'ClientTableNotFound' => 'De klantentabel bestaat niet',
    'LeaveTypeNeedFile'=>'Voor dit soort verlof is een dossier nodig',
    'UserNotExist' => 'De gebruiker met het nummer :matricule bestaat niet',
    'UserDeleted' => 'De gebruiker met het nummer :matricule is verwijderd',
    'SiteNotExist' => 'De site van de gebruiker :matricule bestaat niet',
    'LeaveNotCreated' => 'Het verlof voor de gebruiker :matricule kon niet worden aangemaakt',
    'CurrentUser' => 'De huidige gebruiker heeft niet het juiste profiel',
    'ManagedByCurrentUser'=>'U wordt niet beheerd door deze gebruiker',
    'ErrorLeaveStatusDontAllowModification' => 'Fout, de verlofstatus staat geen wijziging toe',
];
