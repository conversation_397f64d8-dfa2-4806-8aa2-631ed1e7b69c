<?php

return [
    'morning' => ' (<PERSON><PERSON>)',
    'afternoon' => ' (<PERSON><PERSON><PERSON><PERSON>)',
    'CreateSettingsSuccess' => 'Einstellungen erfolgreich erstellt',
    'RemoveSettingsSuccess' => 'Einstellungen erfolgreich entfernt.',
    'UpdateSuccess' => 'Erfolgreich aktualisiert',
    'WaitingPay' => 'Warten auf Zahlung',
    'Waiting' => 'Wartend',
    'InProcess' => 'In Bearbeitung',
    'SuccessStoreLeave' => 'Urlaub erfolgreich erstellt',
    'SuccessCancelLeave' => 'Urlaub erfolgreich storniert',
    'AskForCancelLeave' => 'Anfrage zur Stornierung des Urlaubs gesendet',
    'SuccessCallback' => 'Rückruf erfolgreich',
    'CantCancelLeaveNotValidated' => 'Urlaub kann nicht storniert werden, da er nicht validiert wurde',
    'CantCancelLeaveTransmitted' => 'Urlaub kann nicht storniert werden, da er übermittelt wurde',
    'CantCancelLeaveRefused' => 'Urlaub kann nicht storniert werden, da er abgelehnt wurde',
    'LeaveCanceled' => 'Urlaub storniert',
    'DontOwnLeaveOrManagerUser' => 'Sie können diesen Urlaub nicht ändern oder sind kein Manager dieses Benutzers',
    'CantCancelLeaveValidated' => 'Urlaub kann nicht storniert werden, da er validiert wurde',
    'NotAdminFromUser' => 'Sie sind kein Administrator dieses Benutzers',
    'ErrorTransmisLeaveWrongStatus' => 'Fehler: Urlaub kann aufgrund eines falschen Status nicht übertragen werden',
    'SuccessRefuseLeave' => 'Urlaub abgelehnt',
    'SuccessMassValidationLeave' => 'Urlaube übertragen',
    'SuccessTransmitLeave' => 'Urlaub übertragen',
    'SuccessGetClosedDay' => 'Feiertage abgerufen',
    'SuccessGetHolidays' => 'Urlaub abgerufen',
    'SuccessGetLeave' => 'Urlaub abgerufen',
    'SuccessGetUser' => 'Benutzer abgerufen',
    'SuccessGetCompteur' => 'Zähler abgerufen',
    'SuccessStoreCompteur' => 'Zähler erfolgreich erstellt',
    'SuccessUpdateCompteur' => 'Zähler erfolgreich aktualisiert',
    'SuccessUpdateUser' => 'Benutzer erfolgreich aktualisiert',
    'SuccessUpdateBusinessDay' => 'Arbeitstag erfolgreich aktualisiert',
    'SuccessGetCurrentUser' => 'Aktueller Benutzer abgerufen',
    'SuccessLogout' => 'Erfolgreich abgemeldet',
    'SuccessUpdateCustomer' => 'Kunde erfolgreich aktualisiert',
    'SuccessDeleteCustomer' => 'Kunde erfolgreich gelöscht',
    'SuccessGetBusinessDay' => 'Arbeitstag abgerufen',
    'SuccessGetDocs' => 'Dokumente abgerufen',
    'SuccessUpdateDoc' => 'Dokument erfolgreich aktualisiert',
    'SuccessDeleteFile' => 'Datei erfolgreich gelöscht',
    'SuccessGetHistory' => 'Verlauf abgerufen',
    'SuccessDeleteHistory' => 'Verlauf erfolgreich gelöscht',
    'CantCancelLeave' => 'Urlaub kann nicht storniert werden',
    'OtherAdminValidated' => 'Ein anderer Administrator hat diesen Urlaub bereits validiert',
    'UserNotInClient' => 'Benutzer gehört nicht zu Ihrem Kunden',
    'SuccessAttachUsersToDirector' => 'Benutzer erfolgreich dem Direktor zugewiesen',
    'SuccessDetachUsersToDirector' => 'Benutzer erfolgreich vom Direktor entfernt',
    'SuccessStoreLeaveByTakingInNCounts' => 'Urlaub erfolgreich mit Zähler N erstellt',
    'SuccessStoreHoliday'=>'Die Feiertage wurden erfolgreich erstellt',
    'UserWithNoTags' => 'Mitarbeiter ohne Teams',
    'UserNotManaged' => 'Nicht verwaltete Mitarbeiter',
    'success_store_leave_with_errors' => 'Urlaub wurde erfolgreich erstellt, aber einige Fehler sind aufgetreten.',
    'AnyTags' => 'Kein Team',
    'ImportSucces' => 'Import erfolgreich',
    'ErrorImport' => 'Fehler beim Import',
    'acquis_cp_n_1' => 'acquis_cp_n_1',
    'pris_cp_n_1' => 'pris_cp_n_1',
    'solde_cp_n_1' => 'solde_cp_n_1',
    'acquis_cp_n' => 'acquis_cp_n',
    'pris_cp_n' => 'pris_cp_n',
    'solde_cp_n' => 'solde_cp_n',
    'acquis_type_n_1' => 'acquis_:type_n_1',
    'pris_type_n_1' => 'pris_:type_n_1',
    'solde_type_n_1' => 'solde_:type_n_1',
    'acquis_type_n' => 'acquis_:type_n',
    'pris_type_n' => 'pris_:type_n',
    'solde_type_n' => 'solde_:type_n',
    'ImportUserLeaveCountPartiallySucces' => 'Fehler sind während des Imports der Zähler aufgetreten (:successRows / :rows Zeilen importiert).',
    'ImportUserLeavesPartiallySucces' => 'Fehler sind während des Imports der Abwesenheiten aufgetreten (:successRows / :rows Zeilen importiert).',
    'RegistrationNumber' => 'Mitarbeiternummer',
    'RegistrationNumberEmployee' => 'Mitarbeiternummer',
    'CompanyName' => 'Firmenname',
    'MainEstablishment' => 'Hauptstandort',
    'Email' => 'E-Mail',
    'Employee' => 'Mitarbeiter',
    'EmployeeName' => 'Name Vorname',
    'EmployeeLastname' => 'Nachname',
    'EmployeeFirstname' => 'Vorname',
    'Establishment' => 'Standort',
    'EntryDate' => 'Eintrittsdatum',
    'Acquired' => 'Erworben',
    'Taken' => 'Genommen',
    'Balance' => 'Guthaben',
    'Label' => 'Bezeichnung',
    'CodeConges' => 'Urlaubscode',
    'CodeAbsence' => 'Abwesenheitscode',
    'StartDate' => 'Startdatum',
    'EndDate' => 'Enddatum',
    'Duration' => 'Tage',
    'Status' => 'Status',
    'Site' => 'Standort',
    'TypeCongesAbsence' => 'Art der Abwesenheit/Urlaub',
    'TypeAbsence' => 'Art der Abwesenheit',
    'TypeConges' => 'Typ',
    'Tag' => 'Mannschaft',
    'HeaderRegistrationNumber' => 'mitarbeiternummer',
    'HeaderRegistrationNumberEmployee' => 'mitarbeiternummer',
    'HeaderCompanyName' => 'firmenname',
    'HeaderEstablishment' => 'standort',
    'HeaderMainEstablishment' => 'hauptstandort',
    'HeaderEmail' => 'email',
    'HeaderEmployee' => 'mitarbeiter',
    'HeaderLastname' => 'nachname',
    'HeaderFirstname' => 'vorname',
    'HeaderEntryDate' => 'eintrittsdatum',
    'HeaderEntryDdate' => 'eintrittsdatum',
    'HeaderStartDate' => 'startdatum',
    'HeaderEndDate' => 'enddatum',
    'HeaderDuration' => 'tage',
    'HeaderAcquired' => 'erworben',
    'HeaderTaken' => 'genommen',
    'HeaderBalance' => 'guthaben',
    'HeaderRemaining' => 'übrig',
    'HeaderLeaveTypeName' => 'urlaubstyp',
    'Total' => 'Gesamt',
    'TotalGeneral' => 'Gesamtsumme',
    'store_leave_with_errors' => 'Ein Fehler ist bei der Erstellung des Urlaubs aufgetreten.',
    'waiting_validation_by_manager' => 'Warten auf Genehmigung durch :manager',
    'waiting_cancellation_by_manager' => 'Warten auf Stornierung durch :manager',
    'VALIDATED' => 'Genehmigt',
    'SUBMITTED' => 'Zur Genehmigung eingereicht',
    'REFUSED' => 'Abgelehnt',
    'CANCELED' => 'Storniert',
    'TRANSMITTED' => 'Zur Gehaltsabrechnung übertragen',
    'SUBMITTED_TO_CANCELLATION' => 'Zur Stornierung eingereicht',
    'ImportPartiallySucces' => 'Import teilweise erfolgreich',
    'UnsupportedFileType' => 'Nicht unterstützter Dateityp. Bitte verwenden Sie eine CSV-, XLSX- oder XLS-Datei.',
    'import' => [
        'error_title' => 'Eingabefehler',
        'error_value' => 'Der Wert befindet sich nicht in der Liste.',
        'prompt_title' => 'Wählen Sie aus der Liste',
        'prompt_value' => 'Bitte wählen Sie einen Wert aus der Dropdown-Liste.',
    ],
    'AlreadyTreatedLeavesCount' => 'Bereits behandelte Urlaubsanzahl',
    'WaitingValidationLeavesCount' =>'Anzahl der Urlaube, die auf Validierung warten',
    'WaitingCancelationLeavesCount' =>'Anzahl der Urlaube, die auf Stornierung warten',
    'SuccessCancelLeaveSubToCancel'=> 'Der Stornierungsantrag wurde abgelehnt. Der Urlaub ist jetzt storniert.',
    'SuccessValidateLeaveSubToCancel'=> '"Der Stornierungsantrag wurde erfolgreich validiert, der Urlaub ist daher storniert.',
    'SuccessValidateLeaveSubToCancelValidated' => 'Der Antrag auf Stornierung wurde abgelehnt. Der Urlaub ist auf seinen Status \'Genehmigt\' zurückgekehrt.',
    'SuccessValidateLeaveSubToCancelSubmitted' => 'Der Antrag auf Stornierung wurde abgelehnt. Der Urlaub ist auf seinen Status \'Eingereicht\' zurückgekehrt.',
    'SuccessMassValidationLeaveSubToCancel'=> 'Die Stornierungsanträge wurden erfolgreich validiert, die Urlaube sind daher storniert.',
    'CantCancelAdminOwnLeave'=>'Ein Administrator kann seine eigenen Urlaubsanträge nicht stornieren',
    'SuccessTransmittedLeaveCanceled'=> 'Der Stornierungsantrag wurde erfolgreich bestätigt, der Urlaub ist daher \'Storniert\'.',
];
