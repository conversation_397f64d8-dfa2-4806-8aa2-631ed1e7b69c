<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validierungsnachrichten
    |--------------------------------------------------------------------------
    |
    | Die folgenden Sprachzeilen enthalten die Standardfehlernachrichten, die von
    | der Validierungsklasse verwendet werden. Einige dieser Regeln haben mehrere Versionen,
    | wie zum Beispiel die Größenregeln. Passen Sie jede dieser Nachrichten nach Bedarf hier an.
    |
    */

    'accepted' => 'Das Feld :attribute muss akzeptiert werden.',
    'accepted_if' => 'Das Feld :attribute muss akzeptiert werden, wenn :other den Wert :value hat.',
    'active_url' => 'Das Feld :attribute ist keine gültige URL.',
    'after' => 'Das Feld :attribute muss ein Datum nach :date sein.',
    'after_or_equal' => 'Das Feld :attribute muss ein Datum nach oder gleich :date sein.',
    'alpha' => 'Das Feld :attribute darf nur Buchstaben enthalten.',
    'alpha_dash' => 'Das Feld :attribute darf nur Buchstaben, Zahlen und Bindestriche enthalten.',
    'alpha_num' => 'Das Feld :attribute darf nur Zahlen und Buchstaben enthalten.',
    'array' => 'Das Feld :attribute muss ein Array sein.',
    'ascii' => 'Das Feld :attribute darf nur alphanumerische Zeichen mit einem Byte und Symbole enthalten.',
    'before' => 'Das Feld :attribute muss ein Datum vor :date sein.',
    'before_or_equal' => 'Das Feld :attribute muss ein Datum vor oder gleich :date sein.',
    'between' => [
        'numeric' => 'Der Wert von :attribute muss zwischen :min und :max liegen.',
        'file' => 'Die Datei :attribute muss zwischen :min und :max Kilobytes groß sein.',
        'string' => 'Der Text :attribute muss zwischen :min und :max Zeichen lang sein.',
        'array' => 'Das Array :attribute muss zwischen :min und :max Elemente enthalten.',
    ],
    'boolean' => 'Das Feld :attribute muss wahr oder falsch sein.',
    'confirmed' => 'Das Bestätigungsfeld :attribute stimmt nicht überein.',
    'current_password' => 'Das Passwort ist falsch.',
    'date' => 'Das Feld :attribute ist kein gültiges Datum.',
    'date_equals' => 'Das Feld :attribute muss ein Datum sein, das dem Datum :date entspricht.',
    'date_format' => 'Das Feld :attribute entspricht nicht dem Format :format.',
    'decimal' => 'Das Feld :attribute muss :decimal Dezimalstellen haben.',
    'declined' => 'Das Feld :attribute muss abgelehnt werden.',
    'declined_if' => 'Das Feld :attribute muss abgelehnt werden, wenn :other den Wert :value hat.',
    'different' => 'Die Felder :attribute und :other müssen unterschiedlich sein.',
    'digits' => 'Das Feld :attribute muss :digits Ziffern haben.',
    'digits_between' => 'Das Feld :attribute muss zwischen :min und :max Ziffern haben.',
    'dimensions' => 'Das Feld :attribute hat ungültige Bildabmessungen.',
    'distinct' => 'Das Feld :attribute hat einen doppelten Wert.',
    'doesnt_end_with' => 'Das Feld :attribute darf nicht mit einem der folgenden Elemente enden: :values.',
    'doesnt_start_with' => 'Das Feld :attribute darf nicht mit einem der folgenden Elemente beginnen: :values.',
    'email' => 'Das Feld :attribute muss eine gültige E-Mail-Adresse sein.',
    'ends_with' => 'Das Feld :attribute muss mit einem der folgenden Werte enden: :values.',
    'enum' => 'Das ausgewählte Feld :attribute ist ungültig.',
    'exists' => 'Das ausgewählte Feld :attribute ist ungültig.',
    'file' => 'Das Feld :attribute muss eine Datei sein.',
    'filled' => 'Das Feld :attribute ist erforderlich.',
    'gt' => [
        'array' => 'Das Feld :attribute muss mehr als :value Elemente enthalten.',
        'file' => 'Das Feld :attribute muss mehr als :value Kilobytes groß sein.',
        'numeric' => 'Das Feld :attribute muss größer als :value sein.',
        'string' => 'Das Feld :attribute muss mehr als :value Zeichen lang sein.',
    ],
    'gte' => [
        'array' => 'Das Feld :attribute muss :value oder mehr Elemente enthalten.',
        'file' => 'Das Feld :attribute muss :value Kilobytes oder mehr groß sein.',
        'numeric' => 'Das Feld :attribute muss :value oder mehr sein.',
        'string' => 'Das Feld :attribute muss :value oder mehr Zeichen lang sein.',
    ],
    'image' => 'Das Feld :attribute muss ein Bild sein.',
    'in' => 'Das Feld :attribute ist ungültig.',
    'in_array' => 'Das Feld :attribute existiert nicht in :other.',
    'integer' => 'Das Feld :attribute muss eine ganze Zahl sein.',
    'ip' => 'Das Feld :attribute muss eine gültige IP-Adresse sein.',
    'ipv4' => 'Das Feld :attribute muss eine gültige IPv4-Adresse sein.',
    'ipv6' => 'Das Feld :attribute muss eine gültige IPv6-Adresse sein.',
    'json' => 'Das Feld :attribute muss eine gültige JSON-Zeichenkette sein.',
    'lowercase' => 'Das Feld :attribute muss in Kleinbuchstaben sein.',
    'lt' => [
        'array' => 'Das Feld :attribute muss weniger als :value Elemente enthalten.',
        'file' => 'Das Feld :attribute muss weniger als :value Kilobytes groß sein.',
        'numeric' => 'Das Feld :attribute muss kleiner als :value sein.',
        'string' => 'Das Feld :attribute muss weniger als :value Zeichen lang sein.',
    ],
    'lte' => [
        'array' => 'Das Feld :attribute darf nicht mehr als :value Elemente enthalten.',
        'file' => 'Das Feld :attribute muss kleiner oder gleich :value Kilobytes groß sein.',
        'numeric' => 'Das Feld :attribute muss kleiner oder gleich :value sein.',
        'string' => 'Das Feld :attribute muss kleiner oder gleich :value Zeichen lang sein.',
    ],
    'mac_address' => 'Das Feld :attribute muss eine gültige MAC-Adresse sein.',
    'max' => [
        'array' => 'Das Array :attribute darf nicht mehr als :max Elemente enthalten.',
        'file' => 'Die Datei :attribute darf nicht größer als :max Kilobytes sein.',
        'numeric' => 'Der Wert von :attribute darf nicht größer als :max sein.',
        'string' => 'Der Text von :attribute darf nicht mehr als :max Zeichen enthalten.',
    ],
    'max_digits' => 'Das Feld :attribute darf nicht mehr als :max Ziffern enthalten.',
    'mimes' => 'Das Feld :attribute muss eine Datei des Typs :values sein.',
    'mimetypes' => 'Das Feld :attribute muss eine Datei des Typs :values sein.',
    'min' => [
        'array' => 'Das Array :attribute muss mindestens :min Elemente enthalten.',
        'file' => 'Die Datei :attribute muss mindestens :min Kilobytes groß sein.',
        'numeric' => 'Der Wert von :attribute muss mindestens :min sein.',
        'string' => 'Der Text von :attribute muss mindestens :min Zeichen lang sein.',
    ],
    'min_digits' => 'Das Feld :attribute muss mindestens :min Ziffern haben.',
    'missing' => 'Das Feld :attribute muss fehlen.',
    'missing_if' => 'Das Feld :attribute muss fehlen, wenn :other den Wert :value hat.',
    'missing_unless' => 'Das Feld :attribute muss fehlen, es sei denn, :other ist :value.',
    'missing_with' => 'Das Feld :attribute muss fehlen, wenn :value vorhanden ist.',
    'missing_with_all' => 'Das Feld :attribute muss fehlen, wenn :values vorhanden sind.',
    'multiple_of' => 'Das Feld :attribute muss ein Vielfaches von :value sein.',
    'not_in' => 'Das ausgewählte Feld :attribute ist ungültig.',
    'not_regex' => 'Das Feld :attribute hat ein ungültiges Format.',
    'numeric' => 'Das Feld :attribute muss eine Zahl sein.',
    'password' => [
        'letters' => 'Das Feld :attribute muss mindestens einen Buchstaben enthalten.',
        'mixed' => 'Das Feld :attribute muss mindestens einen Großbuchstaben und einen Kleinbuchstaben enthalten.',
        'numbers' => 'Das Feld :attribute muss mindestens eine Zahl enthalten.',
        'symbols' => 'Das Feld :attribute muss mindestens ein Symbol enthalten.',
        'uncompromised' => 'Der angegebene :attribute ist in einem Datenleck aufgetaucht. Bitte wählen Sie einen anderen :attribute.',
    ],
    'present' => 'Das Feld :attribute muss vorhanden sein.',
    'prohibited' => 'Das Feld :attribute ist verboten.',
    'prohibited_if' => 'Das Feld :attribute ist verboten, wenn :other den Wert :value hat.',
    'prohibited_unless' => 'Das Feld :attribute ist verboten, es sei denn, :other ist in :values.',
    'prohibits' => 'Das Feld :attribute verbietet die Anwesenheit von :other.',
    'regex' => 'Das Format des Feldes :attribute ist ungültig.',
    'required' => 'Das Feld :attribute ist erforderlich.',
    'required_array_keys' => 'Das Feld :attribute muss Einträge für :values enthalten.',
    'required_if' => 'Das Feld :attribute ist erforderlich, wenn der Wert von :other :value ist.',
    'required_if_accepted' => 'Das Feld :attribute ist erforderlich, wenn :other akzeptiert wird.',
    'required_unless' => 'Das Feld :attribute ist erforderlich, es sei denn, :other ist in :values.',
    'required_with' => 'Das Feld :attribute ist erforderlich, wenn :values vorhanden ist.',
    'required_with_all' => 'Das Feld :attribute ist erforderlich, wenn :values vorhanden ist.',
    'required_without' => 'Das Feld :attribute ist erforderlich, wenn :values nicht vorhanden ist.',
    'required_without_all' => 'Das Feld :attribute ist erforderlich, wenn keines von :values vorhanden ist.',
    'same' => 'Die Felder :attribute und :other müssen identisch sein.',
    'size' => [
        'numeric' => 'Der Wert von :attribute muss :size sein.',
        'file' => 'Die Dateigröße von :attribute muss :size Kilobytes betragen.',
        'string' => 'Der Text von :attribute muss :size Zeichen lang sein.',
        'array' => 'Das Array :attribute muss :size Elemente enthalten.',
    ],
    'starts_with' => 'Das :attribute muss mit einem der folgenden Elemente beginnen: :values.',
    'string' => 'Das Feld :attribute muss eine Zeichenkette sein.',
    'timezone' => 'Das :attribute muss eine gültige Zeitzone sein.',
    'unique' => 'Das Feld :attribute ist bereits vergeben.',
    'unique_entry' => 'Jeder Eintrag muss mit einer eindeutigen E-Mail-Adresse und einem eindeutigen Vornamen verknüpft sein.',
    'uploaded' => 'Das Feld :attribute konnte nicht hochgeladen werden.',
    'uppercase' => 'Das :attribute muss in Großbuchstaben sein.',
    'url' => 'Das Feld :attribute muss eine gültige URL sein.',
    'ulid' => 'Das Feld :attribute muss eine gültige ULID sein.',
    'uuid' => 'Das Feld :attribute muss eine gültige UUID sein.',
    'emptyValue' => 'Das Feld :attribute ist leer.',

    /*
    |--------------------------------------------------------------------------
    | Benutzerdefinierte Validierungszeilen
    |--------------------------------------------------------------------------
    |
    | Hier können Sie benutzerdefinierte Validierungsnachrichten für Attribute
    | unter Verwendung der Konvention "attribute.rule" benennen. Dies ermöglicht es uns, schnell eine benutzerdefinierte Sprachzeile für eine
    | bestimmte Attributregel festzulegen.
    |
    */

    'custom' => [
        'leave_already_taken' => 'Aufzeichnung nicht möglich. An diesem Datum wurden bereits Urlaubstage genommen.',
        'empty_field' => 'Zeile :line : Das Feld :field wurde nicht ausgefüllt.',
        'matricule_not_specified' => 'Zeile :line : Die Personalnummer wurde nicht angegeben.',
        'site_not_specified' => 'Zeile :line : Der Standort wurde nicht angegeben.',
        'site_not_existed' => 'Zeile :line : Der Standort existiert nicht - :site.',
        'absence_type_not_found' => 'Zeile :line : Art der Abwesenheit nicht gefunden - Code :leaveTypeCode',
        'leave_type_not_found' => 'Zeile :line : Urlaubstyp nicht gefunden - Code :leaveTypeCode',
        'user_bad_site' => 'Zeile :line : Benutzer ist auf der falschen Seite (:site) - Kennzeichen :matricule',
        'user_not_found' => 'Zeile :line : Benutzer nicht gefunden - Personalnummer :matricule',
        'user_not_specified' => 'Zeile :line : Der Benutzer wurde nicht angegeben',
        'user_deleted' => 'Zeile :line : Der Benutzer wurde gelöscht - Personalnummer :matricule',
        'balance_invalid' => 'Zeile :line : Der Saldo entspricht nicht dem erwarteten Wert',
        'user_not_on_your_clients' => 'Zeile :line : Der Benutzer gehört nicht zu Ihrem Kunden - Personalnummer :matricule',
        'InvalidLeaveTypeNameFormat' => 'Zeile :line : Der Abwesenheitstyp muss ein String sein',
        'InvalidDurationFormat' => 'Zeile :line : Die Abwesenheitsdauer muss numerisch sein',
        'InvalidDurationValue' => 'Zeile :line: Die Dauer der Abwesenheit muss größer als 0 sein',
        'InvalidMatriculeFormat' => 'Zeile :line : Die Personalnummer muss numerisch sein',
        'InvalidLastNameFormat' => 'Zeile :line : Der Nachname muss ein String sein',
        'InvalidFirstNameFormat' => 'Zeile :line : Der Vorname muss ein String sein',
        'InvalidDateFormat' => 'Zeile :line : Ungültiges Datumsformat',
    ],


    /*
    |--------------------------------------------------------------------------
    | Benutzerdefinierte Attributvalidierungen
    |--------------------------------------------------------------------------
    |
    | Die folgenden Sprachzeilen werden verwendet, um unseren Platzhalter für Attribute
    | durch etwas Benutzerfreundlicheres zu ersetzen, wie z. B. "E-Mail-Adresse" anstelle von "email". Das hilft uns einfach, die Nachricht ausdrucksvoller zu machen.
    |
    */

    'attributes' => [
        'activity' => 'Aktivität',
        'activities' => 'Aktivitäten',
        'address' => 'Adresse',
        'addresses' => 'Adressen',
        'age' => 'Alter',
        'ages' => 'Alter',
        'amount' => 'Betrag',
        'amounts' => 'Beträge',
        'answer' => 'Antwort',
        'answers' => 'Antworten',
        'available' => 'verfügbar',
        'availables' => 'verfügbare',
        'barcode' => 'Strichcode',
        'barcodes' => 'Strichcodes',
        'birth_date' => 'Geburtsdatum',
        'brand' => 'Marke',
        'brands' => 'Marken',
        'brand_name' => 'Markenname',
        'buying_price' => 'Kaufpreis',
        'category' => 'Kategorie',
        'categories' => 'Kategorien',
        'city' => 'Stadt',
        'cities' => 'Städte',
        'civility' => 'Anrede',
        'civilities' => 'Anreden',
        'comment' => 'Kommentar',
        'comments' => 'Kommentare',
        'company' => 'Unternehmen',
        'companies' => 'Unternehmen',
        'confirmed' => 'bestätigt',
        'confirmed_at' => 'bestätigt am',
        'content' => 'Inhalt',
        'contents' => 'Inhalte',
        'country' => 'Land',
        'countries' => 'Länder',
        'customer' => 'Kunde',
        'customers' => 'Kunden',
        'day' => 'Tag',
        'days' => 'Tage',
        'date_end' => 'Enddatum',
        'date_start' => 'Startdatum',
        'directory' => 'Verzeichnis',
        'directory_name' => 'Verzeichnisname',
        'directories' => 'Verzeichnisse',
        'directories_name' => 'Verzeichnisnamen',
        'directories_names' => 'Verzeichnisnamen',
        'email_banned' => 'E-Mail gesperrt',
        'email_confirmed' => 'E-Mail bestätigt',
        'email_validated' => 'E-Mail validiert',
        'email_prohibited' => 'E-Mail verboten',
        'emails_banned' => 'E-Mails gesperrt',
        'emails_confirmed' => 'E-Mails bestätigt',
        'emails_validated' => 'E-Mails validiert',
        'emails_prohibited' => 'E-Mails verboten',
        'file' => 'Datei',
        'files' => 'Dateien',
        'first_name' => 'Vorname',
        'first_names' => 'Vornamen',
        'gender' => 'Geschlecht',
        'genders' => 'Geschlechter',
        'hour' => 'Stunde',
        'hours' => 'Stunden',
        'is_active' => 'ist aktiv?',
        'is_banned' => 'gesperrt?',
        'job' => 'Beruf',
        'jobs' => 'Berufe',
        'last_name' => 'Nachname',
        'last_names' => 'Nachnamen',
        'link' => 'Link',
        'links' => 'Links',
        'month' => 'Monat',
        'name' => 'Name',
        'names' => 'Namen',
        'office' => 'Büro',
        'offices' => 'Büros',
        'other' => 'anderes',
        'others' => 'andere',
        'paid_at' => 'bezahlt am',
        'password' => 'Passwort',
        'password_confirmation' => 'Passwortbestätigung',
        'password_current' => 'aktuelles Passwort',
        'passwords' => 'Passwörter',
        'phone' => 'Telefon',
        'phones' => 'Telefone',
        'postal_code' => 'Postleitzahl',
        'price' => 'Preis',
        'published_at' => 'veröffentlicht am',
        'quantity' => 'Menge',
        'quantities' => 'Mengen',
        'rate' => 'Rate',
        'rates' => 'Raten',
        'response' => 'Antwort',
        'responses' => 'Antworten',
        'role' => 'Rolle',
        'roles' => 'Rollen',
        'second' => 'Sekunde',
        'seconds' => 'Sekunden',
        'siren_number' => 'Siren-Nummer',
        'siret_number' => 'Siret-Nummer',
        'size' => 'Größe',
        'sizes' => 'Größen',
        'status' => 'Status',
        'statuses' => 'Statusse',
        'street' => 'Straße',
        'subfolder' => 'Unterordner',
        'subfolders' => 'Unterordner',
        'subdirectory' => 'Unterverzeichnis',
        'subdirectories' => 'Unterverzeichnisse',
        'subject' => 'Thema',
        'subjects' => 'Themen',
        'summary' => 'Zusammenfassung',
        'summarys' => 'Zusammenfassungen',
        'supplier' => 'Lieferant',
        'suppliers' => 'Lieferanten',
        'tax' => 'Steuer',
        'time' => 'Zeit',
        'title' => 'Titel',
        'titles' => 'Titel',
        'user' => 'Benutzer',
        'users' => 'Benutzer',
        'username' => 'Benutzername',
        'usernames' => 'Benutzernamen',
        'value' => 'Wert',
        'values' => 'Werte',
        'vat' => 'MwSt.',
        'vat_rate' => 'MwSt.-Satz',
        'website' => 'Website',
        'websites' => 'Websites',
        'year' => 'Jahr',
        'years' => 'Jahre',
        'default_footer_text' => 'Standard-Fußzeilentext',
        'media_footer' => 'Fußzeilen-Medien',
        'survey_id' => 'Umfrage-ID',
        'firstname' => 'Vorname',
        'lastname' => 'Nachname',
        'email' => 'E-Mail',
        'label' => 'Bezeichnung',
        'description' => 'Beschreibung',
        'started_at' => 'Beginn',
        'ended_at' => 'Ende',
        'accompanying_person_limit' => 'Begrenzung der Begleitpersonen',
        'event_status_id' => 'Ereignisstatus-ID',
        'guest_ids' => 'Teilnehmer-IDs',
        'event_id' => 'Ereignis-ID',
        'phone_number' => 'Telefonnummer',
        'phone_code' => 'Telefonvorwahl',
        'app_from' => 'Erstellt von',
        'sticker' => 'Aufkleber',
        'is_vip' => 'ist VIP',
        'user_related_id' => 'ID des begleitenden Benutzers',
        'is_notified_user_related' => 'Begleitperson benachrichtigen',
        'is_owner' => 'ist Organisator',
        'is_ticket_generated' => 'Einladung gesendet',
        'collection_name' => 'Sammlungsname',
        'model_type' => 'Modelltyp',
        'media' => 'Medien',
        'header_text' => 'Kopfzeilentext',
        'guests' => 'Teilnehmer',
        'entry.survey_id' => 'Umfrage-ID der Antwort',
        'entry.firstname' => 'Vorname der Antwort',
        'entry.lastname' => 'Nachname der Antwort',
        'entry.answers' => 'Antworten der Antwort',
        'entry_id' => 'Antwort-ID',
        'question_id' => 'Frage-ID',
        'hint' => 'Hinweis',
        'order' => 'Reihenfolge',
        'question_type_id' => 'Fragentyp-ID',
        'questionable_type' => 'Frage entität-Typ',
        'questionable_id' => 'Frage entität-ID',
        'is_required' => 'ist erforderlich',
        'has_escape_answer' => 'andere Antwort',
        'location_id' => 'Standort-ID',
        'capacity' => 'Kapazität',
        'overflow' => 'Überlauf erlaubt',
        'reminder_days_before_session' => 'Tage vor der Sitzung Erinnerung',
        'thanks_days_after_session' => 'Tage nach der Sitzung Danke',
        'feedback_survey_id' => 'Feedback-Umfrage-ID',
        'is_survey_related' => 'Umfrage-bezogen',
        'is_signable' => 'unterschreibbar',
        'details' => 'Details',
        'creator_id' => 'Ersteller-ID',
        'survey_status_id' => 'Umfrage-Status-ID',
        'is_template' => 'Vorlage',
        'max_entries' => 'maximale Antworten',
        'updated_by_id' => 'Aktualisiert von Benutzer',
        'expires_at' => 'läuft ab',
        'footer_text' => 'Fußzeilentext',
        'title_alignment' => 'Titel Ausrichtung',
        'holiday_already_exists_for_date_and_client' => 'Für dieses Datum und diesen Kunden existiert bereits ein Feiertag.',
        'missing_value_placeholder' => '[Fehlender Wert]',
        'cell_error_detail' => "Blatt :sheet Zeile :row [Sp: :column]: :errors (Wert: ':value')",
    ],
];
