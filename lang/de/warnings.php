<?php

return [

    'env' => [
        'ActionIsNotAvailableInEnv' => 'Diese Aktion ist in der Umgebung :env nicht verfügbar',
    ],
    'ClientTableDosntExist' => 'Die Kundentabelle existiert nicht',
    'UnauthorizedAction' => 'Nicht autorisierte Aktion',
    'SettingsAlreadyExists' => 'Die Einstellungen existieren bereits',
    'ErrorBatchUpdateTags' => 'Fehler beim Batch-Update der Tags',
    'TagAlreadyExists' => 'Das Tag existiert bereits',
    'BadProfile' => 'Ungültiges Profil',
    'ProfileNotFound' => 'Profil nicht gefunden',
    'InternalError' => 'Interner Fehler',
    'DellUsersSuccess' => 'Benutzer erfolgreich gelöscht',
    'AddUsersSuccess' => 'Benutzer erfolgreich hinzugefügt',
    'LogoutSuccess' => 'Erfolgreich abgemeldet',
    'UserAlreadyTreatedLeave' => 'Sie haben diesen Urlaub bereits genehmigt',
    'NoLeaveCounter' => 'Kein Urlaubszähler gefunden',
    'NotEnoughLeaveDay' => 'Nicht genug Urlaubstage',
    'UserDontMatchJwt' => 'Der Benutzer stimmt nicht mit dem JWT-Token überein',
    'LeaveAlreadyExisteForDate' => 'Das gewünschte Datum ist bereits durch eine andere Anfrage belegt',
    'DurationRequired' => 'Dauer erforderlich',
    'DurationMustBeANumber' => 'Die Dauer muss eine Zahl sein',
    'DurationMustBePositive' => 'Die Dauer muss positiv sein',
    'ErrorLeaveDuration' => 'Fehler bei der Urlaubsdauer',
    'ErrorLeaveDurationHalfDayNotPermit' => 'Fehler bei der Urlaubsdauer, halbe Tage sind nicht erlaubt',
    'GetLeaveType' => 'Urlaubstyp abrufen',
    'ErrorLeaveTypeDontExist' => 'Fehler, der Urlaubstyp existiert nicht',
    'ErrorLeaveTypeDontBelongToClient' => 'Fehler, der Urlaubstyp gehört nicht zum Kunden',
    'ErrorGetStatuses' => 'Fehler beim Abrufen der Status',
    'ErrorStatusDontExist' => 'Status existiert nicht',
    'ErrorLeaveTypeSubFamilyDontExist' => 'Fehler, die Unterfamilie des Urlaubstyps existiert nicht',
    'ErrorLeaveTypeSubFamilyDontBelongToLeave' => 'Fehler, die Unterfamilie des Urlaubstyps gehört nicht zum Urlaubstyp',
    'ErrorStartDateFormat' => 'Falsches Startdatumformat',
    'ErrorEndDateFormat' => 'Falsches Enddatumformat',
    'ErrorStartDateRequired' => 'Startdatum erforderlich',
    'ErrorEndDateRequired' => 'Enddatum erforderlich',
    'ErrorStartDateAboveEndDate' => 'Das Startdatum liegt nach dem Enddatum',
    'ErrorSameDate' => 'Gleiches Start- und Enddatum',
    'DontOwnLeave' => 'Sie haben nicht das Recht, diesen Urlaub zu genehmigen',
    'LeaveDontExist' => 'Urlaub existiert nicht',
    'NotAdminFromUser' => 'Sie sind kein Administrator dieses Benutzers',
    'NotAdminOrManagerFromUser' => 'Sie sind weder Administrator noch Manager dieses Benutzers',
    'NotDirectorOrManagerFromUser' => 'Sie sind weder Direktor noch Manager dieses Benutzers',
    'CantRefuseLeaveWrongStatus' => 'Urlaub kann nicht abgelehnt werden, da er einen falschen Status hat',
    'ErrorNotValidatedByLastManager' => 'Fehler, der Urlaub wurde nicht vom letzten Manager validiert',
    'ErrorLeaveStatusDontAllowValidation' => 'Fehler, der Urlaubsstatus erlaubt keine Validierung',
    'NotManagerFromUser' => 'Sie sind kein Manager dieses Benutzers',
    'SuccessValidateLeave' => 'Urlaub erfolgreich validiert',
    'SuccessMassValidationLeave' => 'Urlaube erfolgreich validiert',
    'ErrorValidateLeave' => 'Fehler bei der Urlaubsvalidierung',
    'ErrorLeaveDontExist' => 'Urlaub existiert nicht',
    'NoManagerOrAdminForRefuseLeave' => 'Es gibt keinen Manager oder Administrator, um diesen Urlaub abzulehnen',
    'ErrorRefuseLeave' => 'Fehler beim Ablehnen des Urlaubs',
    'ErrorActionNotAllowed' => 'Aktion nicht erlaubt',
    'ErrorNoAdminForTransmit' => 'Fehler, kein Administrator zum Übertragen dieses Urlaubs',
    'ErrorTransmitLeave' => 'Fehler bei der Übertragung des Urlaubs',
    'ErrorStoreLeave' => 'Fehler beim Speichern des Urlaubs',
    'SuccessGetClosedDay' => 'Feiertage erfolgreich abgerufen',
    'ErrorValidationData' => 'Fehler bei der Datenvalidierung',
    'ErrorGetClosedDay' => 'Fehler beim Abrufen der Feiertage',
    'SuccessGetHolidays' => 'Urlaub erfolgreich abgerufen',
    'ErrorGetHolidays' => 'Fehler beim Abrufen des Urlaubs',
    'SuccessGetLeave' => 'Urlaub erfolgreich abgerufen',
    'ErrorGetLeave' => 'Fehler beim Abrufen des Urlaubs',
    'ErrorDataArrayDontExist' => 'Datenarray existiert nicht',
    'FileRequired' => 'Datei erforderlich',
    'ActionNotAllowed' => 'Aktion nicht erlaubt',
    'UserDontExist' => 'Benutzer existiert nicht',
    'UserBelongToAnotherClient' => 'Benutzer gehört zu einem anderen Kunden',
    'ErrorDateLeaveTypeNotAllowed' => 'Fehler, das Datum ist für diesen Urlaubstyp nicht erlaubt',
    'DurationTooLong' => 'Dauer zu lang',
    'ErrorValidateRefuseLeave' => 'Fehler bei der Validierung/Ablehnung des Urlaubs',
    'ErrorGetAttachment' => 'Fehler beim Abrufen des Anhangs',
    'CantGenerateFile' => 'Datei kann nicht generiert werden',
    'ErrorExportLeave' => 'Fehler beim Exportieren des Urlaubs',
    'ErrorDontOwnLeaveType'=>'Sie haben nicht das Recht, diesen Urlaubstyp zu ändern',
    'ErrorLeaveTypeNotExist'=>'Der Urlaubstyp existiert nicht',
    'LeaveTypeAlreadyExist'=>'Dieser Urlaubstyp existiert bereits',
    'LeaveSubTypeDontBelongToCustomer'=>'Die Unterfamilie des Urlaubstyps gehört nicht zum Kunden',
    'ErrorUpdateLeaveType'=>'Fehler beim Aktualisieren des Urlaubstyps',
    'SuccessUpdateLeaveType'=>'Urlaubstyp erfolgreich aktualisiert',
    'NotRightForGetLeaveType'=>'Sie haben nicht das Recht, diesen Urlaubstyp abzurufen',
    'ErrorGetLeaveTypes'=>'Fehler beim Abrufen der Urlaubstypen',
    'SuccessGetLeaveType'=>'Urlaubstyp erfolgreich abgerufen',
    'SuccessStoreLeaveType'=>'Urlaubstyp erfolgreich gespeichert',
    'ErrorStoreLeaveType'=>'Fehler beim Speichern des Urlaubstyps',
    'SuccessDeleteLeaveType'=>'Urlaubstyp erfolgreich gelöscht',
    'ErrorDeleteLeaveType'=>'Fehler beim Löschen des Urlaubstyps',
    'ErrorLeaveTypeCantBeDeleted'=>'Dieser Urlaubstyp kann nicht gelöscht werden :leave_type_id',
    'ErrorGetManager'=>'Fehler beim Abrufen des Managers',
    'ErrorStoreManager'=>'Fehler beim Speichern des Managers',
    'ErrorUpdateManager'=>'Fehler beim Aktualisieren des Managers',
    'ErrorDeleteManager'=>'Fehler beim Löschen des Managers',
    'SuccessStoreToken'=>'Token erfolgreich gespeichert',
    'ErrorStoreToken'=>'Fehler beim Speichern des Tokens',
    'SuccessClearToken'=>'Token erfolgreich gelöscht',
    'ErrorDontOwnSite'=>'Sie haben nicht das Recht, diese Seite zu ändern',
    'ErrorGetSite'=>'Fehler beim Abrufen der Seite',
    'ValidationErrors'=>'Validierungsfehler',
    'SuccessGetSites'=>'Seiten erfolgreich abgerufen',
    'ErrorUpdateSite'=>'Fehler beim Aktualisieren der Seite',
    'SuccessGetStatuses'=>'Status erfolgreich abgerufen',
    'NotAccessToTag'=>'Sie haben keinen Zugriff auf dieses Tag',
    'SuccessGetTags'=>'Tags erfolgreich abgerufen',
    'ErrorGetTags'=>'Fehler beim Abrufen der Tags',
    'SuccessGetUser'=>'Benutzer erfolgreich abgerufen',
    'ErrorGetUsers'=>'Fehler beim Abrufen der Benutzer',
    'SuccessUpdateTag'=>'Tag erfolgreich aktualisiert',
    'ErrorUpdateTag'=>'Fehler beim Aktualisieren des Tags',
    'SuccessStoreTag'=>'Tag erfolgreich gespeichert',
    'ErrorStoreTag'=>'Fehler beim Speichern des Tags',
    'ErrorDeleteTag'=>'Fehler beim Löschen des Tags',
    'SuccessDeleteTag'=>'Tag erfolgreich gelöscht',
    'RelationAlreadyExist'=>'Beziehung existiert bereits',
    'SuccessAddUserToTag'=>'Benutzer erfolgreich zum Tag hinzugefügt',
    'ErrorAddUserToTag'=>'Fehler beim Hinzufügen des Benutzers zum Tag',
    'RelationDontExist'=>'Beziehung existiert nicht',
    'SuccessRemoveUserFromTag'=>'Benutzer erfolgreich vom Tag entfernt',
    'ErrorRemoveUserFromTag'=>'Fehler beim Entfernen des Benutzers vom Tag',
    'SuccessUpdateUserTag'=>'Benutzertags erfolgreich aktualisiert',
    'ErrorUpdateUserTag'=>'Fehler beim Aktualisieren der Benutzertags',
    'ErrorGetTeam'=>'Fehler beim Abrufen des Teams',
    'ErrorStoreTeam'=>'Fehler beim Speichern des Teams',
    'ErrorUpdateTeam'=>'Fehler beim Aktualisieren des Teams',
    'ErrorDeleteTeam'=>'Fehler beim Löschen des Teams',
    'SuccessImportFile'=>'Datei erfolgreich importiert',
    'ErrorImportFile'=>'Fehler beim Importieren der Datei',
    'ErrorGetTeamCompteur'=>'Fehler beim Abrufen des Teamzählers',
    'UserDoesNotExist'=>'Benutzer existiert nicht',
    'LeaveTypeDoesntExist'=>'Urlaubstyp existiert nicht',
    'LeaveTypeBelongToAnotherClient'=>'Urlaubstyp gehört zu einem anderen Kunden',
    'UserLeaveCountAlreadyExist'=>'Der Urlaubszähler des Benutzers existiert bereits',
    'ErrorStoreCompteur'=>'Fehler beim Speichern des Zählers',
    'UserLeaveCountDoesntExist'=>'Urlaubszähler des Benutzers existiert nicht',
    'UserLeaveCountBelongToAnotherClient'=>'Urlaubszähler des Benutzers gehört zu einem anderen Kunden',
    'ErrorUpdateCompteur'=>'Fehler beim Aktualisieren des Zählers',
    'ErrorExportCompteur'=>'Fehler beim Exportieren des Zählers',
    'ErrorImportLeave'=>'Fehler beim Importieren der Urlaube',
    'ErrorSaveFileInMinio'=>'Fehler beim Speichern der Datei in MinIO',
    'ErrorSiteDoesntExist'=>'Seite existiert nicht',
    'ErrorUserDontExist'=>'Benutzer existiert nicht',
    'ErrorDontOwnUser'=>'Sie haben nicht das Recht, diesen Benutzer zu ändern',
    'ProfileDontExist'=>'Profil existiert nicht',
    'SiteDontExist'=>'Seite existiert nicht',
    'ErrorUpdateUser'=>'Fehler beim Aktualisieren des Benutzers',
    'ErrorUpdateBusinessDay'=>'Fehler beim Aktualisieren des Geschäftstags',
    'SuccessDeleteUser' =>'Benutzer erfolgreich gelöscht',
    'ErrorGetCurrentUser'=>'Fehler beim Abrufen des aktuellen Benutzers',
    'InvalidDateFormat'=>'Ungültiges Datumsformat für das Datum: :date',
    'NullDateFormat'=>'Start- oder Enddatum ist nul',
    'LeaveTypeNotFound'=>'Urlaubstyp nicht gefunden',
    'LeaveTypeClientMismatch'=>'Urlaubstyp gehört nicht zum Kunden',
    'SubFamilyMismatch'=>'Unterfamilie des Urlaubstyps gehört nicht zum Kunden',
    'ErrorGetUserFromApp'=>'Fehler beim Abrufen des Benutzers aus der Anwendung',
    'ErrorStoreLeaveTypeSubFamily'=>'Fehler beim Speichern der Unterfamilie des Urlaubstyps',
    'ErrorUpdateOtherCustomer'=>'Fehler beim Aktualisieren eines anderen Kunden',
    'CantDisplayCustomerFromThisApp'=>'Sie können diesen Kunden nicht aus dieser Anwendung anzeigen',
    'ErrorStoreClient'=>'Fehler beim Speichern des Kunden',
    'SuccessStoreCustomer'=>'Kunde erfolgreich erstellt',
    'ErrorStoreCustomer'=>'Fehler beim Erstellen des Kunden',
    'ClientNotExist'=>'Kunde existiert nicht',
    'ErrorUpdateCustomer'=>'Fehler beim Aktualisieren des Kunden',
    'ErrorDeleteCustomer'=>'Fehler beim Löschen des Kunden',
    'SuccessUpdateLeaveDate'=>'Urlaubsdatum erfolgreich aktualisiert',
    'ErrorGetBusinessDay'=>'Fehler beim Abrufen des Geschäftstags',
    'ErrorGetValidationScheme'=>'Fehler beim Abrufen des Validierungsschemas',
    'ErrorGetDocs'=>'Fehler beim Abrufen der Dokumente',
    'DocDontExistForSite'=>'Dokument existiert nicht für diese Seite',
    'ErrorGetDoc'=>'Fehler beim Abrufen des Dokuments',
    'ErrorIdArrayDontExist'=>'ID-Array existiert nicht',
    'SiteDontExistForClient'=>'Seite existiert nicht für diesen Kunden',
    'ErrorFileDontExist'=>'Datei existiert nicht',
    'ErrorStoreFile'=>'Fehler beim Speichern der Datei',
    'SuccessStoreDoc'=>'Dokument erfolgreich erstellt',
    'ErrorLoadFile'=>'Fehler beim Laden der Datei',
    'DocDontExistForClient'=>'Dokument existiert nicht für diesen Kunden',
    'CantDeleteForUpdateFile'=>'Dieses Dokument kann nicht gelöscht werden, da es zum Aktualisieren einer Datei verwendet wird',
    'CantDeleteFile'=>'Datei kann nicht gelöscht werden',
    'ErrorDeleteFile'=>'Fehler beim Löschen der Datei',
    'ErrorGetExportHistory'=>'Fehler beim Abrufen der Exporthistorie',
    'ErrorNoHistoryForClient'=>'Keine Historie für diesen Kunden',
    'ErrorDeleteExportHistory'=>'Fehler beim Löschen der Exporthistorie',
    'ErrorCancelLeave'=>'Fehler beim Stornieren des Urlaubs',
    'ErrorValidateCancelLeave'=>'Fehler bei der Validierung der Stornierung des Urlaubs',
    'SuccessGetLeaveTypes'=>'Urlaubstypen erfolgreich abgerufen',
    'SuccessGetSite'=>'Seite erfolgreich abgerufen',
    'SuccessUpdateSite'=>'Seite erfolgreich aktualisiert',
    'SuccessGetTag'=>'Tag erfolgreich abgerufen',
    'ErrorGetTag'=>'Fehler beim Abrufen des Tags',
    'TagDontExist'=>'Tag existiert nicht',
    'ErrorGetTeamFromApp'=>'Fehler beim Abrufen des Teams aus der Anwendung',
    'ErrorStoreTeamFromApp'=>'Fehler beim Speichern des Teams aus der Anwendung',
    'ErrorUpdateTeamFromApp'=>'Fehler beim Aktualisieren des Teams aus der Anwendung',
    'ErrorDeleteTeamFromApp'=>'Fehler beim Löschen des Teams aus der Anwendung',
    'InvalideData'=> 'Ungültige Daten',
    'SuccessStoreUser' => 'Benutzer erfolgreich gespeichert',
    'MustBeInteger'=>'Muss eine ganze Zahl sein',
    'IsRequired'=> 'Ist erforderlich',
    'MustBeEnum'=>'Muss eine Aufzählung sein',
    'MustBeString'=>'Muss eine Zeichenkette sein',
    'MustBeBool'=>'Muss ein Boolescher Wert sein',
    'MustBeNotEmpty'=> 'Darf nicht leer sein',
    'MustBeNumber'=>'Muss eine Zahl sein',
    'MustBeEmail'=>'Muss eine E-Mail sein',
    'MustBeUUID'=> 'Muss ein UUID sein',
    'MustBeDate'=> 'Muss ein Datum sein',
    'MustBeDateAfter'=>'Muss ein Datum nach',
    'MustBeDateBefore'=>'Muss ein Datum vor',
    'MustRespectFormat'=>'Muss das Format respektieren',
    'UserNotFound'=>'Benutzer nicht gefunden',
    'ErrorIdAbove'=>'Die ID muss größer als 0 sein',
    'ErrorIdNumber'=>'Die ID muss eine Zahl sein',
    'ErrorFileType'=>'Der Dateityp ist ungültig',
    'ErrorFileSize'=>'Die Dateigröße ist zu groß',
    'ErrorSendAlert'=>'Fehler beim Senden des Alarms',
    'ErrorSaveHistoric'=>'Fehler beim Speichern der Historie',
    'ClientAlreadyExist'=>'Kunde existiert bereits',
    'ExportNotFound'=>'Der Export wurde nicht gefunden',
    'ReasonDontExist'=>'Grund nicht gefunden',
    'LeaveRefused'=>'Urlaub abgelehnt',
    'UpdateDocIdError'=>'Fehler beim Aktualisieren der Dokument-ID',
    'CantFindUuid'=>'UUID kann nicht gefunden werden',
    'CantDoIt'=>'Sie haben nicht die Berechtigung, diese Aktion auszuführen',
    'HeadersNotFound'=>'Kopfzeilen nicht gefunden',
    'CustomerNotFound'=>'Kunde nicht gefunden',
    'ApiProctectedBySignature'=>'API durch Signatur geschützt',
    'ParameterNotAllowed' => "Der Parameter ':param_name' ist auf dieser Route nicht erlaubt.",
    'RequiredValueForParameter'=>'Der Wert für den Parameter :name ist erforderlich.',
    'UnsignedNumericValue'=>'Das erwartete Format für :name ist ein nicht signierter numerischer Wert.',
    'RequiredBoolean'=>'Das erwartete Format für :name ist ein boolescher Wert 0/1.',
    'RequiredDate'=>'Das erwartete Format für :name ist ein Datumswert',
    'RequiredDateTime'=>'Das erwartete Format für :name ist ein Datumszeitwert',
    'RequiredUuid'=>'Das erwartete Format für :name ist ein UUID-Wert',
    'RequiredString'=>'Das erwartete Format für :name ist ein Zeichenfolgenwert.',
    'InvalidValue' => "Der Wert ':value' ist nicht erlaubt. Erlaubte Werte: :allowed_values.",
    'DirectionAscDesc'=>'Die Richtung muss entweder aufsteigend (asc) oder absteigend (desc) sein.',
    'LackDataForStore'=>'Es fehlen Daten zum Speichern.',
    'FileWeight'=>'Die Datei ist zu groß: maximal 5 MB.',
    'FileTypeJpgPng'=>'Der Dateityp wird nicht unterstützt, nur JPG oder PNG.',
    'MustFilterById'=>'Sie müssen nach ID filtern',
    'ClientTableNotFound'=>'Die Kundentabelle wurde nicht gefunden',
    'LeaveTypeNeedFile'=>'Für diese Art von Urlaub ist eine Datei erforderlich',
    'UserNotExist' => 'Der Benutzer mit der Matrikelnummer :matricule existiert nicht',
    'UserDeleted' => 'Der Benutzer mit der Matrikelnummer :matricule wurde gelöscht',
    'SiteNotExist' => 'Die Seite des Benutzers :matricule existiert nicht',
    'LeaveNotCreated' => 'Der Urlaub für den Benutzer :matricule konnte nicht erstellt werden',
    'CurrentUser' => 'Der aktuelle Benutzer hat nicht das richtige Profil',
    'ManagedByCurrentUser'=>'Sie werden nicht von diesem Benutzer verwaltet',
    'LastNameMissingFromSalarie' => 'Blatt :sheetnbmr Zeile :line: Nachname fehlt in der Spalte Mitarbeiter (Wert: ":salarie").',
    'EmailMissingInvalidNormalized' => 'Blatt :sheetnbmr Zeile :line: E-Mail fehlt oder ist nach der Normalisierung ungültig (ursprüngliche E-Mail: ":original_email").',
    'EmailFormatInvalidNormalized' => 'Blatt :sheetnbmr Zeile :line: Ungültiges E-Mail-Format nach der Normalisierung (normalisierte E-Mail: ":normalized_email", ursprüngliche E-Mail: ":original_email").',
    'UserNotFoundImport' => 'Blatt :sheetnbmr Zeile :line: Benutzer mit normalisierter E-Mail ":normalized_email" nicht gefunden (ursprüngliche E-Mail: ":original_email").',
    'UserLastNameMismatch' => 'Blatt :sheetnbmr Zeile :line: Der Name für den Benutzer (E-Mail: :email) stimmt nicht überein. Erwarteter Name (DB): ":expected_name", gefundener Name (Datei): ":name_from_file".',
    'UserMissingSite' => 'Blatt :sheetnbmr Zeile :line: Standort fehlt für Benutzer mit normalisierter E-Mail ":normalized_email".',
    'SoldeRttInitialInvalidOrMissing' => 'Blatt :sheetnbmr Zeile :line: Anfänglicher RTT-Saldo ist ungültig oder fehlt (Wert: ":value").',
    'AcquisRttInvalidOrMissing' => 'Blatt :sheetnbmr Zeile :line: Der Wert \'RTT Erworben\' ist ungültig oder fehlt (Wert: ":value").',
    'PrisRttInvalidOrMissing' => 'Blatt :sheetnbmr Zeile :line: Der Wert \'RTT Genommen\' ist ungültig oder fehlt (Wert: ":value").',
    'SoldeRttFinalInvalidOrMissing' => 'Blatt :sheetnbmr Zeile :line: Der endgültige RTT-Saldo ist ungültig oder fehlt (Wert: ":value").',
    'RttBalanceMismatch' => 'Blatt :sheetnbmr Zeile :line für :email: RTT-Saldo-Abweichung. Saldo Datei: :solde_fichier, Berechneter Saldo: :solde_calcule.',
    'SoldeCpInvalidOrMissing'=> 'Blatt :sheetnbmr Zeile :line: Saldo für bezahlten Urlaub ungültig oder fehlt (Wert: :value).',

    'CpAcquiredMismatchDetected' => '[X] - Blatt :sheet, Zeile :line : Urlaubskonto für :user_lastname fehlerhaft: Urlaubsanspruch aus Datei (:file_acquired_value) stimmt nicht mit dem aktuellen Urlaubsanspruch in der Datenbank (:db_acquired_value) überein.',
    'CpAnnualUpdate' => 'Blatt :sheet, Zeile :line : Urlaubskonto für :user_lastname aktualisiert. Neuer Urlaubsanspruch erworben: :new_acquired_value.',
    'CpNewCounterCreated' => 'Blatt :sheet, Zeile :line : Urlaubskonto für :user_lastname erstellt mit :acquired_value erworbenem Urlaubsanspruch.',
    'DbErrorCpUpdate' => 'Blatt :sheet, Zeile :line : Datenbankfehler beim Aktualisieren des Urlaubskontos (:leave_type_code). Details: :db_message',

    'ErrorLeaveStatusDontAllowModification' => 'Fehler, der Urlaubsstatus erlaubt keine Änderung',
];
