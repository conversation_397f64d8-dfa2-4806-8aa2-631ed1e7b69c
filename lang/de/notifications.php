<?php

return [
    // General
    'greeting' => 'Hallo :user_firstname,',
    'goodbye' => 'Bis bald!',
    'app_link' => 'ZUR APP GEHEN',
    'conges' => 'Urlaub',
    'start_less_48hours' => ' der in weniger als 48 Stunden beginnt :',

    // Leaves Title
    'title_leave_accepted' => 'CONGÉS : Urlaubsantrag erfolgreich validiert',
    'title_user_leave_accepted' => 'CONGÉS : :user_firstname :user_lastname - Urlaubsantrag erfolgreich validiert',
    'title_follow_asking_leaves' => 'CONGÉS : Verfolgung Ihres Urlaubsantrags',
    'title_leave_asking' => 'CONGÉS : Urlaubsantrag',
    'title_leave_cancelled' => 'CONGÉS : Urlaub storniert!',
    'title_leave_cancelled_by_other_manager' => 'CONGÉS : Stornierung des Urlaubs eines Mitarbeiters',
    'title_leave_cancelled_managed' => "CONGÉS : Stornierung des Urlaubs von :user_firstname :user_lastname",
    'title_leave_cancel_refused' => 'CONGÉS : Ablehnung der Urlaubsstornierung durch Ihren Manager',
    'title_leave_asking_cancelled_managed' => "CONGÉS : Antrag auf Urlaubsstornierung",
    'title_another_administrator_valid_leave' => "CONGÉS : Der Administrator hat einen Urlaubsantrag für Sie bearbeitet",
    'title_another_administrator_refuse_leave' => "CONGÉS : Der Administrator hat einen Urlaubsantrag für Sie bearbeitet",
    'title_leave_refused' => "CONGÉS : Urlaubsantrag abgelehnt",
    'title_leaves_waiting_validation' => "CONGÉS : Ein oder mehrere Urlaube warten auf Validierung",
    'title_leaves_waiting_validation_start_soon' => "CONGÉS : Ein oder mehrere Urlaube, die validiert werden müssen, beginnen in weniger als 48 Stunden",
    'title_leaves_waiting_validation_end_month' => "CONGÉS : Liste der Urlaube des Monats, die auf Validierung warten",
    'title_leaves_waiting_validation_this_week' => "CONGÉS : Liste der Personen, die diese Woche im Urlaub sind",
    'title_leaves_period' => "CONGÉS : Urlaub vom :start_date bis :end_date",

    // Leaves
    'leave_accepted' => 'Ihr Urlaubsantrag wurde von :manager_firstname :manager_lastname validiert, vom :leave_start bis :leave_end - :leave_type',
    'no_more_managers' => 'Keine weiteren Manager müssen den Antrag bearbeiten.',
    'leave_validating_by_other_manager' => 'Der Urlaubsantrag von :user_firstname :user_lastname wurde von :manager_firstname :manager_lastname validiert, vom :leave_start bis :leave_end - :leave_type',
    'request_sent_to_next_manager' => 'Der Antrag wurde nun an :manager_firstname :manager_lastname gesendet.',
    'send_validating_leave' => ':user_firstname :user_lastname hat einen Urlaubsantrag zur Validierung eingereicht, vom :leave_start bis :leave_end - :leave_type',
    'validate_author' => 'Der Antrag wurde von :author_firstname :author_lastname validiert.',
    'leave_cancelled_period' => "Ihr Urlaubsantrag für den Zeitraum vom :leave_start bis :leave_end (:leave_type) wurde erfolgreich storniert.",
    'managed_cancelled_leave' => ":user_firstname :user_lastname hat seinen Urlaubsantrag vom :leave_start bis :leave_end - :leave_type storniert",
    'managed_asking_cancelled_leave' => ":user_firstname :user_lastname hat gerade einen Antrag auf Stornierung des Urlaubsantrags für den Zeitraum vom :leave_start bis :leave_end - :leave_type eingereicht",
    'leave_cancelled_period_by_manager' => "Ihr :leave_type-Antrag für den Zeitraum vom :leave_start bis :leave_end (:leave_type) wurde von :user_firstname :user_lastname storniert.",
    'leave_cancelled_period_by_another_manager' => 'Der Urlaubsantrag für den Mitarbeiter :leave_user_firstname :leave_user_lastname für den Zeitraum vom :leave_start bis :leave_end (:leave_type) wurde von :user_firstname :user_lastname storniert.',
    'leave_cancelled_refused_by_another_manager' => 'Ihr Antrag auf Stornierung von :leave_type für den Zeitraum vom :leave_start bis :leave_end (:leave_type) wurde von :user_firstname:user_lastname abgelehnt.',
    'another_administrator_valid_leave' => ':admin_firstname :admin_lastname hat den Urlaubsantrag von :user_firstname :user_lastname für den Zeitraum vom :leave_start bis :leave_end - :leave_type validiert',
    'another_administrator_refuse_leave' => ':admin_firstname :admin_lastname hat den :leave-Antrag von :user_firstname :user_lastname für den Zeitraum vom :leave_start bis :leave_end - :leave_type abgelehnt',
    'manager_refuse_leave' => ':manager_firstname :manager_lastname hat Ihren Antrag für den Zeitraum vom :leave_start bis :leave_end - :leave_type abgelehnt',
    'refused_leave'=> 'Der Urlaubsantrag von :user_firstname :user_lastname wurde von :manager_firstname :manager_lastname für den Zeitraum vom :leave_start bis :leave_end - :leave_type abgelehnt',
    'leaves_waiting_your_validation' => 'Hier ist die Liste der Urlaube, die auf Ihre Validierung warten:',
    'leaves_waiting_your_validation_in' => 'Hier ist die Liste der Urlaube, die auf Validierung warten',
    'leave_waiting_to_be_validate' => ':user_firstname :user_lastname hat Urlaub ab dem :leave_start - :leave_type beantragt',
    'leave_waiting_to_be_validate_start_soon' => ':user_firstname :user_lastname hat Urlaub beantragt, am :leave_start - :leave_type',
    'leave_waiting_to_be_validate_users' => ':user_firstname :user_lastname vom :leave_start bis :leave_end - :leave_type',
    'leave_waiting_to_be_validate_for_managers_this_week' => 'Hier ist die Liste der Personen, die diese Woche im Urlaub sind:',

    //fcm
    'fcm' => [
        'title' => [
            'title_leaves_period' => 'CONGÉS: Urlaub vom :leave_start bis :leave_end',
            'title_leave_asking' => 'CONGÉS: Antrag für :leave_type',
            'title_leave_cancel_refused_by_other_manager' => 'CONGÉS: Antrag auf :leave_type abgelehnt',
            'title_leave_cancel_push' => 'CONGÉS: Stornierungsantrag für einen Urlaub',
            'title_leave_cancel_refused_manager' => 'CONGÉS: Stornierung Ihres Urlaubsantrags',
            'title_leave_asking_validating' => 'CONGÉS: Genehmigen Sie den ausstehenden Urlaubsantrag',
            'title_leaves_waiting_validation_start_soon' => 'CONGÉS: Ein oder mehrere ausstehende Urlaubsanträge beginnen in weniger als 48 Stunden',
            'title_leaves_waiting_validation' => 'CONGÉS: Ein oder mehrere Urlaubsanträge warten auf Ihre Genehmigung',
            'title_leaves_no_attachment' => 'CONGÉS - Nachweis nicht angegeben',
            'title_leave_cancelled_managed' => 'CONGÉS: Urlaubsstornierung',
            'title_leave_cancelled_by_other_manager' => 'CONGÉS: Stornierung des Urlaubs eines Mitarbeiters',
            'title_leave_cancel_refused' => 'CONGÉS: Ihr Vorgesetzter hat den Stornierungsantrag abgelehnt',
        ],
        'body' => [
            'leave_accepted' => "Ihr Urlaubsantrag wurde von :manager_firstname :manager_lastname genehmigt - :leave_type",
            'leave_period' => "Beantragter Urlaubsbeginn: :leave_start \n Beantragtes Urlaubsende: :leave_end - :leave_type",
            'leave_refuse' => "Urlaubsantrag abgelehnt von: \n :manager_firstname :manager_lastname - :leave_type",
            'leave_cancel_period' => "Urlaubsstornierung beantragt von: :user_firstname :user_lastname - :leave_type",
            'leave_cancel_validate' => "Ihr Urlaubsstornierungsantrag wurde von :manager_firstname :manager_lastname genehmigt - :leave_type",
            'leave_waiting_validation' => "Hallo! Sie haben einen ausstehenden Urlaubsantrag, der Ihre Genehmigung erfordert. Bitte nehmen Sie sich einen Moment Zeit, um den Antrag so schnell wie möglich zu prüfen und zu genehmigen.",
            'leave_waiting_to_be_validate_start_soon' => ":user_firstname :user_lastname hat einen Urlaubsantrag eingereicht, Beginn am :leave_start - :leave_type",
            'leave_waiting_an_attachment' => "Ihr Urlaubsantrag hat noch keinen Nachweis. Bitte reichen Sie diesen so schnell wie möglich nach.",
            'managed_cancelled_leave' => ":user_firstname :user_lastname hat seinen Urlaubsantrag vom :leave_start bis :leave_end storniert - :leave_type",
            'leave_cancelled_period_by_another_manager' => 'Der Urlaubsantrag des Mitarbeiters :leave_user_firstname :leave_user_lastname für den Zeitraum vom :leave_start bis :leave_end wurde von :user_firstname :user_lastname storniert - :leave_type',
            'leave_cancelled_refused_by_another_manager' => 'Ihr Stornierungsantrag für :leave_type vom :leave_start bis :leave_end wurde von :user_firstname :user_lastname abgelehnt',
        ],
    ]
];
