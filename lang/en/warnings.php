<?php
return[
    'env' => [
        'ActionIsNotAvailableInEnv' => 'This action is not available in the environment :env',
    ],
    'ClientTableDosntExist' => 'The client table does not exist',
    'UnauthorizedAction' => 'Unauthorized action',
    'SettingsAlreadyExists' => 'Settings already exist',
    'ErrorBatchUpdateTags' => 'Error during batch update of tags',
    'TagAlreadyExists' => 'The tag already exists',
    'BadProfile' => 'Invalid profile',
    'ProfileNotFound' => 'Profile not found',
    'InternalError' => 'Internal error',
    'DellUsersSuccess' => 'Users successfully deleted',
    'AddUsersSuccess' => 'Users successfully added',
    'LogoutSuccess' => 'Logout successful',
    'UserAlreadyTreatedLeave' => 'You have already validated this leave',
    'NoLeaveCounter' => 'No leave counter found',
    'NotEnoughLeaveDay' => 'Not enough leave days',
    'UserDontMatchJwt' => 'The user does not match the JWT token',
    'LeaveAlreadyExisteForDate' => 'The requested date is already occupied by another request',
    'DurationRequired' => 'Duration required',
    'DurationMustBeANumber' => 'Duration must be a number',
    'DurationMustBePositive' => 'Duration must be positive',
    'ErrorLeaveDuration' => 'Leave duration error',
    'ErrorLeaveDurationHalfDayNotPermit' => 'Leave duration error, half days are not permitted',
    'GetLeaveType' => 'Retrieve leave type',
    'ErrorLeaveTypeDontExist' => 'Error, the leave type does not exist',
    'ErrorLeaveTypeDontBelongToClient' => 'Error, the leave type does not belong to the client',
    'ErrorGetStatuses' => 'Error retrieving statuses',
    'ErrorStatusDontExist' => 'Status does not exist',
    'ErrorLeaveTypeSubFamilyDontExist' => 'Error, the leave type sub-family does not exist',
    'ErrorLeaveTypeSubFamilyDontBelongToLeave' => 'Error, the leave type sub-family does not belong to the leave type',
    'ErrorStartDateFormat' => 'Incorrect start date format',
    'ErrorEndDateFormat' => 'Incorrect end date format',
    'ErrorStartDateRequired' => 'Start date required',
    'ErrorEndDateRequired' => 'End date required',
    'ErrorStartDateAboveEndDate' => 'The start date is greater than the end date',
    'ErrorSameDate' => 'Same start and end date',
    'DontOwnLeave' => 'You do not have the right to validate this leave',
    'LeaveDontExist' => 'Leave does not exist',
    'NotAdminFromUser' => 'You are not an administrator of this user',
    'NotAdminOrManagerFromUser' => 'You are neither an administrator nor a manager of this user',
    'NotDirectorOrManagerFromUser' => 'You are neither director nor manager of this user',
    'CantRefuseLeaveWrongStatus' => 'Cannot refuse this leave because it has a wrong status',
    'ErrorNotValidatedByLastManager' => 'Error, the leave has not been validated by the last manager',
    'ErrorLeaveStatusDontAllowValidation' => 'Error, the leave status does not allow validation',
    'NotManagerFromUser' => 'You are not a manager of this user',
    'SuccessValidateLeave' => 'Leave validated',
    'SuccessMassValidationLeave' => 'Leaves validated',
    'ErrorValidateLeave' => 'Leave validation error',
    'ErrorLeaveDontExist' => 'Leave does not exist',
    'NoManagerOrAdminForRefuseLeave' => 'There is no manager or administrator to refuse this leave',
    'ErrorRefuseLeave' => 'Leave refusal error',
    'ErrorActionNotAllowed' => 'Action not allowed',
    'ErrorNoAdminForTransmit' => 'Error, no administrator to transmit this leave',
    'ErrorTransmitLeave' => 'Leave transmission error',
    'ErrorStoreLeave' => 'Leave storage error',
    'SuccessGetClosedDay' => 'Public holidays retrieved',
    'ErrorValidationData' => 'Data validation error',
    'ErrorGetClosedDay' => 'Error retrieving public holidays',
    'SuccessGetHolidays' => 'Holidays retrieved',
    'ErrorGetHolidays' => 'Error retrieving holidays',
    'SuccessGetLeave' => 'Leave retrieved',
    'ErrorGetLeave' => 'Error retrieving leave',
    'ErrorDataArrayDontExist' => 'Data array does not exist',
    'FileRequired' => 'File required',
    'ActionNotAllowed' => 'Action not allowed',
    'UserDontExist' => 'User does not exist',
    'UserBelongToAnotherClient' => 'User belongs to another client',
    'ErrorDateLeaveTypeNotAllowed' => 'Error, the date is not allowed for this leave type',
    'DurationTooLong' => 'Duration too long',
    'ErrorValidateRefuseLeave' => 'Leave validation/refusal error',
    'ErrorGetAttachment' => 'Error retrieving attachment',
    'CantGenerateFile' => 'Cannot generate file',
    'ErrorExportLeave' => 'Error exporting leaves',
    'ErrorDontOwnLeaveType' => 'You do not have the right to modify this leave type',
    'ErrorLeaveTypeNotExist' => 'The leave type does not exist',
    'LeaveTypeAlreadyExist' => 'This leave type already exists',
    'LeaveSubTypeDontBelongToCustomer' => 'The leave type sub-family does not belong to the client',
    'ErrorUpdateLeaveType' => 'Error updating leave type',
    'SuccessUpdateLeaveType' => 'Leave type updated successfully',
    'NotRightForGetLeaveType' => 'You do not have the right to retrieve this leave type',
    'ErrorGetLeaveTypes' => 'Error retrieving leave types',
    'SuccessGetLeaveType' => 'Leave type retrieved successfully',
    'SuccessStoreLeaveType' => 'Leave type stored successfully',
    'ErrorStoreLeaveType' => 'Error storing leave type',
    'SuccessDeleteLeaveType' => 'Leave type deleted successfully',
    'ErrorDeleteLeaveType' => 'Error deleting leave type',
    'ErrorLeaveTypeCantBeDeleted' => 'This leave type cannot be deleted :leave_type_id',
    'ErrorGetManager' => 'Error retrieving manager',
    'ErrorStoreManager' => 'Error storing manager',
    'ErrorUpdateManager' => 'Error updating manager',
    'ErrorDeleteManager' => 'Error deleting manager',
    'SuccessStoreToken' => 'Token stored successfully',
    'ErrorStoreToken' => 'Error storing token',
    'SuccessClearToken' => 'Token cleared successfully',
    'ErrorDontOwnSite' => 'You do not have the right to modify this site',
    'ErrorGetSite' => 'Error retrieving site',
    'ValidationErrors' => 'Validation errors',
    'SuccessGetSites' => 'Sites retrieved successfully',
    'ErrorUpdateSite' => 'Error updating site',
    'SuccessGetStatuses' => 'Statuses retrieved successfully',
    'NotAccessToTag' => 'You do not have access to this tag',
    'SuccessGetTags' => 'Tags retrieved successfully',
    'ErrorGetTags' => 'Error retrieving tags',
    'SuccessGetUser' => 'User retrieved successfully',
    'ErrorGetUsers' => 'Error retrieving users',
    'TagDontExist' => 'Tag does not exist',
    'SuccessUpdateTag' => 'Tag updated successfully',
    'ErrorUpdateTag' => 'Error updating tag',
    'SuccessStoreTag' => 'Tag stored successfully',
    'ErrorStoreTag' => 'Error storing tag',
    'ErrorDeleteTag' => 'Error deleting tag',
    'SuccessDeleteTag' => 'Tag deleted successfully',
    'RelationAlreadyExist' => 'Relation already exists',
    'SuccessAddUserToTag' => 'User added to tag successfully',
    'ErrorAddUserToTag' => 'Error adding user to tag',
    'RelationDontExist' => 'Relation does not exist',
    'SuccessRemoveUserFromTag' => 'User removed from tag successfully',
    'ErrorRemoveUserFromTag' => 'Error removing user from tag',
    'SuccessUpdateUserTag' => 'User tags updated successfully',
    'ErrorUpdateUserTag' => 'Error updating user tags',
    'ErrorGetTeam' => 'Error retrieving team',
    'ErrorStoreTeam' => 'Error storing team',
    'ErrorUpdateTeam' => 'Error updating team',
    'ErrorDeleteTeam' => 'Error deleting team',
    'SuccessImportFile' => 'File imported successfully',
    'ErrorImportFile' => 'Error importing file',
    'ErrorGetTeamCompteur' => 'Error retrieving team counters',
    'UserDoesNotExist' => 'User does not exist',
    'LeaveTypeDoesntExist' => 'Leave type does not exist',
    'LeaveTypeBelongToAnotherClient' => 'Leave type belongs to another client',
    'UserLeaveCountAlreadyExist' => 'User leave counter already exists',
    'ErrorStoreCompteur' => 'Error storing counter',
    'UserLeaveCountDoesntExist' => 'User leave counter does not exist',
    'UserLeaveCountBelongToAnotherClient' => 'User leave counter belongs to another client',
    'ErrorUpdateCompteur' => 'Error updating counter',
    'ErrorExportCompteur' => 'Error exporting counter',
    'ErrorImportLeave' => 'Error importing leaves',
    'ErrorSaveFileInMinio' => 'Error saving file in MinIO',
    'ErrorSiteDoesntExist' => 'Site does not exist',
    'ErrorUserDontExist' => 'User does not exist',
    'ErrorDontOwnUser' => 'You do not have the right to modify this user',
    'ProfileDontExist' => 'Profile does not exist',
    'SiteDontExist' => 'Site does not exist',
    'ErrorUpdateUser' => 'Error updating user',
    'ErrorUpdateBusinessDay' => 'Error updating business day',
    'SuccessDeleteUser' => 'User deleted successfully',
    'ErrorGetCurrentUser' => 'Error retrieving current user',
    'InvalidDateFormat'=>'Invalid date format for the date: :date',
    'NullDateFormat'=>'Start or end date is null',
    'LeaveTypeNotFound'=>'Leave type not found',
    'LeaveTypeClientMismatch'=>'Leave type does not belong to the client',
    'SubFamilyMismatch'=>'Leave type subfamily does not belong to the client',
    'ErrorGetUserFromApp' => 'Error retrieving user from app',
    'ErrorStoreLeaveTypeSubFamily' => 'Error storing leave type sub-family',
    'ErrorUpdateOtherCustomer' => 'Error updating another customer',
    'CantDisplayCustomerFromThisApp' => 'You cannot display this customer from this app',
    'ErrorStoreClient' => 'Error storing client',
    'SuccessStoreCustomer' => 'Customer created successfully',
    'ErrorStoreCustomer' => 'Error creating customer',
    'ClientNotExist' => 'Client does not exist',
    'ErrorUpdateCustomer' => 'Error updating customer',
    'ErrorDeleteCustomer' => 'Error deleting customer',
    'SuccessUpdateLeaveDate' => 'Leave date updated successfully',
    'ErrorGetBusinessDay' => 'Error retrieving business day',
    'ErrorGetValidationScheme' => 'Error retrieving validation scheme',
    'ErrorGetDocs' => 'Error retrieving documents',
    'DocDontExistForSite' => 'Document does not exist for this site',
    'ErrorGetDoc' => 'Error retrieving document',
    'ErrorIdArrayDontExist' => 'Array ID does not exist',
    'SiteDontExistForClient' => 'Site does not exist for this client',
    'ErrorFileDontExist' => 'File does not exist',
    'ErrorStoreFile' => 'Error storing file',
    'SuccessStoreDoc' => 'Document created successfully',
    'ErrorLoadFile' => 'Error loading file',
    'DocDontExistForClient' => 'Document does not exist for this client',
    'CantDeleteForUpdateFile' => 'Cannot delete this document because it is used to update a file',
    'CantDeleteFile' => 'Cannot delete this file',
    'ErrorDeleteFile' => 'Error deleting file',
    'ErrorGetExportHistory' => 'Error retrieving export history',
    'ErrorNoHistoryForClient' => 'No history for this client',
    'ErrorDeleteExportHistory' => 'Error deleting export history',
    'ErrorCancelLeave' => 'Error canceling leave',
    'ErrorValidateCancelLeave' => 'Error validating leave cancellation',
    'SuccessGetLeaveTypes' => 'Leave types retrieved successfully',
    'SuccessGetSite' => 'Site retrieved successfully',
    'SuccessUpdateSite' => 'Site updated successfully',
    'SuccessGetTag' => 'Tag retrieved successfully',
    'ErrorGetTag' => 'Error retrieving tag',
    'ErrorGetTeamFromApp' => 'Error retrieving team from app',
    'ErrorStoreTeamFromApp' => 'Error storing team from app',
    'ErrorUpdateTeamFromApp' => 'Error updating team from app',
    'ErrorDeleteTeamFromApp' => 'Error deleting team from app',
    'InvalideData' => 'Invalid data',
    'SuccessStoreUser' => 'User registered',
    'MustBeInteger' => 'Must be an integer',
    'IsRequired' => 'Is required',
    'MustBeEnum' => 'Must be an enumeration',
    'MustBeString' => 'Must be a string',
    'MustBeBool' => 'Must be a boolean',
    'MustBeNotEmpty' => 'Must not be empty',
    'MustBeNumber' => 'Must be a number',
    'MustBeEmail' => 'Must be an email',
    'MustBeUUID' => 'Must be a UUID',
    'MustBeDate' => 'Must be a date',
    'MustBeDateAfter' => 'Must be a date after',
    'MustBeDateBefore' => 'Must be a date before',
    'MustRespectFormat' => 'Must respect the format',
    'UserNotFound' => 'User not found',
    'ErrorIdAbove' => 'ID must be greater than 0',
    'ErrorIdNumber' => 'ID must be a number',
    'ErrorFileType' => 'Invalid file type',
    'ErrorFileSize' => 'File size is too large',
    'ErrorSendAlert' => 'Error sending alert',
    'ErrorSaveHistoric' => 'Error saving history',
    'ClientAlreadyExist' => 'Client already exists',
    'ExportNotFound' => 'Export not found',
    'ReasonDontExist' => 'Reason not found',
    'LeaveRefused' => 'Leave refused',
    'UpdateDocIdError' => 'Error updating document ID',
    'CantFindUuid' => 'Cannot find UUID',
    'CantDoIt' => 'You do not have the rights to perform this action',
    'HeadersNotFound' => 'Headers not found',
    'CustomerNotFound' => 'Customer not found',
    'ApiProctectedBySignature' => 'API protected by signature',
    'ParameterNotAllowed' => "The parameter ':param_name' is not allowed on this route.",
    'RequiredValueForParameter' => 'The value for the parameter :name is required.',
    'UnsignedNumericValue' => 'The expected format for :name is an unsigned numeric value.',
    'RequiredBoolean' => 'The expected format for :name is a boolean value 0/1.',
    'RequiredDate' => 'The expected format for :name is a date value',
    'RequiredDateTime' => 'The expected format for :name is a datetime value',
    'RequiredUuid' => 'The expected format for :name is a UUID value',
    'RequiredString' => 'The expected format for :name is a string value.',
    'InvalidValue' => "The value ':value' is not allowed. Allowed values: :allowed_values.",
    'DirectionAscDesc' => 'The direction must be either ascending (asc) or descending (desc).',
    'LackDataForStore' => 'You are missing data to save.',
    'FileWeight' => 'The file is too large: 5MB maximum.',
    'FileTypeJpgPng' => 'The file type is not supported, JPG or PNG only.',
    'MustFilterById' => 'You must filter by id',
    'ClientTableNotFound' => 'The client table does not exist',
    'LeaveTypeNeedFile'=>'This type of leave needs a file',
    'UserNotExist' => 'The user with matricule :matricule does not exist',
    'UserDeleted' => 'The user with matricule :matricule has been deleted',
    'SiteNotExist' => 'The site of the user :matricule does not exist',
    'LeaveNotCreated' => 'The leave for the user :matricule could not be created',
    'CurrentUser' => 'Current user does not have the correct profile',
    'ManagedByCurrentUser'=>'You are not managed by this user',
    'LastNameMissingFromSalarie' => 'Sheet :sheetnbmr Line :line: Last name is missing from the Employee column (value: ":salarie").',
    'EmailMissingInvalidNormalized' => 'Sheet :sheetnbmr Line :line: Email is missing or invalid after normalization (original email: ":original_email").',
    'EmailFormatInvalidNormalized' => 'Sheet :sheetnbmr Line :line: Invalid email format after normalization (normalized email: ":normalized_email", original email: ":original_email").',
    'UserNotFoundImport' => 'Sheet :sheetnbmr Line :line: User not found with normalized email ":normalized_email" (original email: ":original_email").',
    'UserLastNameMismatch' => 'Sheet :sheetnbmr Line :line: The name for the user (email: :email) does not match. Expected name (DB): ":expected_name", name found (file): ":name_from_file".',
    'UserMissingSite' => 'Sheet :sheetnbmr Line :line: Site is missing for user with normalized email ":normalized_email".',
    'SoldeRttInitialInvalidOrMissing' => 'Sheet :sheetnbmr Line :line: Initial RTT balance is invalid or missing (value: ":value").',
    'AcquisRttInvalidOrMissing' => 'Sheet :sheetnbmr Line :line: The \'RTT Earned\' value is invalid or missing (value: ":value").',
    'PrisRttInvalidOrMissing' => 'Sheet :sheetnbmr Line :line: The \'RTT Taken\' value is invalid or missing (value: ":value").',
    'SoldeRttFinalInvalidOrMissing' => 'Sheet :sheetnbmr Line :line: The final RTT balance is invalid or missing (value: ":value").',
    'RttBalanceMismatch' => 'Sheet :sheetnbmr Line :line for :email: RTT balance mismatch. File balance: :solde_fichier, Calculated balance: :solde_calcule.',
    'SoldeCpInvalidOrMissing'=> 'Sheet :sheetnbmr Line :line: Paid Leave balance invalid or missing (value: :value).',

    'CpAcquiredMismatchDetected' => '[X] - Sheet :sheet, Line :line : Leave Counter for :user_lastname incorrect: Leave days from file (:file_acquired_value) do not match current leave days in database (:db_acquired_value).',
    'CpAnnualUpdate' => 'Sheet :sheet, Line :line : Leave Counter for :user_lastname updated. New leave days acquired: :new_acquired_value.',
    'CpNewCounterCreated' => 'Sheet :sheet, Line :line : Leave Counter for :user_lastname created with :acquired_value leave days acquired.',
    'DbErrorCpUpdate' => 'Sheet :sheet, Line :line : Database error while updating Leave Counter (:leave_type_code). Details: :db_message',

    'ErrorLeaveStatusDontAllowModification' => 'Error, the leave status does not allow modification',
];
