<?php

return [
    'morning' => ' (Morning)',
    'afternoon' => ' (Afternoon)',
    'CreateSettingsSuccess' => 'Settings created successfully',
    'RemoveSettingsSuccess' => 'Settings removed successfully.',
    'UpdateSuccess' => 'Update successful',
    'WaitingPay' => 'Waiting for payment',
    'Waiting' => 'Waiting',
    'InProcess' => 'In progress',
    'SuccessStoreLeave' => 'Leave created successfully',
    'SuccessCancelLeave' => 'Leave canceled successfully',
    'AskForCancelLeave' => 'Leave cancellation request sent',
    'SuccessCallback' => 'Callback successful',
    'CantCancelLeaveNotValidated' => 'Cannot cancel this leave as it has not been validated',
    'CantCancelLeaveTransmitted'=>'Cannot cancel this leave because it has been transmitted',
    'CantCancelLeaveRefused' => 'Cannot cancel this leave as it has been refused',
    'LeaveCanceled' => 'Leave canceled',
    'DontOwnLeaveOrManagerUser' => 'You are not authorized to modify this leave or you are not the user’s manager',
    'CantCancelLeaveValidated' => 'Cannot cancel this leave as it has been validated',
    'NotAdminFromUser' => 'You are not an administrator for this user',
    'ErrorTransmisLeaveWrongStatus' => 'Error: leave cannot be transmitted due to incorrect status',
    'SuccessRefuseLeave' => 'Leave refused',
    'SuccessMassValidationLeave' => 'Leaves transmitted',
    'SuccessTransmitLeave' => 'Leave transmitted',
    'SuccessGetClosedDay' => 'Holidays retrieved',
    'SuccessGetHolidays' => 'Vacations retrieved',
    'SuccessGetLeave' => 'Leave retrieved',
    'SuccessGetUser' => 'User retrieved',
    'SuccessGetCompteur' => 'Counters retrieved',
    'SuccessStoreCompteur' => 'Counter created',
    'SuccessUpdateCompteur' => 'Counter updated',
    'SuccessUpdateUser' => 'User updated',
    'SuccessUpdateBusinessDay' => 'Business day updated',
    'SuccessGetCurrentUser' => 'Current user retrieved',
    'SuccessLogout' => 'Logout successful',
    'SuccessUpdateCustomer' => 'Customer updated',
    'SuccessDeleteCustomer' => 'Customer deleted',
    'SuccessGetBusinessDay' => 'Business day retrieved',
    'SuccessGetDocs' => 'Documents retrieved',
    'SuccessUpdateDoc' => 'Document updated',
    'SuccessDeleteFile' => 'File deleted',
    'SuccessGetHistory' => 'History retrieved',
    'SuccessDeleteHistory' => 'History deleted',
    'CantCancelLeave' => 'Cannot cancel this leave',
    'OtherAdminValidated' => 'Another admin has already validated this leave',
    'UserNotInClient' => 'The user does not belong to your client',
    'SuccessAttachUsersToDirector' => 'Users attached to director',
    'SuccessDetachUsersToDirector' => 'Users detached from director',
    'SuccessStoreLeaveByTakingInNCounts' => 'Leave created using counter N',
    'SuccessStoreHoliday'=>'The holidays have been successfully created',
    'UserWithNoTags' => 'Employees without teams',
    'UserNotManaged' => 'Unmanaged employees',
    'success_store_leave_with_errors' => 'Leaves successfully created, but errors occurred.',
    'AnyTags' => 'No team',
    'ImportSucces' => 'Import successful',
    'ErrorImport' => 'Error during import',
    'acquis_cp_n_1' => 'acquired_cp_n_1',
    'pris_cp_n_1' => 'taken_cp_n_1',
    'solde_cp_n_1' => 'balance_cp_n_1',
    'acquis_cp_n' => 'acquired_cp_n',
    'pris_cp_n' => 'taken_cp_n',
    'solde_cp_n' => 'balance_cp_n',
    'acquis_type_n_1' => 'acquired_:type_n_1',
    'pris_type_n_1' => 'taken_:type_n_1',
    'solde_type_n_1' => 'balance_:type_n_1',
    'acquis_type_n' => 'acquired_:type_n',
    'pris_type_n' => 'taken_:type_n',
    'solde_type_n' => 'balance_:type_n',
    'ImportUserLeaveCountPartiallySucces' => 'Errors occurred during the import of counters (:successRows / :rows lines imported).',
    'ImportUserLeavesPartiallySucces' => 'Errors occurred during the import of absences (:successRows / :rows lines imported).',
    'RegistrationNumber' => 'Registration Number',
    'RegistrationNumberEmployee' => 'Employee Registration Number',
    'CompanyName' => 'Company Name',
    'MainEstablishment' => 'Main Establishment',
    'Email' => 'Email',
    'Employee' => 'Employee',
    'EmployeeName' => 'First Name Last Name',
    'EmployeeLastname' => 'Lastname',
    'EmployeeFirstname' => 'Firstname',
    'Establishment' => 'Establishment',
    'EntryDate' => 'Entry Date',
    'Acquired' => 'Acquired',
    'Taken' => 'Taken',
    'Balance' => 'Balance',
    'Label' => 'Label',
    'CodeConges' => 'Leave code',
    'CodeAbsence' => 'Absence code',
    'StartDate' => 'Start Date',
    'EndDate' => 'End Date',
    'Duration' => 'Number of days',
    'Status' => 'Status',
    'Site' => 'Site',
    'TypeCongesAbsence' => 'Type of absence/leave',
    'TypeAbsence' => 'Type of absence',
    'TypeConges' => 'Type',
    'Tag' => 'Team',
    'HeaderRegistrationNumber' => 'registration_number',
    'HeaderRegistrationNumberEmployee' => 'employee_registration_number',
    'HeaderCompanyName' => 'company_name',
    'HeaderEstablishment' => 'establishment',
    'HeaderMainEstablishment' => 'main_establishment',
    'HeaderEmail' => 'email',
    'HeaderEmployee' => 'employee',
    'HeaderLastname' => 'last_name',
    'HeaderFirstname' => 'first_name',
    'HeaderEntryDate' => 'entry_date',
    'HeaderEntryDdate' => 'entry_date',
    'HeaderStartDate' => 'start_date',
    'HeaderEndDate' => 'end_date',
    'HeaderDuration' => 'duration',
    'HeaderAcquired' => 'acquired',
    'HeaderTaken' => 'taken',
    'HeaderBalance' => 'balance',
    'HeaderRemaining' => 'pay',
    'HeaderLeaveTypeName' => 'leave_type_name',
    'Total' => 'Total',
    'TotalGeneral' => 'Grand Total',
    'store_leave_with_errors' => 'An error seems to have occurred when setting the leaves...',
    'waiting_validation_by_manager' => 'Waiting for validation by :manager',
    'waiting_cancellation_by_manager' => 'Waiting for cancellation by :manager',
    'VALIDATED' => 'Validated',
    'SUBMITTED' => 'Submitted for validation',
    'REFUSED' => 'Refused',
    'CANCELED' => 'Canceled',
    'TRANSMITTED' => 'Transmitted to payroll',
    'SUBMITTED_TO_CANCELLATION' => 'Submitted for cancellation',
    'ImportPartiallySucces' => 'Import partially successful',
    'UnsupportedFileType' => 'Unsupported file type. Please use a CSV, XLSX, or XLS file.',
    'import' => [
        'error_title' => 'Input Error',
        'error_value' => 'The value is not in the list.',
        'prompt_title' => 'Choose from the list',
        'prompt_value' => 'Please choose a value from the dropdown list.',
    ],
    'AlreadyTreatedLeavesCount' =>'Already treated leaves count',
    'WaitingValidationLeavesCount' =>'Waiting validation leaves count',
    'WaitingCancelationLeavesCount' =>'Waiting cancelation leaves count',
    'SuccessCancelLeaveSubToCancel'=> 'The cancellation request has been denied. The leave is now canceled.',
    'SuccessValidateLeaveSubToCancel'=> 'The cancellation request has been successfully validated, the leave is therefore canceled.',
    'SuccessValidateLeaveSubToCancelValidated' => 'The cancellation request has been denied. The leave has returned to its \'Validated\' status.',
    'SuccessValidateLeaveSubToCancelSubmitted' => 'The cancellation request has been denied. The leave has returned to its \'Submitted\' status.',
    'SuccessMassValidationLeaveSubToCancel'=> 'The cancellation requests have been successfully validated, the leaves are therefore canceled.',
    'CantCancelAdminOwnLeave'=>'An administrator cannot cancel their own leave',
    'SuccessTransmittedLeaveCanceled'=> 'The cancellation request has been successfully validated, the leave is therefore \'Cancelled\'.',
    'SuccessTransmittedLeaveCanceled'=> 'The cancellation request has been successfully validated, the leave is therefore  \'Cancelled\'.',
    'SheetIgnored' => "ExtraCom Import: Sheet ':sheetName' ignored.",
    'CpTypeNotFound' => 'Sheet :sheetnbmr Line :line: CP leave type not found for the site.',
    'RttTypeNotFound' => 'Sheet :sheetnbmr Line :line: RTT leave type not found for the site.',
];
