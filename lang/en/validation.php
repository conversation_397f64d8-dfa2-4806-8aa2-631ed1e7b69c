<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such as
    | the size rules. Feel free to adjust each of these messages here.
    |
    */

    'accepted' => 'The :attribute field must be accepted.',
    'accepted_if' => 'The :attribute field must be accepted when :other is :value.',
    'active_url' => 'The :attribute field is not a valid URL.',
    'after' => 'The :attribute field must be a date after :date.',
    'after_or_equal' => 'The :attribute field must be a date after or equal to :date.',
    'alpha' => 'The :attribute field may only contain letters.',
    'alpha_dash' => 'The :attribute field may only contain letters, numbers, and dashes.',
    'alpha_num' => 'The :attribute field may only contain numbers and letters.',
    'array' => 'The :attribute field must be an array.',
    'ascii' => 'The :attribute field may only contain one-byte alphanumeric characters and symbols.',
    'before' => 'The :attribute field must be a date before :date.',
    'before_or_equal' => 'The :attribute field must be a date before or equal to :date.',
    'between' => [
        'numeric' => 'The :attribute field must be between :min and :max.',
        'file' => 'The :attribute field must be between :min and :max kilobytes.',
        'string' => 'The :attribute field must be between :min and :max characters.',
        'array' => 'The :attribute field must have between :min and :max items.',
    ],
    'boolean' => 'The :attribute field must be true or false.',
    'confirmed' => 'The :attribute confirmation field does not match.',
    'current_password' => 'The password is incorrect.',
    'date' => 'The :attribute field is not a valid date.',
    'date_equals' => 'The :attribute field must be a date equal to :date.',
    'date_format' => 'The :attribute field does not match the format :format.',
    'decimal' => 'The :attribute field must have :decimal decimal(s).',
    'declined' => 'The :attribute field must be declined.',
    'declined_if' => 'The :attribute field must be declined when :other is :value.',
    'different' => 'The :attribute and :other fields must be different.',
    'digits' => 'The :attribute field must be :digits digits.',
    'digits_between' => 'The :attribute field must be between :min and :max digits.',
    'dimensions' => 'The :attribute field has invalid image dimensions.',
    'distinct' => 'The field has a duplicate value.',
    'doesnt_end_with' => 'The :attribute field cannot end with one of the following: :values.',
    'doesnt_start_with' => 'The :attribute field cannot start with one of the following: :values.',
    'email' => 'The :attribute field must be a valid email address.',
    'ends_with' => 'The :attribute field must end with one of the following: :values.',
    'enum' => 'The selected :attribute is invalid.',
    'exists' => 'The selected :attribute is invalid.',
    'file' => 'The :attribute field must be a file.',
    'filled' => 'The :attribute field is required.',
    'gt' => [
        'array' => 'The :attribute field must have more than :value items.',
        'file' => 'The :attribute field must be greater than :value kilobytes.',
        'numeric' => 'The :attribute field must be greater than :value.',
        'string' => 'The :attribute field must be greater than :value characters.',
    ],
    'gte' => [
        'array' => 'The :attribute field must have :value items or more.',
        'file' => 'The :attribute field must be greater than or equal to :value kilobytes.',
        'numeric' => 'The :attribute field must be greater than or equal to :value.',
        'string' => 'The :attribute field must be greater than or equal to :value characters.',
    ],
    'image' => 'The :attribute field must be an image.',
    'in' => 'The selected :attribute is invalid.',
    'in_array' => 'The :attribute field does not exist in :other.',
    'integer' => 'The :attribute field must be an integer.',
    'ip' => 'The :attribute field must be a valid IP address.',
    'ipv4' => 'The :attribute field must be a valid IPv4 address.',
    'ipv6' => 'The :attribute field must be a valid IPv6 address.',
    'json' => 'The :attribute field must be a valid JSON string.',
    'lowercase' => 'The :attribute field must be lowercase.',
    'lt' => [
        'array' => 'The :attribute field must have less than :value items.',
        'file' => 'The :attribute field must be less than :value kilobytes.',
        'numeric' => 'The :attribute field must be less than :value.',
        'string' => 'The :attribute field must be less than :value characters.',
    ],
    'lte' => [
        'array' => 'The :attribute field must not have more than :value items.',
        'file' => 'The :attribute field must be less than or equal to :value kilobytes.',
        'numeric' => 'The :attribute field must be less than or equal to :value.',
        'string' => 'The :attribute field must be less than or equal to :value characters.',
    ],
    'mac_address' => 'The :attribute field must be a valid MAC address.',
    'max' => [
        'array' => 'The :attribute field may not have more than :max items.',
        'file' => 'The :attribute field may not be greater than :max kilobytes.',
        'numeric' => 'The :attribute field may not be greater than :max.',
        'string' => 'The :attribute field may not be greater than :max characters.',
    ],
    'max_digits' => 'The :attribute field may not have more than :max digits.',
    'mimes' => 'The :attribute field must be a file of type: :values.',
    'mimetypes' => 'The :attribute field must be a file of type: :values.',
    'min' => [
        'array' => 'The :attribute field must have at least :min items.',
        'file' => 'The :attribute field must be at least :min kilobytes.',
        'numeric' => 'The :attribute field must be at least :min.',
        'string' => 'The :attribute field must be at least :min characters.',
    ],
    'min_digits' => 'The :attribute field must have at least :min digits.',
    'missing' => 'The :attribute field must be missing.',
    'missing_if' => 'The :attribute field must be missing when :other is :value.',
    'missing_unless' => 'The :attribute field must be missing unless :other is :value.',
    'missing_with' => 'The :attribute field must be missing when :value is present.',
    'missing_with_all' => 'The :attribute field must be missing when :values are present.',
    'multiple_of' => 'The :attribute field must be a multiple of :value.',
    'not_in' => 'The selected :attribute is invalid.',
    'not_regex' => 'The :attribute field has an invalid format.',
    'numeric' => 'The :attribute field must be a number.',
    'password' => [
        'letters' => 'The :attribute field must contain at least one letter.',
        'mixed' => 'The :attribute field must contain at least one uppercase and one lowercase letter.',
        'numbers' => 'The :attribute field must contain at least one number.',
        'symbols' => 'The :attribute field must contain at least one symbol.',
        'uncompromised' => 'The given :attribute has appeared in a data breach. Please choose a different :attribute.',
    ],
    'present' => 'The :attribute field must be present.',
    'prohibited' => 'The :attribute field is prohibited.',
    'prohibited_if' => 'The :attribute field is prohibited when :other is :value.',
    'prohibited_unless' => 'The :attribute field is prohibited unless :other is in :values.',
    'prohibits' => 'The :attribute field prohibits the presence of :other.',
    'regex' => 'The :attribute field format is invalid.',
    'required' => 'The :attribute field is required.',
    'required_array_keys' => 'The :attribute field must contain entries for: :values.',
    'required_if' => 'The :attribute field is required when :other is :value.',
    'required_if_accepted' => 'The :attribute field is required when :other is accepted.',
    'required_unless' => 'The :attribute field is required unless :other is in :values.',
    'required_with' => 'The :attribute field is required when :values is present.',
    'required_with_all' => 'The :attribute field is required when :values are present.',
    'required_without' => 'The :attribute field is required when :values is not present.',
    'required_without_all' => 'The :attribute field is required when none of :values are present.',
    'same' => 'The :attribute and :other fields must match.',
    'size' => [
        'numeric' => 'The :attribute field must be :size.',
        'file' => 'The :attribute field must be :size kilobytes.',
        'string' => 'The :attribute field must be :size characters.',
        'array' => 'The :attribute field must have :size items.',
    ],
    'starts_with' => 'The :attribute field must start with one of the following: :values.',
    'string' => 'The :attribute field must be a string.',
    'timezone' => 'The :attribute field must be a valid timezone.',
    'unique' => 'The :attribute field has already been taken.',
    'unique_entry' => 'Each entry must be associated with a unique email address and first name.',
    'uploaded' => 'The :attribute field failed to upload.',
    'uppercase' => 'The :attribute field must be uppercase.',
    'url' => 'The :attribute field must be a valid URL.',
    'ulid' => 'The :attribute field must be a valid ULID.',
    'uuid' => 'The :attribute field must be a valid UUID.',
    'emptyValue' => 'The :attribute field is empty.',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Lines
    |--------------------------------------------------------------------------
    |
    | Here, you can specify custom validation messages for attributes using the "attribute.rule" convention to name the lines. This allows us to quickly
    | specify a custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'leave_already_taken' => 'Record not possible. Leave has already been taken on this date.',
        'empty_field' => 'Line :line : The :field field was not filled out.',
        'matricule_not_specified' => 'Line :line : The employee number was not provided.',
        'site_not_specified' => 'Line :line : The site was not specified.',
        'site_not_existed' => 'Line :line : The site does not exist - :site.',
        'absence_type_not_found' => 'Line :line : Type of absence - Code :leaveTypeCode',
        'leave_type_not_found' => 'Line :line : Leave type not found - Code :leaveTypeCode',
        'user_bad_site' => 'Line :line : User is on the wrong site (:site) - Registration number :matricule',
        'user_not_found' => 'Line :line : User not found - Employee number :matricule',
        'user_not_specified' => 'Line :line : The user was not specified',
        'user_deleted' => 'Line :line : The user is deleted - Employee number :matricule',
        'balance_invalid' => 'Line :line : The balance does not match the expected value',
        'user_not_on_your_clients' => 'Line :line : The user is not part of your client - Employee number :matricule',
        'InvalidLeaveTypeNameFormat' => 'Line :line : The leave type must be a string',
        'InvalidDurationFormat' => 'Line :line : The leave duration must be numeric',
        'InvalidDurationValue' => 'Line :line: The duration of the absence must be greater than 0',
        'InvalidMatriculeFormat' => 'Line :line : The employee number must be numeric',
        'InvalidLastNameFormat' => 'Line :line : The last name must be a string',
        'InvalidFirstNameFormat' => 'Line :line : The first name must be a string',
        'InvalidDateFormat' => 'Line :line : Invalid date format',
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to replace our attribute placeholder with something more reader-friendly,
    | like "Email Address" instead of "email".
    | This simply helps make our message more expressive.
    |
    */

    'attributes' => [
        'activity' => 'activity',
        'activities' => 'activities',
        'address' => 'address',
        'addresses' => 'addresses',
        'age' => 'age',
        'ages' => 'ages',
        'amount' => 'amount',
        'amounts' => 'amounts',
        'answer' => 'answer',
        'answers' => 'answers',
        'available' => 'available',
        'availables' => 'availables',
        'barcode' => 'barcode',
        'barcodes' => 'barcodes',
        'birth_date' => 'birth date',
        'brand' => 'brand',
        'brands' => 'brands',
        'brand_name' => 'brand name',
        'buying_price' => 'buying price',
        'category' => 'category',
        'categories' => 'categories',
        'city' => 'city',
        'cities' => 'cities',
        'civility' => 'civility',
        'civilities' => 'civilities',
        'comment' => 'comment',
        'comments' => 'comments',
        'company' => 'company',
        'companies' => 'companies',
        'confirmed' => 'confirmed',
        'confirmed_at' => 'confirmed at',
        'content' => 'content',
        'contents' => 'contents',
        'country' => 'country',
        'countries' => 'countries',
        'customer' => 'customer',
        'customers' => 'customers',
        'day' => 'day',
        'days' => 'days',
        'date_end' => 'end date',
        'date_start' => 'start date',
        'directory' => 'directory',
        'directory_name' => 'directory name',
        'directories' => 'directories',
        'directories_name' => 'directory names',
        'directories_names' => 'directory names',
        'email_banned' => 'banned email',
        'email_confirmed' => 'confirmed email',
        'email_validated' => 'validated email',
        'email_prohibited' => 'prohibited email',
        'emails_banned' => 'banned emails',
        'emails_confirmed' => 'confirmed emails',
        'emails_validated' => 'validated emails',
        'emails_prohibited' => 'prohibited emails',
        'file' => 'file',
        'files' => 'files',
        'first_name' => 'first name',
        'first_names' => 'first names',
        'gender' => 'gender',
        'genders' => 'genders',
        'hour' => 'hour',
        'hours' => 'hours',
        'is_active' => 'is active?',
        'is_banned' => 'banned?',
        'job' => 'job',
        'jobs' => 'jobs',
        'last_name' => 'last name',
        'last_names' => 'last names',
        'link' => 'link',
        'links' => 'links',
        'month' => 'month',
        'name' => 'name',
        'names' => 'names',
        'office' => 'office',
        'offices' => 'offices',
        'other' => 'other',
        'others' => 'others',
        'paid_at' => 'paid at',
        'password' => 'password',
        'password_confirmation' => 'password confirmation',
        'password_current' => 'current password',
        'passwords' => 'passwords',
        'phone' => 'phone',
        'phones' => 'phones',
        'postal_code' => 'postal code',
        'price' => 'price',
        'published_at' => 'published at',
        'quantity' => 'quantity',
        'quantities' => 'quantities',
        'rate' => 'rate',
        'rates' => 'rates',
        'response' => 'response',
        'responses' => 'responses',
        'role' => 'role',
        'roles' => 'roles',
        'second' => 'second',
        'seconds' => 'seconds',
        'siren_number' => 'siren number',
        'siret_number' => 'siret number',
        'size' => 'size',
        'sizes' => 'sizes',
        'status' => 'status',
        'statuses' => 'statuses',
        'street' => 'street',
        'subfolder' => 'subfolder',
        'subfolders' => 'subfolders',
        'subdirectory' => 'subdirectory',
        'subdirectories' => 'subdirectories',
        'subject' => 'subject',
        'subjects' => 'subjects',
        'summary' => 'summary',
        'summarys' => 'summaries',
        'supplier' => 'supplier',
        'suppliers' => 'suppliers',
        'tax' => 'tax',
        'time' => 'time',
        'title' => 'title',
        'titles' => 'titles',
        'user' => 'user',
        'users' => 'users',
        'username' => 'username',
        'usernames' => 'usernames',
        'value' => 'value',
        'values' => 'values',
        'vat' => 'VAT',
        'vat_rate' => 'VAT rate',
        'website' => 'website',
        'websites' => 'websites',
        'year' => 'year',
        'years' => 'years',
        'default_footer_text' => 'default footer text',
        'media_footer' => 'footer media',
        'survey_id' => 'survey ID',
        'firstname' => 'first name',
        'lastname' => 'last name',
        'email' => 'email',
        'label' => 'label',
        'description' => 'description',
        'started_at' => 'start date',
        'ended_at' => 'end date',
        'accompanying_person_limit' => 'accompanying person limit',
        'event_status_id' => 'event status ID',
        'guest_ids' => 'guest IDs',
        'event_id' => 'event ID',
        'phone_number' => 'phone number',
        'phone_code' => 'phone code',
        'app_from' => 'created by',
        'sticker' => 'sticker',
        'is_vip' => 'is VIP',
        'user_related_id' => 'related user ID',
        'is_notified_user_related' => 'notify related user',
        'is_owner' => 'is organizer',
        'is_ticket_generated' => 'ticket generated',
        'collection_name' => 'collection name',
        'model_type' => 'model type',
        'media' => 'media',
        'header_text' => 'header text',
        'guests' => 'guests',
        'entry.survey_id' => 'survey ID of entry',
        'entry.firstname' => 'first name of entry',
        'entry.lastname' => 'last name of entry',
        'entry.answers' => 'answers of entry',
        'entry_id' => 'entry ID',
        'question_id' => 'question ID',
        'hint' => 'hint',
        'order' => 'order',
        'question_type_id' => 'question type ID',
        'questionable_type' => 'questionable type',
        'questionable_id' => 'questionable ID',
        'is_required' => 'is required',
        'has_escape_answer' => 'has other answer',
        'location_id' => 'location ID',
        'capacity' => 'capacity',
        'overflow' => 'overflow allowed',
        'reminder_days_before_session' => 'days before session reminder',
        'thanks_days_after_session' => 'days after session thanks',
        'feedback_survey_id' => 'feedback survey ID',
        'is_survey_related' => 'is survey-related',
        'is_signable' => 'is signable',
        'details' => 'details',
        'creator_id' => 'creator ID',
        'survey_status_id' => 'survey status ID',
        'is_template' => 'is template',
        'max_entries' => 'max entries',
        'updated_by_id' => 'updated by user',
        'expires_at' => 'expires at',
        'footer_text' => 'footer text',
        'title_alignment' => 'title alignment',
        'missing_value_placeholder' => '[Missing Value]',
        'cell_error_detail' => "Sheet :sheet Row :row [Col: :column]: :errors (Value: ':value')",
        'holiday_already_exists_for_date_and_client' => 'A holiday already exists for this date and client.'
    ],
];
