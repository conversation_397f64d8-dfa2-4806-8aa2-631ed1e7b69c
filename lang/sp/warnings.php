<?php

return [

    'env' => [
        'ActionIsNotAvailableInEnv' => 'Esta acción no está disponible en el entorno :env',
    ],
    'ClientTableDosntExist' => 'La tabla de clientes no existe',
    'UnauthorizedAction' => 'Acción no autorizada',
    'SettingsAlreadyExists' => 'La configuración ya existe',
    'ErrorBatchUpdateTags' => 'Error al actualizar las etiquetas en lote',
    'TagAlreadyExists' => 'La etiqueta ya existe',
    'BadProfile' => 'Perfil inválido',
    'ProfileNotFound' => 'Perfil no encontrado',
    'InternalError' => 'Error interno',
    'DellUsersSuccess' => 'Usuarios eliminados con éxito',
    'AddUsersSuccess' => 'Usuarios añadidos con éxito',
    'LogoutSuccess' => 'Cierre de sesión exitoso',
    'UserAlreadyTreatedLeave' => 'Ya ha validado este permiso',
    'NoLeaveCounter' => 'No se encontró contador de permisos',
    'NotEnoughLeaveDay' => 'No hay suficientes días de permiso',
    'UserDontMatchJwt' => 'El usuario no coincide con el token JWT',
    'LeaveAlreadyExisteForDate' => 'La fecha solicitada ya está ocupada por otra solicitud',
    'DurationRequired' => 'Duración requerida',
    'DurationMustBeANumber' => 'La duración debe ser un número',
    'DurationMustBePositive' => 'La duración debe ser positiva',
    'ErrorLeaveDuration' => 'Error en la duración del permiso',
    'ErrorLeaveDurationHalfDayNotPermit' => 'Error en la duración del permiso, no se permiten medios días',
    'GetLeaveType' => 'Obtener tipo de permiso',
    'ErrorLeaveTypeDontExist' => 'Error, el tipo de permiso no existe',
    'ErrorLeaveTypeDontBelongToClient' => 'Error, el tipo de permiso no pertenece al cliente',
    'ErrorGetStatuses' => 'Error al obtener los estados',
    'ErrorStatusDontExist' => 'El estado no existe',
    'ErrorLeaveTypeSubFamilyDontExist' => 'Error, la subfamilia del tipo de permiso no existe',
    'ErrorLeaveTypeSubFamilyDontBelongToLeave' => 'Error, la subfamilia del tipo de permiso no pertenece al tipo de permiso',
    'ErrorStartDateRequired' => 'Fecha de inicio requerida',
    'ErrorEndDateRequired' => 'Fecha de fin requerida',
    'ErrorStartDateAboveEndDate' => 'La fecha de inicio es posterior a la fecha de fin',
    'ErrorSameDate' => 'Misma fecha de inicio y fin',
    'DontOwnLeave' => 'No tiene derecho a validar este permiso',
    'LeaveDontExist' => 'El permiso no existe',
    'NotAdminFromUser' => 'No es administrador de este usuario',
    'NotAdminOrManagerFromUser' => 'No es ni administrador ni gerente de este usuario',
    'NotDirectorOrManagerFromUser' => 'No eres director ni administrador de este usuario.',
    'CantRefuseLeaveWrongStatus' => 'No se puede rechazar este permiso porque tiene un estado incorrecto',
    'ErrorNotValidatedByLastManager' => 'Error, el permiso no ha sido validado por el último gerente',
    'ErrorLeaveStatusDontAllowValidation' => 'Error, el estado del permiso no permite su validación',
    'NotManagerFromUser' => 'No es gerente de este usuario',
    'SuccessValidateLeave' => 'Permiso validado con éxito',
    'SuccessMassValidationLeave' => 'Permisos validados con éxito',
    'ErrorValidateLeave' => 'Error al validar el permiso',
    'ErrorLeaveDontExist' => 'El permiso no existe',
    'NoManagerOrAdminForRefuseLeave' => 'No hay gerente o administrador para rechazar este permiso',
    'ErrorRefuseLeave' => 'Error al rechazar el permiso',
    'ErrorActionNotAllowed' => 'Acción no permitida',
    'ErrorNoAdminForTransmit' => 'Error, no hay administrador para transmitir este permiso',
    'ErrorTransmitLeave' => 'Error al transmitir el permiso',
    'ErrorStoreLeave' => 'Error al almacenar el permiso',
    'SuccessGetClosedDay' => 'Días festivos recuperados con éxito',
    'ErrorValidationData' => 'Error en la validación de datos',
    'ErrorGetClosedDay' => 'Error al recuperar los días festivos',
    'SuccessGetHolidays' => 'Vacaciones recuperadas con éxito',
    'ErrorGetHolidays' => 'Error al recuperar las vacaciones',
    'SuccessGetLeave' => 'Permiso recuperado con éxito',
    'ErrorGetLeave' => 'Error al recuperar el permiso',
    'ErrorDataArrayDontExist' => 'El array de datos no existe',
    'FileRequired' => 'Archivo requerido',
    'ActionNotAllowed' => 'Acción no permitida',
    'UserDontExist' => 'El usuario no existe',
    'UserBelongToAnotherClient' => 'El usuario pertenece a otro cliente',
    'ErrorDateLeaveTypeNotAllowed' => 'Error, la fecha no está permitida para este tipo de permiso',
    'DurationTooLong' => 'Duración demasiado larga',
    'ErrorValidateRefuseLeave' => 'Error al validar/rechazar el permiso',
    'ErrorGetAttachment' => 'Error al obtener el archivo adjunto',
    'CantGenerateFile' => 'No se puede generar el archivo',
    'ErrorExportLeave' => 'Error al exportar los permisos',
    'ErrorDontOwnLeaveType'=>'No tiene derecho a modificar este tipo de permiso',
    'ErrorLeaveTypeNotExist'=>'El tipo de permiso no existe',
    'LeaveTypeAlreadyExist'=>'Este tipo de permiso ya existe',
    'LeaveSubTypeDontBelongToCustomer'=>'La subfamilia del tipo de permiso no pertenece al cliente',
    'ErrorUpdateLeaveType'=>'Error al actualizar el tipo de permiso',
    'SuccessUpdateLeaveType'=>'Tipo de permiso actualizado con éxito',
    'NotRightForGetLeaveType'=>'No tiene derecho a obtener este tipo de permiso',
    'ErrorGetLeaveTypes'=>'Error al obtener los tipos de permiso',
    'SuccessGetLeaveType'=>'Tipo de permiso recuperado con éxito',
    'SuccessStoreLeaveType'=>'Tipo de permiso almacenado con éxito',
    'ErrorStoreLeaveType'=>'Error al almacenar el tipo de permiso',
    'SuccessDeleteLeaveType'=>'Tipo de permiso eliminado con éxito',
    'ErrorDeleteLeaveType'=>'Error al eliminar el tipo de permiso',
    'ErrorLeaveTypeCantBeDeleted'=>'Este tipo de permiso no se puede eliminar :leave_type_id',
    'ErrorGetManager'=>'Error al obtener el gerente',
    'ErrorStoreManager'=>'Error al almacenar el gerente',
    'ErrorUpdateManager'=>'Error al actualizar el gerente',
    'ErrorDeleteManager'=>'Error al eliminar el gerente',
    'SuccessStoreToken'=>'Token almacenado con éxito',
    'ErrorStoreToken'=>'Error al almacenar el token',
    'SuccessClearToken'=>'Token eliminado con éxito',
    'ErrorDontOwnSite'=>'No tiene derecho a modificar este sitio',
    'ErrorGetSite'=>'Error al obtener el sitio',
    'ValidationErrors'=>'Errores de validación',
    'SuccessGetSites'=>'Sitios recuperados con éxito',
    'ErrorUpdateSite'=>'Error al actualizar el sitio',
    'SuccessGetStatuses'=>'Estados recuperados con éxito',
    'NotAccessToTag'=>'No tiene acceso a esta etiqueta',
    'SuccessGetTags'=>'Etiquetas recuperadas con éxito',
    'ErrorGetTags'=>'Error al obtener las etiquetas',
    'SuccessGetUser'=>'Usuario recuperado con éxito',
    'ErrorGetUsers'=>'Error al obtener los usuarios',
    'SuccessUpdateTag'=>'Etiqueta actualizada con éxito',
    'ErrorUpdateTag'=>'Error al actualizar la etiqueta',
    'SuccessStoreTag'=>'Etiqueta almacenada con éxito',
    'ErrorStoreTag'=>'Error al almacenar la etiqueta',
    'ErrorDeleteTag'=>'Error al eliminar la etiqueta',
    'SuccessDeleteTag'=>'Etiqueta eliminada con éxito',
    'RelationAlreadyExist'=>'Relación ya existente',
    'SuccessAddUserToTag'=>'Usuario añadido a la etiqueta con éxito',
    'ErrorAddUserToTag'=>'Error al añadir el usuario a la etiqueta',
    'RelationDontExist'=>'Relación inexistente',
    'SuccessRemoveUserFromTag'=>'Usuario eliminado de la etiqueta con éxito',
    'ErrorRemoveUserFromTag'=>'Error al eliminar el usuario de la etiqueta',
    'SuccessUpdateUserTag'=>'Etiquetas del usuario actualizadas con éxito',
    'ErrorUpdateUserTag'=>'Error al actualizar las etiquetas del usuario',
    'ErrorGetTeam'=>'Error al obtener el equipo',
    'ErrorStoreTeam'=>'Error al almacenar el equipo',
    'ErrorUpdateTeam'=>'Error al actualizar el equipo',
    'ErrorDeleteTeam'=>'Error al eliminar el equipo',
    'SuccessImportFile'=>'Archivo importado con éxito',
    'ErrorImportFile'=>'Error al importar el archivo',
    'ErrorGetTeamCompteur'=>'Error al obtener el contador del equipo',
    'UserDoesNotExist'=>'El usuario no existe',
    'LeaveTypeDoesntExist'=>'El tipo de permiso no existe',
    'LeaveTypeBelongToAnotherClient'=>'El tipo de permiso pertenece a otro cliente',
    'UserLeaveCountAlreadyExist'=>'El contador de permisos del usuario ya existe',
    'ErrorStoreCompteur'=>'Error al almacenar el contador',
    'UserLeaveCountDoesntExist'=>'El contador de permisos del usuario no existe',
    'UserLeaveCountBelongToAnotherClient'=>'El contador de permisos del usuario pertenece a otro cliente',
    'ErrorUpdateCompteur'=>'Error al actualizar el contador',
    'ErrorExportCompteur'=>'Error al exportar el contador',
    'ErrorImportLeave'=>'Error al importar los permisos',
    'ErrorSaveFileInMinio'=>'Error al guardar el archivo en MinIO',
    'ErrorSiteDoesntExist'=>'El sitio no existe',
    'ErrorUserDontExist'=>'El usuario no existe',
    'ErrorDontOwnUser'=>'No tiene derecho a modificar este usuario',
    'ProfileDontExist'=>'El perfil no existe',
    'SiteDontExist'=>'El sitio no existe',
    'ErrorUpdateUser'=>'Error al actualizar el usuario',
    'ErrorUpdateBusinessDay'=>'Error al actualizar el día laborable',
    'SuccessDeleteUser' =>'Usuario eliminado con éxito',
    'ErrorGetCurrentUser'=>'Error al obtener el usuario actual',
    'InvalidDateFormat'=>'Formato de fecha inválido para la fecha: :date',
    'NullDateFormat'=>'La fecha de inicio o fin es nula',
    'LeaveTypeNotFound'=>'Tipo de permiso no encontrado',
    'LeaveTypeClientMismatch'=>'El tipo de permiso no pertenece al cliente',
    'SubFamilyMismatch'=>'La subfamilia del tipo de permiso no pertenece al cliente',
    'ErrorGetUserFromApp'=>'Error al obtener el usuario desde la aplicación',
    'ErrorStoreLeaveTypeSubFamily'=>'Error al almacenar la subfamilia del tipo de permiso',
    'ErrorUpdateOtherCustomer'=>'Error al actualizar otro cliente',
    'CantDisplayCustomerFromThisApp'=>'No puede mostrar este cliente desde esta aplicación',
    'ErrorStoreClient'=>'Error al almacenar el cliente',
    'SuccessStoreCustomer'=>'Cliente creado con éxito',
    'ErrorStoreCustomer'=>'Error al crear el cliente',
    'ClientNotExist'=>'El cliente no existe',
    'ErrorUpdateCustomer'=>'Error al actualizar el cliente',
    'ErrorDeleteCustomer'=>'Error al eliminar el cliente',
    'SuccessUpdateLeaveDate'=>'Fecha de permiso actualizada con éxito',
    'ErrorGetBusinessDay'=>'Error al obtener el día laborable',
    'ErrorGetValidationScheme'=>'Error al obtener el esquema de validación',
    'ErrorGetDocs'=>'Error al obtener los documentos',
    'DocDontExistForSite'=>'El documento no existe para este sitio',
    'ErrorGetDoc'=>'Error al obtener el documento',
    'ErrorIdArrayDontExist'=>'El array de ID no existe',
    'SiteDontExistForClient'=>'El sitio no existe para este cliente',
    'ErrorFileDontExist'=>'El archivo no existe',
    'ErrorStoreFile'=>'Error al almacenar el archivo',
    'SuccessStoreDoc'=>'Documento creado con éxito',
    'ErrorLoadFile'=>'Error al cargar el archivo',
    'DocDontExistForClient'=>'El documento no existe para este cliente',
    'CantDeleteForUpdateFile'=>'No se puede eliminar este documento porque se utiliza para actualizar un archivo',
    'CantDeleteFile'=>'No se puede eliminar el archivo',
    'ErrorDeleteFile'=>'Error al eliminar el archivo',
    'ErrorGetExportHistory'=>'Error al obtener el historial de exportaciones',
    'ErrorNoHistoryForClient'=>'No hay historial para este cliente',
    'ErrorDeleteExportHistory'=>'Error al eliminar el historial de exportaciones',
    'ErrorCancelLeave'=>'Error al cancelar el permiso',
    'ErrorValidateCancelLeave'=>'Error al validar la cancelación del permiso',
    'SuccessGetLeaveTypes'=>'Tipos de permiso recuperados con éxito',
    'SuccessGetSite'=>'Sitio recuperado con éxito',
    'SuccessUpdateSite'=>'Sitio actualizado con éxito',
    'SuccessGetTag'=>'Etiqueta recuperada con éxito',
    'ErrorGetTag'=>'Error al obtener la etiqueta',
    'TagDontExist'=>'La etiqueta no existe',
    'ErrorGetTeamFromApp'=>'Error al obtener el equipo desde la aplicación',
    'ErrorStoreTeamFromApp'=>'Error al almacenar el equipo desde la aplicación',
    'ErrorUpdateTeamFromApp'=>'Error al actualizar el equipo desde la aplicación',
    'ErrorDeleteTeamFromApp'=>'Error al eliminar el equipo desde la aplicación',
    'InvalideData'=> 'Datos inválidos',
    'SuccessStoreUser' => 'Usuario registrado con éxito',
    'MustBeInteger'=>'Debe ser un entero',
    'IsRequired'=> 'Es requerido',
    'MustBeEnum'=>'Debe ser una enumeración',
    'MustBeString'=>'Debe ser una cadena de caracteres',
    'MustBeBool'=>'Debe ser un booleano',
    'MustBeNotEmpty'=> 'No debe estar vacío',
    'MustBeNumber'=>'Debe ser un número',
    'MustBeEmail'=>'Debe ser un correo electrónico',
    'MustBeUUID'=> 'Debe ser un UUID',
    'MustBeDate'=> 'Debe ser una fecha',
    'MustBeDateAfter'=>'Debe ser una fecha después',
    'MustBeDateBefore'=>'Debe ser una fecha antes',
    'MustRespectFormat'=>'Debe respetar el formato',
    'UserNotFound'=>'Usuario no encontrado',
    'ErrorIdAbove'=>'El identificador debe ser mayor que 0',
    'ErrorIdNumber'=>'El identificador debe ser un número',
    'ErrorFileType'=>'El tipo de archivo es inválido',
    'ErrorFileSize'=>'El tamaño del archivo es demasiado grande',
    'ErrorSendAlert'=>'Error al enviar la alerta',
    'ErrorSaveHistoric'=>'Error al guardar el historial',
    'ClientAlreadyExist'=>'El cliente ya existe',
    'ExportNotFound'=>'La exportación no fue encontrada',
    'ReasonDontExist'=>'Razón no encontrada',
    'LeaveRefused'=>'Permiso rechazado',
    'UpdateDocIdError'=>'Error al actualizar el identificador del documento',
    'CantFindUuid'=>'No se puede encontrar el uuid',
    'CantDoIt'=>'No tiene los derechos para realizar esta acción',
    'HeadersNotFound'=>'Encabezados no encontrados',
    'CustomerNotFound'=>'Cliente no encontrado',
    'ApiProctectedBySignature'=>'API protegida por firma',
    'ParameterNotAllowed' => "El parámetro ':param_name' no está permitido en esta ruta.",
    'RequiredValueForParameter'=>'El valor para el parámetro :name es obligatorio.',
    'UnsignedNumericValue'=>'El formato esperado para :name es un valor numérico sin signo.',
    'RequiredBoolean'=>'El formato esperado para :name es un valor booleano 0/1.',
    'RequiredDate'=>'El formato esperado para :name es un valor de fecha',
    'RequiredDateTime'=>'El formato esperado para :name es un valor de fecha y hora',
    'RequiredUuid'=>'El formato esperado para :name es un valor uuid',
    'RequiredString'=>'El formato esperado para :name es un valor de cadena.',
    'InvalidValue' => "El valor ':value' no está permitido. Valores permitidos: :allowed_values.",
    'DirectionAscDesc'=>'La dirección debe ser ascendente (asc) o descendente (desc).',
    'LackDataForStore'=>'Faltan datos para guardar.',
    'FileWeight'=>'El archivo es demasiado grande: máximo 5 MB.',
    'FileTypeJpgPng'=>'El tipo de archivo no es compatible, solo JPG o PNG.',
    'MustFilterById'=>'Debe filtrar por id',
    'ClientTableNotFound'=>'La tabla de clientes no fue encontrada',
    'LeaveTypeNeedFile'=>'Este tipo de permiso necesita un expediente',
    'UserNotExist' => 'El usuario con matrícula :matricule no existe',
    'UserDeleted' => 'El usuario con matrícula :matricule  ha sido eliminado',
    'SiteNotExist' => 'El sitio del usuario :matricule no existe',
    'LeaveNotCreated' => 'El permiso para el usuario :matricule no pudo ser creado',
    'CurrentUser' => 'El usuario actual no tiene el perfil correcto',
    'ManagedByCurrentUser'=>'No eres gestionado por este usuario',
    'ErrorLeaveStatusDontAllowModification' => 'Error, el estado del permiso no permite modificación',
];
