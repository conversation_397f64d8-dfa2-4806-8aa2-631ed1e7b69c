<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Reglas de validación de idioma
    |--------------------------------------------------------------------------
    |
    | Las siguientes reglas de idioma contienen los mensajes de error predeterminados
    | que se usan por el validador. Algunas de estas reglas tienen versiones
    | alternativas, como las reglas para el tamaño. Siéntete libre de modificar estos
    | mensajes aquí.
    |
    */

    'accepted' => 'El campo :attribute debe ser aceptado.',
    'accepted_if' => 'El campo :attribute debe ser aceptado cuando :other sea :value.',
    'active_url' => 'El campo :attribute no es una URL válida.',
    'after' => 'El campo :attribute debe ser una fecha después de :date.',
    'after_or_equal' => 'El campo :attribute debe ser una fecha igual o posterior a :date.',
    'alpha' => 'El campo :attribute solo debe contener letras.',
    'alpha_dash' => 'El campo :attribute solo debe contener letras, números y guiones.',
    'alpha_num' => 'El campo :attribute solo debe contener letras y números.',
    'array' => 'El campo :attribute debe ser un arreglo.',
    'ascii' => 'El campo :attribute solo puede contener caracteres alfanuméricos de un solo byte y símbolos.',
    'before' => 'El campo :attribute debe ser una fecha antes de :date.',
    'before_or_equal' => 'El campo :attribute debe ser una fecha igual o anterior a :date.',
    'between' => [
        'numeric' => 'El campo :attribute debe estar entre :min y :max.',
        'file' => 'El campo :attribute debe tener entre :min y :max kilobytes.',
        'string' => 'El campo :attribute debe tener entre :min y :max caracteres.',
        'array' => 'El campo :attribute debe tener entre :min y :max elementos.',
    ],
    'boolean' => 'El campo :attribute debe ser verdadero o falso.',
    'confirmed' => 'La confirmación del campo :attribute no coincide.',
    'current_password' => 'La contraseña es incorrecta.',
    'date' => 'El campo :attribute no es una fecha válida.',
    'date_equals' => 'El campo :attribute debe ser una fecha igual a :date.',
    'date_format' => 'El campo :attribute no coincide con el formato :format.',
    'decimal' => 'El campo :attribute debe tener :decimal decimales.',
    'declined' => 'El campo :attribute debe ser rechazado.',
    'declined_if' => 'El campo :attribute debe ser rechazado cuando :other sea :value.',
    'different' => 'Los campos :attribute y :other deben ser diferentes.',
    'digits' => 'El campo :attribute debe ser :digits dígitos.',
    'digits_between' => 'El campo :attribute debe tener entre :min y :max dígitos.',
    'dimensions' => 'El campo :attribute tiene dimensiones de imagen inválidas.',
    'distinct' => 'El campo :attribute tiene un valor duplicado.',
    'doesnt_end_with' => 'El campo :attribute no debe terminar con uno de los siguientes: :values.',
    'doesnt_start_with' => 'El campo :attribute no debe comenzar con uno de los siguientes: :values.',
    'email' => 'El campo :attribute debe ser una dirección de correo electrónico válida.',
    'ends_with' => 'El campo :attribute debe terminar con uno de los siguientes: :values.',
    'enum' => 'El :attribute seleccionado es inválido.',
    'exists' => 'El :attribute seleccionado es inválido.',
    'file' => 'El campo :attribute debe ser un archivo.',
    'filled' => 'El campo :attribute es obligatorio.',
    'gt' => [
        'array' => 'El campo :attribute debe tener más de :value elementos.',
        'file' => 'El campo :attribute debe tener más de :value kilobytes.',
        'numeric' => 'El campo :attribute debe ser mayor que :value.',
        'string' => 'El campo :attribute debe tener más de :value caracteres.',
    ],
    'gte' => [
        'array' => 'El campo :attribute debe tener :value elementos o más.',
        'file' => 'El campo :attribute debe ser mayor o igual que :value kilobytes.',
        'numeric' => 'El campo :attribute debe ser mayor o igual que :value.',
        'string' => 'El campo :attribute debe tener :value caracteres o más.',
    ],
    'image' => 'El campo :attribute debe ser una imagen.',
    'in' => 'El :attribute seleccionado es inválido.',
    'in_array' => 'El campo :attribute no existe en :other.',
    'integer' => 'El campo :attribute debe ser un número entero.',
    'ip' => 'El campo :attribute debe ser una dirección IP válida.',
    'ipv4' => 'El campo :attribute debe ser una dirección IPv4 válida.',
    'ipv6' => 'El campo :attribute debe ser una dirección IPv6 válida.',
    'json' => 'El campo :attribute debe ser una cadena JSON válida.',
    'lowercase' => 'El campo :attribute debe estar en minúsculas.',
    'lt' => [
        'array' => 'El campo :attribute debe tener menos de :value elementos.',
        'file' => 'El campo :attribute debe tener menos de :value kilobytes.',
        'numeric' => 'El campo :attribute debe ser menor que :value.',
        'string' => 'El campo :attribute debe tener menos de :value caracteres.',
    ],
    'lte' => [
        'array' => 'El campo :attribute no puede tener más de :value elementos.',
        'file' => 'El campo :attribute debe ser menor o igual que :value kilobytes.',
        'numeric' => 'El campo :attribute debe ser menor o igual que :value.',
        'string' => 'El campo :attribute debe tener menos o igual que :value caracteres.',
    ],
    'mac_address' => 'El campo :attribute debe ser una dirección MAC válida.',
    'max' => [
        'array' => 'El campo :attribute no puede tener más de :max elementos.',
        'file' => 'El campo :attribute no puede ser mayor que :max kilobytes.',
        'numeric' => 'El campo :attribute no puede ser mayor que :max.',
        'string' => 'El campo :attribute no puede ser mayor que :max caracteres.',
    ],
    'max_digits' => 'El campo :attribute no puede tener más de :max dígitos.',
    'mimes' => 'El campo :attribute debe ser un archivo de tipo: :values.',
    'mimetypes' => 'El campo :attribute debe ser un archivo de tipo: :values.',
    'min' => [
        'array' => 'El campo :attribute debe tener al menos :min elementos.',
        'file' => 'El campo :attribute debe tener al menos :min kilobytes.',
        'numeric' => 'El campo :attribute debe ser al menos :min.',
        'string' => 'El campo :attribute debe tener al menos :min caracteres.',
    ],
    'min_digits' => 'El campo :attribute debe tener al menos :min dígitos.',
    'missing' => 'El campo :attribute debe faltar.',
    'missing_if' => 'El campo :attribute debe faltar cuando :other sea :value.',
    'missing_unless' => 'El campo :attribute debe faltar a menos que :other sea :value.',
    'missing_with' => 'El campo :attribute debe faltar cuando :value esté presente.',
    'missing_with_all' => 'El campo :attribute debe faltar cuando :values estén presentes.',
    'multiple_of' => 'El campo :attribute debe ser un múltiplo de :value.',
    'not_in' => 'El :attribute seleccionado es inválido.',
    'not_regex' => 'El formato del campo :attribute es inválido.',
    'numeric' => 'El campo :attribute debe ser un número.',
    'password' => [
        'letters' => 'El campo :attribute debe contener al menos una letra.',
        'mixed' => 'El campo :attribute debe contener al menos una letra mayúscula y una minúscula.',
        'numbers' => 'El campo :attribute debe contener al menos un número.',
        'symbols' => 'El campo :attribute debe contener al menos un símbolo.',
        'uncompromised' => 'El :attribute proporcionado ha sido comprometido. Elija otro.',
    ],
    'present' => 'El campo :attribute debe estar presente.',
    'prohibited' => 'El campo :attribute está prohibido.',
    'prohibited_if' => 'El campo :attribute está prohibido cuando :other es :value.',
    'prohibited_unless' => 'El campo :attribute está prohibido a menos que :other esté en :values.',
    'prohibits' => 'El campo :attribute prohíbe la presencia de :other.',
    'regex' => 'El formato del campo :attribute es inválido.',
    'required' => 'El campo :attribute es obligatorio.',
    'required_array_keys' => 'El campo :attribute debe contener entradas para: :values.',
    'required_if' => 'El campo :attribute es obligatorio cuando :other es :value.',
    'required_if_accepted' => 'El campo :attribute es obligatorio cuando :other es aceptado.',
    'required_unless' => 'El campo :attribute es obligatorio a menos que :other esté en :values.',
    'required_with' => 'El campo :attribute es obligatorio cuando :values está presente.',
    'required_with_all' => 'El campo :attribute es obligatorio cuando :values están presentes.',
    'required_without' => 'El campo :attribute es obligatorio cuando :values no está presente.',
    'required_without_all' => 'El campo :attribute es obligatorio cuando ninguno de :values está presente.',
    'same' => 'Los campos :attribute y :other deben coincidir.',
    'size' => [
        'numeric' => 'El campo :attribute debe ser :size.',
        'file' => 'El campo :attribute debe ser :size kilobytes.',
        'string' => 'El campo :attribute debe ser :size caracteres.',
        'array' => 'El campo :attribute debe tener :size elementos.',
    ],
    'starts_with' => 'El campo :attribute debe comenzar con uno de los siguientes: :values.',
    'string' => 'El campo :attribute debe ser una cadena.',
    'timezone' => 'El campo :attribute debe ser una zona horaria válida.',
    'unique' => 'El campo :attribute ya ha sido tomado.',
    'unique_entry' => 'Cada entrada debe estar asociada con un correo electrónico y un nombre único.',
    'uploaded' => 'El campo :attribute no se pudo cargar.',
    'uppercase' => 'El campo :attribute debe estar en mayúsculas.',
    'url' => 'El campo :attribute debe ser una URL válida.',
    'ulid' => 'El campo :attribute debe ser un ULID válido.',
    'uuid' => 'El campo :attribute debe ser un UUID válido.',
    'emptyValue' => 'El campo :attribute está vacío.',

    /*
    |--------------------------------------------------------------------------
    | Mensajes de validación personalizados
    |--------------------------------------------------------------------------
    |
    | Aquí puedes especificar mensajes de validación personalizados para los atributos,
    | usando la convención "attribute.rule" para nombrar las reglas. Esto nos permite
    | proporcionar rápidamente un mensaje de traducción específico para un atributo y regla dada.
    |
    */

    'custom' => [
        'leave_already_taken' => 'Registro fallido. Ya se han tomado días de vacaciones en esta fecha',
        'empty_field' => 'Línea :line : El campo :field no está completado.',
        'matricule_not_specified' => 'Línea :line : No se ha especificado el número de personal.',
        'site_not_specified' => 'Línea :line : No se ha especificado el sitio.',
        'site_not_existed' => 'Línea :line : El sitio no existe - :site.',
        'absence_type_not_found' => 'Línea :line : tipo de ausencia no encontrado - Código :leaveTypeCode',
        'leave_type_not_found' => 'Línea :line : Tipo de permiso no encontrado - Código :leaveTypeCode',
        'user_bad_site' => 'Línea :line : El usuario está en el sitio equivocado (:site) - Matrícula :matricule',
        'user_not_found' => 'Línea :line : Usuario no encontrado - Número de personal :matricule',
        'user_not_specified' => 'Línea :line : No se ha especificado el usuario',
        'user_deleted' => 'Línea :line : El usuario ha sido eliminado - Número de personal :matricule',
        'balance_invalid' => 'Línea :line : El saldo no coincide con el valor esperado',
        'user_not_on_your_clients' => 'Línea :line : El usuario no es parte de tu cliente - Número de personal :matricule',
        'InvalidLeaveTypeNameFormat' => 'Línea :line : El tipo de permiso debe ser una cadena de caracteres',
        'InvalidDurationFormat' => 'Línea :line : La duración del permiso debe ser numérica',
        'InvalidDurationValue' => 'Línea :line: La duración de la ausencia debe ser mayor que 0',
        'InvalidMatriculeFormat' => 'Línea :line : El número de personal debe ser numérico',
        'InvalidLastNameFormat' => 'Línea :line : El apellido debe ser una cadena de caracteres',
        'InvalidFirstNameFormat' => 'Línea :line : El nombre debe ser una cadena de caracteres',
        'InvalidDateFormat' => 'Línea :line : Formato de fecha no válido',
    ],

    /*
    |--------------------------------------------------------------------------
    | Atributos personalizados para la validación
    |--------------------------------------------------------------------------
    |
    | Las siguientes reglas se usan para reemplazar nuestros atributos de marcador de posición
    | por algo más amigable para el lector, como "Correo electrónico" en lugar de "email". Esto nos permite hacer que nuestros mensajes sean más expresivos.
    |
    */

    'attributes' => [
        'activity' => 'actividad',
        'activities' => 'actividades',
        'address' => 'dirección',
        'addresses' => 'direcciones',
        'age' => 'edad',
        'ages' => 'edades',
        'amount' => 'cantidad',
        'amounts' => 'cantidades',
        'answer' => 'respuesta',
        'answers' => 'respuestas',
        'available' => 'disponible',
        'availables' => 'disponibles',
        'barcode' => 'código de barras',
        'barcodes' => 'códigos de barras',
        'birth_date' => 'fecha de nacimiento',
        'brand' => 'marca',
        'brands' => 'marcas',
        'brand_name' => 'nombre de la marca',
        'buying_price' => 'precio de compra',
        'category' => 'categoría',
        'categories' => 'categorías',
        'city' => 'ciudad',
        'cities' => 'ciudades',
        'civility' => 'civismo',
        'civilities' => 'civismos',
        'comment' => 'comentario',
        'comments' => 'comentarios',
        'company' => 'empresa',
        'companies' => 'empresas',
        'confirmed' => 'confirmado',
        'confirmed_at' => 'confirmado el',
        'content' => 'contenido',
        'contents' => 'contenidos',
        'country' => 'país',
        'countries' => 'países',
        'customer' => 'cliente',
        'customers' => 'clientes',
        'day' => 'día',
        'days' => 'días',
        'date_end' => 'fecha de finalización',
        'date_start' => 'fecha de inicio',
        'directory' => 'directorio',
        'directory_name' => 'nombre del directorio',
        'directories' => 'directorios',
        'directories_name' => 'nombre de los directorios',
        'directories_names' => 'nombres de los directorios',
        'email_banned' => 'correo electrónico prohibido',
        'email_confirmed' => 'correo electrónico confirmado',
        'email_validated' => 'correo electrónico validado',
        'email_prohibited' => 'correo electrónico prohibido',
        'emails_banned' => 'correos electrónicos prohibidos',
        'emails_confirmed' => 'correos electrónicos confirmados',
        'emails_validated' => 'correos electrónicos validados',
        'emails_prohibited' => 'correos electrónicos prohibidos',
        'file' => 'archivo',
        'files' => 'archivos',
        'first_name' => 'nombre',
        'first_names' => 'nombres',
        'gender' => 'género',
        'genders' => 'géneros',
        'hour' => 'hora',
        'hours' => 'horas',
        'is_active' => '¿está activo?',
        'is_banned' => '¿está prohibido?',
        'job' => 'trabajo',
        'jobs' => 'trabajos',
        'last_name' => 'apellido',
        'last_names' => 'apellidos',
        'link' => 'enlace',
        'links' => 'enlaces',
        'month' => 'mes',
        'name' => 'nombre',
        'names' => 'nombres',
        'office' => 'oficina',
        'offices' => 'oficinas',
        'other' => 'otro',
        'others' => 'otros',
        'paid_at' => 'pagado el',
        'password' => 'contraseña',
        'password_confirmation' => 'confirmación de la contraseña',
        'password_current' => 'contraseña actual',
        'passwords' => 'contraseñas',
        'phone' => 'teléfono',
        'phones' => 'teléfonos',
        'postal_code' => 'código postal',
        'price' => 'precio',
        'published_at' => 'publicado el',
        'quantity' => 'cantidad',
        'quantities' => 'cantidades',
        'rate' => 'tasa',
        'rates' => 'tasas',
        'response' => 'respuesta',
        'responses' => 'respuestas',
        'role' => 'rol',
        'roles' => 'roles',
        'second' => 'segundo',
        'seconds' => 'segundos',
        'siren_number' => 'número siren',
        'siret_number' => 'número siret',
        'size' => 'tamaño',
        'sizes' => 'tamaños',
        'status' => 'estado',
        'statuses' => 'estados',
        'street' => 'calle',
        'subfolder' => 'subcarpeta',
        'subfolders' => 'subcarpetas',
        'subdirectory' => 'subdirectorio',
        'subdirectories' => 'subdirectorios',
        'subject' => 'asunto',
        'subjects' => 'asuntos',
        'summary' => 'resumen',
        'summarys' => 'resúmenes',
        'supplier' => 'proveedor',
        'suppliers' => 'proveedores',
        'tax' => 'impuesto',
        'time' => 'hora',
        'title' => 'título',
        'titles' => 'títulos',
        'user' => 'usuario',
        'users' => 'usuarios',
        'username' => 'nombre de usuario',
        'usernames' => 'nombres de usuario',
        'value' => 'valor',
        'values' => 'valores',
        'vat' => 'IVA',
        'vat_rate' => 'tasa de IVA',
        'website' => 'sitio web',
        'websites' => 'sitios web',
        'year' => 'año',
        'years' => 'años',
        'default_footer_text' => 'texto de pie de página predeterminado',
        'media_footer' => 'pie de página de medios',
        'survey_id' => 'ID de encuesta',
        'firstname' => 'primer nombre',
        'lastname' => 'apellido',
        'email' => 'correo electrónico',
        'label' => 'etiqueta',
        'description' => 'descripción',
        'started_at' => 'comenzado el',
        'ended_at' => 'finalizado el',
        'accompanying_person_limit' => 'límite de personas acompañantes',
        'event_status_id' => 'ID de estado del evento',
        'guest_ids' => 'ID de invitados',
        'event_id' => 'ID de evento',
        'phone_number' => 'número de teléfono',
        'phone_code' => 'código de teléfono',
        'app_from' => 'Creado desde',
        'sticker' => 'pegatina',
        'is_vip' => 'es VIP',
        'user_related_id' => 'ID de usuario relacionado',
        'is_notified_user_related' => 'usuario notificado por correo electrónico',
        'is_owner' => 'es propietario',
        'is_ticket_generated' => 'ticket generado',
        'collection_name' => 'nombre de la colección',
        'model_type' => 'tipo de modelo',
        'media' => 'medios',
        'header_text' => 'texto del encabezado',
        'guests' => 'invitados',
        'entry.survey_id' => 'ID de encuesta de la entrada',
        'entry.firstname' => 'primer nombre de la entrada',
        'entry.lastname' => 'apellido de la entrada',
        'entry.answers' => 'respuestas de la entrada',
        'entry_id' => 'ID de la entrada',
        'question_id' => 'ID de la pregunta',
        'hint' => 'pista',
        'order' => 'orden',
        'question_type_id' => 'ID de tipo de pregunta',
        'questionable_type' => 'tipo de entidad vinculada',
        'questionable_id' => 'ID de entidad vinculada',
        'is_required' => 'es obligatorio',
        'has_escape_answer' => 'pregunta tiene otra respuesta',
        'location_id' => 'ID de ubicación',
        'capacity' => 'capacidad',
        'overflow' => 'desbordamiento permitido',
        'reminder_days_before_session' => 'número de días para el recordatorio antes de la sesión',
        'thanks_days_after_session' => 'número de días para el agradecimiento después de la sesión',
        'feedback_survey_id' => 'ID de encuesta de retroalimentación',
        'is_survey_related' => 'pregunta relacionada con encuesta',
        'is_signable' => 'requiere firma',
        'details' => 'detalles',
        'creator_id' => 'ID del creador',
        'survey_status_id' => 'ID de estado de la encuesta',
        'is_template' => 'plantilla',
        'max_entries' => 'máximo de entradas',
        'updated_by_id' => 'actualizado por ID de usuario',
        'expires_at' => 'vence el',
        'footer_text' => 'texto del pie de página',
        'title_alignment' => 'alineación del título',
        'holiday_already_exists_for_date_and_client' => 'Ya existe un día festivo para esta fecha y este cliente.'
    ],
];
