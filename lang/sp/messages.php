<?php

return [
    'morning' => ' (<PERSON><PERSON><PERSON>)',
    'afternoon' => ' (<PERSON><PERSON>)',
    'CreateSettingsSuccess' => 'Creación de configuraciones exitosa',
    'RemoveSettingsSuccess' => 'Las configuraciones han sido eliminadas exitosamente.',
    'UpdateSuccess' => 'Actualización exitosa',
    'WaitingPay' => 'En espera de pago',
    'Waiting' => 'En espera',
    'InProcess' => 'En proceso',
    'SuccessStoreLeave' => 'Permiso creado',
    'SuccessCancelLeave' => 'Permiso cancelado',
    'AskForCancelLeave' => 'Solicitud de cancelación de permiso enviada',
    'SuccessCallback' => 'Llamada exitosa',
    'CantCancelLeaveNotValidated' => 'No se puede cancelar este permiso porque no ha sido validado',
    'CantCancelLeaveTransmitted' => 'No se puede cancelar este permiso porque ha sido transmitido',
    'CantCancelLeaveRefused' => 'No se puede cancelar este permiso porque ha sido rechazado',
    'LeaveCanceled' => 'Permiso cancelado',
    'DontOwnLeaveOrManagerUser' => 'No tienes permiso para modificar este permiso o no eres el administrador de este usuario',
    'CantCancelLeaveValidated' => 'No se puede cancelar este permiso porque ha sido validado',
    'NotAdminFromUser' => 'No eres administrador de este usuario',
    'ErrorTransmisLeaveWrongStatus' => 'Error, no se puede transmitir el permiso porque tiene un estado incorrecto',
    'SuccessRefuseLeave' => 'Permiso rechazado',
    'SuccessMassValidationLeave' => 'Permisos transmitidos',
    'SuccessTransmitLeave' => 'Permiso transmitido',
    'SuccessGetClosedDay' => 'Días festivos recuperados',
    'SuccessGetHolidays' => 'Vacaciones recuperadas',
    'SuccessGetLeave' => 'Permiso recuperado',
    'SuccessGetUser' => 'Usuario recuperado',
    'SuccessGetCompteur' => 'Contadores recuperados',
    'SuccessStoreCompteur' => 'Contador creado',
    'SuccessUpdateCompteur' => 'Contador actualizado',
    'SuccessUpdateUser' => 'Usuario actualizado',
    'SuccessUpdateBusinessDay' => 'Día hábil actualizado',
    'SuccessGetCurrentUser' => 'Usuario actual recuperado',
    'SuccessLogout' => 'Cierre de sesión exitoso',
    'SuccessUpdateCustomer' => 'Cliente actualizado',
    'SuccessDeleteCustomer' => 'Cliente eliminado',
    'SuccessGetBusinessDay' => 'Día hábil recuperado',
    'SuccessGetDocs' => 'Documentos recuperados',
    'SuccessUpdateDoc' => 'Documento actualizado',
    'SuccessDeleteFile' => 'Archivo eliminado',
    'SuccessGetHistory' => 'Historial recuperado',
    'SuccessDeleteHistory' => 'Historial eliminado',
    'CantCancelLeave' => 'No se puede cancelar este permiso',
    'OtherAdminValidated' => 'Otro administrador ya ha validado este permiso',
    'UserNotInClient' => 'El usuario no pertenece a tu cliente',
    'SuccessAttachUsersToDirector' => 'Usuarios asignados al director',
    'SuccessDetachUsersToDirector' => 'Usuarios desvinculados del director',
    'SuccessStoreLeaveByTakingInNCounts' => 'Permiso creado utilizando el contador N',
    'SuccessStoreHoliday'=>'Los días festivos se han creado correctamente',
    'UserWithNoTags' => 'Empleados sin equipos',
    'UserNotManaged' => 'Empleados no gestionados',
    'success_store_leave_with_errors' => 'Los permisos fueron creados exitosamente, pero ocurrieron errores.',
    'AnyTags' => 'Sin equipo',
    'ImportSucces' => 'Importación exitosa',
    'ErrorImport' => 'Error durante la importación',
    'acquis_cp_n_1' => 'adquirido_cp_n_1',
    'pris_cp_n_1' => 'tomado_cp_n_1',
    'solde_cp_n_1' => 'saldo_cp_n_1',
    'acquis_cp_n' => 'adquirido_cp_n',
    'pris_cp_n' => 'tomado_cp_n',
    'solde_cp_n' => 'saldo_cp_n',
    'acquis_type_n_1' => 'adquirido_:tipo_n_1',
    'pris_type_n_1' => 'tomado_:tipo_n_1',
    'solde_type_n_1' => 'saldo_:tipo_n_1',
    'acquis_type_n' => 'adquirido_:tipo_n',
    'pris_type_n' => 'tomado_:tipo_n',
    'solde_type_n' => 'saldo_:tipo_n',
    'ImportUserLeaveCountPartiallySucces' => 'Se produjeron errores durante la importación de los contadores (:successRows / :rows líneas importadas).',
    'ImportUserLeavesPartiallySucces' => 'Se produjeron errores durante la importación de las ausencias (:successRows / :rows líneas importadas).',
    'RegistrationNumber' => 'Número de registro',
    'RegistrationNumberEmployee' => 'Número de registro de empleado',
    'CompanyName' => 'Razón social',
    'MainEstablishment' => 'Establecimiento principal',
    'Email' => 'Correo electrónico',
    'Employee' => 'Empleado',
    'EmployeeName' => 'Nombre y apellido',
    'EmployeeLastname' => 'Apellido',
    'EmployeeFirstname' => 'Nombre',
    'Establishment' => 'Establecimiento',
    'EntryDate' => 'Fecha de ingreso',
    'Acquired' => 'Adquirido',
    'Taken' => 'Tomado',
    'Balance' => 'Saldo',
    'Label' => 'Etiqueta',
    'CodeConges' => 'Código de permiso',
    'CodeAbsence' => 'Código de ausencia',
    'StartDate' => 'Fecha de inicio',
    'EndDate' => 'Fecha de finalización',
    'Duration' => 'Número de días',
    'Status' => 'Estado',
    'Site' => 'Sitio',
    'TypeCongesAbsence' => 'Tipo de ausencia/vacaciones',
    'TypeAbsence' => 'Tipo de ausencia',
    'TypeConges' => 'Tipo',
    'Tag' => 'Equipo',
    'HeaderRegistrationNumber' => 'número_registro',
    'HeaderRegistrationNumberEmployee' => 'número_registro_empleado',
    'HeaderCompanyName' => 'razón_social',
    'HeaderEstablishment' => 'establecimiento',
    'HeaderMainEstablishment' => 'establecimiento_principal',
    'HeaderEmail' => 'correo_electronico',
    'HeaderEmployee' => 'empleado',
    'HeaderLastname' => 'apellido',
    'HeaderFirstname' => 'nombre',
    'HeaderEntryDate' => 'fecha_ingreso',
    'HeaderEntryDdate' => 'fecha_ingreso',
    'HeaderStartDate' => 'fecha_inicio',
    'HeaderEndDate' => 'fecha_fin',
    'HeaderDuration' => 'duración',
    'HeaderAcquired' => 'adquirido',
    'HeaderTaken' => 'tomado',
    'HeaderBalance' => 'saldo',
    'HeaderRemaining' => 'restante',
    'HeaderLeaveTypeName' => 'tipo_permiso',
    'Total' => 'Total',
    'TotalGeneral' => 'Total general',
    'store_leave_with_errors' => 'Parece que hubo un error al establecer los permisos...',
    'waiting_validation_by_manager' => 'Esperando validación por :manager',
    'waiting_cancellation_by_manager' => 'Esperando cancelación por :manager',
    'VALIDATED' => 'Validado',
    'SUBMITTED' => 'Enviado para validación',
    'REFUSED' => 'Rechazado',
    'CANCELED' => 'Cancelado',
    'TRANSMITTED' => 'Transmitido en pago',
    'SUBMITTED_TO_CANCELLATION' => 'Enviado para cancelación',
    'ImportPartiallySucces' => 'La importación fue parcialmente exitosa',
    'UnsupportedFileType' => 'Tipo de archivo no soportado. Por favor, utilice un archivo CSV, XLSX o XLS.',
    'import' => [
        'error_title' => 'Error de entrada',
        'error_value' => 'El valor no está en la lista.',
        'prompt_title' => 'Elija de la lista',
        'prompt_value' => 'Por favor, elija un valor de la lista desplegable.',
    ],
    'AlreadyTreatedLeavesCount' => 'Conteo de permisos ya tratados',
    'WaitingValidationLeavesCount' => 'Conteo de permisos ya tratados',
    'WaitingCancelationLeavesCount' => 'Conteo de permisos en espera de cancelación',
    'SuccessCancelLeaveSubToCancel'=> 'La solicitud de cancelación ha sido denegada. La licencia queda cancelada.',
    'SuccessValidateLeaveSubToCancel'=> 'La solicitud de cancelación se ha validado correctamente. La licencia está cancelada.',
    'SuccessValidateLeaveSubToCancelValidated' => 'La solicitud de cancelación ha sido denegada. El permiso ha vuelto a su estado \'Validado\'.',
    'SuccessValidateLeaveSubToCancelSubmitted' => 'La solicitud de cancelación ha sido denegada. El permiso ha vuelto a su estado \'Presentado\'.',
    'SuccessMassValidationLeaveSubToCancel'=> 'Las solicitudes de cancelación han sido validadas con éxito, por lo tanto, los permisos están cancelados.',
    'CantCancelAdminOwnLeave'=>'Un administrador no puede cancelar sus propios días de vacaciones',
    'SuccessTransmittedLeaveCanceled'=> 'La solicitud de cancelación ha sido validada con éxito, por lo tanto, el permiso está \'Cancelado\'.'
];
